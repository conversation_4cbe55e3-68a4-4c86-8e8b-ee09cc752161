import { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Modal,
  Form,
  Input,
  Switch,
  message,
  Space,
  Tag,
  Popconfirm,
  // Avatar,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  // FileTextOutlined,
} from '@ant-design/icons';
import type { EmailTemplate } from '../types';
import RichTextEditor from '../components/RichTextEditor';

const { Search } = Input;

const Templates: React.FC = () => {
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [previewTemplate, setPreviewTemplate] = useState<EmailTemplate | null>(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  // 模拟数据
  useEffect(() => {
    const mockTemplates: EmailTemplate[] = [
      {
        id: 1,
        userId: 1,
        name: '欢迎邮件',
        subject: '欢迎加入我们的团队！',
        content: '<p>亲爱的 {{name}}，</p><p>欢迎加入我们的团队！我们很高兴能与您一起工作。</p><p>如有任何问题，请随时联系我们。</p><p>祝好！</p>',
        isPublic: true,
        createdAt: '2024-01-01',
        updatedAt: '2024-01-01',
        user: {
          id: 1,
          username: 'admin',
          displayName: '管理员',
        },
      },
      {
        id: 2,
        userId: 1,
        name: '会议邀请',
        subject: '会议邀请 - {{meeting_title}}',
        content: '<p>您好，</p><p>我们诚挚邀请您参加以下会议：</p><p><strong>会议主题：</strong>{{meeting_title}}</p><p><strong>时间：</strong>{{meeting_time}}</p><p><strong>地点：</strong>{{meeting_location}}</p><p>期待您的参与！</p>',
        isPublic: false,
        createdAt: '2024-01-02',
        updatedAt: '2024-01-02',
        user: {
          id: 1,
          username: 'admin',
          displayName: '管理员',
        },
      },
    ];

    setTemplates(mockTemplates);
  }, []);

  const handleAdd = () => {
    setEditingTemplate(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (template: EmailTemplate) => {
    setEditingTemplate(template);
    form.setFieldsValue(template);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      setTemplates(templates.filter(t => t.id !== id));
      message.success('模板删除成功');
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handlePreview = (template: EmailTemplate) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  const handleCopy = (template: EmailTemplate) => {
    const newTemplate = {
      ...template,
      id: Date.now(),
      name: `${template.name} (副本)`,
      isPublic: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setTemplates([...templates, newTemplate]);
    message.success('模板复制成功');
  };

  const handleUseTemplate = (template: EmailTemplate) => {
    // 打开写邮件页面并填充模板内容
    const params = new URLSearchParams({
      subject: template.subject,
      content: template.content,
    });
    window.open(`/compose?${params.toString()}`, '_blank');
  };

  const handleSubmit = async (values: any) => {
    try {
      if (editingTemplate) {
        // 更新模板
        const updatedTemplates = templates.map(t =>
          t.id === editingTemplate.id ? { ...t, ...values, updatedAt: new Date().toISOString() } : t
        );
        setTemplates(updatedTemplates);
        message.success('模板更新成功');
      } else {
        // 添加新模板
        const newTemplate: EmailTemplate = {
          id: Date.now(),
          userId: 1,
          ...values,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          user: {
            id: 1,
            username: 'admin',
            displayName: '管理员',
          },
        };
        setTemplates([...templates, newTemplate]);
        message.success('模板添加成功');
      }
      setModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('操作失败');
    }
  };

  const filteredTemplates = templates.filter(template =>
    template.name.toLowerCase().includes(searchText.toLowerCase()) ||
    template.subject.toLowerCase().includes(searchText.toLowerCase())
  );

  return (
    <div className="p-6">
      <Card
        title="邮件模板"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            创建模板
          </Button>
        }
      >
        <div className="mb-4">
          <Search
            placeholder="搜索模板..."
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: 300 }}
            allowClear
          />
        </div>

        <List
          grid={{ gutter: 16, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 4 }}
          dataSource={filteredTemplates}
          loading={loading}
          renderItem={(template) => (
            <List.Item>
              <Card
                size="small"
                title={
                  <div className="flex items-center justify-between">
                    <span className="truncate">{template.name}</span>
                    {template.isPublic && (
                      <Tag color="blue" size="small">公开</Tag>
                    )}
                  </div>
                }
                actions={[
                  <Button
                    type="text"
                    size="small"
                    icon={<EyeOutlined />}
                    onClick={() => handlePreview(template)}
                  />,
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopy(template)}
                  />,
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(template)}
                  />,
                  <Popconfirm
                    title="确定删除这个模板吗？"
                    onConfirm={() => handleDelete(template.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      danger
                    />
                  </Popconfirm>,
                ]}
              >
                <div className="space-y-2">
                  <div>
                    <div className="text-sm font-medium text-gray-700">主题:</div>
                    <div className="text-sm text-gray-600 truncate">{template.subject}</div>
                  </div>
                  <div>
                    <div className="text-sm font-medium text-gray-700">内容预览:</div>
                    <div
                      className="text-sm text-gray-600 line-clamp-3"
                      dangerouslySetInnerHTML={{
                        __html: template.content.substring(0, 100) + '...'
                      }}
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-400">
                      {new Date(template.updatedAt).toLocaleDateString()}
                    </div>
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => handleUseTemplate(template)}
                    >
                      使用模板
                    </Button>
                  </div>
                </div>
              </Card>
            </List.Item>
          )}
        />
      </Card>

      {/* 添加/编辑模板模态框 */}
      <Modal
        title={editingTemplate ? '编辑模板' : '创建模板'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label="模板名称"
            name="name"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>

          <Form.Item
            label="邮件主题"
            name="subject"
            rules={[{ required: true, message: '请输入邮件主题' }]}
          >
            <Input placeholder="请输入邮件主题，可使用 {{变量名}} 作为占位符" />
          </Form.Item>

          <Form.Item
            label="邮件内容"
            name="content"
            rules={[{ required: true, message: '请输入邮件内容' }]}
          >
            <RichTextEditor
              placeholder="请输入邮件内容，可使用 {{变量名}} 作为占位符"
              height={250}
            />
          </Form.Item>

          <Form.Item
            label="公开模板"
            name="isPublic"
            valuePropName="checked"
          >
            <Switch />
            <div className="text-sm text-gray-500 mt-1">
              公开模板可以被其他用户使用
            </div>
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                {editingTemplate ? '更新' : '创建'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 模板预览模态框 */}
      <Modal
        title="模板预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button
            key="use"
            type="primary"
            onClick={() => {
              if (previewTemplate) {
                handleUseTemplate(previewTemplate);
              }
              setPreviewVisible(false);
            }}
          >
            使用模板
          </Button>,
        ]}
        width={700}
      >
        {previewTemplate && (
          <div className="space-y-4">
            <div>
              <strong>模板名称:</strong> {previewTemplate.name}
            </div>
            <div>
              <strong>邮件主题:</strong> {previewTemplate.subject}
            </div>
            <div>
              <strong>邮件内容:</strong>
              <div
                className="mt-2 p-3 border border-gray-200 rounded min-h-32"
                dangerouslySetInnerHTML={{ __html: previewTemplate.content }}
              />
            </div>
            <div className="text-sm text-gray-500">
              <strong>提示:</strong> {{变量名}} 格式的文本在使用时可以替换为实际内容
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Templates;
