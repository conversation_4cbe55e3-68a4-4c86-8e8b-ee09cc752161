#!/bin/bash

# 开发环境快速设置脚本

set -e

echo "🚀 设置邮件Token系统开发环境..."

# 配置变量
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
BACKEND_DIR="$PROJECT_ROOT"
FRONTEND_DIR="$(dirname "$PROJECT_ROOT")/frontend"

# 1. 检查环境
echo "🔍 检查开发环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 16+"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 16 ]; then
    echo "❌ Node.js版本过低，需要16+，当前版本: $(node -v)"
    exit 1
fi

echo "✅ Node.js版本: $(node -v)"

# 检查npm
if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

echo "✅ npm版本: $(npm -v)"

# 2. 安装依赖
echo "📦 安装项目依赖..."

# 后端依赖
echo "安装后端依赖..."
cd "$BACKEND_DIR"
npm install

# 前端依赖
if [ -d "$FRONTEND_DIR" ]; then
    echo "安装前端依赖..."
    cd "$FRONTEND_DIR"
    npm install
else
    echo "⚠️  前端目录不存在: $FRONTEND_DIR"
fi

# 3. 创建开发环境配置
echo "⚙️  创建开发环境配置..."

# 后端开发配置
cd "$BACKEND_DIR"
if [ ! -f ".env.development" ]; then
    cat > .env.development << 'EOF'
# 数据库配置
DATABASE_URL="mysql://mailuser:HOUsc@<EMAIL>:3306/mailserver"

# JWT配置
JWT_SECRET="dev-jwt-secret-change-in-production"
JWT_EXPIRES_IN="7d"

# 邮件服务器配置
IMAP_HOST="mail.blindedby.love"
IMAP_PORT=993
IMAP_SECURE=true
SMTP_HOST="mail.blindedby.love"
SMTP_PORT=587
SMTP_SECURE=false

# Token认证配置
MAIL_TOKEN_ENABLED=true
MAIL_TOKEN_EXPIRY=86400
MAIL_TOKEN_CLEANUP_INTERVAL=3600
ENABLE_TOKEN_LOGGING=true

# 开发模式配置
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# CORS配置
CORS_ORIGIN="http://localhost:5173"
EOF
    echo "✅ 后端开发配置已创建"
else
    echo "✅ 后端开发配置已存在"
fi

# 前端开发配置
if [ -d "$FRONTEND_DIR" ]; then
    cd "$FRONTEND_DIR"
    if [ ! -f ".env.development" ]; then
        cat > .env.development << 'EOF'
# API配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=10000

# 开发模式配置
VITE_NODE_ENV=development
VITE_LOG_LEVEL=debug

# 功能开关
VITE_ENABLE_TOKEN_AUTH=true
VITE_ENABLE_DEBUG_PANEL=true
EOF
        echo "✅ 前端开发配置已创建"
    else
        echo "✅ 前端开发配置已存在"
    fi
fi

# 4. 创建开发工具
echo "🛠️  创建开发工具..."

cd "$BACKEND_DIR"
mkdir -p dev-tools

# Token API测试脚本
cat > dev-tools/test-token-api.js << 'EOF'
const axios = require('axios');

const API_BASE = 'http://localhost:3000';
const TEST_USER = '<EMAIL>';
const TEST_PASSWORD = 'your-password'; // 请修改为实际密码

async function testTokenAPI() {
  try {
    console.log('🧪 测试Token API...');
    
    // 1. 用户登录获取JWT
    console.log('1️⃣ 用户登录...');
    const loginResponse = await axios.post(`${API_BASE}/api/auth/login`, {
      email: TEST_USER,
      password: TEST_PASSWORD
    });
    
    const jwtToken = loginResponse.data.token;
    console.log('✅ 登录成功，获得JWT Token');
    
    // 2. 生成邮件Token
    console.log('2️⃣ 生成邮件Token...');
    const tokenResponse = await axios.post(
      `${API_BASE}/api/auth/generate-mail-token`,
      { purpose: 'imap' },
      { headers: { Authorization: `Bearer ${jwtToken}` } }
    );
    
    const mailToken = tokenResponse.data.data.token;
    console.log('✅ 邮件Token生成成功');
    console.log(`Token: ${mailToken.substring(0, 20)}...`);
    
    // 3. 验证邮件Token
    console.log('3️⃣ 验证邮件Token...');
    const verifyResponse = await axios.post(`${API_BASE}/api/auth/verify-mail-token`, {
      username: TEST_USER,
      token: mailToken,
      purpose: 'imap'
    });
    
    if (verifyResponse.data.valid) {
      console.log('✅ Token验证成功');
      console.log('用户信息:', verifyResponse.data);
    } else {
      console.log('❌ Token验证失败');
    }
    
    // 4. 获取Token统计
    console.log('4️⃣ 获取Token统计...');
    const statsResponse = await axios.get(
      `${API_BASE}/api/auth/mail-token-stats`,
      { headers: { Authorization: `Bearer ${jwtToken}` } }
    );
    
    console.log('✅ Token统计:', statsResponse.data.data);
    
    console.log('🎉 所有测试通过');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

if (require.main === module) {
  testTokenAPI();
}

module.exports = { testTokenAPI };
EOF

# 开发服务启动脚本
cat > dev-tools/start-dev.sh << 'EOF'
#!/bin/bash

# 开发服务启动脚本

echo "🚀 启动开发服务..."

# 获取项目路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKEND_DIR="$(dirname "$SCRIPT_DIR")"
FRONTEND_DIR="$(dirname "$BACKEND_DIR")/frontend"

# 启动后端服务
echo "📡 启动后端服务..."
cd "$BACKEND_DIR"

# 编译TypeScript
npm run build

# 启动开发服务（后台运行）
npm run dev &
BACKEND_PID=$!

echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"
echo "📍 后端地址: http://localhost:3000"

# 等待后端服务启动
sleep 3

# 检查后端服务
if curl -s http://localhost:3000/api/health >/dev/null; then
    echo "✅ 后端服务健康检查通过"
else
    echo "❌ 后端服务启动失败"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# 启动前端服务
if [ -d "$FRONTEND_DIR" ]; then
    echo "🎨 启动前端服务..."
    cd "$FRONTEND_DIR"
    
    # 启动前端开发服务（后台运行）
    npm run dev &
    FRONTEND_PID=$!
    
    echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"
    echo "📍 前端地址: http://localhost:5173"
else
    echo "⚠️  前端目录不存在，跳过前端服务启动"
    FRONTEND_PID=""
fi

# 创建停止脚本
cat > "$BACKEND_DIR/dev-tools/stop-dev.sh" << STOP_EOF
#!/bin/bash
echo "⏹️  停止开发服务..."
if [ -n "$BACKEND_PID" ]; then
    kill $BACKEND_PID 2>/dev/null && echo "✅ 后端服务已停止"
fi
if [ -n "$FRONTEND_PID" ]; then
    kill $FRONTEND_PID 2>/dev/null && echo "✅ 前端服务已停止"
fi
STOP_EOF

chmod +x "$BACKEND_DIR/dev-tools/stop-dev.sh"

echo ""
echo "🎉 开发环境启动完成！"
echo "=================================================="
echo "📍 服务地址:"
echo "  - 后端API: http://localhost:3000"
if [ -n "$FRONTEND_PID" ]; then
echo "  - 前端界面: http://localhost:5173"
fi
echo ""
echo "🔧 管理命令:"
echo "  - 停止服务: ./dev-tools/stop-dev.sh"
echo "  - 测试Token: node dev-tools/test-token-api.js"
echo "  - 查看日志: tail -f logs/combined.log"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap "echo ''; echo '⏹️  正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit 0" INT

# 保持脚本运行
wait
EOF

chmod +x dev-tools/start-dev.sh

# 数据库连接测试脚本
cat > dev-tools/test-db-connection.js << 'EOF'
const { PrismaClient } = require('@prisma/client');

async function testDatabaseConnection() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 测试数据库连接...');
    
    // 测试基本连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 测试用户表
    const userCount = await prisma.user.count();
    console.log(`✅ 用户表查询成功，共 ${userCount} 个用户`);
    
    // 测试Token表
    const tokenCount = await prisma.mailToken.count();
    console.log(`✅ Token表查询成功，共 ${tokenCount} 个Token`);
    
    console.log('🎉 数据库测试通过');
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  testDatabaseConnection();
}

module.exports = { testDatabaseConnection };
EOF

echo "✅ 开发工具已创建"

# 5. 编译项目
echo "🔨 编译项目..."
cd "$BACKEND_DIR"
npm run build

# 6. 测试数据库连接
echo "🔍 测试数据库连接..."
if node dev-tools/test-db-connection.js; then
    echo "✅ 数据库连接测试通过"
else
    echo "⚠️  数据库连接测试失败，请检查配置"
fi

echo ""
echo "🎉 开发环境设置完成！"
echo "=================================================="
echo ""
echo "📁 项目结构:"
echo "  - 后端目录: $BACKEND_DIR"
if [ -d "$FRONTEND_DIR" ]; then
echo "  - 前端目录: $FRONTEND_DIR"
fi
echo "  - 开发工具: $BACKEND_DIR/dev-tools/"
echo ""
echo "🚀 快速开始:"
echo "  1. 启动开发服务: ./dev-tools/start-dev.sh"
echo "  2. 测试Token API: node dev-tools/test-token-api.js"
echo "  3. 测试数据库: node dev-tools/test-db-connection.js"
echo ""
echo "📖 详细文档: docs/LOCAL_DEVELOPMENT.md"
