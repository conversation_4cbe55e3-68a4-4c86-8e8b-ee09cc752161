const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testAppPasswordFeature() {
  try {
    console.log('开始测试应用专用密码功能...');

    // 1. 检查数据库连接
    console.log('1. 检查数据库连接...');
    await prisma.$connect();
    console.log('✓ 数据库连接成功');

    // 2. 检查是否存在app_passwords表
    console.log('2. 检查app_passwords表...');
    try {
      const result = await prisma.$queryRaw`SHOW TABLES LIKE 'app_passwords'`;
      if (result.length === 0) {
        console.log('⚠ app_passwords表不存在，需要运行迁移');
        
        // 尝试创建表
        console.log('3. 创建app_passwords表...');
        await prisma.$executeRaw`
          CREATE TABLE IF NOT EXISTS app_passwords (
            id int NOT NULL AUTO_INCREMENT,
            user_id int NOT NULL,
            name varchar(191) NOT NULL,
            password varchar(191) NOT NULL,
            purpose varchar(191) NOT NULL DEFAULT 'imap',
            is_active boolean NOT NULL DEFAULT true,
            last_used_at datetime(3) NULL,
            expires_at datetime(3) NULL,
            created_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
            updated_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
            PRIMARY KEY (id),
            UNIQUE KEY app_passwords_user_id_name_key (user_id, name),
            KEY app_passwords_user_id_fkey (user_id),
            CONSTRAINT app_passwords_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `;
        console.log('✓ app_passwords表创建成功');
      } else {
        console.log('✓ app_passwords表已存在');
      }
    } catch (error) {
      console.error('❌ 检查/创建app_passwords表失败:', error.message);
      return;
    }

    // 3. 查找一个测试用户
    console.log('4. 查找测试用户...');
    const testUser = await prisma.user.findFirst({
      where: { isActive: true },
      select: { id: true, email: true }
    });

    if (!testUser) {
      console.log('⚠ 没有找到活跃用户，无法测试');
      return;
    }

    console.log(`✓ 找到测试用户: ${testUser.email} (ID: ${testUser.id})`);

    // 4. 测试创建应用专用密码
    console.log('5. 测试创建应用专用密码...');
    try {
      // 先删除可能存在的测试密码
      await prisma.appPassword.deleteMany({
        where: {
          userId: testUser.id,
          name: 'Test IMAP Password'
        }
      });

      const appPassword = await prisma.appPassword.create({
        data: {
          userId: testUser.id,
          name: 'Test IMAP Password',
          password: '$6$testsalt$testhash', // 测试用的哈希
          purpose: 'imap',
          isActive: true
        }
      });

      console.log(`✓ 应用专用密码创建成功 (ID: ${appPassword.id})`);

      // 5. 测试查询应用专用密码
      console.log('6. 测试查询应用专用密码...');
      const userAppPasswords = await prisma.appPassword.findMany({
        where: { userId: testUser.id }
      });

      console.log(`✓ 查询到 ${userAppPasswords.length} 个应用专用密码`);

      // 6. 测试更新最后使用时间
      console.log('7. 测试更新最后使用时间...');
      await prisma.appPassword.update({
        where: { id: appPassword.id },
        data: { lastUsedAt: new Date() }
      });

      console.log('✓ 最后使用时间更新成功');

      // 7. 清理测试数据
      console.log('8. 清理测试数据...');
      await prisma.appPassword.delete({
        where: { id: appPassword.id }
      });

      console.log('✓ 测试数据清理完成');

    } catch (error) {
      console.error('❌ 应用专用密码操作失败:', error.message);
    }

    console.log('\n🎉 应用专用密码功能测试完成！');

  } catch (error) {
    console.error('❌ 测试失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testAppPasswordFeature().catch(console.error);
