#!/usr/bin/env node

/**
 * 完整的数据库迁移脚本
 * 用于处理Prisma schema更新和数据库迁移
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 开始数据库迁移流程...\n');

// 步骤1: 检查环境
console.log('📋 步骤1: 检查环境配置');
try {
  const envPath = path.join(__dirname, '../.env');
  if (!fs.existsSync(envPath)) {
    throw new Error('.env 文件不存在');
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  if (!envContent.includes('DATABASE_URL')) {
    throw new Error('.env 文件中缺少 DATABASE_URL');
  }
  
  console.log('✅ 环境配置检查通过');
} catch (error) {
  console.error('❌ 环境配置检查失败:', error.message);
  process.exit(1);
}

// 步骤2: 检查Prisma schema
console.log('\n📋 步骤2: 检查Prisma schema');
try {
  const schemaPath = path.join(__dirname, '../prisma/schema.prisma');
  if (!fs.existsSync(schemaPath)) {
    throw new Error('schema.prisma 文件不存在');
  }
  
  const schemaContent = fs.readFileSync(schemaPath, 'utf8');
  
  // 检查关键模型是否存在
  const requiredModels = ['User', 'Email', 'SubAccountPermission', 'SubAccountUsage'];
  const missingModels = requiredModels.filter(model => 
    !schemaContent.includes(`model ${model}`)
  );
  
  if (missingModels.length > 0) {
    throw new Error(`缺少必需的模型: ${missingModels.join(', ')}`);
  }
  
  console.log('✅ Prisma schema 检查通过');
} catch (error) {
  console.error('❌ Prisma schema 检查失败:', error.message);
  process.exit(1);
}

// 步骤3: 生成Prisma客户端
console.log('\n📋 步骤3: 生成Prisma客户端');
try {
  console.log('正在生成Prisma客户端...');
  execSync('npx prisma generate', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  console.log('✅ Prisma客户端生成成功');
} catch (error) {
  console.error('❌ Prisma客户端生成失败:', error.message);
  process.exit(1);
}

// 步骤4: 推送数据库更改
console.log('\n📋 步骤4: 推送数据库更改');
try {
  console.log('正在推送数据库更改...');
  execSync('npx prisma db push --accept-data-loss', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  console.log('✅ 数据库更改推送成功');
} catch (error) {
  console.error('❌ 数据库更改推送失败:', error.message);
  console.log('\n尝试使用迁移方式...');
  
  try {
    execSync('npx prisma migrate dev --name add-sub-account-features', { 
      stdio: 'inherit',
      cwd: path.join(__dirname, '..')
    });
    console.log('✅ 数据库迁移成功');
  } catch (migrateError) {
    console.error('❌ 数据库迁移也失败了:', migrateError.message);
    process.exit(1);
  }
}

// 步骤5: 验证数据库连接
console.log('\n📋 步骤5: 验证数据库连接');
try {
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  
  console.log('正在测试数据库连接...');
  
  // 测试基本查询
  const userCount = await prisma.user.count();
  console.log(`✅ 数据库连接成功，当前用户数: ${userCount}`);
  
  // 测试子账户相关表
  try {
    const permissionCount = await prisma.subAccountPermission.count();
    console.log(`✅ 子账户权限表可用，记录数: ${permissionCount}`);
  } catch (permError) {
    console.log('⚠️  子账户权限表可能需要初始化');
  }
  
  await prisma.$disconnect();
} catch (error) {
  console.error('❌ 数据库连接验证失败:', error.message);
  process.exit(1);
}

// 步骤6: 重新构建项目
console.log('\n📋 步骤6: 重新构建项目');
try {
  console.log('正在重新构建项目...');
  execSync('npm run build', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  console.log('✅ 项目构建成功');
} catch (error) {
  console.error('❌ 项目构建失败:', error.message);
  process.exit(1);
}

console.log('\n🎉 数据库迁移流程完成！');
console.log('\n📝 后续步骤:');
console.log('1. 重启后端服务器: npm run dev');
console.log('2. 检查API端点是否正常工作');
console.log('3. 测试子账户相关功能');
console.log('4. 如有问题，查看日志文件: logs/error.log');

process.exit(0);
