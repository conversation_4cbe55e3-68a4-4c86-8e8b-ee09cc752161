# 邮件Token认证系统部署指南

## 概述

本文档描述如何在生产服务器上部署邮件Token认证系统，该系统使用JWT令牌替代传统的用户名密码认证，提供更安全的邮件访问方式。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用      │    │   后端API       │    │   邮件服务器    │
│                 │    │                 │    │                 │
│ - Token管理     │◄──►│ - Token生成     │◄──►│ - Dovecot       │
│ - 用户界面      │    │ - Token验证     │    │ - Token认证     │
│                 │    │ - 用户管理      │    │ - IMAP/SMTP     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   MySQL数据库   │
                       │                 │
                       │ - 用户数据      │
                       │ - Token记录     │
                       │ - 使用日志      │
                       └─────────────────┘
```

## 部署前准备

### 系统要求

- **操作系统**: Ubuntu 20.04+ / CentOS 7+
- **Node.js**: 16.0+
- **MySQL**: 8.0+
- **Dovecot**: 2.3+
- **内存**: 最少2GB
- **磁盘**: 最少10GB可用空间

### 权限要求

- Root权限（用于配置系统服务）
- MySQL管理员权限
- Dovecot配置权限

### 备份建议

部署前请备份以下内容：
- MySQL数据库
- Dovecot配置文件
- 现有的后端代码

## 快速部署

### 1. 下载部署脚本

```bash
# 进入项目目录
cd /var/www/email-system/backend

# 确保部署脚本可执行
chmod +x deploy/*.sh
```

### 2. 执行完整部署

```bash
# 执行一键部署脚本
sudo ./deploy/deploy-token-system.sh
```

### 3. 验证部署

```bash
# 检查服务状态
mail-token-admin status

# 测试Token认证
mail-token-admin test
```

## 分步部署

如果需要分步部署或出现问题，可以单独执行各个步骤：

### 步骤1: 数据库迁移

```bash
# 执行数据库迁移
mysql -u root -p mailserver < deploy/01-database-migration.sql
```

### 步骤2: 后端服务部署

```bash
# 部署后端Token服务
sudo ./deploy/03-backend-token-deploy.sh
```

### 步骤3: Dovecot配置

```bash
# 配置Dovecot Token认证
sudo ./deploy/02-dovecot-token-setup.sh
```

## 配置说明

### 环境变量

在 `.env.production` 文件中添加以下配置：

```env
# 邮件Token认证配置
MAIL_TOKEN_ENABLED=true
MAIL_TOKEN_EXPIRY=86400
MAIL_TOKEN_CLEANUP_INTERVAL=3600
ENABLE_TOKEN_LOGGING=true
```

### Dovecot配置

主要配置文件：`/etc/dovecot/conf.d/10-auth-token.conf`

```conf
# Token认证配置
auth_mechanisms = plain login

passdb {
  driver = checkpassword
  args = /usr/local/bin/dovecot-token-auth.sh
}

userdb {
  driver = static
  args = uid=vmail gid=vmail home=/var/mail/vhosts/%d/%n
}
```

## 管理和维护

### 管理工具

部署完成后，可以使用 `mail-token-admin` 工具进行管理：

```bash
# 查看服务状态
mail-token-admin status

# 查看服务日志
mail-token-admin logs

# 重启所有服务
mail-token-admin restart

# 测试Token认证
mail-token-admin test

# 清理过期Token
mail-token-admin cleanup
```

### 日志文件

- **后端日志**: `/var/www/email-system/backend/logs/`
- **Dovecot Token日志**: `/var/log/dovecot-token-auth.log`
- **健康检查日志**: `/var/log/dovecot-token-health.log`

### 监控任务

系统自动配置了以下监控任务：

- **健康检查**: 每5分钟检查API可用性
- **Token清理**: 每小时清理过期Token
- **日志备份**: 每天备份Token使用日志

## 故障排除

### 常见问题

#### 1. Token验证失败

**症状**: 用户无法通过Token认证登录邮箱

**排查步骤**:
```bash
# 检查后端API状态
curl http://localhost:3000/api/health

# 查看Token认证日志
tail -f /var/log/dovecot-token-auth.log

# 测试Token生成和验证
mail-token-admin test
```

#### 2. Dovecot服务异常

**症状**: Dovecot服务无法启动或频繁重启

**排查步骤**:
```bash
# 检查Dovecot配置
doveconf -n

# 查看Dovecot日志
journalctl -u dovecot -f

# 恢复备份配置
cp /root/dovecot-backup-*/dovecot/dovecot.conf /etc/dovecot/
systemctl restart dovecot
```

#### 3. 数据库连接问题

**症状**: Token无法保存到数据库

**排查步骤**:
```bash
# 检查数据库连接
mysql -u root -p -e "SELECT 1"

# 验证表结构
mysql -u root -p mailserver -e "SHOW TABLES LIKE 'mail_%'"

# 检查权限
mysql -u root -p -e "SHOW GRANTS FOR 'mailuser'@'localhost'"
```

### 性能优化

#### 1. Token缓存

在高并发场景下，可以启用Token缓存：

```env
# 启用Redis缓存
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379
TOKEN_CACHE_TTL=300
```

#### 2. 数据库优化

```sql
-- 添加索引优化查询性能
CREATE INDEX idx_mail_tokens_active ON mail_tokens(user_id, is_revoked, expires_at);
CREATE INDEX idx_token_logs_recent ON mail_token_usage_logs(created_at DESC);
```

## 安全建议

### 1. Token安全

- 定期轮换JWT密钥
- 设置合适的Token过期时间
- 启用Token撤销机制

### 2. 网络安全

- 使用HTTPS/TLS加密通信
- 限制API访问来源
- 启用防火墙规则

### 3. 监控和审计

- 监控异常Token使用
- 记录所有认证事件
- 定期审查访问日志

## 升级和回滚

### 升级流程

1. 备份当前配置和数据
2. 停止相关服务
3. 更新代码和配置
4. 执行数据库迁移
5. 重启服务并验证

### 回滚流程

1. 停止新版本服务
2. 恢复备份的配置文件
3. 回滚数据库更改
4. 重启服务并验证

## 支持和联系

如果在部署过程中遇到问题，请：

1. 查看部署日志文件
2. 检查系统要求是否满足
3. 参考故障排除章节
4. 联系技术支持团队

---

**注意**: 本部署指南适用于生产环境，请在测试环境中充分验证后再部署到生产环境。
