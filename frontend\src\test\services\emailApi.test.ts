import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { getEmails, getEmailById, sendEmail, markAsRead, markAsUnread, deleteEmail, moveEmail } from '../../services/emailApi';
import type { EmailData } from '../../types';

// Mock the api module
vi.mock('../../config/api', () => ({
  default: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    patch: vi.fn(),
    delete: vi.fn(),
  },
}));

import api from '../../config/api';

describe('EmailApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getEmails', () => {
    it('should get emails list successfully', async () => {
      const mockParams = {
        page: 1,
        limit: 20,
        folderId: 1,
      };

      const mockResponse = {
        data: {
          data: {
            data: [
              {
                id: '1',
                subject: 'Test Email 1',
                senderEmail: '<EMAIL>',
                senderName: 'Sender 1',
                isRead: false,
                isStarred: false,
                receivedAt: '2024-01-01T00:00:00Z',
              },
              {
                id: '2',
                subject: 'Test Email 2',
                senderEmail: '<EMAIL>',
                senderName: 'Sender 2',
                isRead: true,
                isStarred: true,
                receivedAt: '2024-01-02T00:00:00Z',
              },
            ],
            pagination: {
              page: 1,
              limit: 20,
              total: 2,
              totalPages: 1,
              hasNext: false,
              hasPrev: false,
            },
          },
        },
      };

      vi.mocked(api.get).mockResolvedValue(mockResponse);

      const result = await getEmails(mockParams);

      expect(api.get).toHaveBeenCalledWith('/emails', { params: mockParams });
      expect(result).toEqual(mockResponse.data.data);
      expect(result.data).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
    });

    it('should get emails with default params', async () => {
      const mockResponse = {
        data: {
          data: {
            data: [],
            pagination: {
              page: 1,
              limit: 20,
              total: 0,
              totalPages: 0,
              hasNext: false,
              hasPrev: false,
            },
          },
        },
      };

      vi.mocked(api.get).mockResolvedValue(mockResponse);

      const result = await getEmails();

      expect(api.get).toHaveBeenCalledWith('/emails', { params: {} });
      expect(result).toEqual(mockResponse.data.data);
    });

    it('should handle error when getting emails', async () => {
      const mockError = new Error('Failed to fetch emails');
      vi.mocked(api.get).mockRejectedValue(mockError);

      await expect(getEmails()).rejects.toThrow('Failed to fetch emails');
      expect(api.get).toHaveBeenCalledWith('/emails', { params: {} });
    });
  });

  describe('getEmailById', () => {
    it('should get email by id successfully', async () => {
      const emailId = 'test-email-id';
      const mockResponse = {
        data: {
          data: {
            id: emailId,
            subject: 'Test Email',
            senderEmail: '<EMAIL>',
            senderName: 'Sender',
            recipients: [{ email: '<EMAIL>', name: 'Recipient' }],
            contentText: 'Test email content',
            contentHtml: '<p>Test email content</p>',
            isRead: false,
            isStarred: false,
            receivedAt: '2024-01-01T00:00:00Z',
            attachments: [],
          },
        },
      };

      vi.mocked(api.get).mockResolvedValue(mockResponse);

      const result = await getEmailById(emailId);

      expect(api.get).toHaveBeenCalledWith(`/emails/${emailId}`);
      expect(result).toEqual(mockResponse.data.data);
      expect(result.id).toBe(emailId);
      expect(result.subject).toBe('Test Email');
    });

    it('should handle error when email not found', async () => {
      const emailId = 'non-existent-id';
      const mockError = new Error('Email not found');
      vi.mocked(api.get).mockRejectedValue(mockError);

      await expect(getEmailById(emailId)).rejects.toThrow('Email not found');
      expect(api.get).toHaveBeenCalledWith(`/emails/${emailId}`);
    });
  });

  describe('sendEmail', () => {
    it('should send email successfully', async () => {
      const emailData: EmailData = {
        to: [{ email: '<EMAIL>', name: 'Recipient' }],
        cc: [{ email: '<EMAIL>', name: 'CC User' }],
        subject: 'Test Email',
        content: 'This is a test email',
        priority: 'normal',
      };

      const mockResponse = {
        data: {
          data: {
            id: 'sent-email-id',
            messageId: 'message-id-123',
            subject: emailData.subject,
            senderEmail: '<EMAIL>',
            recipients: emailData.to,
            ccRecipients: emailData.cc,
            contentText: emailData.content,
            sentAt: '2024-01-01T00:00:00Z',
          },
        },
      };

      vi.mocked(api.post).mockResolvedValue(mockResponse);

      const result = await sendEmail(emailData);

      expect(api.post).toHaveBeenCalledWith('/emails', emailData);
      expect(result).toEqual(mockResponse.data.data);
      expect(result.subject).toBe(emailData.subject);
    });

    it('should handle error when sending email fails', async () => {
      const emailData: EmailData = {
        to: [{ email: 'invalid-email' }],
        subject: 'Test Email',
        content: 'Test content',
      };

      const mockError = new Error('Invalid email address');
      vi.mocked(api.post).mockRejectedValue(mockError);

      await expect(sendEmail(emailData)).rejects.toThrow('Invalid email address');
      expect(api.post).toHaveBeenCalledWith('/emails', emailData);
    });
  });

  describe('markAsRead', () => {
    it('should mark email as read successfully', async () => {
      const emailId = 'test-email-id';
      const mockResponse = {
        data: {
          data: {
            id: emailId,
            isRead: true,
          },
        },
      };

      vi.mocked(api.patch).mockResolvedValue(mockResponse);

      const result = await markAsRead(emailId);

      expect(api.patch).toHaveBeenCalledWith(`/emails/${emailId}/read`);
      expect(result).toEqual(mockResponse.data.data);
      expect(result.isRead).toBe(true);
    });

    it('should handle error when marking as read fails', async () => {
      const emailId = 'non-existent-id';
      const mockError = new Error('Email not found');
      vi.mocked(api.patch).mockRejectedValue(mockError);

      await expect(markAsRead(emailId)).rejects.toThrow('Email not found');
      expect(api.patch).toHaveBeenCalledWith(`/emails/${emailId}/read`);
    });
  });

  describe('markAsUnread', () => {
    it('should mark email as unread successfully', async () => {
      const emailId = 'test-email-id';
      const mockResponse = {
        data: {
          data: {
            id: emailId,
            isRead: false,
          },
        },
      };

      vi.mocked(api.patch).mockResolvedValue(mockResponse);

      const result = await markAsUnread(emailId);

      expect(api.patch).toHaveBeenCalledWith(`/emails/${emailId}/unread`);
      expect(result).toEqual(mockResponse.data.data);
      expect(result.isRead).toBe(false);
    });
  });

  describe('deleteEmail', () => {
    it('should delete email successfully', async () => {
      const emailId = 'test-email-id';
      vi.mocked(api.delete).mockResolvedValue({});

      await deleteEmail(emailId);

      expect(api.delete).toHaveBeenCalledWith(`/emails/${emailId}`);
    });

    it('should handle error when deleting email fails', async () => {
      const emailId = 'non-existent-id';
      const mockError = new Error('Email not found');
      vi.mocked(api.delete).mockRejectedValue(mockError);

      await expect(deleteEmail(emailId)).rejects.toThrow('Email not found');
      expect(api.delete).toHaveBeenCalledWith(`/emails/${emailId}`);
    });
  });

  describe('moveEmail', () => {
    it('should move email to folder successfully', async () => {
      const emailId = 'test-email-id';
      const folderId = 2;
      const mockResponse = {
        data: {
          data: {
            id: emailId,
            folderId: folderId,
          },
        },
      };

      vi.mocked(api.patch).mockResolvedValue(mockResponse);

      const result = await moveEmail(emailId, folderId);

      expect(api.patch).toHaveBeenCalledWith(`/emails/${emailId}/move`, { folderId });
      expect(result).toEqual(mockResponse.data.data);
      expect(result.folderId).toBe(folderId);
    });

    it('should handle error when moving email fails', async () => {
      const emailId = 'test-email-id';
      const folderId = 999; // non-existent folder
      const mockError = new Error('Folder not found');
      vi.mocked(api.patch).mockRejectedValue(mockError);

      await expect(moveEmail(emailId, folderId)).rejects.toThrow('Folder not found');
      expect(api.patch).toHaveBeenCalledWith(`/emails/${emailId}/move`, { folderId });
    });
  });
});
