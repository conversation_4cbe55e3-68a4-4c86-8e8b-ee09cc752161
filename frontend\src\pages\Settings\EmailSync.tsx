import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Button,
  Space,
  Tabs,
  Form,
  Switch,
  InputNumber,
  Select,
  Alert,
  Divider,
  Statistic,
  Progress,
  message,
} from 'antd';
import {
  ArrowLeftOutlined,
  SwapOutlined as SyncOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import EmailSyncStatus from '../../components/EmailSyncStatus';
import api from '../../config/api';
import type { ApiResponse } from '../../types';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface SyncSettings {
  autoSync: boolean;
  syncInterval: number;
  syncOnStartup: boolean;
  syncFolders: string[];
  maxEmailsPerSync: number;
  retryAttempts: number;
  enableRealTimeSync: boolean;
}

interface SyncStats {
  totalSynced: number;
  lastSyncTime: string;
  syncErrors: number;
  avgSyncTime: number;
  successRate: number;
}

const EmailSyncPage: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [syncSettings, setSyncSettings] = useState<SyncSettings>({
    autoSync: true,
    syncInterval: 5,
    syncOnStartup: true,
    syncFolders: ['INBOX', 'SENT', 'DRAFT'],
    maxEmailsPerSync: 100,
    retryAttempts: 3,
    enableRealTimeSync: true,
  });
  const [syncStats, setSyncStats] = useState<SyncStats>({
    totalSynced: 0,
    lastSyncTime: '',
    syncErrors: 0,
    avgSyncTime: 0,
    successRate: 100,
  });

  // 获取同步设置
  const fetchSyncSettings = async () => {
    try {
      setLoading(true);
      const response = await api.get<ApiResponse<SyncSettings>>('/emails/sync/settings');
      if (response.data.success && response.data.data) {
        setSyncSettings(response.data.data);
        form.setFieldsValue(response.data.data);
      }
    } catch (error) {
      console.error('获取同步设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取同步统计
  const fetchSyncStats = async () => {
    try {
      const response = await api.get<ApiResponse<SyncStats>>('/emails/sync/stats');
      if (response.data.success && response.data.data) {
        setSyncStats(response.data.data);
      }
    } catch (error) {
      console.error('获取同步统计失败:', error);
    }
  };

  // 保存同步设置
  const handleSaveSettings = async (values: SyncSettings) => {
    try {
      setLoading(true);
      await api.put('/emails/sync/settings', values);
      setSyncSettings(values);
      message.success('同步设置保存成功');
    } catch (error) {
      message.error('保存同步设置失败');
      console.error('保存同步设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 重置同步设置
  const handleResetSettings = () => {
    const defaultSettings: SyncSettings = {
      autoSync: true,
      syncInterval: 5,
      syncOnStartup: true,
      syncFolders: ['INBOX', 'SENT', 'DRAFT'],
      maxEmailsPerSync: 100,
      retryAttempts: 3,
      enableRealTimeSync: true,
    };
    setSyncSettings(defaultSettings);
    form.setFieldsValue(defaultSettings);
  };

  useEffect(() => {
    fetchSyncSettings();
    fetchSyncStats();
  }, []);

  return (
    <Card>
            <div style={{ marginBottom: '24px' }}>
              <Space>
                <Button
                  type="text"
                  icon={<ArrowLeftOutlined />}
                  onClick={() => navigate('/settings')}
                >
                  返回设置
                </Button>
              </Space>

              <div style={{ marginTop: '16px' }}>
                <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <SyncOutlined />
                  邮件同步设置
                </Title>
                <Text type="secondary">
                  管理邮件自动同步功能，控制同步间隔和状态
                </Text>
              </div>
            </div>

            <Tabs defaultActiveKey="status" size="large">
              <TabPane
                tab={
                  <span>
                    <SyncOutlined />
                    同步状态
                  </span>
                }
                key="status"
              >
                <EmailSyncStatus />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <SettingOutlined />
                    同步设置
                  </span>
                }
                key="settings"
              >
                <Card>
                  <Form
                    form={form}
                    layout="vertical"
                    initialValues={syncSettings}
                    onFinish={handleSaveSettings}
                  >
                    <Row gutter={[24, 24]}>
                      <Col span={12}>
                        <Form.Item
                          name="autoSync"
                          label="自动同步"
                          valuePropName="checked"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="syncOnStartup"
                          label="启动时同步"
                          valuePropName="checked"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[24, 24]}>
                      <Col span={12}>
                        <Form.Item
                          name="syncInterval"
                          label="同步间隔（分钟）"
                          rules={[{ required: true, message: '请输入同步间隔' }]}
                        >
                          <InputNumber
                            min={1}
                            max={60}
                            style={{ width: '100%' }}
                            addonAfter="分钟"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="maxEmailsPerSync"
                          label="每次同步最大邮件数"
                          rules={[{ required: true, message: '请输入最大邮件数' }]}
                        >
                          <InputNumber
                            min={10}
                            max={1000}
                            style={{ width: '100%' }}
                            addonAfter="封"
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Row gutter={[24, 24]}>
                      <Col span={12}>
                        <Form.Item
                          name="retryAttempts"
                          label="重试次数"
                          rules={[{ required: true, message: '请输入重试次数' }]}
                        >
                          <InputNumber
                            min={1}
                            max={10}
                            style={{ width: '100%' }}
                            addonAfter="次"
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="enableRealTimeSync"
                          label="实时同步"
                          valuePropName="checked"
                        >
                          <Switch
                            checkedChildren="开启"
                            unCheckedChildren="关闭"
                          />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="syncFolders"
                      label="同步文件夹"
                      rules={[{ required: true, message: '请选择同步文件夹' }]}
                    >
                      <Select
                        mode="multiple"
                        placeholder="选择要同步的文件夹"
                        style={{ width: '100%' }}
                      >
                        <Option value="INBOX">收件箱</Option>
                        <Option value="SENT">发件箱</Option>
                        <Option value="DRAFT">草稿箱</Option>
                        <Option value="TRASH">垃圾箱</Option>
                        <Option value="SPAM">垃圾邮件</Option>
                        <Option value="ARCHIVE">归档</Option>
                      </Select>
                    </Form.Item>

                    <Divider />

                    <Space>
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        icon={<CheckCircleOutlined />}
                      >
                        保存设置
                      </Button>
                      <Button
                        onClick={handleResetSettings}
                        icon={<ExclamationCircleOutlined />}
                      >
                        重置为默认
                      </Button>
                    </Space>
                  </Form>
                </Card>
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <BarChartOutlined />
                    同步统计
                  </span>
                }
                key="stats"
              >
                <Row gutter={[24, 24]}>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="总同步邮件数"
                        value={syncStats.totalSynced}
                        prefix={<SyncOutlined />}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="同步错误数"
                        value={syncStats.syncErrors}
                        prefix={<ExclamationCircleOutlined />}
                        valueStyle={{ color: syncStats.syncErrors > 0 ? '#cf1322' : '#3f8600' }}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="平均同步时间"
                        value={syncStats.avgSyncTime}
                        suffix="秒"
                        prefix={<ClockCircleOutlined />}
                        precision={1}
                      />
                    </Card>
                  </Col>
                  <Col span={6}>
                    <Card>
                      <Statistic
                        title="成功率"
                        value={syncStats.successRate}
                        suffix="%"
                        prefix={<CheckCircleOutlined />}
                        precision={1}
                        valueStyle={{ color: syncStats.successRate >= 95 ? '#3f8600' : '#cf1322' }}
                      />
                    </Card>
                  </Col>
                </Row>

                <Row gutter={[24, 24]} style={{ marginTop: '24px' }}>
                  <Col span={24}>
                    <Card title="同步性能">
                      <div style={{ marginBottom: '16px' }}>
                        <Text strong>同步成功率</Text>
                        <Progress
                          percent={syncStats.successRate}
                          status={syncStats.successRate >= 95 ? 'success' : 'exception'}
                          strokeColor={syncStats.successRate >= 95 ? '#52c41a' : '#ff4d4f'}
                        />
                      </div>

                      {syncStats.lastSyncTime && (
                        <Alert
                          message={`最后同步时间: ${new Date(syncStats.lastSyncTime).toLocaleString('zh-CN')}`}
                          type="info"
                          showIcon
                        />
                      )}
                    </Card>
                  </Col>
                </Row>
              </TabPane>
            </Tabs>
    </Card>
  );
};

export default EmailSyncPage;
