import { Response } from 'express';
import { AuthenticatedRequest, AppError, ApiResponse, CreateAppPasswordRequest } from '../types';
import logger from '../utils/logger';
import {
  createAppPassword,
  getUserAppPasswords,
  deleteAppPassword,
  regenerateAppPassword,
  toggleAppPasswordStatus
} from '../services/appPasswordService';

/**
 * 应用专用密码控制器
 * 处理应用专用密码相关的API请求
 */

/**
 * 获取用户的所有应用专用密码
 * GET /api/app-passwords
 */
export const getAppPasswords = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    
    const appPasswords = await getUserAppPasswords(userId);
    
    const response: ApiResponse = {
      success: true,
      message: '获取应用专用密码列表成功',
      data: { appPasswords }
    };
    
    res.json(response);
  } catch (error) {
    logger.error('获取应用专用密码列表失败:', error);
    throw new AppError('获取应用专用密码列表失败', 500);
  }
};

/**
 * 创建新的应用专用密码
 * POST /api/app-passwords
 */
export const createNewAppPassword = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { name, purpose, expiresAt }: CreateAppPasswordRequest = req.body;
    
    if (!name || name.trim().length === 0) {
      throw new AppError('应用专用密码名称不能为空', 400);
    }
    
    if (name.length > 50) {
      throw new AppError('应用专用密码名称不能超过50个字符', 400);
    }
    
    // 验证purpose
    const validPurposes = ['imap', 'smtp', 'api'];
    if (purpose && !validPurposes.includes(purpose)) {
      throw new AppError('无效的应用专用密码用途', 400);
    }
    
    // 验证过期时间
    if (expiresAt && new Date(expiresAt) <= new Date()) {
      throw new AppError('过期时间必须在未来', 400);
    }
    
    const result = await createAppPassword(userId, {
      name: name.trim(),
      purpose: purpose || 'imap',
      expiresAt: expiresAt ? new Date(expiresAt) : undefined
    });
    
    const response: ApiResponse = {
      success: true,
      message: '应用专用密码创建成功',
      data: {
        appPassword: result.appPassword,
        plainPassword: result.plainPassword
      }
    };
    
    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('创建应用专用密码失败:', error);
    throw new AppError(error.message || '创建应用专用密码失败', 500);
  }
};

/**
 * 删除应用专用密码
 * DELETE /api/app-passwords/:id
 */
export const deleteAppPasswordById = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const appPasswordId = parseInt(req.params.id);
    
    if (isNaN(appPasswordId)) {
      throw new AppError('无效的应用专用密码ID', 400);
    }
    
    await deleteAppPassword(userId, appPasswordId);
    
    const response: ApiResponse = {
      success: true,
      message: '应用专用密码删除成功'
    };
    
    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('删除应用专用密码失败:', error);
    throw new AppError(error.message || '删除应用专用密码失败', 500);
  }
};

/**
 * 重新生成应用专用密码
 * POST /api/app-passwords/:id/regenerate
 */
export const regenerateAppPasswordById = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const appPasswordId = parseInt(req.params.id);
    
    if (isNaN(appPasswordId)) {
      throw new AppError('无效的应用专用密码ID', 400);
    }
    
    const result = await regenerateAppPassword(userId, appPasswordId);
    
    const response: ApiResponse = {
      success: true,
      message: '应用专用密码重新生成成功',
      data: {
        plainPassword: result.plainPassword
      }
    };
    
    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('重新生成应用专用密码失败:', error);
    throw new AppError(error.message || '重新生成应用专用密码失败', 500);
  }
};

/**
 * 启用/停用应用专用密码
 * PATCH /api/app-passwords/:id/toggle
 */
export const toggleAppPasswordById = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const appPasswordId = parseInt(req.params.id);
    const { isActive } = req.body;
    
    if (isNaN(appPasswordId)) {
      throw new AppError('无效的应用专用密码ID', 400);
    }
    
    if (typeof isActive !== 'boolean') {
      throw new AppError('isActive必须是布尔值', 400);
    }
    
    await toggleAppPasswordStatus(userId, appPasswordId, isActive);
    
    const response: ApiResponse = {
      success: true,
      message: `应用专用密码${isActive ? '启用' : '停用'}成功`
    };
    
    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('切换应用专用密码状态失败:', error);
    throw new AppError(error.message || '切换应用专用密码状态失败', 500);
  }
};

/**
 * 获取单个应用专用密码详情
 * GET /api/app-passwords/:id
 */
export const getAppPasswordById = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const appPasswordId = parseInt(req.params.id);
    
    if (isNaN(appPasswordId)) {
      throw new AppError('无效的应用专用密码ID', 400);
    }
    
    const appPasswords = await getUserAppPasswords(userId);
    const appPassword = appPasswords.find(ap => ap.id === appPasswordId);
    
    if (!appPassword) {
      throw new AppError('应用专用密码不存在', 404);
    }
    
    const response: ApiResponse = {
      success: true,
      message: '获取应用专用密码详情成功',
      data: { appPassword }
    };
    
    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取应用专用密码详情失败:', error);
    throw new AppError('获取应用专用密码详情失败', 500);
  }
};
