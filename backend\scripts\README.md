# 管理脚本使用说明

本目录包含了各种管理和维护脚本，用于系统管理、用户管理、数据库维护等操作。

## 用户管理脚本

### 修改用户密码 - changePassword.js

用于修改用户的登录密码和邮件密码。

**使用方法：**

```bash
# 通过邮箱修改密码
node scripts/changePassword.js --email <EMAIL> --password newpassword123

# 通过用户ID修改密码
node scripts/changePassword.js --id 123 --password newpassword123

# 通过用户名修改密码
node scripts/changePassword.js --username johndoe --password newpassword123

# 查看帮助信息
node scripts/changePassword.js --help
```

**参数说明：**
- `--email`: 用户邮箱地址
- `--id`: 用户ID
- `--username`: 用户名
- `--password`: 新密码（必需，至少6位）
- `--help`: 显示帮助信息

**示例：**
```bash
# 修改管理员密码
node scripts/changePassword.js --email <EMAIL> --password admin123456

# 修改特定用户密码
node scripts/changePassword.js --id 1 --password newpassword123
```

**注意事项：**
- 脚本会同时更新Web登录密码和邮件密码
- 操作前会显示用户信息供确认
- 需要按Enter键确认操作
- 可以按Ctrl+C取消操作

### 初始化管理员 - init-admin.js

用于创建系统管理员账户。

```bash
node scripts/init-admin.js
```

### 修复管理员角色 - fix-admin-role.js

用于修复管理员角色权限。

```bash
node scripts/fix-admin-role.js
```

## 系统管理脚本

### 数据库结构检查 - check-database-structure.js

检查数据库结构完整性。

```bash
node scripts/check-database-structure.js
```

### 系统验证 - verify-system.js

验证系统各组件状态。

```bash
node scripts/verify-system.js
```

### 用户认证测试 - verify-auth-flow.js

测试用户认证流程。

```bash
node scripts/verify-auth-flow.js
```

## 邮件相关脚本

### 发送测试邮件 - send-test-email.js

发送测试邮件验证邮件功能。

```bash
node scripts/send-test-email.js
```

### 手动同步邮件 - manual-start-sync.js

手动启动邮件同步。

```bash
node scripts/manual-start-sync.js
```

### 检查最近邮件 - check-recent-emails.js

检查最近的邮件记录。

```bash
node scripts/check-recent-emails.js
```

## 应用密码管理

### 重新生成应用密码 - regenerate-app-passwords.js

重新生成用户的应用密码。

```bash
node scripts/regenerate-app-passwords.js
```

### 测试应用密码 - test-app-password.js

测试应用密码功能。

```bash
node scripts/test-app-password.js
```

## 数据迁移脚本

### 多账户迁移 - migrate-to-multi-account.js

迁移到多账户系统。

```bash
node scripts/migrate-to-multi-account.js
```

### 应用密码迁移 - apply-app-passwords-migration.js

应用密码系统迁移。

```bash
node scripts/apply-app-passwords-migration.js
```

## 监控和调试脚本

### 账户状态检查 - check-account-status.js

检查用户账户状态。

```bash
node scripts/check-account-status.js
```

### 用户认证调试 - debug-user-auth.js

调试用户认证问题。

```bash
node scripts/debug-user-auth.js
```

### 系统监控测试 - test-system-monitoring.js

测试系统监控功能。

```bash
node scripts/test-system-monitoring.js
```

## 使用注意事项

1. **权限要求**：大部分脚本需要数据库访问权限
2. **环境变量**：确保正确配置了数据库连接等环境变量
3. **备份**：在执行修改操作前建议备份数据库
4. **日志**：脚本执行过程会输出详细日志信息
5. **确认操作**：涉及数据修改的脚本通常需要用户确认

## 常见问题

### 数据库连接失败
```bash
# 检查数据库连接配置
cat .env | grep DATABASE

# 测试数据库连接
node scripts/check-database-structure.js
```

### 权限不足
```bash
# 确保以正确的用户身份运行脚本
# 检查文件权限
ls -la scripts/
```

### 脚本执行失败
```bash
# 查看详细错误信息
node scripts/script-name.js 2>&1 | tee script.log

# 检查Node.js版本
node --version
```

## 开发和维护

如需添加新的管理脚本：

1. 在 `scripts/` 目录下创建新脚本
2. 添加适当的错误处理和日志记录
3. 更新此README文档
4. 添加必要的测试

## 安全提醒

- 这些脚本具有系统管理权限，请谨慎使用
- 不要在生产环境中随意执行未测试的脚本
- 定期审查脚本权限和访问控制
- 保护好包含敏感信息的脚本和日志文件
