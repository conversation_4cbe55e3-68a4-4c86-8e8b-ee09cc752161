import api from '../config/api';
import type { ApiResponse } from '../types';

// 邮箱文件夹状态接口
export interface MailboxFolderStatus {
  email: string;
  exists: boolean;
  folders: string[];
  missing: string[];
}

// 邮箱统计信息接口
export interface MailboxStats {
  email: string;
  totalSize: number;
  messageCount: number;
  folders: Array<{
    name: string;
    messageCount: number;
    size: number;
  }>;
}

/**
 * 检查邮箱文件夹状态
 */
export const checkMailboxFolders = async (): Promise<MailboxFolderStatus> => {
  const response = await api.get<ApiResponse<MailboxFolderStatus>>('/mailbox/folders/check');
  return response.data.data!;
};

/**
 * 创建邮箱文件夹
 */
export const createMailboxFolders = async (): Promise<{ email: string }> => {
  const response = await api.post<ApiResponse<{ email: string }>>('/mailbox/folders/create');
  return response.data.data!;
};

/**
 * 修复邮箱文件夹
 */
export const repairMailboxFolders = async (): Promise<{ email: string }> => {
  const response = await api.post<ApiResponse<{ email: string }>>('/mailbox/folders/repair');
  return response.data.data!;
};

/**
 * 获取邮箱统计信息
 */
export const getMailboxStats = async (): Promise<MailboxStats> => {
  const response = await api.get<ApiResponse<MailboxStats>>('/mailbox/stats');
  return response.data.data!;
};
