import { useEffect, useState, useRef } from "react";
import {
  Empty,
  message,
  Avatar,
} from "antd";
import { UserOutlined } from "@ant-design/icons";
import { useEmailStore } from "../store/emailStore";
import { useViewSettingsStore, calculateCompactMode } from "../store/viewSettingsStore";
import type { Email } from "../types/email";
import EmailStarredLogic from "../components/EmailStarredLogic";
import EmailPageHeader from "../components/EmailPageHeader";
import "../styles/email-layout.css";

const EmailStarred: React.FC = () => {
  const {
    selectedEmail,
    loading,
    fetchEmails,
    setSelectedEmail,
    updateEmail,
    syncEmails,
  } = useEmailStore();

  // 状态管理
  const [searchText, setSearchText] = useState('');
  const [searchType, setSearchType] = useState('all');
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);

  // refs
  const containerRef = useRef<HTMLDivElement>(null);

  // 使用全局视图设置
  const {
    viewMode,
    setViewMode,
    listWidth,
    setListWidth,
    isCompactMode,
    setIsCompactMode,
  } = useViewSettingsStore();

  useEffect(() => {
    // 获取星标邮件
    fetchEmails({ isStarred: 'true' });
  }, [fetchEmails]);

  // 处理邮件点击
  const handleEmailClick = async (email: Email) => {
    setSelectedEmail(email);

    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      await updateEmail(email.id, { isRead: true });
    }
  };

  // 处理星标切换
  const handleStarToggle = async (emailId: string, starred: boolean) => {
    try {
      await updateEmail(emailId, { isStarred: starred });
      message.success(starred ? '已添加星标' : '已移除星标');
    } catch (error) {
      message.error('星标操作失败');
    }
  };

  // 拖拽调整宽度
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    const startX = e.clientX;
    const startWidth = listWidth;

    const handleMouseMove = (e: MouseEvent) => {
      const newWidth = startWidth + (e.clientX - startX);
      const minWidth = 300;
      const maxWidth = window.innerWidth * 0.7;
      setListWidth(Math.max(minWidth, Math.min(maxWidth, newWidth)));
    };

    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  // 检测是否为紧凑模式
  useEffect(() => {
    const checkCompactMode = () => {
      const newCompactMode = calculateCompactMode(viewMode, listWidth);
      setIsCompactMode(newCompactMode);
    };

    checkCompactMode();
    window.addEventListener('resize', checkCompactMode);
    return () => window.removeEventListener('resize', checkCompactMode);
  }, [viewMode, listWidth, setIsCompactMode]);

  return (
    <div
      className={`email-container ${viewMode}`}
      ref={containerRef}
      data-view-mode={viewMode}
    >
      <EmailPageHeader
        title="星标邮件"
        searchText={searchText}
        searchType={searchType}
        onSearchTextChange={setSearchText}
        onSearchTypeChange={setSearchType}
        onClearSearch={() => setSearchText('')}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        loading={loading}
        onRefresh={syncEmails}
        showAdvancedSearch={showAdvancedSearch}
        onToggleAdvancedSearch={() => setShowAdvancedSearch(!showAdvancedSearch)}
        emails={[]}
        selectedEmailIds={[]}
        currentPage={1}
        pageSize={20}
        onSelectAll={() => {}}
        onClearSelection={() => {}}
        searchTypeOptions={[
          { value: 'all', label: '全部' },
          { value: 'sender', label: '发件人' },
          { value: 'recipient', label: '收件人' },
          { value: 'subject', label: '主题' },
          { value: 'content', label: '内容' }
        ]}
      />

      {/* 内容区域 */}
      <div className={`email-content flex ${viewMode === "list" ? "flex-col" : "flex-row"}`}>
        {/* 邮件主体容器 - 包含列表、拖拽条、详情面板 */}
        <div className="email-main-container">
          {/* 邮件列表面板 */}
          <div
            className="email-list-panel"
            style={{
              width: viewMode === "list" ? "100%" : `${listWidth}px`,
              minWidth: viewMode === "list" ? "100%" : `${listWidth}px`,
              maxWidth: viewMode === "list" ? "100%" : `${listWidth}px`
            }}
          >
            <EmailStarredLogic
              isCompactMode={isCompactMode}
              onEmailClick={handleEmailClick}
              selectedEmail={selectedEmail}
              searchText={searchText}
              selectedEmailIds={[]}
              onSelectEmail={() => {}} // 星标页面不需要复选框功能
            />
          </div>

          {/* 拖拽条 */}
          {viewMode === "split" && (
            <div
              className="resize-handle"
              onMouseDown={handleMouseDown}
              style={{ cursor: "col-resize" }}
            />
          )}

          {/* 邮件详情面板 */}
          {viewMode === "split" && (
            <div className="email-detail-panel" style={{ flex: 1 }}>
              {selectedEmail ? (
                <div className="email-detail">
                  <div className="detail-header">
                    <h2>{selectedEmail.subject || "(无主题)"}</h2>
                    <div className="email-meta">
                      <Avatar icon={<UserOutlined />} size={32} />
                      <div className="sender-info">
                        <div className="sender-name">
                          {selectedEmail.folderName === 'sent' ? (
                            `收件人: ${(() => {
                              try {
                                const recipients = typeof selectedEmail.recipients === 'string'
                                  ? JSON.parse(selectedEmail.recipients)
                                  : selectedEmail.recipients || [];
                                return recipients.map((r: any) => r.name || r.email).join(', ');
                              } catch {
                                return selectedEmail.recipients || '';
                              }
                            })()}`
                          ) : (
                            selectedEmail.senderName || selectedEmail.senderEmail
                          )}
                        </div>
                        <div className="email-date">
                          {(selectedEmail.receivedAt || selectedEmail.sentAt)
                            ? new Date(selectedEmail.receivedAt || selectedEmail.sentAt).toLocaleString()
                            : ""}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="detail-content">
                    <div
                      dangerouslySetInnerHTML={{
                        __html:
                          selectedEmail.contentHtml ||
                          selectedEmail.contentText ||
                          "",
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="no-email-selected">
                  <Empty description="请选择一封邮件查看详情" />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailStarred;
