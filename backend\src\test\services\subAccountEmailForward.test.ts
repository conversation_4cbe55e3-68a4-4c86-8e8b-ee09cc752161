import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { subAccountEmailForwardService } from '../../services/subAccountEmailForwardService';
import prisma from '../../config/database';

describe('SubAccountEmailForwardService', () => {
  let testParentUserId: number;
  let testSubUserId: number;
  let testDomainId: number;
  let testInboxFolderId: number;
  let testParentInboxFolderId: number;

  beforeEach(async () => {
    // 创建测试域名
    const domain = await prisma.virtualDomain.create({
      data: {
        name: 'test.com',
        active: 1
      }
    });
    testDomainId = domain.id;

    // 创建主账户
    const parentUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'parentuser',
        password: 'hashedpassword',
        mailPassword: 'mailpassword',
        domainId: testDomainId,
        isSubAccountEnabled: true,
        maxSubAccounts: 5,
        accountType: 'main'
      }
    });
    testParentUserId = parentUser.id;

    // 创建子账户
    const subUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'subuser',
        password: 'hashedpassword',
        mailPassword: 'mailpassword',
        domainId: testDomainId,
        parentUserId: testParentUserId,
        accountType: 'sub'
      }
    });
    testSubUserId = subUser.id;

    // 创建收件箱文件夹
    const subInboxFolder = await prisma.folder.create({
      data: {
        name: '收件箱',
        type: 'inbox',
        userId: testSubUserId
      }
    });
    testInboxFolderId = subInboxFolder.id;

    const parentInboxFolder = await prisma.folder.create({
      data: {
        name: '收件箱',
        type: 'inbox',
        userId: testParentUserId
      }
    });
    testParentInboxFolderId = parentInboxFolder.id;

    // 设置子账户邮件转发权限
    await prisma.subAccountPermission.create({
      data: {
        subUserId: testSubUserId,
        permissionType: 'email_forward',
        isAllowed: true
      }
    });
  });

  afterEach(async () => {
    // 清理测试数据
    await prisma.email.deleteMany({
      where: {
        OR: [
          { userId: testParentUserId },
          { userId: testSubUserId }
        ]
      }
    });
    await prisma.subAccountPermission.deleteMany({
      where: { subUserId: testSubUserId }
    });
    await prisma.folder.deleteMany({
      where: {
        OR: [
          { userId: testParentUserId },
          { userId: testSubUserId }
        ]
      }
    });
    await prisma.user.deleteMany({
      where: {
        OR: [
          { id: testParentUserId },
          { id: testSubUserId }
        ]
      }
    });
    await prisma.virtualDomain.delete({
      where: { id: testDomainId }
    });
  });

  describe('handleSubAccountEmailForward', () => {
    it('should forward email from sub account to parent account', async () => {
      // 创建子账户收到的邮件
      const originalEmail = await prisma.email.create({
        data: {
          messageId: 'test-message-123',
          subject: '测试邮件',
          senderEmail: '<EMAIL>',
          senderName: '外部发件人',
          recipientEmail: '<EMAIL>',
          textContent: '这是一封测试邮件',
          htmlContent: '<p>这是一封测试邮件</p>',
          receivedAt: new Date(),
          isRead: false,
          userId: testSubUserId,
          folderId: testInboxFolderId
        }
      });

      // 执行转发
      await subAccountEmailForwardService.handleSubAccountEmailForward(originalEmail);

      // 检查主账户是否收到转发邮件
      const forwardedEmails = await prisma.email.findMany({
        where: {
          userId: testParentUserId,
          subject: { contains: '[转发自 <EMAIL>]' }
        }
      });

      expect(forwardedEmails).toHaveLength(1);
      expect(forwardedEmails[0].subject).toBe('[转发自 <EMAIL>] 测试邮件');
      expect(forwardedEmails[0].senderEmail).toBe('<EMAIL>');
      expect(forwardedEmails[0].textContent).toContain('原收件人: <EMAIL>');
      expect(forwardedEmails[0].textContent).toContain('这是一封测试邮件');
    });

    it('should not forward email if user is not a sub account', async () => {
      // 创建主账户收到的邮件
      const originalEmail = await prisma.email.create({
        data: {
          messageId: 'test-message-456',
          subject: '主账户邮件',
          senderEmail: '<EMAIL>',
          senderName: '外部发件人',
          recipientEmail: '<EMAIL>',
          textContent: '这是主账户的邮件',
          receivedAt: new Date(),
          isRead: false,
          userId: testParentUserId,
          folderId: testParentInboxFolderId
        }
      });

      // 执行转发（应该不会转发）
      await subAccountEmailForwardService.handleSubAccountEmailForward(originalEmail);

      // 检查是否没有转发邮件
      const forwardedEmails = await prisma.email.findMany({
        where: {
          userId: testParentUserId,
          subject: { contains: '[转发自' }
        }
      });

      expect(forwardedEmails).toHaveLength(0);
    });
  });

  describe('isEmailForwardEnabled', () => {
    it('should return true when email forward is enabled', async () => {
      const isEnabled = await subAccountEmailForwardService.isEmailForwardEnabled(testSubUserId);
      expect(isEnabled).toBe(true);
    });

    it('should return false when email forward is disabled', async () => {
      // 禁用邮件转发
      await prisma.subAccountPermission.update({
        where: {
          subUserId_permissionType: {
            subUserId: testSubUserId,
            permissionType: 'email_forward'
          }
        },
        data: { isAllowed: false }
      });

      const isEnabled = await subAccountEmailForwardService.isEmailForwardEnabled(testSubUserId);
      expect(isEnabled).toBe(false);
    });

    it('should return false for main account', async () => {
      const isEnabled = await subAccountEmailForwardService.isEmailForwardEnabled(testParentUserId);
      expect(isEnabled).toBe(false);
    });
  });

  describe('setEmailForwardPermission', () => {
    it('should enable email forward permission', async () => {
      await subAccountEmailForwardService.setEmailForwardPermission(testSubUserId, true);
      
      const permission = await prisma.subAccountPermission.findUnique({
        where: {
          subUserId_permissionType: {
            subUserId: testSubUserId,
            permissionType: 'email_forward'
          }
        }
      });

      expect(permission?.isAllowed).toBe(true);
    });

    it('should disable email forward permission', async () => {
      await subAccountEmailForwardService.setEmailForwardPermission(testSubUserId, false);
      
      const permission = await prisma.subAccountPermission.findUnique({
        where: {
          subUserId_permissionType: {
            subUserId: testSubUserId,
            permissionType: 'email_forward'
          }
        }
      });

      expect(permission?.isAllowed).toBe(false);
    });
  });
});
