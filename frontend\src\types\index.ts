// 重新导出所有类型
export * from './api';
export * from './email';
export * from './user';

// 保持向后兼容的类型别名
export type { User, LoginData, RegisterData, AuthResponse } from './user';
export type { Email, EmailData, EmailRecipient, EmailAttachment, Folder, Label } from './email';
export type { ApiResponse, PaginatedResponse, PaginationParams } from './api';

// 这些类型已经在单独的文件中定义，通过上面的 export * 重新导出
// 保留一些特殊的应用状态类型

// 应用状态类型
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
}

// 表单相关类型
export interface FormState<T = Record<string, unknown>> {
  values: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isSubmitting: boolean;
  isValid: boolean;
}
