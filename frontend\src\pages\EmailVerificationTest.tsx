import React, { useState } from 'react';
import { Card, Form, Input, Button, Alert, Space, Typography, Divider } from 'antd';
import { MailOutlined, SafetyOutlined } from '@ant-design/icons';
import * as emailVerificationApi from '../services/emailVerificationApi';

const { Title, Text } = Typography;

const EmailVerificationTest: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; content: string } | null>(null);
  const [verificationData, setVerificationData] = useState<any>(null);

  // 发送验证码
  const handleSendCode = async (values: { email: string; type: string }) => {
    try {
      setLoading(true);
      setMessage(null);

      const result = await emailVerificationApi.sendVerificationCode({
        email: values.email,
        type: values.type as any
      });

      setMessage({
        type: 'success',
        content: `验证码已发送到 ${result.email}，有效期 ${result.expiresIn / 60} 分钟`
      });

      setVerificationData(result);
    } catch (error: any) {
      setMessage({
        type: 'error',
        content: error.response?.data?.message || '发送验证码失败'
      });
    } finally {
      setLoading(false);
    }
  };

  // 验证验证码
  const handleVerifyCode = async (values: { email: string; code: string; type: string }) => {
    try {
      setVerifying(true);
      setMessage(null);

      const result = await emailVerificationApi.verifyCode({
        email: values.email,
        code: values.code,
        type: values.type as any
      });

      setMessage({
        type: 'success',
        content: `验证成功！邮箱 ${result.email} 已验证`
      });
    } catch (error: any) {
      setMessage({
        type: 'error',
        content: error.response?.data?.message || '验证失败'
      });
    } finally {
      setVerifying(false);
    }
  };

  // 获取验证状态
  const handleCheckStatus = async () => {
    const email = form.getFieldValue('email');
    const type = form.getFieldValue('type') || 'REGISTRATION';

    if (!email) {
      setMessage({
        type: 'error',
        content: '请先输入邮箱地址'
      });
      return;
    }

    try {
      const status = await emailVerificationApi.getVerificationStatus(email, type);
      setMessage({
        type: 'success',
        content: `状态：${status.hasActiveCode ? '有活跃验证码' : '无活跃验证码'}${
          status.attempts ? `，已尝试 ${status.attempts} 次` : ''
        }`
      });
    } catch (error: any) {
      setMessage({
        type: 'error',
        content: error.response?.data?.message || '获取状态失败'
      });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        <Card>
          <Title level={2} className="text-center mb-6">
            邮箱验证功能测试
          </Title>

          {message && (
            <Alert
              message={message.content}
              type={message.type}
              showIcon
              closable
              onClose={() => setMessage(null)}
              className="mb-4"
            />
          )}

          {/* 发送验证码 */}
          <Card title="1. 发送验证码" className="mb-4">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSendCode}
              initialValues={{ type: 'REGISTRATION' }}
            >
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱地址"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="type"
                label="验证类型"
                rules={[{ required: true, message: '请选择验证类型' }]}
              >
                <Input
                  placeholder="REGISTRATION / PASSWORD_RESET / EMAIL_CHANGE"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={loading}
                    icon={<MailOutlined />}
                  >
                    发送验证码
                  </Button>
                  <Button onClick={handleCheckStatus}>
                    检查状态
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>

          {/* 验证验证码 */}
          <Card title="2. 验证验证码">
            <Form
              layout="vertical"
              onFinish={handleVerifyCode}
              initialValues={{ type: 'REGISTRATION' }}
            >
              <Form.Item
                name="email"
                label="邮箱地址"
                rules={[
                  { required: true, message: '请输入邮箱地址' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input
                  prefix={<MailOutlined />}
                  placeholder="请输入邮箱地址"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="code"
                label="验证码"
                rules={[
                  { required: true, message: '请输入验证码' },
                  { pattern: /^\d{6}$/, message: '验证码必须是6位数字' }
                ]}
              >
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="请输入6位验证码"
                  maxLength={6}
                  size="large"
                />
              </Form.Item>

              <Form.Item
                name="type"
                label="验证类型"
                rules={[{ required: true, message: '请选择验证类型' }]}
              >
                <Input
                  placeholder="REGISTRATION / PASSWORD_RESET / EMAIL_CHANGE"
                  size="large"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={verifying}
                  icon={<SafetyOutlined />}
                  block
                >
                  验证验证码
                </Button>
              </Form.Item>
            </Form>
          </Card>

          {verificationData && (
            <Card title="验证码信息" className="mt-4">
              <pre className="bg-gray-100 p-4 rounded text-sm">
                {JSON.stringify(verificationData, null, 2)}
              </pre>
            </Card>
          )}

          <Divider />

          <div className="text-center">
            <Text type="secondary" className="text-sm">
              这是邮箱验证功能的测试页面，用于验证API是否正常工作
            </Text>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default EmailVerificationTest;
