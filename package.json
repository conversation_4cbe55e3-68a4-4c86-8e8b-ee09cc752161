{"name": "email-system", "version": "1.0.0", "description": "Complete email system with frontend and backend", "private": true, "workspaces": ["frontend", "backend"], "scripts": {"install:all": "npm install && npm run install:frontend && npm run install:backend", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "dev": "npm run dev:backend && npm run dev:frontend", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "dev:start": "node scripts/dev-server.js start", "dev:stop": "node scripts/dev-server.js stop", "dev:restart": "node scripts/dev-server.js restart", "dev:status": "node scripts/dev-server.js status", "dev:kill": "taskkill /F /IM node.exe", "build": "npm run build:backend && npm run build:frontend", "build:dev": "npm run build:backend:dev && npm run build:frontend:dev", "build:staging": "npm run build:backend:staging && npm run build:frontend:staging", "build:frontend": "cd frontend && npm run build", "build:frontend:dev": "cd frontend && npm run build:dev", "build:frontend:staging": "cd frontend && npm run build:staging", "build:backend": "cd backend && npm run build:prod", "build:backend:dev": "cd backend && npm run build", "build:backend:staging": "cd backend && npm run build", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm run test:run", "test:backend": "cd backend && npm run test", "test:watch": "concurrently \"npm run test:frontend:watch\" \"npm run test:backend:watch\"", "test:frontend:watch": "cd frontend && npm run test", "test:backend:watch": "cd backend && npm run test:watch", "test:coverage": "npm run test:frontend:coverage && npm run test:backend:coverage", "test:frontend:coverage": "cd frontend && npm run test:coverage", "test:backend:coverage": "cd backend && npm run test:coverage", "test:e2e": "cd backend && npm run test:e2e", "lint": "npm run lint:frontend && npm run lint:backend", "lint:fix": "npm run lint:frontend:fix && npm run lint:backend:fix", "lint:frontend": "cd frontend && npm run lint", "lint:frontend:fix": "cd frontend && npm run lint:fix", "lint:backend": "cd backend && npm run lint", "lint:backend:fix": "cd backend && npm run lint:fix", "type-check": "npm run type-check:frontend && npm run type-check:backend", "type-check:frontend": "cd frontend && npm run type-check", "type-check:backend": "cd backend && npm run type-check", "clean": "npm run clean:frontend && npm run clean:backend", "clean:frontend": "cd frontend && npm run clean", "clean:backend": "cd backend && npm run clean", "start": "npm run start:backend", "start:backend": "cd backend && npm run start:prod", "start:frontend": "cd frontend && npm run start", "db:generate": "cd backend && npm run db:generate", "db:migrate": "cd backend && npm run db:migrate", "db:migrate:prod": "cd backend && npm run db:migrate:prod", "db:seed": "cd backend && npm run db:seed", "db:studio": "cd backend && npm run db:studio", "db:reset": "cd backend && npm run db:reset", "docker:build": "docker-compose build", "docker:build:prod": "docker-compose -f docker-compose.prod.yml build", "docker:up": "docker-compose up -d", "docker:up:prod": "docker-compose -f docker-compose.prod.yml up -d", "docker:down": "docker-compose down", "docker:down:prod": "docker-compose -f docker-compose.prod.yml down", "docker:logs": "docker-compose logs -f", "deploy": "bash scripts/deploy.sh", "backup": "bash scripts/backup.sh", "update": "bash scripts/update.sh"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["email", "system", "nodejs", "react", "typescript", "prisma"], "author": "Email System Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "mailparser": "^3.7.3"}}