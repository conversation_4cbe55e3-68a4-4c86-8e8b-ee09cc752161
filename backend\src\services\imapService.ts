// 这个文件已被 imapFlowService.ts 替换
// 请使用新的 ImapFlow 服务

import prisma from '../config/database';
import logger from '../utils/logger';
import { subAccountEmailForwardService } from './subAccountEmailForwardService';

export * from './imapFlowService';

// 为了向后兼容，重新导出一些常用函数
import {
  syncUserEmailsViaImapFlow as fetchNewEmailsViaIMAP,
  fetchNewEmailsViaImapFlow,
  testImapConnection,
  getConnectionPoolStatus,
  markEmailAsRead,
  markEmailAsUnread,
  syncDatabaseStatusToIMAP
} from './imapFlowService';

// 兼容性包装函数 - 直接使用fetchNewEmailsViaImapFlow
export async function fetchNewEmails(userEmail: string): Promise<any[]> {
  try {
    if (!userEmail) {
      throw new Error('userEmail 参数是必需的');
    }

    // fetchNewEmailsViaImapFlow 已经返回邮件数组
    return await fetchNewEmailsViaImapFlow(userEmail);
  } catch (error) {
    console.error('fetchNewEmails 错误:', error);
    throw error; // 重新抛出错误，让调用者处理
  }
}

export {
  fetchNewEmailsViaIMAP,
  testImapConnection,
  getConnectionPoolStatus,
  markEmailAsRead,
  markEmailAsUnread,
  syncDatabaseStatusToIMAP
};

// 获取用户IMAP配置 - 兼容性函数
export async function getUserImapConfig(userEmail: string) {
  const { getUserImapFlowConfig } = await import('../utils/imapFlowHelper');
  const config = await getUserImapFlowConfig(userEmail);
  
  // 转换为旧格式以保持兼容性
  return {
    user: config.auth.user,
    password: config.auth.pass,
    host: config.host,
    port: config.port,
    tls: config.secure,
    tlsOptions: config.tls
  };
}

// 保存接收到的邮件 - 兼容性函数
export async function saveReceivedEmail(parsedMail: any, userEmail: string) {
  try {
    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: { id: true, email: true }
    });

    if (!user) {
      logger.error(`用户 ${userEmail} 不存在`);
      return null;
    }

    // 获取收件箱文件夹
    const inboxFolder = await prisma.folder.findFirst({
      where: {
        userId: user.id,
        type: 'inbox'
      }
    });

    if (!inboxFolder) {
      logger.error(`用户 ${userEmail} 的收件箱不存在`);
      return null;
    }

    // 生成messageId（如果没有的话）
    const messageId = parsedMail.messageId || `${Date.now()}-${Math.random().toString(36).substring(2, 11)}@${userEmail.split('@')[1]}`;

    // 检查邮件是否已存在
    const existingEmail = await prisma.email.findFirst({
      where: {
        messageId: messageId,
        userId: user.id
      }
    });

    if (existingEmail) {
      logger.info(`邮件 ${messageId} 已存在，跳过保存`);
      return existingEmail;
    }

    // 处理发件人信息
    const senderEmail = parsedMail.from?.value?.[0]?.address || parsedMail.from?.text || '';
    const senderName = parsedMail.from?.value?.[0]?.name || '';

    // 处理收件人信息
    const recipients = JSON.stringify(parsedMail.to?.value || []);
    const ccRecipients = JSON.stringify(parsedMail.cc?.value || []);
    const bccRecipients = JSON.stringify(parsedMail.bcc?.value || []);

    // 处理附件
    const attachments = JSON.stringify(parsedMail.attachments?.map((att: any) => ({
      filename: att.filename,
      contentType: att.contentType,
      size: att.size,
      cid: att.cid
    })) || []);

    // 创建邮件记录
    const email = await prisma.email.create({
      data: {
        messageId,
        userId: user.id,
        folderId: inboxFolder.id,
        subject: parsedMail.subject || '(无主题)',
        senderEmail,
        senderName,
        recipients,
        ccRecipients,
        bccRecipients,
        contentText: parsedMail.text || '',
        contentHtml: parsedMail.html || '',
        attachments,
        isRead: false, // 新邮件默认未读
        isStarred: false,
        receivedAt: parsedMail.date || new Date()
      }
    });

    logger.info(`新邮件已保存: ${messageId} (用户: ${userEmail})`);

    // 检查是否为子账户，如果是则转发到主账户
    try {
      await subAccountEmailForwardService.handleSubAccountEmailForward(email);
    } catch (forwardError) {
      logger.error(`子账户邮件转发失败: ${messageId}`, forwardError);
      // 转发失败不影响邮件保存
    }

    return email;

  } catch (error) {
    logger.error(`保存邮件失败 (用户: ${userEmail}):`, error);
    return null;
  }
}

// 确定目标用户ID - 兼容性函数  
export async function determineTargetUserId(emailData: any) {
  // 这个函数的实现应该在 imapFlowService.ts 中
  console.warn('determineTargetUserId 函数需要在 imapFlowService.ts 中实现');
  return null;
}