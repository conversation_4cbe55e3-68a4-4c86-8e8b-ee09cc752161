#!/bin/bash

# PM2 管理脚本
# 用于启动、停止、重启和监控邮箱系统后端服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查PM2是否安装
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        log_error "PM2 未安装，请先安装 PM2:"
        echo "npm install -g pm2"
        exit 1
    fi
}

# 检查后端构建文件
check_build() {
    if [ ! -f "backend/dist/index.js" ]; then
        log_warning "后端构建文件不存在，正在构建..."
        cd backend
        npm run build
        cd ..
        log_success "后端构建完成"
    fi
}

# 更新PM2配置中的路径
update_config_path() {
    local current_path=$(pwd)
    local backend_path="$current_path/backend"
    
    # 使用sed替换配置文件中的路径
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        sed -i '' "s|cwd: '/path/to/your/project/backend'|cwd: '$backend_path'|g" backend/ecosystem.config.js
    else
        # Linux
        sed -i "s|cwd: '/path/to/your/project/backend'|cwd: '$backend_path'|g" backend/ecosystem.config.js
    fi
    
    log_info "已更新PM2配置文件路径: $backend_path"
}

# 启动服务
start_service() {
    local env=${1:-dev}
    local app_name="mailbox-$env"
    
    log_info "启动 $env 环境服务..."
    
    check_pm2
    check_build
    update_config_path
    
    # 检查服务是否已经运行
    if pm2 list | grep -q "$app_name"; then
        log_warning "服务 $app_name 已在运行，正在重启..."
        pm2 restart "$app_name"
    else
        log_info "启动新的服务实例..."
        pm2 start backend/ecosystem.config.js --only "$app_name"
    fi
    
    log_success "$env 环境服务启动完成"
    show_status
}

# 停止服务
stop_service() {
    local env=${1:-dev}
    local app_name="mailbox-$env"
    
    log_info "停止 $env 环境服务..."
    
    if pm2 list | grep -q "$app_name"; then
        pm2 stop "$app_name"
        log_success "$env 环境服务已停止"
    else
        log_warning "$env 环境服务未运行"
    fi
}

# 重启服务
restart_service() {
    local env=${1:-dev}
    local app_name="mailbox-$env"
    
    log_info "重启 $env 环境服务..."
    
    if pm2 list | grep -q "$app_name"; then
        pm2 restart "$app_name"
        log_success "$env 环境服务已重启"
    else
        log_warning "$env 环境服务未运行，正在启动..."
        start_service "$env"
    fi
    
    show_status
}

# 删除服务
delete_service() {
    local env=${1:-dev}
    local app_name="mailbox-$env"
    
    log_info "删除 $env 环境服务..."
    
    if pm2 list | grep -q "$app_name"; then
        pm2 delete "$app_name"
        log_success "$env 环境服务已删除"
    else
        log_warning "$env 环境服务不存在"
    fi
}

# 显示服务状态
show_status() {
    log_info "当前PM2服务状态:"
    pm2 list
}

# 显示日志
show_logs() {
    local env=${1:-dev}
    local app_name="mailbox-$env"
    
    log_info "显示 $env 环境服务日志..."
    pm2 logs "$app_name" --lines 50
}

# 监控服务
monitor_service() {
    log_info "启动PM2监控界面..."
    pm2 monit
}

# 保存PM2配置
save_config() {
    log_info "保存PM2配置..."
    pm2 save
    log_success "PM2配置已保存"
}

# 设置开机自启
setup_startup() {
    log_info "设置PM2开机自启..."
    pm2 startup
    log_info "请按照上面的提示执行命令，然后运行:"
    echo "pm2 save"
}

# 显示帮助信息
show_help() {
    echo "PM2 邮箱系统管理脚本"
    echo ""
    echo "用法: $0 <command> [environment]"
    echo ""
    echo "命令:"
    echo "  start [env]     启动服务 (env: dev|staging|prod, 默认: dev)"
    echo "  stop [env]      停止服务"
    echo "  restart [env]   重启服务"
    echo "  delete [env]    删除服务"
    echo "  status          显示服务状态"
    echo "  logs [env]      显示服务日志"
    echo "  monitor         启动监控界面"
    echo "  save            保存PM2配置"
    echo "  startup         设置开机自启"
    echo "  help            显示帮助信息"
    echo ""
    echo "环境:"
    echo "  dev             开发环境 (默认)"
    echo "  staging         预发布环境"
    echo "  prod            生产环境"
    echo ""
    echo "示例:"
    echo "  $0 start dev          # 启动开发环境"
    echo "  $0 start prod         # 启动生产环境"
    echo "  $0 restart staging    # 重启预发布环境"
    echo "  $0 logs prod          # 查看生产环境日志"
}

# 主函数
main() {
    local command=${1:-help}
    local env=${2:-dev}
    
    case $command in
        "start")
            start_service "$env"
            ;;
        "stop")
            stop_service "$env"
            ;;
        "restart")
            restart_service "$env"
            ;;
        "delete")
            delete_service "$env"
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$env"
            ;;
        "monitor")
            monitor_service
            ;;
        "save")
            save_config
            ;;
        "startup")
            setup_startup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 脚本入口
main "$@"
