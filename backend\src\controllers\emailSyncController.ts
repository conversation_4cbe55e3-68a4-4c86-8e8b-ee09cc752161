import { Response } from 'express';
import { AuthenticatedRequest, AppError, ApiResponse } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';
import { syncIMAPToDatabase } from '../services/imapService';
import { reassignOrphanEmails, recoverEmailsFromPhysicalFolder } from '../services/mailboxService';

interface SyncSettings {
  autoSync: boolean;
  syncInterval: number;
  syncOnStartup: boolean;
  syncFolders: string[];
  maxEmailsPerSync: number;
  retryAttempts: number;
  enableRealTimeSync: boolean;
}

interface SyncStats {
  totalSynced: number;
  lastSyncTime: string;
  syncErrors: number;
  avgSyncTime: number;
  successRate: number;
}

// 获取同步设置
export const getSyncSettings = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;

    // 从数据库获取用户的同步设置
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        syncSettings: true,
      },
    });

    // 默认设置
    const defaultSettings: SyncSettings = {
      autoSync: true,
      syncInterval: 5,
      syncOnStartup: true,
      syncFolders: ['INBOX', 'SENT', 'DRAFT'],
      maxEmailsPerSync: 100,
      retryAttempts: 3,
      enableRealTimeSync: true,
    };

    let settings = defaultSettings;
    if (user?.syncSettings) {
      try {
        settings = { ...defaultSettings, ...JSON.parse(user.syncSettings) };
      } catch (error) {
        logger.warn('解析同步设置失败，使用默认设置:', error);
      }
    }

    const response: ApiResponse = {
      success: true,
      message: '获取同步设置成功',
      data: settings,
    };

    res.json(response);
  } catch (error) {
    logger.error('获取同步设置失败:', error);
    throw new AppError('获取同步设置失败', 500);
  }
};

// 更新同步设置
export const updateSyncSettings = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const settings: SyncSettings = req.body;

    // 验证设置
    if (settings.syncInterval < 1 || settings.syncInterval > 60) {
      throw new AppError('同步间隔必须在1-60分钟之间', 400);
    }

    if (settings.maxEmailsPerSync < 10 || settings.maxEmailsPerSync > 1000) {
      throw new AppError('每次同步最大邮件数必须在10-1000之间', 400);
    }

    if (settings.retryAttempts < 1 || settings.retryAttempts > 10) {
      throw new AppError('重试次数必须在1-10之间', 400);
    }

    // 更新用户设置
    await prisma.user.update({
      where: { id: userId },
      data: {
        syncSettings: JSON.stringify(settings),
      },
    });

    const response: ApiResponse = {
      success: true,
      message: '同步设置更新成功',
      data: settings,
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('更新同步设置失败:', error);
    throw new AppError('更新同步设置失败', 500);
  }
};

// 获取同步统计
export const getSyncStats = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 获取最近30天的邮件统计
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const emails = await prisma.email.findMany({
      where: {
        userId,
        createdAt: { gte: thirtyDaysAgo },
      },
      select: {
        id: true,
        createdAt: true,
      },
    });

    // 模拟同步统计数据
    const stats: SyncStats = {
      totalSynced: emails.length,
      lastSyncTime: emails.length > 0 ? emails[emails.length - 1].createdAt?.toISOString() || '' : '',
      syncErrors: Math.floor(emails.length * 0.02), // 假设2%的错误率
      avgSyncTime: 2.5, // 平均2.5秒
      successRate: 98.0, // 98%成功率
    };

    const response: ApiResponse = {
      success: true,
      message: '获取同步统计成功',
      data: stats,
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取同步统计失败:', error);
    throw new AppError('获取同步统计失败', 500);
  }
};

// 手动触发同步
export const triggerSync = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true },
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 触发IMAP同步
    const syncResult = await syncIMAPToDatabase(user.email);

    const response: ApiResponse = {
      success: true,
      message: '邮件同步完成',
      data: {
        syncedCount: syncResult.syncedCount,
        skippedCount: syncResult.skippedCount,
        errorCount: syncResult.errorCount,
        details: syncResult.syncedEmails,
      },
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('手动同步失败:', error);
    throw new AppError('邮件同步失败', 500);
  }
};

// 获取同步日志
export const getSyncLogs = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { page = 1, limit = 20 } = req.query;

    const pageNum = parseInt(page as string);
    const limitNum = parseInt(limit as string);
    const skip = (pageNum - 1) * limitNum;

    // 这里应该从实际的同步日志表获取数据
    // 目前返回模拟数据
    const logs = [
      {
        id: 1,
        timestamp: new Date().toISOString(),
        type: 'success',
        message: '邮件同步成功',
        details: '同步了5封新邮件',
      },
      {
        id: 2,
        timestamp: new Date(Date.now() - 300000).toISOString(),
        type: 'info',
        message: '开始邮件同步',
        details: '连接到IMAP服务器',
      },
    ];

    const response: ApiResponse = {
      success: true,
      message: '获取同步日志成功',
      data: {
        logs,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: logs.length,
          totalPages: Math.ceil(logs.length / limitNum),
          hasNext: pageNum < Math.ceil(logs.length / limitNum),
          hasPrev: pageNum > 1,
        },
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('获取同步日志失败:', error);
    throw new AppError('获取同步日志失败', 500);
  }
};

// 重新分配孤儿邮件
export const reassignOrphanEmailsController = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, role: true },
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    logger.info(`用户 ${user.email} 请求重新分配孤儿邮件和恢复物理文件夹邮件`);

    // 1. 执行数据库孤儿邮件重新分配
    const orphanResult = await reassignOrphanEmails(user.email);
    logger.info(`数据库孤儿邮件重新分配完成: ${orphanResult.reassignedCount} 封`);

    // 2. 从物理文件夹恢复邮件
    const recoveryResult = await recoverEmailsFromPhysicalFolder(user.email);
    logger.info(`物理文件夹邮件恢复完成: 恢复 ${recoveryResult.recoveredCount} 封，跳过 ${recoveryResult.skippedCount} 封，错误 ${recoveryResult.errorCount} 封`);

    const totalRecovered = orphanResult.reassignedCount + recoveryResult.recoveredCount;

    let message = '';
    if (totalRecovered > 0) {
      message = `成功恢复 ${totalRecovered} 封邮件`;
      if (orphanResult.reassignedCount > 0) {
        message += `（数据库孤儿邮件: ${orphanResult.reassignedCount} 封`;
      }
      if (recoveryResult.recoveredCount > 0) {
        if (orphanResult.reassignedCount > 0) {
          message += `，物理文件夹: ${recoveryResult.recoveredCount} 封）`;
        } else {
          message += `（物理文件夹: ${recoveryResult.recoveredCount} 封）`;
        }
      } else if (orphanResult.reassignedCount > 0) {
        message += '）';
      }
    } else {
      message = '没有找到需要恢复的邮件';
    }

    const response: ApiResponse = {
      success: true,
      message,
      data: {
        totalRecovered,
        orphanEmails: {
          reassignedCount: orphanResult.reassignedCount,
          reassignedEmails: orphanResult.reassignedEmails
        },
        physicalFolderEmails: {
          recoveredCount: recoveryResult.recoveredCount,
          skippedCount: recoveryResult.skippedCount,
          errorCount: recoveryResult.errorCount,
          recoveredEmails: recoveryResult.recoveredEmails
        },
        userEmail: user.email
      },
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('重新分配孤儿邮件失败:', error);
    throw new AppError('重新分配孤儿邮件失败', 500);
  }
};
