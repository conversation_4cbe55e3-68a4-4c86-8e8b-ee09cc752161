import { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Upload,
  message,
  Space,
  Tag,
  Divider,
  Switch,
  Modal,
  Select,
  Tooltip,
  Badge,
  Avatar,
  Dropdown,
  Progress,
} from 'antd';
import {
  SendOutlined,
  SaveOutlined,
  PaperClipOutlined,
  DeleteOutlined,
  EyeOutlined,
  FolderOutlined,
  UserOutlined,
  CloseOutlined,
  SettingOutlined,
  ExclamationCircleOutlined,
  ArrowDownOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  MinusOutlined,
} from '@ant-design/icons';
import { useEmailStore } from '../store/emailStore';
import type { EmailData, EmailRecipient } from '../types';
import RichTextEditor from '../components/RichTextEditor';
import FileManager from '../components/FileManager';
import { getEmailById } from '../services/emailApi';

const { TextArea } = Input;
// const { Option } = Select;

const Compose: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { sendEmail, saveDraft, loading } = useEmailStore();
  const [recipients, setRecipients] = useState<EmailRecipient[]>([]);
  const [ccRecipients, setCcRecipients] = useState<EmailRecipient[]>([]);
  const [bccRecipients, setBccRecipients] = useState<EmailRecipient[]>([]);
  const [showCc, setShowCc] = useState(false);
  const [showBcc, setShowBcc] = useState(false);
  const [emailContent, setEmailContent] = useState('');
  const [attachments, setAttachments] = useState<any[]>([]);
  const [isHtmlMode, setIsHtmlMode] = useState(true);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [useSignature, setUseSignature] = useState(true);
  const [selectedSignature, setSelectedSignature] = useState<string>('default');
  const [signatures, setSignatures] = useState<any[]>([]);
  const [priority, setPriority] = useState<'low' | 'normal' | 'high'>('normal');
  const [fileManagerVisible, setFileManagerVisible] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [autoSaveStatus, setAutoSaveStatus] = useState<'saved' | 'saving' | 'unsaved'>('saved');
  const [uploadProgress, setUploadProgress] = useState<number>(0);

  // 加载签名列表
  useEffect(() => {
    loadSignatures();
  }, []);

  // 检查是否是回复或转发
  useEffect(() => {
    const replyTo = searchParams.get('replyTo');
    const forward = searchParams.get('forward');
    const subject = searchParams.get('subject');
    const content = searchParams.get('content');

    // 如果是从模板页面来的，直接设置内容
    if (subject && content) {
      form.setFieldsValue({ subject });
      setEmailContent(content);
      return;
    }

    if (replyTo) {
      loadEmailForReply(replyTo);
    }

    if (forward) {
      loadEmailForForward(forward);
    }
  }, [searchParams]);

  // 加载签名列表
  const loadSignatures = async () => {
    try {
      // 模拟签名数据 - 实际应该从API获取
      const mockSignatures = [
        {
          id: 'default',
          name: '默认签名',
          content: `
            <div style="font-family: Arial, sans-serif; color: #333; margin-top: 20px;">
              <p>此致<br/>敬礼！</p>
              <hr style="border: none; border-top: 1px solid #ddd; margin: 10px 0;">
              <p><strong>张三</strong><br/>
              高级软件工程师<br/>
              ABC科技有限公司<br/>
              电话：+86 138-0000-0000<br/>
              邮箱：<EMAIL></p>
            </div>
          `,
          isDefault: true,
          isEnabled: true
        },
        {
          id: 'simple',
          name: '简洁签名',
          content: `
            <div style="font-family: Arial, sans-serif; color: #666; margin-top: 20px;">
              <p>Best regards,<br/>张三</p>
            </div>
          `,
          isDefault: false,
          isEnabled: true
        }
      ];
      setSignatures(mockSignatures);

      // 设置默认签名
      const defaultSig = mockSignatures.find(sig => sig.isDefault);
      if (defaultSig) {
        setSelectedSignature(defaultSig.id);
      }
    } catch (error) {
      console.error('加载签名失败:', error);
    }
  };

  // 加载邮件用于回复
  const loadEmailForReply = async (emailId: string) => {
    try {
      const originalEmail = await getEmailById(emailId);

        // 设置回复的收件人为原邮件的发件人
        const replyTo = [{ email: originalEmail.senderEmail, name: originalEmail.senderName }];
        setRecipients(replyTo);

        // 设置回复主题
        const replySubject = originalEmail.subject?.startsWith('Re: ')
          ? originalEmail.subject
          : `Re: ${originalEmail.subject || '(无主题)'}`;
        form.setFieldsValue({ subject: replySubject });

        // 设置回复内容，包含原邮件引用
        const originalContent = originalEmail.contentHtml || originalEmail.contentText || '';
        const replyContent = `
          <br><br>
          <div style="border-left: 3px solid #ccc; padding-left: 10px; margin-left: 10px;">
            <p><strong>原邮件信息：</strong></p>
            <p><strong>发件人：</strong> ${originalEmail.senderName || originalEmail.senderEmail}</p>
            <p><strong>发送时间：</strong> ${new Date(originalEmail.receivedAt || originalEmail.createdAt).toLocaleString()}</p>
            <p><strong>主题：</strong> ${originalEmail.subject || '(无主题)'}</p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 10px 0;">
            ${originalContent}
          </div>
        `;
        setEmailContent(replyContent);
    } catch (error) {
      console.error('加载邮件失败:', error);
      message.error('加载原邮件失败');
    }
  };

  // 加载邮件用于转发
  const loadEmailForForward = async (emailId: string) => {
    try {
      const originalEmail = await getEmailById(emailId);

      if (originalEmail) {
        // 设置转发主题
        const forwardSubject = originalEmail.subject?.startsWith('Fwd: ')
          ? originalEmail.subject
          : `Fwd: ${originalEmail.subject || '(无主题)'}`;
        form.setFieldsValue({ subject: forwardSubject });

        // 设置转发内容，包含原邮件完整信息
        const originalContent = originalEmail.contentHtml || originalEmail.contentText || '';
        const forwardContent = `
          <br><br>
          <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; background-color: #f9f9f9;">
            <p><strong>---------- 转发邮件 ----------</strong></p>
            <p><strong>发件人：</strong> ${originalEmail.senderName || originalEmail.senderEmail}</p>
            <p><strong>发送时间：</strong> ${new Date(originalEmail.receivedAt || originalEmail.createdAt).toLocaleString()}</p>
            <p><strong>收件人：</strong> ${JSON.parse(originalEmail.recipients || '[]').map((r: any) => r.email).join(', ')}</p>
            ${originalEmail.ccRecipients ? `<p><strong>抄送：</strong> ${JSON.parse(originalEmail.ccRecipients).map((r: any) => r.email).join(', ')}</p>` : ''}
            <p><strong>主题：</strong> ${originalEmail.subject || '(无主题)'}</p>
            <hr style="border: none; border-top: 1px solid #ddd; margin: 10px 0;">
            ${originalContent}
          </div>
        `;
        setEmailContent(forwardContent);

        // 如果原邮件有附件，也复制过来
        if (originalEmail.attachments) {
          try {
            const attachmentList = JSON.parse(originalEmail.attachments);
            if (Array.isArray(attachmentList) && attachmentList.length > 0) {
              setAttachments(attachmentList.map((att: any, index: number) => ({
                uid: `forward-${index}`,
                name: att.filename || `附件${index + 1}`,
                status: 'done',
                url: att.path,
                size: att.size,
              })));
            }
          } catch (e) {
            console.warn('解析附件信息失败:', e);
          }
        }
      }
    } catch (error) {
      console.error('加载邮件失败:', error);
      message.error('加载原邮件失败');
    }
  };

  const handleSend = async (values: any) => {
    try {
      if (recipients.length === 0) {
        message.error('请至少添加一个收件人');
        return;
      }

      // 准备邮件内容，如果启用签名则添加签名
      let finalContent = emailContent;
      if (useSignature && selectedSignature) {
        const signature = signatures.find(sig => sig.id === selectedSignature);
        if (signature && signature.isEnabled) {
          finalContent = emailContent + signature.content;
        }
      }

      const emailData: EmailData = {
        to: recipients,
        cc: showCc && ccRecipients.length > 0 ? ccRecipients : undefined,
        bcc: showBcc && bccRecipients.length > 0 ? bccRecipients : undefined,
        subject: values.subject,
        content: finalContent,
        attachments: attachments,
        priority: priority,
      };

      await sendEmail(emailData);
      message.success('邮件发送成功');
      navigate('/inbox');
    } catch (error) {
      message.error('邮件发送失败');
    }
  };

  const handleSaveDraft = async () => {
    try {
      const values = form.getFieldsValue();
      const emailData = {
        to: recipients,
        cc: showCc && ccRecipients.length > 0 ? ccRecipients : undefined,
        bcc: showBcc && bccRecipients.length > 0 ? bccRecipients : undefined,
        subject: values.subject || '',
        content: emailContent,
        attachments: attachments,
      };

      await saveDraft(emailData);
      message.success('草稿保存成功');
    } catch (error) {
      message.error('保存草稿失败');
    }
  };

  const parseEmailInput = (input: string): EmailRecipient[] => {
    if (!input.trim()) return [];

    return input
      .split(/[,;]/)
      .map(email => email.trim())
      .filter(email => email && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email))
      .map(email => ({ email }));
  };

  const handleRecipientsChange = (value: string) => {
    const parsed = parseEmailInput(value);
    setRecipients(parsed);

    // 实时验证邮箱格式
    const invalid = value.split(/[,;]/).filter(email => {
      const trimmed = email.trim();
      return trimmed && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(trimmed);
    });

    if (invalid.length > 0) {
      form.setFields([{
        name: 'to',
        errors: [`无效的邮箱地址: ${invalid.join(', ')}`]
      }]);
    } else {
      form.setFields([{ name: 'to', errors: [] }]);
    }
  };

  const handleCcChange = (value: string) => {
    setCcRecipients(parseEmailInput(value));
  };

  const handleBccChange = (value: string) => {
    setBccRecipients(parseEmailInput(value));
  };

  const handleAttachmentChange = (info: any) => {
    setAttachments(info.fileList);
  };

  const handlePreview = () => {
    setPreviewVisible(true);
  };

  const handleFileManagerSelect = (files: any[]) => {
    // 将选中的文件添加到附件列表
    const newAttachments = files.map(file => ({
      uid: file.id,
      name: file.name,
      status: 'done',
      url: file.url,
      size: file.size,
    }));
    setAttachments(prev => [...prev, ...newAttachments]);
    setFileManagerVisible(false);
  };

  // 获取优先级显示信息
  const getPriorityInfo = (priority: string) => {
    switch (priority) {
      case 'high':
        return { icon: <ExclamationCircleOutlined />, color: '#ff4d4f', text: '高优先级' };
      case 'low':
        return { icon: <ArrowDownOutlined />, color: '#52c41a', text: '低优先级' };
      default:
        return { icon: <MinusOutlined />, color: '#1890ff', text: '普通' };
    }
  };

  // 自动保存草稿
  useEffect(() => {
    const autoSave = setTimeout(() => {
      if (emailContent || form.getFieldValue('subject')) {
        setAutoSaveStatus('saving');
        handleSaveDraft().then(() => {
          setAutoSaveStatus('saved');
        }).catch(() => {
          setAutoSaveStatus('unsaved');
        });
      }
    }, 30000); // 30秒自动保存

    return () => clearTimeout(autoSave);
  }, [emailContent, form]);

  // 计算附件总大小
  const getTotalAttachmentSize = () => {
    return attachments.reduce((total, file) => total + (file.size || 0), 0);
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="h-full bg-secondary compose-layout">
      <div className="max-w-6xl mx-auto h-full flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-primary shadow-sm border-b border-light p-4 compose-header">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <h1 className="text-xl font-semibold text-primary m-0">写邮件</h1>

              {/* 自动保存状态 */}
              <div className="flex items-center space-x-2 text-xs text-secondary">
                {autoSaveStatus === 'saving' && (
                  <>
                    <ClockCircleOutlined className="animate-spin" />
                    <span>正在保存...</span>
                  </>
                )}
                {autoSaveStatus === 'saved' && (
                  <>
                    <CheckCircleOutlined className="text-success" />
                    <span>已保存</span>
                  </>
                )}
                {autoSaveStatus === 'unsaved' && (
                  <>
                    <ExclamationCircleOutlined className="text-warning" />
                    <span>未保存</span>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center space-x-2">
              {/* 编辑模式切换 */}
              <Tooltip title={isHtmlMode ? "切换到纯文本模式" : "切换到富文本模式"}>
                <Switch
                  checkedChildren="富文本"
                  unCheckedChildren="纯文本"
                  checked={isHtmlMode}
                  onChange={setIsHtmlMode}
                  size="small"
                />
              </Tooltip>

              {/* 预览按钮 */}
              <Tooltip title="预览邮件">
                <Button
                  type="text"
                  icon={<EyeOutlined />}
                  onClick={handlePreview}
                  className="header-action-btn"
                />
              </Tooltip>

              {/* 最小化按钮 */}
              <Tooltip title={isMinimized ? "展开" : "最小化"}>
                <Button
                  type="text"
                  icon={isMinimized ? <ArrowDownOutlined /> : <MinusOutlined />}
                  onClick={() => setIsMinimized(!isMinimized)}
                  className="header-action-btn"
                />
              </Tooltip>

              {/* 关闭按钮 */}
              <Tooltip title="关闭">
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => navigate('/inbox')}
                  className="header-action-btn text-error hover:bg-error-light"
                />
              </Tooltip>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        {!isMinimized && (
          <div className="flex-1 overflow-hidden">
            <div className="h-full p-6 overflow-auto">
              <Card className="shadow-md border-0 compose-card" bodyStyle={{ padding: '32px' }}>
                <Form
                  form={form}
                  layout="vertical"
                  onFinish={handleSend}
                  className="space-y-6"
                >
                  {/* 收件人区域 */}
                  <div className="bg-secondary rounded-lg p-6 space-y-4">
                    {/* 收件人 */}
                    <Form.Item
                      label={
                        <span className="text-base font-medium text-primary">收件人</span>
                      }
                      name="to"
                      rules={[
                        { required: true, message: '请输入收件人' },
                        {
                          validator: (_, value) => {
                            if (!value) return Promise.resolve();
                            const emails = value.split(/[,;]/).map((e: string) => e.trim()).filter((e: string) => e);
                            const invalid = emails.filter((email: string) => !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email));
                            if (invalid.length > 0) {
                              return Promise.reject(new Error(`无效的邮箱地址: ${invalid.join(', ')}`));
                            }
                            return Promise.resolve();
                          }
                        }
                      ]}
                      className="mb-0"
                    >
                      <Input
                        placeholder="请输入收件人邮箱地址，多个地址用逗号或分号分隔"
                        onChange={(e) => handleRecipientsChange(e.target.value)}
                        size="large"
                        suffix={
                          <Space>
                            {!showCc && (
                              <Button
                                type="link"
                                size="small"
                                onClick={() => setShowCc(true)}
                                className="text-primary hover:text-primary-hover"
                              >
                                抄送
                              </Button>
                            )}
                            {!showBcc && (
                              <Button
                                type="link"
                                size="small"
                                onClick={() => setShowBcc(true)}
                                className="text-primary hover:text-primary-hover"
                              >
                                密送
                              </Button>
                            )}
                          </Space>
                        }
                      />
                    </Form.Item>

                    {/* 收件人标签显示 */}
                    {recipients.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {recipients.map((recipient, index) => (
                          <Tag
                            key={index}
                            color="blue"
                            closable
                            onClose={() => {
                              const newRecipients = recipients.filter((_, i) => i !== index);
                              setRecipients(newRecipients);
                              form.setFieldValue('to', newRecipients.map(r => r.email).join(', '));
                            }}
                            className="flex items-center"
                          >
                            <Avatar size="small" icon={<UserOutlined />} className="mr-1" />
                            {recipient.email}
                          </Tag>
                        ))}
                      </div>
                    )}

                    {/* 抄送 */}
                    {showCc && (
                      <Form.Item
                        label={<span className="text-sm font-medium text-secondary">抄送</span>}
                        name="cc"
                        className="mb-0"
                      >
                        <Input
                          placeholder="请输入抄送人邮箱地址"
                          onChange={(e) => handleCcChange(e.target.value)}
                          size="large"
                          suffix={
                            <Tooltip title="移除抄送">
                              <Button
                                type="link"
                                size="small"
                                icon={<CloseOutlined />}
                                onClick={() => setShowCc(false)}
                                className="text-tertiary hover:text-error"
                              />
                            </Tooltip>
                          }
                        />
                      </Form.Item>
                    )}

                    {/* 抄送标签显示 */}
                    {showCc && ccRecipients.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {ccRecipients.map((recipient, index) => (
                          <Tag
                            key={index}
                            color="green"
                            closable
                            onClose={() => {
                              const newCcRecipients = ccRecipients.filter((_, i) => i !== index);
                              setCcRecipients(newCcRecipients);
                              form.setFieldValue('cc', newCcRecipients.map(r => r.email).join(', '));
                            }}
                            className="flex items-center"
                          >
                            <Avatar size="small" icon={<UserOutlined />} className="mr-1" />
                            {recipient.email}
                          </Tag>
                        ))}
                      </div>
                    )}

                    {/* 密送 */}
                    {showBcc && (
                      <Form.Item
                        label={<span className="text-sm font-medium text-secondary">密送</span>}
                        name="bcc"
                        className="mb-0"
                      >
                        <Input
                          placeholder="请输入密送人邮箱地址"
                          onChange={(e) => handleBccChange(e.target.value)}
                          size="large"
                          suffix={
                            <Tooltip title="移除密送">
                              <Button
                                type="link"
                                size="small"
                                icon={<CloseOutlined />}
                                onClick={() => setShowBcc(false)}
                                className="text-tertiary hover:text-error"
                              />
                            </Tooltip>
                          }
                        />
                      </Form.Item>
                    )}

                    {/* 密送标签显示 */}
                    {showBcc && bccRecipients.length > 0 && (
                      <div className="flex flex-wrap gap-2">
                        {bccRecipients.map((recipient, index) => (
                          <Tag
                            key={index}
                            color="orange"
                            closable
                            onClose={() => {
                              const newBccRecipients = bccRecipients.filter((_, i) => i !== index);
                              setBccRecipients(newBccRecipients);
                              form.setFieldValue('bcc', newBccRecipients.map(r => r.email).join(', '));
                            }}
                            className="flex items-center"
                          >
                            <Avatar size="small" icon={<UserOutlined />} className="mr-1" />
                            {recipient.email}
                          </Tag>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* 主题和优先级 */}
                  <div className="bg-secondary rounded-lg p-6 space-y-4">
                    <div className="flex items-end space-x-4">
                      {/* 主题 */}
                      <Form.Item
                        label={<span className="text-base font-medium text-primary">主题</span>}
                        name="subject"
                        rules={[{ required: true, message: '请输入邮件主题' }]}
                        className="flex-1 mb-0"
                      >
                        <Input
                          placeholder="请输入邮件主题"
                          size="large"
                          className="text-base"
                        />
                      </Form.Item>

                      {/* 优先级 */}
                      <Form.Item
                        label={<span className="text-sm font-medium text-secondary">优先级</span>}
                        className="mb-0"
                      >
                        <Select
                          value={priority}
                          onChange={setPriority}
                          size="large"
                          style={{ width: '140px' }}
                          options={[
                            {
                              label: (
                                <span className="flex items-center text-success">
                                  <ArrowDownOutlined className="mr-2" />
                                  低优先级
                                </span>
                              ),
                              value: 'low'
                            },
                            {
                              label: (
                                <span className="flex items-center text-primary">
                                  <MinusOutlined className="mr-2" />
                                  普通
                                </span>
                              ),
                              value: 'normal'
                            },
                            {
                              label: (
                                <span className="flex items-center text-error">
                                  <ExclamationCircleOutlined className="mr-2" />
                                  高优先级
                                </span>
                              ),
                              value: 'high'
                            }
                          ]}
                        />
                      </Form.Item>
                    </div>

                    {/* 优先级提示 */}
                    {priority !== 'normal' && (
                      <div className={`
                        flex items-center p-3 rounded-lg text-sm
                        ${priority === 'high' ? 'bg-error-light text-error' : 'bg-success-light text-success'}
                      `}>
                        {getPriorityInfo(priority).icon}
                        <span className="ml-2">
                          {priority === 'high'
                            ? '高优先级邮件将在收件人的邮箱中优先显示'
                            : '低优先级邮件可能不会立即引起收件人注意'
                          }
                        </span>
                      </div>
                    )}
                  </div>

                  {/* 附件 */}
                  <div className="bg-secondary rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-base font-medium text-primary">附件</span>
                      {attachments.length > 0 && (
                        <div className="text-sm text-secondary">
                          {attachments.length} 个文件，共 {formatFileSize(getTotalAttachmentSize())}
                        </div>
                      )}
                    </div>

                    <div className="space-y-4">
                      {/* 上传按钮 */}
                      <div className="flex items-center space-x-3">
                        <Upload
                          multiple
                          beforeUpload={(file) => {
                            const isLt10M = file.size / 1024 / 1024 < 10;
                            if (!isLt10M) {
                              message.error('文件大小不能超过10MB');
                              return false;
                            }
                            setUploadProgress(0);
                            // 模拟上传进度
                            const interval = setInterval(() => {
                              setUploadProgress(prev => {
                                if (prev >= 100) {
                                  clearInterval(interval);
                                  return 100;
                                }
                                return prev + 10;
                              });
                            }, 100);
                            return false; // 阻止自动上传
                          }}
                          onChange={handleAttachmentChange}
                          showUploadList={false}
                          fileList={attachments}
                        >
                          <Button
                            icon={<PaperClipOutlined />}
                            size="large"
                            className="bg-primary-light text-primary border-primary-light hover:bg-primary hover:text-white"
                          >
                            本地上传
                          </Button>
                        </Upload>

                        <Button
                          icon={<FolderOutlined />}
                          onClick={() => setFileManagerVisible(true)}
                          size="large"
                          className="bg-secondary border-light"
                        >
                          文件管理器
                        </Button>

                        <div className="text-xs text-tertiary">
                          支持多文件上传，单个文件最大10MB
                        </div>
                      </div>

                      {/* 上传进度 */}
                      {uploadProgress > 0 && uploadProgress < 100 && (
                        <Progress
                          percent={uploadProgress}
                          size="small"
                          status="active"
                          className="mb-2"
                        />
                      )}

                      {/* 附件列表 */}
                      {attachments.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          {attachments.map((file, index) => (
                            <div
                              key={index}
                              className="flex items-center p-3 bg-primary rounded-lg border border-light hover:shadow-md transition-all"
                            >
                              <div className="flex-shrink-0 w-10 h-10 bg-primary-light rounded-lg flex items-center justify-center mr-3">
                                <PaperClipOutlined className="text-primary" />
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="text-sm font-medium text-primary truncate">
                                  {file.name}
                                </div>
                                <div className="text-xs text-secondary">
                                  {formatFileSize(file.size || 0)}
                                </div>
                              </div>
                              <Tooltip title="移除附件">
                                <Button
                                  type="text"
                                  size="small"
                                  icon={<CloseOutlined />}
                                  onClick={() => {
                                    const newAttachments = attachments.filter((_, i) => i !== index);
                                    setAttachments(newAttachments);
                                  }}
                                  className="text-tertiary hover:text-error ml-2"
                                />
                              </Tooltip>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 邮件内容 */}
                  <div className="bg-secondary rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-base font-medium text-primary">邮件内容</span>
                      <div className="flex items-center space-x-2 text-xs text-secondary">
                        <span>字数: {emailContent.replace(/<[^>]*>/g, '').length}</span>
                        {isHtmlMode && <span>| HTML模式</span>}
                      </div>
                    </div>

                    <Form.Item
                      rules={[{ required: true, message: '请输入邮件内容' }]}
                      className="mb-0"
                    >
                      <div className="border border-light rounded-lg overflow-hidden">
                        {isHtmlMode ? (
                          <RichTextEditor
                            value={emailContent}
                            onChange={(content) => {
                              setEmailContent(content);
                              setAutoSaveStatus('unsaved');
                            }}
                            placeholder="请输入邮件内容..."
                            height={400}
                          />
                        ) : (
                          <TextArea
                            rows={16}
                            value={emailContent}
                            onChange={(e) => {
                              setEmailContent(e.target.value);
                              setAutoSaveStatus('unsaved');
                            }}
                            placeholder="请输入邮件内容..."
                            className="font-mono border-0 resize-none"
                            style={{
                              fontSize: '14px',
                              lineHeight: '1.6'
                            }}
                          />
                        )}
                      </div>
                    </Form.Item>
                  </div>

                  {/* 签名设置 */}
                  <div className="bg-secondary rounded-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-base font-medium text-primary">邮件签名</span>
                      <Switch
                        checked={useSignature}
                        onChange={setUseSignature}
                        checkedChildren="启用"
                        unCheckedChildren="禁用"
                      />
                    </div>

                    {useSignature && (
                      <div className="space-y-4">
                        <Select
                          value={selectedSignature}
                          onChange={setSelectedSignature}
                          placeholder="选择签名模板"
                          size="large"
                          className="w-full"
                          options={signatures
                            .filter(sig => sig.isEnabled)
                            .map(sig => ({
                              label: (
                                <div className="flex items-center justify-between">
                                  <span>{sig.name}</span>
                                  {sig.isDefault && (
                                    <Tag color="blue" size="small">默认</Tag>
                                  )}
                                </div>
                              ),
                              value: sig.id
                            }))
                          }
                          suffixIcon={<SettingOutlined />}
                        />

                        {selectedSignature && (
                          <div className="border border-light rounded-lg p-4 bg-primary">
                            <div className="text-xs text-secondary mb-2 flex items-center justify-between">
                              <span>签名预览</span>
                              <Button
                                type="link"
                                size="small"
                                onClick={() => {
                                  // 这里可以打开签名编辑器
                                  message.info('签名编辑功能开发中...');
                                }}
                                className="text-xs"
                              >
                                编辑签名
                              </Button>
                            </div>
                            <div
                              className="text-sm"
                              dangerouslySetInnerHTML={{
                                __html: signatures.find(sig => sig.id === selectedSignature)?.content || ''
                              }}
                            />
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between pt-6 border-t border-light">
                    <div className="flex items-center space-x-3">
                      <Button
                        type="primary"
                        htmlType="submit"
                        icon={<SendOutlined />}
                        loading={loading}
                        size="large"
                        className="px-8"
                      >
                        {loading ? '发送中...' : '发送邮件'}
                      </Button>

                      <Button
                        icon={<SaveOutlined />}
                        onClick={handleSaveDraft}
                        size="large"
                        className="px-6"
                        disabled={loading}
                      >
                        保存草稿
                      </Button>

                      <Dropdown
                        menu={{
                          items: [
                            {
                              key: 'schedule',
                              icon: <ClockCircleOutlined />,
                              label: '定时发送',
                              onClick: () => message.info('定时发送功能开发中...')
                            },
                            {
                              key: 'template',
                              icon: <FolderOutlined />,
                              label: '保存为模板',
                              onClick: () => message.info('保存模板功能开发中...')
                            }
                          ]
                        }}
                        trigger={['click']}
                      >
                        <Button size="large" className="px-4">
                          更多选项
                        </Button>
                      </Dropdown>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        onClick={() => navigate('/inbox')}
                        size="large"
                        className="px-6"
                      >
                        取消
                      </Button>
                    </div>
                  </div>
                </Form>

              </Card>
            </div>
          </div>
        )}

        {/* 最小化状态显示 */}
        {isMinimized && (
          <div className="bg-primary shadow-lg border border-light p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <span className="text-sm font-medium text-primary">写邮件</span>
                <span className="text-xs text-secondary">
                  {form.getFieldValue('subject') || '(无主题)'}
                </span>
                {recipients.length > 0 && (
                  <Badge count={recipients.length} size="small" className="ml-2">
                    <UserOutlined className="text-tertiary" />
                  </Badge>
                )}
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  type="text"
                  icon={<ArrowDownOutlined />}
                  onClick={() => setIsMinimized(false)}
                  size="small"
                />
                <Button
                  type="text"
                  icon={<CloseOutlined />}
                  onClick={() => navigate('/inbox')}
                  size="small"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 邮件预览模态框 */}
      <Modal
        title={
          <div className="flex items-center space-x-2">
            <EyeOutlined />
            <span>邮件预览</span>
          </div>
        }
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)} size="large">
            关闭
          </Button>,
          <Button
            key="send"
            type="primary"
            icon={<SendOutlined />}
            onClick={() => {
              setPreviewVisible(false);
              form.submit();
            }}
            size="large"
          >
            发送邮件
          </Button>,
        ]}
        width={900}
        className="email-preview-modal"
      >
        <div className="space-y-6">
          {/* 邮件头部信息 */}
          <div className="bg-secondary rounded-lg p-4 space-y-3">
            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-secondary w-16">收件人:</span>
              <div className="flex flex-wrap gap-1">
                {recipients.map((r, index) => (
                  <Tag key={index} color="blue" className="mb-1">
                    {r.email}
                  </Tag>
                ))}
              </div>
            </div>

            {showCc && ccRecipients.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-secondary w-16">抄送:</span>
                <div className="flex flex-wrap gap-1">
                  {ccRecipients.map((r, index) => (
                    <Tag key={index} color="green" className="mb-1">
                      {r.email}
                    </Tag>
                  ))}
                </div>
              </div>
            )}

            {showBcc && bccRecipients.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium text-secondary w-16">密送:</span>
                <div className="flex flex-wrap gap-1">
                  {bccRecipients.map((r, index) => (
                    <Tag key={index} color="orange" className="mb-1">
                      {r.email}
                    </Tag>
                  ))}
                </div>
              </div>
            )}

            <div className="flex items-center space-x-2">
              <span className="text-sm font-medium text-secondary w-16">主题:</span>
              <span className="text-sm text-primary font-medium">
                {form.getFieldValue('subject') || '(无主题)'}
              </span>
              {priority !== 'normal' && (
                <Tag color={getPriorityInfo(priority).color} className="ml-2">
                  {getPriorityInfo(priority).text}
                </Tag>
              )}
            </div>
          </div>

          {/* 邮件内容 */}
          <div>
            <div className="text-sm font-medium text-secondary mb-3">邮件内容:</div>
            <div
              className="p-4 border border-light rounded-lg bg-primary min-h-32 prose prose-sm max-w-none"
              dangerouslySetInnerHTML={{ __html: emailContent || '<p class="text-tertiary">(无内容)</p>' }}
            />
          </div>

          {/* 附件 */}
          {attachments.length > 0 && (
            <div>
              <div className="text-sm font-medium text-secondary mb-3">
                附件 ({attachments.length} 个文件，共 {formatFileSize(getTotalAttachmentSize())}):
              </div>
              <div className="grid grid-cols-2 gap-2">
                {attachments.map((file, index) => (
                  <div key={index} className="flex items-center p-2 bg-secondary rounded border">
                    <PaperClipOutlined className="text-tertiary mr-2" />
                    <div className="flex-1 min-w-0">
                      <div className="text-xs text-primary truncate">{file.name}</div>
                      <div className="text-xs text-tertiary">{formatFileSize(file.size || 0)}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 签名预览 */}
          {useSignature && selectedSignature && (
            <div>
              <div className="text-sm font-medium text-secondary mb-3">邮件签名:</div>
              <div
                className="p-3 bg-secondary rounded border text-sm"
                dangerouslySetInnerHTML={{
                  __html: signatures.find(sig => sig.id === selectedSignature)?.content || ''
                }}
              />
            </div>
          )}
        </div>
      </Modal>

      {/* 文件管理器 */}
      <FileManager
        visible={fileManagerVisible}
        onClose={() => setFileManagerVisible(false)}
        onSelectFiles={handleFileManagerSelect}
        multiple={true}
        maxSize={50}
        acceptTypes={[]}
      />
    </div>
  );
};

export default Compose;
