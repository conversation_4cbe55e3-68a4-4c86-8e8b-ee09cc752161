import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import { hashPassword, verifyPassword } from '../utils/password';
import { generateTOTPSecret, verifyTOTP, generateBackupCodes } from '../utils/twoFactor';
import { logSecurityEvent } from '../utils/securityLogger';
import logger from '../utils/logger';

// 获取用户安全设置
export const getSecuritySettings = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  let settings = await prisma.userSecuritySettings.findUnique({
    where: { userId },
  });

  // 如果没有安全设置，创建默认设置
  if (!settings) {
    settings = await prisma.userSecuritySettings.create({
      data: { userId },
    });
  }

  // 不返回敏感信息
  const response: ApiResponse = {
    success: true,
    message: '获取安全设置成功',
    data: {
      passwordExpiryDays: settings.passwordExpiryDays,
      lastPasswordChange: settings.lastPasswordChange,
      twoFactorEnabled: settings.twoFactorEnabled,
      loginNotification: settings.loginNotification,
      suspiciousActivity: settings.suspiciousActivity,
      maxActiveSessions: settings.maxActiveSessions,
      sessionTimeout: settings.sessionTimeout,
      hasBackupCodes: !!settings.backup_codes,
      ipWhitelist: settings.ipWhitelist ? JSON.parse(settings.ipWhitelist) : [],
    },
  };

  res.json(response);
};

// 更新安全设置
export const updateSecuritySettings = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const {
    passwordExpiryDays,
    loginNotification,
    suspiciousActivity,
    maxActiveSessions,
    sessionTimeout,
    ipWhitelist,
  } = req.body;

  const updateData: any = {};

  if (passwordExpiryDays !== undefined) updateData.passwordExpiryDays = passwordExpiryDays;
  if (loginNotification !== undefined) updateData.loginNotification = loginNotification;
  if (suspiciousActivity !== undefined) updateData.suspiciousActivity = suspiciousActivity;
  if (maxActiveSessions !== undefined) updateData.maxActiveSessions = maxActiveSessions;
  if (sessionTimeout !== undefined) updateData.sessionTimeout = sessionTimeout;
  if (ipWhitelist !== undefined) updateData.ipWhitelist = JSON.stringify(ipWhitelist);

  const settings = await prisma.userSecuritySettings.upsert({
    where: { userId },
    update: updateData,
    create: { userId, ...updateData },
  });

  await logSecurityEvent(userId, 'security_settings_updated', req.ip || 'unknown', req.get('User-Agent'), {
    changes: Object.keys(updateData),
  });

  const response: ApiResponse = {
    success: true,
    message: '安全设置更新成功',
    data: {
      passwordExpiryDays: settings.passwordExpiryDays,
      lastPasswordChange: settings.lastPasswordChange,
      twoFactorEnabled: settings.twoFactorEnabled,
      loginNotification: settings.loginNotification,
      suspiciousActivity: settings.suspiciousActivity,
      maxActiveSessions: settings.maxActiveSessions,
      sessionTimeout: settings.sessionTimeout,
      hasBackupCodes: !!settings.backup_codes,
      ipWhitelist: settings.ipWhitelist ? JSON.parse(settings.ipWhitelist) : [],
    },
  };

  res.json(response);
};

// 启用两步验证
export const enableTwoFactor = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { password } = req.body;

  // 验证密码
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  const isPasswordValid = await verifyPassword(password, user.password);
  if (!isPasswordValid) {
    throw new AppError('密码错误', 400);
  }

  // 生成TOTP密钥
  const secret = generateTOTPSecret();
  const backupCodes = generateBackupCodes();

  // 加密存储
  const encryptedSecret = encrypt(secret);
  const encryptedBackupCodes = encrypt(JSON.stringify(backupCodes));

  await prisma.userSecuritySettings.upsert({
    where: { userId },
    update: {
      twoFactorSecret: encryptedSecret,
      backup_codes: encryptedBackupCodes,
    },
    create: {
      userId,
      twoFactorSecret: encryptedSecret,
      backup_codes: encryptedBackupCodes,
    },
  });

  await logSecurityEvent(userId, '2fa_setup_initiated', req.ip || 'unknown', req.get('User-Agent'));

  const response: ApiResponse = {
    success: true,
    message: '两步验证设置成功，请使用验证器应用扫描二维码',
    data: {
      secret,
      backupCodes,
      qrCodeUrl: `otpauth://totp/EmailSystem:${user.email}?secret=${secret}&issuer=EmailSystem`,
    },
  };

  res.json(response);
};

// 确认启用两步验证
export const confirmTwoFactor = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { code } = req.body;

  const settings = await prisma.userSecuritySettings.findUnique({
    where: { userId },
  });

  if (!settings || !settings.twoFactorSecret) {
    throw new AppError('两步验证未设置', 400);
  }

  const secret = decrypt(settings.twoFactorSecret);
  const isValid = verifyTOTP(code, secret);

  if (!isValid) {
    throw new AppError('验证码错误', 400);
  }

  await prisma.userSecuritySettings.update({
    where: { userId },
    data: { twoFactorEnabled: true },
  });

  await logSecurityEvent(userId, '2fa_enabled', req.ip || 'unknown', req.get('User-Agent'));

  const response: ApiResponse = {
    success: true,
    message: '两步验证启用成功',
  };

  res.json(response);
};

// 禁用两步验证
export const disableTwoFactor = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { password, code } = req.body;

  // 验证密码
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  const isPasswordValid = await verifyPassword(password, user.password);
  if (!isPasswordValid) {
    throw new AppError('密码错误', 400);
  }

  const settings = await prisma.userSecuritySettings.findUnique({
    where: { userId },
  });

  if (!settings || !settings.twoFactorEnabled) {
    throw new AppError('两步验证未启用', 400);
  }

  // 验证TOTP代码或备用代码
  let isValid = false;
  if (settings.twoFactorSecret) {
    const secret = decrypt(settings.twoFactorSecret);
    isValid = verifyTOTP(code, secret);
  }

  if (!isValid && settings.backup_codes) {
    const backupCodes = JSON.parse(decrypt(settings.backup_codes));
    isValid = backupCodes.includes(code);
  }

  if (!isValid) {
    throw new AppError('验证码错误', 400);
  }

  await prisma.userSecuritySettings.update({
    where: { userId },
    data: {
      twoFactorEnabled: false,
      twoFactorSecret: null,
      backup_codes: null,
    },
  });

  await logSecurityEvent(userId, '2fa_disabled', req.ip || 'unknown', req.get('User-Agent'));

  const response: ApiResponse = {
    success: true,
    message: '两步验证已禁用',
  };

  res.json(response);
};

// 生成新的备用代码
export const generateNewBackupCodes = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { password } = req.body;

  // 验证密码
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  const isPasswordValid = await verifyPassword(password, user.password);
  if (!isPasswordValid) {
    throw new AppError('密码错误', 400);
  }

  const settings = await prisma.userSecuritySettings.findUnique({
    where: { userId },
  });

  if (!settings || !settings.twoFactorEnabled) {
    throw new AppError('两步验证未启用', 400);
  }

  const backupCodes = generateBackupCodes();
  const encryptedBackupCodes = encrypt(JSON.stringify(backupCodes));

  await prisma.userSecuritySettings.update({
    where: { userId },
    data: { backup_codes: encryptedBackupCodes },
  });

  await logSecurityEvent(userId, 'backup_codes_regenerated', req.ip || 'unknown', req.get('User-Agent'));

  const response: ApiResponse = {
    success: true,
    message: '备用代码生成成功',
    data: { backupCodes },
  };

  res.json(response);
};

// 获取安全日志
export const getSecurityLogs = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { page = '1', limit = '20' } = req.query as any;

  const pageNum = parseInt(page) || 1;
  const limitNum = parseInt(limit) || 20;
  const skip = (pageNum - 1) * limitNum;

  const [logs, total] = await Promise.all([
    prisma.securityLog.findMany({
      where: { userId },
      skip,
      take: limitNum,
      orderBy: { createdAt: 'desc' },
    }),
    prisma.securityLog.count({ where: { userId } }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '获取安全日志成功',
    data: {
      logs,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取活跃会话
export const getActiveSessions = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  const sessions = await prisma.userSession.findMany({
    where: {
      userId,
      expiresAt: { gt: new Date() },
    },
    select: {
      id: true,
      createdAt: true,
      expiresAt: true,
      lastUsedAt: true,
      ipAddress: true,
      userAgent: true,
    },
    orderBy: { createdAt: 'desc' },
  });

  const response: ApiResponse = {
    success: true,
    message: '获取活跃会话成功',
    data: sessions,
  };

  res.json(response);
};

// 终止会话
export const terminateSession = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { sessionId } = req.params;

  const session = await prisma.userSession.findFirst({
    where: {
      id: sessionId,
      userId,
    },
  });

  if (!session) {
    throw new AppError('会话不存在', 404);
  }

  await prisma.userSession.delete({
    where: { id: sessionId },
  });

  await logSecurityEvent(userId, 'session_terminated', req.ip || 'unknown', req.get('User-Agent'), {
    terminatedSessionId: sessionId,
  });

  const response: ApiResponse = {
    success: true,
    message: '会话已终止',
  };

  res.json(response);
};

// 终止所有其他会话
export const terminateAllOtherSessions = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const currentSessionToken = req.headers.authorization?.substring(7);

  // 获取当前会话ID（这里需要从JWT中解析或从数据库查找）
  // 为简化，我们删除所有会话然后重新创建当前会话
  const deletedSessions = await prisma.userSession.deleteMany({
    where: { userId },
  });

  await logSecurityEvent(userId, 'all_sessions_terminated', req.ip || 'unknown', req.get('User-Agent'), {
    terminatedCount: deletedSessions.count,
  });

  const response: ApiResponse = {
    success: true,
    message: `已终止 ${deletedSessions.count} 个会话`,
  };

  res.json(response);
};
