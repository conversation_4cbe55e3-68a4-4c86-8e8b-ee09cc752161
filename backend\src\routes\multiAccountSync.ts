import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate, requireAdmin } from '../middleware/auth';
import { validateId } from '../middleware/validation';
import * as multiAccountSyncController from '../controllers/multiAccountSyncController';

const router = Router();

// 所有路由都需要认证
router.use(authenticate);

// 获取同步状态
router.get('/status',
  asyncHandler(multiAccountSyncController.getSyncStatus)
);

// 获取同步统计信息
router.get('/statistics',
  asyncHandler(multiAccountSyncController.getSyncStatistics)
);

// 启动用户的邮箱同步
router.post('/start-user-sync',
  asyncHandler(multiAccountSyncController.startUserSync)
);

// 停止用户的邮箱同步
router.post('/stop-user-sync',
  asyncHandler(multiAccountSyncController.stopUserSync)
);

// 启动单个邮箱账户同步
router.post('/accounts/:id/start',
  validateId,
  asyncHandler(multiAccountSyncController.startAccountSync)
);

// 停止单个邮箱账户同步
router.post('/accounts/:id/stop',
  validateId,
  asyncHandler(multiAccountSyncController.stopAccountSync)
);

// 重启单个邮箱账户同步
router.post('/accounts/:id/restart',
  validateId,
  asyncHandler(multiAccountSyncController.restartAccountSync)
);

// 检查账户同步状态
router.get('/accounts/:id/status',
  validateId,
  asyncHandler(multiAccountSyncController.checkAccountSyncStatus)
);

// 以下路由需要管理员权限
router.use(requireAdmin);

// 启动所有同步
router.post('/start-all',
  asyncHandler(multiAccountSyncController.startAllSync)
);

// 停止所有同步
router.post('/stop-all',
  asyncHandler(multiAccountSyncController.stopAllSync)
);

export default router;
