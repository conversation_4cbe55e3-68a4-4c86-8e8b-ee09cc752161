#!/usr/bin/env node

/**
 * 手动启动统一邮件同步服务
 */

async function manualStartSync() {
  try {
    console.log('🚀 手动启动统一邮件同步服务...\n');

    // 动态导入避免循环依赖
    const { unifiedEmailSyncService } = await import('../src/services/unifiedEmailSyncService.js');
    
    console.log('1. 检查当前状态...');
    const currentStatus = unifiedEmailSyncService.getStatus();
    console.log('当前状态:', JSON.stringify(currentStatus, null, 2));

    console.log('\n2. 停止现有连接...');
    await unifiedEmailSyncService.stopAllUserSync();

    console.log('\n3. 等待 2 秒...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    console.log('\n4. 启动所有用户同步...');
    await unifiedEmailSyncService.startAllUserSync();

    console.log('\n5. 检查启动后状态...');
    const newStatus = unifiedEmailSyncService.getStatus();
    console.log('新状态:', JSON.stringify(newStatus, null, 2));

    console.log('\n✅ 统一邮件同步服务启动完成！');
    
    // 保持脚本运行以观察日志
    console.log('\n📊 监控同步状态（按 Ctrl+C 退出）...');
    
    setInterval(() => {
      const status = unifiedEmailSyncService.getStatus();
      console.log(`\n[${new Date().toLocaleTimeString()}] 同步状态:`);
      console.log(`  总连接数: ${status.totalConnections}`);
      console.log(`  活跃连接数: ${status.activeConnections}`);
      
      if (status.users.length > 0) {
        status.users.forEach(user => {
          console.log(`  - ${user.userEmail}: ${user.isActive ? '✅' : '❌'} (${user.imapState})`);
        });
      }
    }, 30000); // 每30秒检查一次

  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

// 处理退出信号
process.on('SIGINT', async () => {
  console.log('\n\n🛑 收到退出信号，停止同步服务...');
  try {
    const { unifiedEmailSyncService } = await import('../src/services/unifiedEmailSyncService.js');
    await unifiedEmailSyncService.stopAllUserSync();
    console.log('✅ 同步服务已停止');
  } catch (error) {
    console.error('❌ 停止服务失败:', error);
  }
  process.exit(0);
});

manualStartSync();
