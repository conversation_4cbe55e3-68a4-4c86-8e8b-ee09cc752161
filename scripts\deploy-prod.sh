#!/bin/bash

# 生产环境部署脚本
# 用于解决跨域问题和确保正确的环境配置

set -e

echo "🚀 开始生产环境部署..."

# 检查必要的文件
if [ ! -f "backend/.env.prod" ]; then
    echo "❌ 错误: backend/.env.prod 文件不存在"
    exit 1
fi

if [ ! -f "frontend/.env.production" ]; then
    echo "❌ 错误: frontend/.env.production 文件不存在"
    exit 1
fi

# 1. 停止现有服务
echo "📦 停止现有服务..."
docker-compose -f docker-compose.prod.yml down || true

# 2. 构建后端
echo "🔨 构建后端服务..."
cd backend
# 不需要复制环境变量文件，构建时会自动根据 NODE_ENV 加载对应文件
NODE_ENV=production npm run build:prod
cd ..

# 3. 构建前端
echo "🔨 构建前端服务..."
cd frontend
npm run build
cd ..

# 4. 重新构建 Docker 镜像
echo "🐳 构建 Docker 镜像..."
docker-compose -f docker-compose.prod.yml build --no-cache

# 5. 启动服务
echo "🚀 启动生产环境服务..."
docker-compose -f docker-compose.prod.yml up -d

# 6. 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 7. 检查服务状态
echo "🔍 检查服务状态..."
docker-compose -f docker-compose.prod.yml ps

# 8. 测试 API 连接
echo "🧪 测试 API 连接..."
if curl -f -s https://mail.blindedby.love/api/health > /dev/null; then
    echo "✅ API 服务正常"
else
    echo "❌ API 服务异常，请检查日志"
    docker-compose -f docker-compose.prod.yml logs backend
    exit 1
fi

# 9. 测试前端访问
echo "🧪 测试前端访问..."
if curl -f -s https://mail.blindedby.love > /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常，请检查日志"
    docker-compose -f docker-compose.prod.yml logs frontend
    exit 1
fi

echo "🎉 生产环境部署完成！"
echo "📱 前端地址: https://mail.blindedby.love"
echo "🔗 API 地址: https://mail.blindedby.love/api"

# 10. 显示日志
echo "📋 显示最近的日志..."
docker-compose -f docker-compose.prod.yml logs --tail=20
