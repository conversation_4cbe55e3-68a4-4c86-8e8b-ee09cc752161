import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Modal,
  Form,
  message,
  Space,
  Avatar,
  Tag,
  Popconfirm,
  Select,
  Upload,
  Dropdown,
  Divider,
  Row,
  Col,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  MailOutlined,
  DownloadOutlined,
  UploadOutlined,
  MoreOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import type { Contact, ContactGroup } from '../types';
import * as contactApi from '../services/contactApi';

const { Search } = Input;
const { Option } = Select;

const Contacts: React.FC = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [groups, setGroups] = useState<ContactGroup[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [groupModalVisible, setGroupModalVisible] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [editingGroup, setEditingGroup] = useState<ContactGroup | null>(null);
  const [searchText, setSearchText] = useState('');
  const [selectedGroupId, setSelectedGroupId] = useState<number | undefined>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();
  const [groupForm] = Form.useForm();

  // 加载联系人数据
  const loadContacts = async () => {
    try {
      setLoading(true);
      const response = await contactApi.getContacts({
        page: pagination.current,
        limit: pagination.pageSize,
        search: searchText,
        groupId: selectedGroupId,
      });
      setContacts(response.contacts);
      setPagination(prev => ({
        ...prev,
        total: response.pagination.total,
      }));
    } catch (error) {
      message.error('加载联系人失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载联系人分组
  const loadGroups = async () => {
    try {
      const groups = await contactApi.getContactGroups();
      setGroups(groups);
    } catch (error) {
      message.error('加载分组失败');
    }
  };

  useEffect(() => {
    loadContacts();
  }, [pagination.current, pagination.pageSize, searchText, selectedGroupId]);

  useEffect(() => {
    loadGroups();
  }, []);

  const handleAdd = () => {
    setEditingContact(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (contact: Contact) => {
    setEditingContact(contact);
    form.setFieldsValue(contact);
    setModalVisible(true);
  };

  const handleDelete = async (id: number) => {
    try {
      await contactApi.deleteContact(id);
      message.success('联系人删除成功');
      loadContacts();
    } catch (error) {
      message.error('删除失败');
    }
  };

  const handleBatchDelete = async () => {
    try {
      await contactApi.batchDeleteContacts(selectedRowKeys);
      message.success(`成功删除 ${selectedRowKeys.length} 个联系人`);
      setSelectedRowKeys([]);
      loadContacts();
    } catch (error) {
      message.error('批量删除失败');
    }
  };

  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      if (editingContact) {
        await contactApi.updateContact(editingContact.id, values);
        message.success('联系人更新成功');
      } else {
        await contactApi.createContact(values);
        message.success('联系人添加成功');
      }
      setModalVisible(false);
      form.resetFields();
      loadContacts();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 分组管理
  const handleAddGroup = () => {
    setEditingGroup(null);
    groupForm.resetFields();
    setGroupModalVisible(true);
  };

  const handleEditGroup = (group: ContactGroup) => {
    setEditingGroup(group);
    groupForm.setFieldsValue(group);
    setGroupModalVisible(true);
  };

  const handleDeleteGroup = async (id: number) => {
    try {
      await contactApi.deleteContactGroup(id);
      message.success('分组删除成功');
      loadGroups();
      if (selectedGroupId === id) {
        setSelectedGroupId(undefined);
      }
    } catch (error) {
      message.error('删除分组失败');
    }
  };

  const handleGroupSubmit = async (values: { name: string }) => {
    try {
      if (editingGroup) {
        await contactApi.updateContactGroup(editingGroup.id, values);
        message.success('分组更新成功');
      } else {
        await contactApi.createContactGroup(values);
        message.success('分组创建成功');
      }
      setGroupModalVisible(false);
      groupForm.resetFields();
      loadGroups();
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 导入导出功能
  const handleExport = async (format: 'csv' | 'vcf') => {
    try {
      const blob = await contactApi.exportContacts(format);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `contacts.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
      message.success('导出成功');
    } catch (error) {
      message.error('导出失败');
    }
  };

  const handleImport = async (file: File, format: 'csv' | 'vcf') => {
    try {
      const result = await contactApi.importContacts(file, format);
      message.success(`导入成功：${result.imported} 个联系人，跳过 ${result.skipped} 个`);
      if (result.errors.length > 0) {
        message.warning(`有 ${result.errors.length} 个错误`);
      }
      loadContacts();
    } catch (error) {
      message.error('导入失败');
    }
  };

  const columns = [
    {
      title: '头像',
      dataIndex: 'avatarUrl',
      key: 'avatar',
      width: 60,
      render: (avatarUrl: string) => (
        <Avatar
          src={avatarUrl}
          icon={<UserOutlined />}
          size="small"
        />
      ),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: Contact) => (
        <div>
          <div className="font-medium">{name || '未知'}</div>
          <div className="text-sm text-gray-500">{record.email}</div>
        </div>
      ),
    },
    {
      title: '分组',
      dataIndex: 'group',
      key: 'group',
      render: (group: ContactGroup) => (
        group ? <Tag color="blue">{group.name}</Tag> : '-'
      ),
    },
    {
      title: '公司',
      dataIndex: 'company',
      key: 'company',
      render: (company: string) => company || '-',
    },
    {
      title: '电话',
      dataIndex: 'phone',
      key: 'phone',
      render: (phone: string) => phone || '-',
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
      render: (notes: string) => notes || '-',
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_: unknown, record: Contact) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<MailOutlined />}
            onClick={() => window.open(`/compose?to=${record.email}`, '_blank')}
          />
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          />
          <Popconfirm
            title="确定删除这个联系人吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              danger
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 表格行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedKeys: React.Key[]) => {
      setSelectedRowKeys(selectedKeys as number[]);
    },
  };

  // 导入导出菜单
  const exportMenuItems = [
    {
      key: 'csv',
      label: '导出为 CSV',
      onClick: () => handleExport('csv'),
    },
    {
      key: 'vcf',
      label: '导出为 VCF',
      onClick: () => handleExport('vcf'),
    },
  ];

  const importMenuItems = [
    {
      key: 'csv',
      label: (
        <Upload
          accept=".csv"
          showUploadList={false}
          beforeUpload={(file) => {
            handleImport(file, 'csv');
            return false;
          }}
        >
          导入 CSV 文件
        </Upload>
      ),
    },
    {
      key: 'vcf',
      label: (
        <Upload
          accept=".vcf"
          showUploadList={false}
          beforeUpload={(file) => {
            handleImport(file, 'vcf');
            return false;
          }}
        >
          导入 VCF 文件
        </Upload>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Row gutter={16}>
        {/* 左侧分组列表 */}
        <Col span={6}>
          <Card
            title="联系人分组"
            size="small"
            extra={
              <Button
                type="text"
                size="small"
                icon={<PlusOutlined />}
                onClick={handleAddGroup}
              />
            }
          >
            <div className="space-y-2">
              <div
                className={`p-2 cursor-pointer rounded ${
                  !selectedGroupId ? 'bg-blue-50 text-blue-600' : 'hover:bg-gray-50'
                }`}
                onClick={() => setSelectedGroupId(undefined)}
              >
                <TeamOutlined className="mr-2" />
                全部联系人
                <span className="float-right text-gray-400">
                  {pagination.total}
                </span>
              </div>
              {groups.map((group) => (
                <div
                  key={group.id}
                  className={`p-2 cursor-pointer rounded flex items-center justify-between ${
                    selectedGroupId === group.id
                      ? 'bg-blue-50 text-blue-600'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedGroupId(group.id)}
                >
                  <span>
                    <TeamOutlined className="mr-2" />
                    {group.name}
                    <span className="ml-2 text-gray-400">
                      {group._count?.contacts || 0}
                    </span>
                  </span>
                  <Dropdown
                    menu={{
                      items: [
                        {
                          key: 'edit',
                          label: '编辑',
                          onClick: () => handleEditGroup(group),
                        },
                        {
                          key: 'delete',
                          label: '删除',
                          danger: true,
                          onClick: () => handleDeleteGroup(group.id),
                        },
                      ],
                    }}
                    trigger={['click']}
                  >
                    <Button
                      type="text"
                      size="small"
                      icon={<MoreOutlined />}
                      onClick={(e) => e.stopPropagation()}
                    />
                  </Dropdown>
                </div>
              ))}
            </div>
          </Card>
        </Col>

        {/* 右侧联系人列表 */}
        <Col span={18}>
          <Card
            title="联系人管理"
            extra={
              <Space>
                <Dropdown menu={{ items: importMenuItems }}>
                  <Button icon={<UploadOutlined />}>
                    导入
                  </Button>
                </Dropdown>
                <Dropdown menu={{ items: exportMenuItems }}>
                  <Button icon={<DownloadOutlined />}>
                    导出
                  </Button>
                </Dropdown>
                {selectedRowKeys.length > 0 && (
                  <Popconfirm
                    title={`确定删除选中的 ${selectedRowKeys.length} 个联系人吗？`}
                    onConfirm={handleBatchDelete}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button danger>
                      批量删除 ({selectedRowKeys.length})
                    </Button>
                  </Popconfirm>
                )}
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleAdd}
                >
                  添加联系人
                </Button>
              </Space>
            }
          >
            <div className="mb-4">
              <Search
                placeholder="搜索联系人..."
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: 300 }}
                allowClear
              />
            </div>

            <Table
              columns={columns}
              dataSource={contacts}
              rowKey="id"
              loading={loading}
              rowSelection={rowSelection}
              pagination={{
                current: pagination.current,
                pageSize: pagination.pageSize,
                total: pagination.total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                onChange: (page, pageSize) => {
                  setPagination(prev => ({
                    ...prev,
                    current: page,
                    pageSize: pageSize || prev.pageSize,
                  }));
                },
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 添加/编辑联系人模态框 */}
      <Modal
        title={editingContact ? '编辑联系人' : '添加联系人'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            label="邮箱地址"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input placeholder="请输入邮箱地址" />
          </Form.Item>

          <Form.Item
            label="姓名"
            name="name"
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>

          <Form.Item
            label="电话"
            name="phone"
          >
            <Input placeholder="请输入电话号码" />
          </Form.Item>

          <Form.Item
            label="公司"
            name="company"
          >
            <Input placeholder="请输入公司名称" />
          </Form.Item>

          <Form.Item
            label="分组"
            name="groupId"
          >
            <Select placeholder="选择分组" allowClear>
              {groups.map((group) => (
                <Option key={group.id} value={group.id}>
                  {group.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="备注"
            name="notes"
          >
            <Input.TextArea
              rows={3}
              placeholder="请输入备注信息"
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                {editingContact ? '更新' : '添加'}
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加/编辑分组模态框 */}
      <Modal
        title={editingGroup ? '编辑分组' : '添加分组'}
        open={groupModalVisible}
        onCancel={() => {
          setGroupModalVisible(false);
          groupForm.resetFields();
        }}
        footer={null}
        width={400}
      >
        <Form
          form={groupForm}
          layout="vertical"
          onFinish={handleGroupSubmit}
        >
          <Form.Item
            label="分组名称"
            name="name"
            rules={[
              { required: true, message: '请输入分组名称' },
            ]}
          >
            <Input placeholder="请输入分组名称" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
              >
                {editingGroup ? '更新' : '添加'}
              </Button>
              <Button
                onClick={() => {
                  setGroupModalVisible(false);
                  groupForm.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default Contacts;
