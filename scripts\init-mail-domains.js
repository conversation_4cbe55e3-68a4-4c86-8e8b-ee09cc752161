const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

/**
 * 初始化邮件域名数据
 */

async function initMailDomains() {
  try {
    console.log('开始初始化邮件域名...');

    // 创建默认域名
    const defaultDomains = [
      'localhost',
      'blindedby.love', // 这个会被配置脚本替换
    ];

    for (const domainName of defaultDomains) {
      const existingDomain = await prisma.virtualDomain.findUnique({
        where: { name: domainName }
      });

      if (!existingDomain) {
        await prisma.virtualDomain.create({
          data: {
            name: domainName,
            active: 1
          }
        });
        console.log(`创建域名: ${domainName}`);
      } else {
        console.log(`域名已存在: ${domainName}`);
      }
    }

    // 为现有用户设置域名关联
    const users = await prisma.user.findMany({
      where: {
        email: { not: null },
        domainId: null
      }
    });

    for (const user of users) {
      if (user.email) {
        const emailDomain = user.email.split('@')[1];
        const domain = await prisma.virtualDomain.findUnique({
          where: { name: emailDomain }
        });

        if (domain) {
          await prisma.user.update({
            where: { id: user.id },
            data: { domainId: domain.id }
          });
          console.log(`用户 ${user.email} 关联到域名 ${emailDomain}`);
        } else {
          // 创建新域名
          const newDomain = await prisma.virtualDomain.create({
            data: {
              name: emailDomain,
              active: 1
            }
          });
          
          await prisma.user.update({
            where: { id: user.id },
            data: { domainId: newDomain.id }
          });
          console.log(`创建新域名 ${emailDomain} 并关联用户 ${user.email}`);
        }
      }
    }

    // 创建一些默认别名
    const defaultAliases = [
      { source: '<EMAIL>', destination: '<EMAIL>' },
      { source: '<EMAIL>', destination: '<EMAIL>' },
      { source: '<EMAIL>', destination: '<EMAIL>' },
    ];

    const mainDomain = await prisma.virtualDomain.findUnique({
      where: { name: 'blindedby.love' }
    });

    if (mainDomain) {
      for (const alias of defaultAliases) {
        const existingAlias = await prisma.virtualAlias.findFirst({
          where: {
            domainId: mainDomain.id,
            source: alias.source
          }
        });

        if (!existingAlias) {
          await prisma.virtualAlias.create({
            data: {
              domainId: mainDomain.id,
              source: alias.source,
              destination: alias.destination,
              active: 1
            }
          });
          console.log(`创建别名: ${alias.source} -> ${alias.destination}`);
        }
      }
    }

    console.log('邮件域名初始化完成！');
  } catch (error) {
    console.error('初始化邮件域名失败:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initMailDomains()
    .then(() => {
      console.log('脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = initMailDomains;
