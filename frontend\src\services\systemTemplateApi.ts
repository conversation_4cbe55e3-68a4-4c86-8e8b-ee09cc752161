import api from '../config/api';
import type { ApiResponse } from '../types';

export interface SystemTemplate {
  id: string;
  name: string;
  type: 'welcome' | 'notification' | 'bounce' | 'custom';
  subject: string;
  content: string;
  htmlContent?: string;
  description?: string;
  isActive: boolean;
  updatedAt: string;
}

export interface SystemTemplateFormData {
  name: string;
  type: 'welcome' | 'notification' | 'bounce' | 'custom';
  subject: string;
  content: string;
  htmlContent?: string;
  description?: string;
  isActive?: boolean;
}

// 获取系统模板列表
export const getSystemTemplates = async (): Promise<SystemTemplate[]> => {
  const response = await api.get<ApiResponse<SystemTemplate[]>>('/system-templates');
  return response.data.data!;
};

// 更新系统模板
export const updateSystemTemplate = async (id: string, data: SystemTemplateFormData): Promise<SystemTemplate> => {
  const response = await api.put<ApiResponse<SystemTemplate>>(`/system-templates/${id}`, data);
  return response.data.data!;
};
