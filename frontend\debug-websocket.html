<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket 调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .log {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        .button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>📡 WebSocket 调试工具</h1>
    
    <div id="status" class="status disconnected">
        状态: 未连接
    </div>
    
    <div>
        <button class="button" onclick="connect()">连接 WebSocket</button>
        <button class="button" onclick="disconnect()">断开连接</button>
        <button class="button" onclick="joinRoom()">加入房间</button>
        <button class="button" onclick="clearLog()">清空日志</button>
    </div>
    
    <div>
        <label>用户ID: </label>
        <input type="number" id="userId" value="1" placeholder="输入用户ID">
    </div>
    
    <h3>📋 连接日志</h3>
    <div id="log" class="log"></div>
    
    <h3>📧 收到的邮件通知</h3>
    <div id="emailLog" class="log"></div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        let socket = null;
        const logDiv = document.getElementById('log');
        const emailLogDiv = document.getElementById('emailLog');
        const statusDiv = document.getElementById('status');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'black';
            logEntry.textContent = `[${timestamp}] ${message}`;
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function emailLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = 'blue';
            logEntry.textContent = `[${timestamp}] ${message}`;
            emailLogDiv.appendChild(logEntry);
            emailLogDiv.scrollTop = emailLogDiv.scrollHeight;
        }
        
        function updateStatus(connected) {
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.textContent = '状态: 已连接 ✅';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.textContent = '状态: 未连接 ❌';
            }
        }
        
        function connect() {
            if (socket) {
                log('WebSocket 已经连接', 'error');
                return;
            }
            
            log('正在连接 WebSocket...');
            
            socket = io('http://localhost:3001', {
                transports: ['websocket', 'polling']
            });
            
            socket.on('connect', () => {
                log('✅ WebSocket 连接成功', 'success');
                log(`Socket ID: ${socket.id}`, 'success');
                updateStatus(true);
            });
            
            socket.on('disconnect', (reason) => {
                log(`❌ WebSocket 断开连接: ${reason}`, 'error');
                updateStatus(false);
            });
            
            socket.on('connect_error', (error) => {
                log(`❌ 连接错误: ${error.message}`, 'error');
                updateStatus(false);
            });
            
            // 监听新邮件事件
            socket.on('newEmails', (data) => {
                emailLog(`🎉 收到新邮件通知!`);
                emailLog(`邮件数量: ${data.count}`);
                emailLog(`邮件详情: ${JSON.stringify(data.emails, null, 2)}`);
                
                // 在页面标题显示通知
                document.title = `📧 ${data.count} 封新邮件 - WebSocket 调试`;
                
                // 3秒后恢复标题
                setTimeout(() => {
                    document.title = 'WebSocket 调试工具';
                }, 3000);
            });
            
            // 监听其他可能的事件
            socket.onAny((eventName, ...args) => {
                if (eventName !== 'newEmails') {
                    log(`📨 收到事件: ${eventName}, 数据: ${JSON.stringify(args)}`);
                }
            });
        }
        
        function disconnect() {
            if (socket) {
                socket.disconnect();
                socket = null;
                log('WebSocket 已断开连接');
                updateStatus(false);
            } else {
                log('WebSocket 未连接', 'error');
            }
        }
        
        function joinRoom() {
            if (!socket) {
                log('请先连接 WebSocket', 'error');
                return;
            }
            
            const userId = document.getElementById('userId').value;
            if (!userId) {
                log('请输入用户ID', 'error');
                return;
            }
            
            log(`正在加入房间: user_${userId}`);
            socket.emit('join', userId);
            log(`✅ 已发送加入房间请求`, 'success');
        }
        
        function clearLog() {
            logDiv.innerHTML = '';
            emailLogDiv.innerHTML = '';
        }
        
        // 页面加载时自动连接
        window.onload = function() {
            log('页面加载完成，可以开始调试 WebSocket 连接');
            log('步骤：1. 点击"连接 WebSocket" 2. 输入用户ID 3. 点击"加入房间"');
        };
    </script>
</body>
</html>
