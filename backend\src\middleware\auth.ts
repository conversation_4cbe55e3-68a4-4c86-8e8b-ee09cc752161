import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AuthenticatedRequest, AppError, JWTPayload, AsyncRequestHandler, User } from '../types';
import prisma from '../config/database';
import config from '../config/env';

// JWT认证中间件 - Express 5.x 兼容
export const authenticate: AsyncRequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      throw new AppError('未提供访问令牌', 401);
    }

    const token = authHeader.substring(7); // 移除 "Bearer " 前缀

    // 验证JWT
    const decoded = jwt.verify(token, config.JWT_SECRET) as JWTPayload;

    // 查找用户
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        avatarUrl: true,
        isActive: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!user) {
      throw new AppError('用户不存在', 401);
    }

    if (!user.isActive) {
      throw new AppError('用户账户已被禁用', 401);
    }

    // 将用户信息添加到请求对象 - Express 5.x 兼容
    (req as AuthenticatedRequest).user = user as any;
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError) {
      next(new AppError('无效的访问令牌', 401));
    } else if (error instanceof jwt.TokenExpiredError) {
      next(new AppError('访问令牌已过期', 401));
    } else {
      next(error);
    }
  }
};

// 可选认证中间件（不强制要求登录）
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next();
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, config.JWT_SECRET) as JWTPayload;

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        avatarUrl: true,
        isActive: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (user && user.isActive) {
      req.user = user as User;
    }

    next();
  } catch (error) {
    // 可选认证失败时不抛出错误，继续执行
    next();
  }
};

// 管理员权限检查中间件 - Express 5.x 兼容
export const requireAdmin: AsyncRequestHandler = async (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const authReq = req as AuthenticatedRequest;
  if (!authReq.user) {
    return next(new AppError('需要登录', 401));
  }

  try {
    // 查询用户完整信息，包括角色
    const user = await prisma.user.findUnique({
      where: { id: authReq.user.id },
      select: {
        id: true,
        email: true,
        role: true,
        isActive: true
      }
    });

    if (!user) {
      return next(new AppError('用户不存在', 401));
    }

    if (!user.isActive) {
      return next(new AppError('用户账户已被禁用', 401));
    }

    // 检查是否为管理员
    if (user.role !== 'admin') {
      return next(new AppError('需要管理员权限', 403));
    }

    // 更新请求中的用户信息
    authReq.user = { ...authReq.user, role: user.role };
    next();
  } catch (error) {
    return next(new AppError('权限验证失败', 500));
  }
};
