# 密码生成机制说明

## 概述

本系统使用 SHA512-CRYPT 格式生成邮件密码，确保与 Dovecot 邮件服务器完全兼容。系统已移除所有降级方案，始终使用服务器端的`doveadm`命令生成密码。

## 安全特性

### 1. 输入验证

- 密码长度限制：1-128 字符
- 禁止危险字符：`'"`;\|&<>$(){}[]\\`
- 禁止换行符和回车符
- 防止命令注入攻击

### 2. 安全转义

- 使用单引号包围密码
- 正确转义内部单引号
- 防止 shell 命令注入

### 3. 环境适配

- **开发环境**：通过 SSH 连接到邮件服务器执行`doveadm`命令
- **生产环境**：直接在本地执行`doveadm`命令（邮件服务器与应用在同一台机器）

## 环境配置

### 开发环境

```bash
NODE_ENV=development
# SSH连接将自动使用
```

### 生产环境

```bash
NODE_ENV=production
MAIL_SERVER_LOCAL=true
# 不要设置 SKIP_SSH_DOVEADM=true
```

## 使用方法

### 生成密码

```typescript
import { generateMailPassword } from "../utils/mailPasswordUtils";

try {
  const hashedPassword = generateMailPassword("userPassword123");
  console.log(hashedPassword); // $6$salt$hash...
} catch (error) {
  console.error("密码生成失败:", error.message);
}
```

### 验证密码

```typescript
import { verifyMailPassword } from "../utils/mailPasswordUtils";

const isValid = verifyMailPassword("userPassword123", "$6$salt$hash...");
console.log(isValid); // true/false
```

## 测试

### 模拟密码生成测试（开发环境）

```bash
npm run build:dev
npm run test:password
```

### 真实SSH连接测试

```bash
npm run build:dev
npm run test:password:ssh
```

测试将验证：

- 安全密码的正确生成
- 不安全密码的正确拒绝
- 生成密码的格式验证
- SSH连接和doveadm命令执行
- {SHA512-CRYPT}前缀的正确处理

## 错误处理

### 常见错误

1. **密码包含不安全字符**

   - 错误：`密码包含不安全字符或格式不正确`
   - 解决：检查密码是否包含特殊字符

2. **doveadm 命令执行失败**

   - 错误：`邮件密码生成失败: doveadm命令执行失败`
   - 解决：检查 Dovecot 是否正确安装和配置

3. **SSH 连接失败**（开发环境）
   - 错误：`SSH连接失败`
   - 解决：检查 SSH 密钥配置和网络连接

### 调试步骤

1. 检查环境变量配置
2. 验证 Dovecot 安装：`doveadm --version`
3. 测试密码生成：`npm run test:password`
4. 查看详细日志

## 安全注意事项

1. **永远不要**在日志中记录明文密码
2. **确保**生产环境正确设置`MAIL_SERVER_LOCAL=true`
3. **定期**更新 Dovecot 版本以获得安全补丁
4. **监控**密码生成失败率，及时发现问题

## 兼容性

- Dovecot 2.3.x+
- SHA512-CRYPT 格式
- 标准 Unix crypt(3)兼容
- Postfix SASL 兼容

## 性能考虑

- 密码生成时间：通常 < 100ms
- 验证时间：通常 < 50ms
- 超时设置：10 秒（生成）/ 5 秒（验证）
- 并发限制：由系统资源决定
