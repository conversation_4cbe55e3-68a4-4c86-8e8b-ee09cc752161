import api from '../config/api';
import type { User, ApiResponse } from '../types';

// 更新用户信息
export const updateUserProfile = async (data: {
  displayName?: string;
  avatarUrl?: string;
}): Promise<User> => {
  const response = await api.put<ApiResponse<User>>('/auth/profile', data);
  return response.data.data!;
};

// 上传头像
export const uploadAvatar = async (file: File): Promise<{ url: string }> => {
  const formData = new FormData();
  formData.append('avatar', file);
  
  const response = await api.post<ApiResponse<{ url: string }>>('/auth/upload-avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data.data!;
};
