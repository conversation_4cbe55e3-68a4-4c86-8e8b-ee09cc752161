import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import realTimeEmailService from '../services/realTimeEmailService';
import logger from '../utils/logger';

// 启动实时邮件服务
export const startRealTimeService = async (req: Request, res: Response) => {
  try {
    await realTimeEmailService.initialize();

    const response: ApiResponse = {
      success: true,
      message: '实时邮件服务已启动',
      data: realTimeEmailService.getStatus()
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动实时邮件服务失败: ${(error as Error).message}`, 500);
  }
};

// 停止实时邮件服务
export const stopRealTimeService = async (req: Request, res: Response) => {
  try {
    realTimeEmailService.stopAll();

    const response: ApiResponse = {
      success: true,
      message: '实时邮件服务已停止'
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止实时邮件服务失败: ${(error as Error).message}`, 500);
  }
};

// 获取实时邮件服务状态
export const getRealTimeServiceStatus = async (req: Request, res: Response) => {
  try {
    const status = realTimeEmailService.getStatus();

    const response: ApiResponse = {
      success: true,
      message: '获取服务状态成功',
      data: status
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取服务状态失败: ${(error as Error).message}`, 500);
  }
};

// 启动用户实时监听
export const startUserRealTimeSync = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    await realTimeEmailService.startUserPollingSync(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `用户 ${userEmail} 实时邮件监听已启动`
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动用户实时监听失败: ${(error as Error).message}`, 500);
  }
};

// 停止用户实时监听
export const stopUserRealTimeSync = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    realTimeEmailService.stopUserRealTimeSync(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `用户 ${userEmail} 实时邮件监听已停止`
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止用户实时监听失败: ${(error as Error).message}`, 500);
  }
};

// 手动触发邮件检查
export const triggerEmailCheck = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    const newEmails = await realTimeEmailService.triggerEmailCheck(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `邮件检查完成，发现 ${newEmails.length} 封新邮件`,
      data: {
        count: newEmails.length,
        emails: newEmails
      }
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`触发邮件检查失败: ${(error as Error).message}`, 500);
  }
};
