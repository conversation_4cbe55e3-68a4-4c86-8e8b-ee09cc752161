-- 添加子账户支持的数据库迁移脚本

-- 1. 为用户表添加子账户相关字段
ALTER TABLE users 
ADD COLUMN parent_user_id INT NULL COMMENT '父账户ID，NULL表示主账户',
ADD COLUMN account_type ENUM('main', 'sub') NOT NULL DEFAULT 'main' COMMENT '账户类型：主账户或子账户',
ADD COLUMN sub_account_permissions JSON NULL COMMENT '子账户权限配置',
ADD COLUMN max_sub_accounts INT NOT NULL DEFAULT 5 COMMENT '最大子账户数量',
ADD COLUMN sub_account_quota BIGINT NULL COMMENT '子账户配额（字节）',
ADD COLUMN is_sub_account_enabled BOOLEAN NOT NULL DEFAULT false COMMENT '是否启用子账户功能';

-- 2. 添加外键约束
ALTER TABLE users 
ADD CONSTRAINT fk_users_parent 
FOREIGN KEY (parent_user_id) REFERENCES users(id) ON DELETE CASCADE;

-- 3. 添加索引
CREATE INDEX idx_parent_user_id ON users(parent_user_id);
CREATE INDEX idx_account_type ON users(account_type);
CREATE INDEX idx_sub_account_enabled ON users(is_sub_account_enabled);

-- 4. 创建子账户权限表
CREATE TABLE sub_account_permissions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sub_user_id INT NOT NULL,
    permission_type ENUM('email_send', 'email_receive', 'email_delete', 'folder_manage', 'contact_manage', 'template_manage', 'rule_manage') NOT NULL,
    is_allowed BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sub_user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_sub_user_permission (sub_user_id, permission_type),
    INDEX idx_sub_user_id (sub_user_id),
    INDEX idx_permission_type (permission_type)
) COMMENT='子账户权限详细配置表';

-- 5. 创建子账户配额使用表
CREATE TABLE sub_account_usage (
    id INT AUTO_INCREMENT PRIMARY KEY,
    sub_user_id INT NOT NULL,
    usage_type ENUM('storage', 'emails_sent', 'emails_received', 'contacts', 'folders') NOT NULL,
    current_usage BIGINT NOT NULL DEFAULT 0,
    usage_limit BIGINT NULL COMMENT 'NULL表示无限制',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (sub_user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_sub_user_usage (sub_user_id, usage_type),
    INDEX idx_sub_user_id (sub_user_id),
    INDEX idx_usage_type (usage_type)
) COMMENT='子账户使用量统计表';

-- 6. 创建子账户活动日志表
CREATE TABLE sub_account_activities (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_user_id INT NOT NULL,
    sub_user_id INT NOT NULL,
    activity_type ENUM('login', 'logout', 'email_send', 'email_receive', 'permission_change', 'quota_change') NOT NULL,
    activity_description TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    metadata JSON NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sub_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_parent_user_id (parent_user_id),
    INDEX idx_sub_user_id (sub_user_id),
    INDEX idx_activity_type (activity_type),
    INDEX idx_created_at (created_at)
) COMMENT='子账户活动日志表';

-- 7. 为邮件表添加子账户相关字段
ALTER TABLE emails 
ADD COLUMN parent_user_id INT NULL COMMENT '如果是子账户的邮件，记录父账户ID',
ADD COLUMN is_shared_with_parent BOOLEAN NOT NULL DEFAULT false COMMENT '是否与父账户共享';

-- 8. 添加邮件表的外键和索引
ALTER TABLE emails 
ADD CONSTRAINT fk_emails_parent_user 
FOREIGN KEY (parent_user_id) REFERENCES users(id) ON DELETE SET NULL;

CREATE INDEX idx_emails_parent_user_id ON emails(parent_user_id);
CREATE INDEX idx_emails_shared_with_parent ON emails(is_shared_with_parent);

-- 9. 为文件夹表添加子账户支持
ALTER TABLE folders 
ADD COLUMN is_shared_with_parent BOOLEAN NOT NULL DEFAULT false COMMENT '是否与父账户共享',
ADD COLUMN shared_with_subs BOOLEAN NOT NULL DEFAULT false COMMENT '是否与子账户共享';

CREATE INDEX idx_folders_shared_with_parent ON folders(is_shared_with_parent);
CREATE INDEX idx_folders_shared_with_subs ON folders(shared_with_subs);

-- 10. 为联系人表添加子账户支持
ALTER TABLE contacts 
ADD COLUMN is_shared_with_parent BOOLEAN NOT NULL DEFAULT false COMMENT '是否与父账户共享',
ADD COLUMN shared_with_subs BOOLEAN NOT NULL DEFAULT false COMMENT '是否与子账户共享';

CREATE INDEX idx_contacts_shared_with_parent ON contacts(is_shared_with_parent);
CREATE INDEX idx_contacts_shared_with_subs ON contacts(shared_with_subs);

-- 11. 创建子账户邀请表
CREATE TABLE sub_account_invitations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    parent_user_id INT NOT NULL,
    email VARCHAR(191) NOT NULL,
    invitation_token VARCHAR(191) NOT NULL UNIQUE,
    permissions JSON NULL COMMENT '邀请时设置的权限',
    quota_limit BIGINT NULL COMMENT '邀请时设置的配额',
    expires_at TIMESTAMP NOT NULL,
    accepted_at TIMESTAMP NULL,
    sub_user_id INT NULL COMMENT '接受邀请后创建的子账户ID',
    status ENUM('pending', 'accepted', 'expired', 'cancelled') NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (sub_user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_parent_user_id (parent_user_id),
    INDEX idx_invitation_token (invitation_token),
    INDEX idx_email (email),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
) COMMENT='子账户邀请表';

-- 12. 插入默认权限配置
INSERT INTO sub_account_permissions (sub_user_id, permission_type, is_allowed) 
SELECT id, 'email_send', true FROM users WHERE account_type = 'sub'
ON DUPLICATE KEY UPDATE is_allowed = is_allowed;

INSERT INTO sub_account_permissions (sub_user_id, permission_type, is_allowed) 
SELECT id, 'email_receive', true FROM users WHERE account_type = 'sub'
ON DUPLICATE KEY UPDATE is_allowed = is_allowed;

-- 13. 创建视图：主账户及其子账户概览
CREATE OR REPLACE VIEW main_account_overview AS
SELECT 
    m.id as main_user_id,
    m.email as main_email,
    m.username as main_username,
    m.display_name as main_display_name,
    COUNT(s.id) as sub_account_count,
    m.max_sub_accounts,
    SUM(CASE WHEN s.is_active = 1 THEN 1 ELSE 0 END) as active_sub_accounts,
    SUM(su.current_usage) as total_storage_used
FROM users m
LEFT JOIN users s ON m.id = s.parent_user_id AND s.account_type = 'sub'
LEFT JOIN sub_account_usage su ON s.id = su.sub_user_id AND su.usage_type = 'storage'
WHERE m.account_type = 'main' AND m.is_sub_account_enabled = 1
GROUP BY m.id, m.email, m.username, m.display_name, m.max_sub_accounts;

-- 14. 创建视图：子账户详细信息
CREATE OR REPLACE VIEW sub_account_details AS
SELECT 
    s.id as sub_user_id,
    s.email as sub_email,
    s.username as sub_username,
    s.display_name as sub_display_name,
    s.is_active as sub_is_active,
    s.created_at as sub_created_at,
    m.id as parent_user_id,
    m.email as parent_email,
    m.username as parent_username,
    GROUP_CONCAT(DISTINCT sp.permission_type) as permissions,
    su_storage.current_usage as storage_used,
    su_storage.usage_limit as storage_limit,
    su_emails.current_usage as emails_sent_count
FROM users s
JOIN users m ON s.parent_user_id = m.id
LEFT JOIN sub_account_permissions sp ON s.id = sp.sub_user_id AND sp.is_allowed = 1
LEFT JOIN sub_account_usage su_storage ON s.id = su_storage.sub_user_id AND su_storage.usage_type = 'storage'
LEFT JOIN sub_account_usage su_emails ON s.id = su_emails.sub_user_id AND su_emails.usage_type = 'emails_sent'
WHERE s.account_type = 'sub'
GROUP BY s.id, s.email, s.username, s.display_name, s.is_active, s.created_at, 
         m.id, m.email, m.username, su_storage.current_usage, su_storage.usage_limit, su_emails.current_usage;
