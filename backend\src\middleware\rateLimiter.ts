import rateLimit from 'express-rate-limit';
import config from '../config/env';
// 通用限流配置
export const rateLimiter = rateLimit({
  windowMs: config.RATE_LIMIT_WINDOW * 60 * 1000, // 转换为毫秒
  max: config.RATE_LIMIT_MAX,
  message: {
    success: false,
    message: '请求过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
  // 生产环境下处理代理IP
  keyGenerator: (req) => {
    // 如果在代理后面，使用真实IP
    const forwarded = req.headers['x-forwarded-for'];
    if (forwarded && typeof forwarded === 'string') {
      return forwarded.split(',')[0].trim();
    }
    return req.ip || req.connection.remoteAddress || 'unknown';
  },
  // 跳过内部健康检查
  skip: (req) => {
    return req.path === '/health' || req.path === '/api/health';
  }
});

// 登录限流配置（更严格）
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 5, // 最多5次尝试
  message: {
    success: false,
    message: '登录尝试次数过多，请15分钟后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // 成功的请求不计入限制
});

// 邮件发送限流配置
export const emailSendLimiter = rateLimit({
  windowMs: 60 * 1000, // 1分钟
  max: 10, // 每分钟最多发送10封邮件
  message: {
    success: false,
    message: '邮件发送过于频繁，请稍后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// 注册限流配置
export const registerLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1小时
  max: 3, // 每小时最多注册3次
  message: {
    success: false,
    message: '注册尝试次数过多，请1小时后再试',
  },
  standardHeaders: true,
  legacyHeaders: false,
});
