import api from '../config/api';
import type { ApiResponse, PaginatedResponse } from '../types';

// 邮件规则条件类型
export interface EmailRuleCondition {
  field: 'from' | 'to' | 'subject' | 'content' | 'hasAttachment' | 'headers';
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex';
  value: string;
  caseSensitive?: boolean;
}

// 邮件规则动作类型
export interface EmailRuleAction {
  type: 'move' | 'addLabel' | 'markRead' | 'markStarred' | 'delete' | 'forward' | 'moveToFolder' | 'addFlag' | 'stopProcessing';
  value?: any;
  folder?: string;
  flag?: string;
  createIfNotExists?: boolean;
}

// 邮件规则接口
export interface EmailRule {
  id: number;
  userId: number;
  name: string;
  conditions: EmailRuleCondition[];
  actions: EmailRuleAction[];
  isActive: boolean;
  priority: number;
  isSystemRule?: boolean;
  templateId?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 邮件规则模板接口
export interface EmailRuleTemplate {
  id: string;
  name: string;
  description?: string;
  conditions: EmailRuleCondition[];
  actions: EmailRuleAction[];
  defaultPriority: number;
  isActive: boolean;
  category: 'system' | 'user' | 'admin';
  createdAt: string;
  updatedAt: string;
}

// 创建邮件规则参数
export interface CreateEmailRuleParams {
  name: string;
  conditions: EmailRuleCondition[];
  actions: EmailRuleAction[];
  isActive?: boolean;
  priority?: number;
}

// 更新邮件规则参数
export interface UpdateEmailRuleParams {
  name?: string;
  conditions?: EmailRuleCondition[];
  actions?: EmailRuleAction[];
  isActive?: boolean;
  priority?: number;
}

// 测试规则参数
export interface TestRuleParams {
  conditions: EmailRuleCondition[];
  emailId?: string;
}

// 测试规则结果
export interface TestRuleResult {
  matched: boolean;
  email?: any;
}

// 获取邮件规则列表
export const getEmailRules = async (params?: {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  isActive?: boolean;
}): Promise<PaginatedResponse<EmailRule>> => {
  const response = await api.get<ApiResponse<PaginatedResponse<EmailRule>>>('/email-rules', {
    params,
  });
  return response.data.data!;
};

// 获取邮件规则详情
export const getEmailRuleById = async (id: number): Promise<EmailRule> => {
  const response = await api.get<ApiResponse<EmailRule>>(`/email-rules/${id}`);
  return response.data.data!;
};

// 创建邮件规则
export const createEmailRule = async (params: CreateEmailRuleParams): Promise<EmailRule> => {
  const response = await api.post<ApiResponse<EmailRule>>('/email-rules', params);
  return response.data.data!;
};

// 更新邮件规则
export const updateEmailRule = async (id: number, params: UpdateEmailRuleParams): Promise<EmailRule> => {
  const response = await api.put<ApiResponse<EmailRule>>(`/email-rules/${id}`, params);
  return response.data.data!;
};

// 删除邮件规则
export const deleteEmailRule = async (id: number): Promise<void> => {
  await api.delete(`/email-rules/${id}`);
};

// 切换邮件规则状态
export const toggleEmailRuleStatus = async (id: number): Promise<EmailRule> => {
  const response = await api.patch<ApiResponse<EmailRule>>(`/email-rules/${id}/toggle`);
  return response.data.data!;
};

// 测试邮件规则
export const testEmailRule = async (params: TestRuleParams): Promise<TestRuleResult> => {
  const response = await api.post<ApiResponse<TestRuleResult>>('/email-rules/test', params);
  return response.data.data!;
};

// ===== 规则模板相关API =====

// 获取所有规则模板
export const getAllRuleTemplates = async (): Promise<EmailRuleTemplate[]> => {
  const response = await api.get<ApiResponse<EmailRuleTemplate[]>>('/email-rule-templates');
  return response.data.data!;
};

// 根据分类获取规则模板
export const getRuleTemplatesByCategory = async (category: string): Promise<EmailRuleTemplate[]> => {
  const response = await api.get<ApiResponse<EmailRuleTemplate[]>>(`/email-rule-templates/category/${category}`);
  return response.data.data!;
};

// 从模板创建规则
export const createRuleFromTemplate = async (params: {
  templateId: string;
  customizations?: {
    name?: string;
    priority?: number;
    isActive?: boolean;
    conditionOverrides?: Partial<EmailRuleCondition>[];
    actionOverrides?: Partial<EmailRuleAction>[];
  };
}): Promise<void> => {
  await api.post<ApiResponse<void>>('/email-rule-templates/create-rule', params);
};

// 为当前用户创建发件副本规则
export const createSentCopyRule = async (): Promise<void> => {
  await api.post<ApiResponse<void>>('/email-rule-templates/create-sent-copy-rule');
};

// 管理员：为所有用户创建系统规则
export const createSystemRulesForAllUsers = async (): Promise<void> => {
  await api.post<ApiResponse<void>>('/email-rule-templates/admin/create-system-rules');
};

// 管理员：初始化规则模板
export const initializeRuleTemplates = async (): Promise<void> => {
  await api.post<ApiResponse<void>>('/email-rule-templates/admin/initialize');
};

// 获取所有活跃规则（不分页）
export const getActiveEmailRules = async (): Promise<EmailRule[]> => {
  const response = await getEmailRules({ limit: 1000, isActive: true });
  return response.data;
};
