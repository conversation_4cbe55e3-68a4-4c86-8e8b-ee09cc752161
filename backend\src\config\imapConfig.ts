/**
 * IMAP连接配置
 * 用于优化IMAP连接管理，减少频繁连接断开
 */

export interface IMAPConnectionConfig {
  // 连接池配置
  maxConnections: number;
  connectionTimeout: number; // 连接超时时间（毫秒）
  idleTimeout: number; // 空闲超时时间（毫秒）
  
  // 重连配置
  maxRetries: number;
  retryDelay: number; // 重连延迟（毫秒）
  
  // 同步配置
  defaultSyncInterval: number; // 默认同步间隔（分钟）
  minSyncInterval: number; // 最小同步间隔（分钟）
  maxSyncInterval: number; // 最大同步间隔（分钟）
  
  // 性能配置
  keepAlive: boolean; // 保持连接活跃
  enableCompression: boolean; // 启用压缩
  fetchBatchSize: number; // 批量获取邮件数量
}

export const defaultIMAPConfig: IMAPConnectionConfig = {
  // 连接池配置
  maxConnections: 20, // 最大连接数
  connectionTimeout: 30 * 60 * 1000, // 30分钟连接超时
  idleTimeout: 10 * 60 * 1000, // 10分钟空闲超时
  
  // 重连配置
  maxRetries: 3,
  retryDelay: 5000, // 5秒重连延迟
  
  // 同步配置
  defaultSyncInterval: 10, // 默认10分钟同步一次
  minSyncInterval: 5, // 最小5分钟
  maxSyncInterval: 60, // 最大60分钟
  
  // 性能配置
  keepAlive: true,
  enableCompression: false, // 暂时禁用压缩避免兼容性问题
  fetchBatchSize: 50, // 每次获取50封邮件
};

/**
 * 获取用户特定的IMAP配置
 */
export const getUserIMAPConfig = (userEmail: string): IMAPConnectionConfig => {
  // 可以根据用户类型或邮箱域名返回不同配置
  const config = { ...defaultIMAPConfig };
  
  // 管理员用户可以有更频繁的同步
  if (userEmail.includes('admin') || userEmail.includes('postmaster')) {
    config.defaultSyncInterval = 5;
    config.minSyncInterval = 2;
  }
  
  return config;
};

/**
 * 验证同步间隔是否在合理范围内
 */
export const validateSyncInterval = (interval: number): number => {
  const config = defaultIMAPConfig;
  return Math.max(config.minSyncInterval, Math.min(interval, config.maxSyncInterval));
};

/**
 * 获取IMAP连接选项
 */
export const getIMAPConnectionOptions = (host: string, port: number, secure: boolean) => {
  return {
    host,
    port,
    tls: secure,
    tlsOptions: {
      rejectUnauthorized: false, // 允许自签名证书
      servername: host,
    },
    authTimeout: 10000, // 10秒认证超时
    connTimeout: 10000, // 10秒连接超时
    keepalive: {
      interval: 10000, // 10秒心跳间隔
      idleInterval: 300000, // 5分钟空闲心跳
      forceNoop: false,
    },
    // 启用调试模式（生产环境应关闭）
    debug: process.env.NODE_ENV === 'development' ? console.log : undefined,
  };
};
