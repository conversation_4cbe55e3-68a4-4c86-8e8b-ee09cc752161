import api from '../config/api';
import type { ApiResponse } from '../types';

export interface TrackingOptions {
  trackOpens?: boolean;
  trackClicks?: boolean;
  trackReplies?: boolean;
  requestReceipt?: boolean;
}

export interface TrackingEvent {
  id: number;
  eventType: string;
  ipAddress?: string;
  userAgent?: string;
  location?: string;
  clickedUrl?: string;
  metadata?: string;
  createdAt: string;
}

export interface TrackingStats {
  trackingId: string;
  isDelivered: boolean;
  deliveredAt?: string;
  isOpened: boolean;
  firstOpenedAt?: string;
  lastOpenedAt?: string;
  openCount: number;
  isReplied: boolean;
  repliedAt?: string;
  clickCount: number;
  uniqueClicks: number;
  events: TrackingEvent[];
}

export interface ReceiptStats {
  total: number;
  read: number;
  delivery: number;
  failure: number;
  pending: number;
  sent: number;
  received: number;
  failed: number;
  receipts: EmailReceipt[];
}

export interface EmailReceipt {
  id: number;
  emailId: string;
  recipientEmail: string;
  receiptType: string;
  status: string;
  subject?: string;
  requestedAt: string;
  sentAt?: string;
  receivedAt?: string;
}

export interface TrackingOverview {
  totalTracked: number;
  totalOpened: number;
  totalClicked: number;
  totalReplied: number;
  totalDelivered: number;
  openRate: string;
  clickRate: string;
  replyRate: string;
  recentActivity: TrackingEvent[];
}

// 获取邮件跟踪统计
export const getTrackingStats = async (emailId: string): Promise<{
  tracking: TrackingStats | null;
  receipts: ReceiptStats;
}> => {
  const response = await api.get<ApiResponse<{
    tracking: TrackingStats | null;
    receipts: ReceiptStats;
  }>>(`/tracking/stats/${emailId}`);
  return response.data.data!;
};

// 创建邮件跟踪
export const createTracking = async (emailId: string, options: TrackingOptions): Promise<any> => {
  const response = await api.post<ApiResponse<any>>(`/tracking/create/${emailId}`, options);
  return response.data.data!;
};

// 发送已读回执请求
export const sendReceiptRequest = async (
  emailId: string,
  recipientEmail: string,
  originalSubject: string,
  originalMessageId: string
): Promise<any> => {
  const response = await api.post<ApiResponse<any>>(`/tracking/receipt/${emailId}`, {
    recipientEmail,
    originalSubject,
    originalMessageId,
  });
  return response.data.data!;
};

// 获取跟踪概览
export const getTrackingOverview = async (days: number = 30): Promise<TrackingOverview> => {
  const response = await api.get<ApiResponse<TrackingOverview>>('/tracking/overview', {
    params: { days },
  });
  return response.data.data!;
};

// 事件类型映射
export const EVENT_TYPE_LABELS: Record<string, string> = {
  'open': '邮件打开',
  'click': '链接点击',
  'reply': '邮件回复',
  'delivery': '投递成功',
  'bounce': '投递失败',
};

// 获取事件类型的友好显示名称
export const getEventTypeLabel = (eventType: string): string => {
  return EVENT_TYPE_LABELS[eventType] || eventType;
};

// 回执状态映射
export const RECEIPT_STATUS_LABELS: Record<string, string> = {
  'pending': '等待中',
  'sent': '已发送',
  'received': '已确认',
  'failed': '发送失败',
};

// 获取回执状态的友好显示名称
export const getReceiptStatusLabel = (status: string): string => {
  return RECEIPT_STATUS_LABELS[status] || status;
};

// 回执类型映射
export const RECEIPT_TYPE_LABELS: Record<string, string> = {
  'read': '已读回执',
  'delivery': '投递回执',
  'failure': '失败回执',
};

// 获取回执类型的友好显示名称
export const getReceiptTypeLabel = (type: string): string => {
  return RECEIPT_TYPE_LABELS[type] || type;
};

// 格式化跟踪统计数据
export const formatTrackingStats = (stats: TrackingStats | null) => {
  if (!stats) {
    return {
      status: '未跟踪',
      openRate: '0%',
      clickRate: '0%',
      summary: '此邮件未启用跟踪功能',
    };
  }

  const openRate = stats.openCount > 0 ? '100%' : '0%';
  const clickRate = stats.clickCount > 0 && stats.openCount > 0 
    ? `${Math.round((stats.clickCount / stats.openCount) * 100)}%` 
    : '0%';

  let status = '已发送';
  if (stats.isReplied) {
    status = '已回复';
  } else if (stats.isOpened) {
    status = '已打开';
  } else if (stats.isDelivered) {
    status = '已投递';
  }

  const summary = [
    stats.isDelivered ? '已投递' : '投递中',
    stats.isOpened ? `已打开 ${stats.openCount} 次` : '未打开',
    stats.clickCount > 0 ? `点击 ${stats.clickCount} 次` : '无点击',
    stats.isReplied ? '已回复' : '未回复',
  ].join(' • ');

  return {
    status,
    openRate,
    clickRate,
    summary,
  };
};

// 生成跟踪报告
export const generateTrackingReport = (stats: TrackingStats | null, receipts: ReceiptStats) => {
  const report = {
    delivery: {
      status: stats?.isDelivered ? '成功' : '未知',
      time: stats?.deliveredAt ? new Date(stats.deliveredAt).toLocaleString() : '未知',
    },
    opens: {
      count: stats?.openCount || 0,
      firstTime: stats?.firstOpenedAt ? new Date(stats.firstOpenedAt).toLocaleString() : '未打开',
      lastTime: stats?.lastOpenedAt ? new Date(stats.lastOpenedAt).toLocaleString() : '未打开',
    },
    clicks: {
      total: stats?.clickCount || 0,
      unique: stats?.uniqueClicks || 0,
      urls: stats?.events
        .filter(e => e.eventType === 'click')
        .map(e => e.clickedUrl)
        .filter(Boolean) || [],
    },
    replies: {
      status: stats?.isReplied ? '已回复' : '未回复',
      time: stats?.repliedAt ? new Date(stats.repliedAt).toLocaleString() : '未回复',
    },
    receipts: {
      read: receipts.read,
      delivery: receipts.delivery,
      failure: receipts.failure,
      pending: receipts.pending,
    },
  };

  return report;
};
