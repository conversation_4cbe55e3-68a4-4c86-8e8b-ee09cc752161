import { Response } from 'express';
import prisma from '../config/database';
import logger from '../utils/logger';

// 邮件规则条件类型
export interface EmailRuleCondition {
  field: 'from' | 'to' | 'subject' | 'content' | 'hasAttachment' | 'headers' | 'senderMatchesUser';
  operator: 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex';
  value: string;
  caseSensitive?: boolean;
}

// 邮件规则动作类型
export interface EmailRuleAction {
  type: 'move' | 'addLabel' | 'markRead' | 'markStarred' | 'delete' | 'forward' | 'moveToFolder' | 'addFlag' | 'stopProcessing';
  value?: any; // 根据动作类型，可能是文件夹ID、标签ID、邮箱地址等
  folder?: string; // 文件夹名称
  flag?: string; // 邮件标志
  createIfNotExists?: boolean; // 是否创建不存在的文件夹
}

// 邮件规则接口
export interface EmailRule {
  id: number;
  userId: number;
  name: string;
  conditions: EmailRuleCondition[];
  actions: EmailRuleAction[];
  isActive: boolean;
  priority: number;
  isSystemRule?: boolean;
  templateId?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

// 邮件规则模板接口
export interface EmailRuleTemplate {
  id: string;
  name: string;
  description?: string;
  conditions: EmailRuleCondition[];
  actions: EmailRuleAction[];
  defaultPriority: number;
  isActive: boolean;
  category: 'system' | 'user' | 'admin';
  createdAt: string;
  updatedAt: string;
}

// 获取邮件规则列表
export const getEmailRules = async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = 1,
    limit = 20,
    sortBy = 'priority',
    sortOrder = 'asc',
    isActive
  }: PaginationParams & { isActive?: string } = req.query as any;

  // 确保 page 和 limit 是数字
  const pageNum = parseInt(String(page)) || 1;
  const limitNum = parseInt(String(limit)) || 20;
  const skip = (pageNum - 1) * limitNum;
  const userId = req.user!.id;

  const where: any = { userId };
  if (isActive !== undefined) {
    where.isActive = isActive === 'true';
  }

  const [rules, total] = await Promise.all([
    prisma.emailRule.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
    }),
    prisma.emailRule.count({ where }),
  ]);

  // 解析JSON字段
  const parsedRules = rules.map(rule => ({
    ...rule,
    conditions: JSON.parse(rule.conditions),
    actions: JSON.parse(rule.actions),
  }));

  const response: ApiResponse = {
    success: true,
    message: '获取邮件规则列表成功',
    data: {
      rules: parsedRules,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取邮件规则详情
export const getEmailRuleById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const rule = await prisma.emailRule.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!rule) {
    throw new AppError('邮件规则不存在', 404);
  }

  const parsedRule = {
    ...rule,
    conditions: JSON.parse(rule.conditions),
    actions: JSON.parse(rule.actions),
  };

  const response: ApiResponse = {
    success: true,
    message: '获取邮件规则详情成功',
    data: parsedRule,
  };

  res.json(response);
};

// 创建邮件规则
export const createEmailRule = async (req: AuthenticatedRequest, res: Response) => {
  const { name, conditions, actions, isActive = true, priority = 0 } = req.body;
  const userId = req.user!.id;

  // 验证条件和动作
  if (!Array.isArray(conditions) || conditions.length === 0) {
    throw new AppError('规则条件不能为空', 400);
  }

  if (!Array.isArray(actions) || actions.length === 0) {
    throw new AppError('规则动作不能为空', 400);
  }

  // 检查规则名称是否已存在
  const existingRule = await prisma.emailRule.findFirst({
    where: {
      userId,
      name,
    },
  });

  if (existingRule) {
    throw new AppError('规则名称已存在', 409);
  }

  const rule = await prisma.emailRule.create({
    data: {
      userId,
      name,
      conditions: JSON.stringify(conditions),
      actions: JSON.stringify(actions),
      isActive,
      priority,
    },
  });

  const parsedRule = {
    ...rule,
    conditions: JSON.parse(rule.conditions),
    actions: JSON.parse(rule.actions),
  };

  const response: ApiResponse = {
    success: true,
    message: '邮件规则创建成功',
    data: parsedRule,
  };

  res.status(201).json(response);
};

// 更新邮件规则
export const updateEmailRule = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { name, conditions, actions, isActive, priority } = req.body;
  const userId = req.user!.id;

  // 检查规则是否存在
  const existingRule = await prisma.emailRule.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingRule) {
    throw new AppError('邮件规则不存在', 404);
  }

  // 如果更新名称，检查是否与其他规则重名
  if (name && name !== existingRule.name) {
    const duplicateRule = await prisma.emailRule.findFirst({
      where: {
        userId,
        name,
        id: { not: parseInt(id) },
      },
    });

    if (duplicateRule) {
      throw new AppError('规则名称已存在', 409);
    }
  }

  const updateData: any = {};
  if (name !== undefined) updateData.name = name;
  if (conditions !== undefined) {
    if (!Array.isArray(conditions) || conditions.length === 0) {
      throw new AppError('规则条件不能为空', 400);
    }
    updateData.conditions = JSON.stringify(conditions);
  }
  if (actions !== undefined) {
    if (!Array.isArray(actions) || actions.length === 0) {
      throw new AppError('规则动作不能为空', 400);
    }
    updateData.actions = JSON.stringify(actions);
  }
  if (isActive !== undefined) updateData.isActive = isActive;
  if (priority !== undefined) updateData.priority = priority;

  const rule = await prisma.emailRule.update({
    where: { id: parseInt(id) },
    data: updateData,
  });

  const parsedRule = {
    ...rule,
    conditions: JSON.parse(rule.conditions),
    actions: JSON.parse(rule.actions),
  };

  const response: ApiResponse = {
    success: true,
    message: '邮件规则更新成功',
    data: parsedRule,
  };

  res.json(response);
};

// 删除邮件规则
export const deleteEmailRule = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  // 检查规则是否存在
  const existingRule = await prisma.emailRule.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingRule) {
    throw new AppError('邮件规则不存在', 404);
  }

  await prisma.emailRule.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '邮件规则删除成功',
  };

  res.json(response);
};

// 切换规则状态
export const toggleEmailRuleStatus = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const rule = await prisma.emailRule.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!rule) {
    throw new AppError('邮件规则不存在', 404);
  }

  const updatedRule = await prisma.emailRule.update({
    where: { id: parseInt(id) },
    data: { isActive: !rule.isActive },
  });

  const parsedRule = {
    ...updatedRule,
    conditions: JSON.parse(updatedRule.conditions),
    actions: JSON.parse(updatedRule.actions),
  };

  const response: ApiResponse = {
    success: true,
    message: `邮件规则已${updatedRule.isActive ? '启用' : '禁用'}`,
    data: parsedRule,
  };

  res.json(response);
};

// 手动应用邮件规则到现有邮件
export const applyRulesToExistingEmails = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { folderId, emailIds } = req.body;

  try {
    let emails: any[];

    if (emailIds && Array.isArray(emailIds)) {
      // 处理指定的邮件
      emails = await prisma.email.findMany({
        where: {
          id: { in: emailIds },
          userId,
          isDeleted: false,
        },
      });
    } else if (folderId) {
      // 处理指定文件夹中的邮件
      emails = await prisma.email.findMany({
        where: {
          userId,
          folderId: parseInt(folderId),
          isDeleted: false,
        },
        take: 100, // 限制处理数量，避免超时
      });
    } else {
      // 处理INBOX中的邮件
      const inboxFolder = await prisma.folder.findFirst({
        where: {
          userId,
          type: 'inbox',
        },
      });

      if (!inboxFolder) {
        throw new AppError('未找到收件箱文件夹', 404);
      }

      emails = await prisma.email.findMany({
        where: {
          userId,
          folderId: inboxFolder.id,
          isDeleted: false,
        },
        take: 100,
      });
    }

    if (emails.length === 0) {
      const response: ApiResponse = {
        success: true,
        message: '没有找到需要处理的邮件',
        data: { processedCount: 0 },
      };
      return res.json(response);
    }

    // 应用规则到每封邮件
    let processedCount = 0;
    const { applyRulesToEmail } = await import('../services/emailRuleService');

    for (const email of emails) {
      try {
        await applyRulesToEmail(email as any);
        processedCount++;
      } catch (error) {
        logger.error(`为邮件 ${email.id} 应用规则失败:`, error);
        // 继续处理其他邮件
      }
    }

    const response: ApiResponse = {
      success: true,
      message: `成功处理了 ${processedCount} 封邮件`,
      data: {
        totalEmails: emails.length,
        processedCount,
        skippedCount: emails.length - processedCount
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('应用邮件规则失败:', error);
    throw error;
  }
};
