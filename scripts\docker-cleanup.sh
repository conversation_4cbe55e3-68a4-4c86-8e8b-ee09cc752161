#!/bin/bash

# Docker 清理脚本
# 用于清理 Docker 镜像、容器、卷和网络

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Docker 清理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help        显示帮助信息"
    echo "  -a, --all         清理所有内容（危险操作）"
    echo "  -c, --containers  清理停止的容器"
    echo "  -i, --images      清理未使用的镜像"
    echo "  -v, --volumes     清理未使用的卷"
    echo "  -n, --networks    清理未使用的网络"
    echo "  -s, --system      执行系统清理"
    echo "  --project         只清理项目相关的资源"
    echo ""
    echo "示例:"
    echo "  $0 --project      # 只清理项目相关资源"
    echo "  $0 -c -i          # 清理容器和镜像"
    echo "  $0 --system       # 执行系统清理"
}

# 确认操作
confirm_action() {
    local message=$1
    echo -e "${YELLOW}$message${NC}"
    read -p "确认继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 清理项目相关资源
cleanup_project() {
    log_info "清理项目相关资源..."
    
    # 停止项目容器
    log_info "停止项目容器..."
    docker-compose -f docker/docker-compose.yml down 2>/dev/null || true
    docker-compose -f docker/docker-compose.prod.yml down 2>/dev/null || true
    
    # 删除项目镜像
    log_info "删除项目镜像..."
    docker images | grep "email-system" | awk '{print $3}' | xargs -r docker rmi -f 2>/dev/null || true
    
    # 删除项目卷（谨慎操作）
    log_warning "发现项目数据卷，是否删除？（这将删除所有数据）"
    docker volume ls | grep "email" | awk '{print $2}' | while read volume; do
        echo "  - $volume"
    done
    
    read -p "删除项目数据卷? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume ls | grep "email" | awk '{print $2}' | xargs -r docker volume rm 2>/dev/null || true
        log_success "项目数据卷已删除"
    else
        log_info "保留项目数据卷"
    fi
    
    log_success "项目资源清理完成"
}

# 清理停止的容器
cleanup_containers() {
    log_info "清理停止的容器..."
    
    local stopped_containers=$(docker ps -a -q -f status=exited)
    if [ -n "$stopped_containers" ]; then
        docker rm $stopped_containers
        log_success "已清理停止的容器"
    else
        log_info "没有停止的容器需要清理"
    fi
}

# 清理未使用的镜像
cleanup_images() {
    log_info "清理未使用的镜像..."
    
    # 清理悬空镜像
    local dangling_images=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling_images" ]; then
        docker rmi $dangling_images
        log_success "已清理悬空镜像"
    else
        log_info "没有悬空镜像需要清理"
    fi
    
    # 清理未使用的镜像
    docker image prune -f
    log_success "已清理未使用的镜像"
}

# 清理未使用的卷
cleanup_volumes() {
    log_info "清理未使用的卷..."
    
    local unused_volumes=$(docker volume ls -qf dangling=true)
    if [ -n "$unused_volumes" ]; then
        confirm_action "这将删除所有未使用的数据卷，可能包含重要数据！"
        docker volume rm $unused_volumes
        log_success "已清理未使用的卷"
    else
        log_info "没有未使用的卷需要清理"
    fi
}

# 清理未使用的网络
cleanup_networks() {
    log_info "清理未使用的网络..."
    
    docker network prune -f
    log_success "已清理未使用的网络"
}

# 执行系统清理
cleanup_system() {
    log_info "执行 Docker 系统清理..."
    
    confirm_action "这将清理所有未使用的 Docker 资源！"
    
    docker system prune -a -f --volumes
    log_success "系统清理完成"
}

# 清理所有内容
cleanup_all() {
    log_error "危险操作：这将清理所有 Docker 资源！"
    confirm_action "确认要清理所有 Docker 资源吗？这将删除所有容器、镜像、卷和网络！"
    
    # 停止所有容器
    log_info "停止所有容器..."
    docker stop $(docker ps -aq) 2>/dev/null || true
    
    # 删除所有容器
    log_info "删除所有容器..."
    docker rm $(docker ps -aq) 2>/dev/null || true
    
    # 删除所有镜像
    log_info "删除所有镜像..."
    docker rmi $(docker images -q) -f 2>/dev/null || true
    
    # 删除所有卷
    log_info "删除所有卷..."
    docker volume rm $(docker volume ls -q) 2>/dev/null || true
    
    # 删除所有网络
    log_info "删除所有自定义网络..."
    docker network rm $(docker network ls -q) 2>/dev/null || true
    
    log_success "所有 Docker 资源已清理"
}

# 显示清理前状态
show_before_status() {
    log_info "清理前状态:"
    echo "容器数量: $(docker ps -a | wc -l)"
    echo "镜像数量: $(docker images | wc -l)"
    echo "卷数量: $(docker volume ls | wc -l)"
    echo "网络数量: $(docker network ls | wc -l)"
    echo ""
}

# 显示清理后状态
show_after_status() {
    log_info "清理后状态:"
    echo "容器数量: $(docker ps -a | wc -l)"
    echo "镜像数量: $(docker images | wc -l)"
    echo "卷数量: $(docker volume ls | wc -l)"
    echo "网络数量: $(docker network ls | wc -l)"
    echo ""
    
    # 显示磁盘使用情况
    log_info "Docker 磁盘使用情况:"
    docker system df
}

# 主函数
main() {
    local cleanup_containers=false
    local cleanup_images=false
    local cleanup_volumes=false
    local cleanup_networks=false
    local cleanup_system=false
    local cleanup_all=false
    local cleanup_project=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -a|--all)
                cleanup_all=true
                shift
                ;;
            -c|--containers)
                cleanup_containers=true
                shift
                ;;
            -i|--images)
                cleanup_images=true
                shift
                ;;
            -v|--volumes)
                cleanup_volumes=true
                shift
                ;;
            -n|--networks)
                cleanup_networks=true
                shift
                ;;
            -s|--system)
                cleanup_system=true
                shift
                ;;
            --project)
                cleanup_project=true
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 如果没有指定任何选项，显示帮助
    if [ "$cleanup_all" = false ] && [ "$cleanup_containers" = false ] && \
       [ "$cleanup_images" = false ] && [ "$cleanup_volumes" = false ] && \
       [ "$cleanup_networks" = false ] && [ "$cleanup_system" = false ] && \
       [ "$cleanup_project" = false ]; then
        show_help
        exit 0
    fi
    
    # 检查 Docker 是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    
    # 显示清理前状态
    show_before_status
    
    # 执行清理操作
    if [ "$cleanup_all" = true ]; then
        cleanup_all
    elif [ "$cleanup_project" = true ]; then
        cleanup_project
    elif [ "$cleanup_system" = true ]; then
        cleanup_system
    else
        [ "$cleanup_containers" = true ] && cleanup_containers
        [ "$cleanup_images" = true ] && cleanup_images
        [ "$cleanup_volumes" = true ] && cleanup_volumes
        [ "$cleanup_networks" = true ] && cleanup_networks
    fi
    
    # 显示清理后状态
    show_after_status
    
    log_success "清理操作完成！"
}

# 脚本入口
main "$@"
