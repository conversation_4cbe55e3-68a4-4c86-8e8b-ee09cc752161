import api from '../config/api';
import type { Contact, ContactGroup, PaginatedResponse, ApiResponse } from '../types';

// 联系人相关API

// 获取联系人列表
export const getContacts = async (params?: {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  groupId?: number;
  search?: string;
}): Promise<PaginatedResponse<Contact>> => {
  const response = await api.get<ApiResponse<PaginatedResponse<Contact>>>('/contacts', {
    params,
  });
  return response.data.data!;
};

// 获取联系人详情
export const getContactById = async (id: number): Promise<Contact> => {
  const response = await api.get<ApiResponse<Contact>>(`/contacts/${id}`);
  return response.data.data!;
};

// 创建联系人
export const createContact = async (data: {
  email: string;
  name?: string;
  phone?: string;
  company?: string;
  notes?: string;
  groupId?: number;
}): Promise<Contact> => {
  const response = await api.post<ApiResponse<Contact>>('/contacts', data);
  return response.data.data!;
};

// 更新联系人
export const updateContact = async (id: number, data: {
  email?: string;
  name?: string;
  phone?: string;
  company?: string;
  notes?: string;
  groupId?: number;
}): Promise<Contact> => {
  const response = await api.put<ApiResponse<Contact>>(`/contacts/${id}`, data);
  return response.data.data!;
};

// 删除联系人
export const deleteContact = async (id: number): Promise<void> => {
  await api.delete(`/contacts/${id}`);
};

// 批量删除联系人
export const batchDeleteContacts = async (ids: number[]): Promise<void> => {
  await api.post('/contacts/batch-delete', { ids });
};

// 联系人分组相关API

// 获取联系人分组列表
export const getContactGroups = async (): Promise<ContactGroup[]> => {
  const response = await api.get<ApiResponse<ContactGroup[]>>('/contacts/groups');
  return response.data.data!;
};

// 创建联系人分组
export const createContactGroup = async (data: {
  name: string;
}): Promise<ContactGroup> => {
  const response = await api.post<ApiResponse<ContactGroup>>('/contacts/groups', data);
  return response.data.data!;
};

// 更新联系人分组
export const updateContactGroup = async (id: number, data: {
  name: string;
}): Promise<ContactGroup> => {
  const response = await api.put<ApiResponse<ContactGroup>>(`/contacts/groups/${id}`, data);
  return response.data.data!;
};

// 删除联系人分组
export const deleteContactGroup = async (id: number): Promise<void> => {
  await api.delete(`/contacts/groups/${id}`);
};

// 导入导出功能

// 导出联系人
export const exportContacts = async (format: 'csv' | 'vcf' = 'csv'): Promise<Blob> => {
  const response = await api.get(`/contacts/export?format=${format}`, {
    responseType: 'blob',
  });
  return response.data;
};

// 导入联系人
export const importContacts = async (file: File, format: 'csv' | 'vcf' = 'csv'): Promise<{
  imported: number;
  skipped: number;
  errors: string[];
}> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('format', format);
  
  const response = await api.post<ApiResponse<{
    imported: number;
    skipped: number;
    errors: string[];
  }>>('/contacts/import', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data.data!;
};

// 从邮件中添加联系人
export const addContactFromEmail = async (data: {
  email: string;
  name?: string;
  fromEmailId?: string;
}): Promise<Contact> => {
  const response = await api.post<ApiResponse<Contact>>('/contacts/from-email', data);
  return response.data.data!;
};

// 搜索联系人建议（用于邮件撰写时的自动完成）
export const searchContactSuggestions = async (query: string): Promise<Contact[]> => {
  const response = await api.get<ApiResponse<Contact[]>>('/contacts/suggestions', {
    params: { q: query },
  });
  return response.data.data!;
};
