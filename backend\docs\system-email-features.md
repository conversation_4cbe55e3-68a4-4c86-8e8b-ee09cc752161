# 系统邮件功能说明

本文档介绍了新实现的系统邮件功能，包括欢迎邮件、退信处理和通知功能。

## 功能概述

### 1. 欢迎邮件功能

- **触发时机**：用户注册成功后自动发送
- **发送者**：<EMAIL>（欢迎邮件系统）
- **功能**：向新注册用户发送欢迎邮件，介绍系统功能

### 2. 退信处理功能

- **触发时机**：邮件发送失败时自动处理
- **处理方式**：
  - 将退信邮件保存到原发件人的收件箱
  - 使用*************************发送退信通知
  - 自动解析退信原因并格式化显示

### 3. 退信通知功能

- **发送者**：<EMAIL>（邮件管理员）
- **功能**：向原发件人发送详细的退信通知邮件
- **内容**：包含失败原因、原始收件人、原始主题等信息

## 系统用户

系统使用以下特殊用户来发送系统邮件：

| 用户       | 邮箱                      | 角色      | 用途         |
| ---------- | ------------------------- | --------- | ------------ |
| welcome    | <EMAIL>    | moderator | 发送欢迎邮件 |
| postmaster | <EMAIL> | admin     | 发送退信通知 |
| admin      | <EMAIL>      | admin     | 系统管理员   |

## 邮件模板

系统提供以下邮件模板：

### 欢迎邮件模板

- **名称**：欢迎邮件模板
- **主题**：欢迎使用邮箱系统 - {{username}}
- **变量**：
  - `{{username}}`：用户名
  - `{{userEmail}}`：用户邮箱

### 退信通知模板

- **名称**：退信通知模板
- **主题**：邮件投递失败通知 - {{originalSubject}}
- **变量**：
  - `{{originalRecipientEmail}}`：原始收件人
  - `{{originalSubject}}`：原始主题
  - `{{bounceReason}}`：退信原因
  - `{{originalMessageId}}`：原始邮件 ID

## API 接口

### 管理员功能接口

#### 1. 手动处理退信邮件

```http
POST /api/bounce/process
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "originalSenderEmail": "<EMAIL>",
  "originalRecipientEmail": "<EMAIL>",
  "originalSubject": "测试邮件",
  "bounceReason": "550 User unknown",
  "originalMessageId": "message-id-123"
}
```

#### 2. 发送测试欢迎邮件

```http
POST /api/bounce/test-welcome
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "userEmail": "<EMAIL>",
  "username": "testuser"
}
```

#### 3. 检查邮件是否为退信邮件

```http
POST /api/bounce/check
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "senderEmail": "<EMAIL>",
  "subject": "Undelivered Mail Returned to Sender",
  "textContent": "550 User unknown in virtual mailbox table"
}
```

#### 4. 获取系统邮件统计

```http
GET /api/bounce/stats
Authorization: Bearer <admin-token>
```

## 工作流程

### 用户注册流程

1. 用户提交注册信息
2. 系统创建用户账户
3. 系统自动调用 `SystemEmailService.sendWelcomeEmail()`
4. welcome 用户发送欢迎邮件给新用户

### 邮件发送失败处理流程

1. 用户发送邮件
2. 邮件服务器投递失败
3. 系统捕获发送错误
4. 系统调用 `SystemEmailService.sendBounceNotification()`
5. postmaster 用户发送退信通知给原发件人

### 退信邮件处理流程

1. 邮件服务器接收到退信邮件
2. 系统同步邮件时检测到退信邮件
3. 系统调用 `BounceEmailProcessor.processBounceEmail()`
4. 退信邮件被保存到原发件人的收件箱
5. 系统发送退信通知邮件

## 配置要求

### 系统用户初始化

确保系统中存在必要的系统用户：

```bash
npm run db:seed
```

### 邮件模板初始化

系统模板会在首次启动时自动创建，也可以通过 API 手动创建。

### SMTP 配置

确保 `.env` 文件中配置了正确的 SMTP 设置：

```env
SMTP_HOST=mail.blindedby.love
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_password
```

## 测试

### 运行系统邮件测试

```bash
npm run test:system-emails
```

### 测试内容

- 检查系统用户状态
- 检查邮件模板
- 测试欢迎邮件发送
- 测试退信通知发送

## 注意事项

1. **系统用户权限**：welcome 和 postmaster 用户需要有效的邮件密码才能发送邮件
2. **模板变量**：邮件模板中的变量会在发送时自动替换
3. **退信检测**：系统会自动检测常见的退信邮件模式
4. **错误处理**：邮件发送失败不会影响主要业务流程
5. **日志记录**：所有系统邮件操作都会记录详细日志

## 故障排除

### 常见问题

1. **欢迎邮件发送失败**

   - 检查 welcome 用户是否存在且激活
   - 检查 SMTP 配置是否正确
   - 查看应用日志获取详细错误信息

2. **退信通知发送失败**

   - 检查 postmaster 用户是否存在且激活
   - 检查邮件模板是否正确配置
   - 确认原发件人邮箱地址有效

3. **退信邮件检测不准确**
   - 检查退信邮件的发件人和内容格式
   - 可能需要调整 `BounceEmailProcessor.isBounceEmail()` 的检测逻辑

### 日志查看

```bash
# 查看应用日志
tail -f logs/app.log

# 查看错误日志
tail -f logs/error.log
```
