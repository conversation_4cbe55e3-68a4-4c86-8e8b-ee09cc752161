#!/bin/bash

# Docker 快速设置脚本
# 用于初始化 Docker 环境和配置

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Docker 快速设置脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境 (默认)"
    echo "  prod    生产环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -f, --force    强制重新设置"
    echo "  --skip-env     跳过环境变量设置"
    echo "  --skip-dirs    跳过目录创建"
    echo "  --skip-deps    跳过依赖检查"
    echo ""
    echo "示例:"
    echo "  $0             # 设置开发环境"
    echo "  $0 prod        # 设置生产环境"
    echo "  $0 -f dev      # 强制重新设置开发环境"
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    local missing_deps=()
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        missing_deps+=("docker")
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        missing_deps+=("docker-compose")
    fi
    
    # 检查 curl
    if ! command -v curl &> /dev/null; then
        missing_deps+=("curl")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        log_error "缺少以下依赖: ${missing_deps[*]}"
        log_info "请安装缺少的依赖后重新运行脚本"
        exit 1
    fi
    
    # 检查 Docker 是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 设置环境变量文件
setup_env_file() {
    local env=$1
    local force=$2
    
    log_info "设置环境变量文件..."
    
    local env_file=".env"
    local example_file=".env.docker.example"
    
    if [ "$env" = "prod" ]; then
        env_file=".env.production"
    fi
    
    # 检查是否已存在
    if [ -f "$env_file" ] && [ "$force" != "true" ]; then
        log_warning "环境变量文件 $env_file 已存在"
        read -p "是否覆盖? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "跳过环境变量文件设置"
            return
        fi
    fi
    
    # 复制示例文件
    if [ -f "$example_file" ]; then
        cp "$example_file" "$env_file"
        log_success "已创建环境变量文件: $env_file"
    else
        log_warning "示例文件 $example_file 不存在，创建基本配置"
        create_basic_env_file "$env_file" "$env"
    fi
    
    # 生成随机密码
    generate_random_passwords "$env_file"
    
    log_warning "请编辑 $env_file 文件并设置正确的配置值"
}

# 创建基本环境变量文件
create_basic_env_file() {
    local env_file=$1
    local env=$2
    
    cat > "$env_file" << EOF
# 基本配置
DB_ROOT_PASSWORD=$(generate_password)
DB_NAME=mailserver
DB_USER=emailuser
DB_PASSWORD=$(generate_password)
REDIS_PASSWORD=$(generate_password)
JWT_SECRET=$(generate_password 32)
NODE_ENV=$env
FRONTEND_URL=http://localhost
SMTP_HOST=mail.blindedby.love
SMTP_PORT=587
IMAP_HOST=mail.blindedby.love
IMAP_PORT=993
EOF
    
    log_success "已创建基本环境变量文件"
}

# 生成随机密码
generate_password() {
    local length=${1:-16}
    openssl rand -base64 $length | tr -d "=+/" | cut -c1-$length
}

# 更新环境变量文件中的随机密码
generate_random_passwords() {
    local env_file=$1
    
    log_info "生成随机密码..."
    
    # 生成随机密码
    local db_root_pass=$(generate_password 20)
    local db_pass=$(generate_password 20)
    local redis_pass=$(generate_password 20)
    local jwt_secret=$(generate_password 32)
    
    # 更新文件
    sed -i "s/your-strong-root-password/$db_root_pass/g" "$env_file" 2>/dev/null || true
    sed -i "s/your-strong-db-password/$db_pass/g" "$env_file" 2>/dev/null || true
    sed -i "s/your-strong-redis-password/$redis_pass/g" "$env_file" 2>/dev/null || true
    sed -i "s/your-super-secret-jwt-key-at-least-32-characters-long/$jwt_secret/g" "$env_file" 2>/dev/null || true
    
    log_success "随机密码已生成"
}

# 创建必要的目录
create_directories() {
    local env=$1
    
    log_info "创建必要的目录..."
    
    local dirs=()
    
    if [ "$env" = "prod" ]; then
        dirs=(
            "/var/lib/email-system/mysql"
            "/var/lib/email-system/redis"
            "/var/lib/email-system/uploads"
            "/var/log/email-system"
            "./docker/ssl"
        )
    else
        dirs=(
            "./docker/ssl"
            "./logs"
            "./uploads"
        )
    fi
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            if [[ "$dir" == /var/* ]]; then
                sudo mkdir -p "$dir"
                sudo chown -R $USER:$USER "$dir"
            else
                mkdir -p "$dir"
            fi
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录创建完成"
}

# 设置 SSL 证书（开发环境）
setup_ssl_dev() {
    log_info "设置开发环境 SSL 证书..."
    
    local ssl_dir="./docker/ssl"
    
    if [ ! -f "$ssl_dir/cert.pem" ] || [ ! -f "$ssl_dir/key.pem" ]; then
        log_info "生成自签名 SSL 证书..."
        
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$ssl_dir/key.pem" \
            -out "$ssl_dir/cert.pem" \
            -subj "/C=CN/ST=State/L=City/O=Organization/CN=localhost"
        
        log_success "SSL 证书已生成"
    else
        log_info "SSL 证书已存在"
    fi
}

# 拉取 Docker 镜像
pull_images() {
    log_info "拉取 Docker 镜像..."
    
    local images=(
        "mysql:8.0"
        "redis:7-alpine"
        "node:18-alpine"
        "nginx:alpine"
    )
    
    for image in "${images[@]}"; do
        log_info "拉取镜像: $image"
        docker pull "$image"
    done
    
    log_success "镜像拉取完成"
}

# 验证设置
verify_setup() {
    local env=$1
    
    log_info "验证设置..."
    
    local env_file=".env"
    if [ "$env" = "prod" ]; then
        env_file=".env.production"
    fi
    
    # 检查环境变量文件
    if [ ! -f "$env_file" ]; then
        log_error "环境变量文件 $env_file 不存在"
        return 1
    fi
    
    # 检查必需的环境变量
    local required_vars=("DB_PASSWORD" "JWT_SECRET" "REDIS_PASSWORD")
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file" || grep -q "^$var=$" "$env_file"; then
            log_error "环境变量 $var 未设置或为空"
            return 1
        fi
    done
    
    # 检查 Docker Compose 文件
    local compose_file="docker/docker-compose.yml"
    if [ "$env" = "prod" ]; then
        compose_file="docker/docker-compose.prod.yml"
    fi
    
    if [ ! -f "$compose_file" ]; then
        log_error "Docker Compose 文件 $compose_file 不存在"
        return 1
    fi
    
    # 验证 Docker Compose 配置
    if ! docker-compose -f "$compose_file" config > /dev/null 2>&1; then
        log_error "Docker Compose 配置验证失败"
        return 1
    fi
    
    log_success "设置验证完成"
}

# 显示下一步操作
show_next_steps() {
    local env=$1
    
    log_success "Docker 环境设置完成！"
    echo ""
    log_info "下一步操作:"
    echo "1. 编辑环境变量文件并设置正确的配置"
    echo "2. 启动服务: ./scripts/docker-start.sh $env"
    echo "3. 监控服务: ./scripts/docker-monitor.sh -s $env"
    echo ""
    log_info "有用的命令:"
    echo "  启动服务: ./scripts/docker-start.sh $env"
    echo "  停止服务: ./scripts/docker-start.sh -d $env"
    echo "  查看日志: ./scripts/docker-start.sh -l $env"
    echo "  监控状态: ./scripts/docker-monitor.sh -w $env"
    echo "  清理资源: ./scripts/docker-cleanup.sh --project"
}

# 主函数
main() {
    local env="dev"
    local force="false"
    local skip_env="false"
    local skip_dirs="false"
    local skip_deps="false"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -f|--force)
                force="true"
                shift
                ;;
            --skip-env)
                skip_env="true"
                shift
                ;;
            --skip-dirs)
                skip_dirs="true"
                shift
                ;;
            --skip-deps)
                skip_deps="true"
                shift
                ;;
            dev|prod)
                env=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始设置 Docker 环境 (环境: $env)"
    
    # 执行设置步骤
    [ "$skip_deps" != "true" ] && check_dependencies
    [ "$skip_env" != "true" ] && setup_env_file "$env" "$force"
    [ "$skip_dirs" != "true" ] && create_directories "$env"
    
    if [ "$env" = "dev" ]; then
        setup_ssl_dev
    fi
    
    pull_images
    
    if verify_setup "$env"; then
        show_next_steps "$env"
    else
        log_error "设置验证失败，请检查配置"
        exit 1
    fi
}

# 脚本入口
main "$@"
