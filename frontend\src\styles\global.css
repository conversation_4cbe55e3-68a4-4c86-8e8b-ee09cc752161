/* 引入设计系统 */
@import './design-system.css';
@import './email-layout.css';

/* 引入 Tailwind CSS */
@import '../index.css';

/* 认证页面动画效果 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-card {
  animation: fadeInUp 0.6s ease-out;
}

.auth-logo {
  animation: float 3s ease-in-out infinite;
}

/* 输入框聚焦效果 */
.ant-input:focus,
.ant-input-password:focus {
  box-shadow: 0 0 0 2px rgba(255, 127, 80, 0.1); /* 珊瑚粉聚焦效果 */
}

/* 按钮悬停效果 */
.auth-button {
  position: relative;
  overflow: hidden;
}

.auth-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.auth-button:hover::before {
  left: 100%;
}

/* 邮件编辑器样式 */
.email-editor {
  min-height: 300px;
}

.email-editor .rdw-editor-toolbar {
  border: 1px solid #d9d9d9;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
}

.email-editor .rdw-editor-main {
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  min-height: 200px;
  padding: 12px;
}

/* 邮件列表样式 */
.email-list {
  background-color: var(--bg-primary);
}

.email-item {
  border-bottom: 1px solid var(--border-light);
  position: relative;
  background-color: var(--bg-primary);
  margin-bottom: 2px;
}

.email-item:hover {
  background-color: var(--bg-hover);
  box-shadow: var(--shadow-sm);
}

/* 未读邮件样式 - 移除背景色，只保留字体加粗 */
.email-item.unread .email-subject {
  font-weight: 600;
}

.email-item.selected {
  background-color: var(--bg-active);
  box-shadow: var(--shadow-md);
}

.email-item.selected:hover {
  background-color: var(--bg-active);
}

/* 邮件项悬停时显示操作按钮 */
.email-item .group-hover\:opacity-100 {
  opacity: 0;
  transition: opacity var(--transition-fast);
}

.email-item:hover .group-hover\:opacity-100 {
  opacity: 1;
}

/* 邮件发送者头像样式 */
.email-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
}

/* 邮件内容区域 */
.email-content {
  flex: 1;
  min-width: 0;
}

.email-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.email-sender {
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
}

.email-time {
  color: var(--text-tertiary);
  font-size: var(--font-size-xs);
  white-space: nowrap;
}

.email-subject {
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-tight);
}

.email-preview {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 邮件列表响应式优化 */
@media (max-width: 768px) {
  /* 移动端邮件列表布局 */
  .inbox-layout {
    flex-direction: column;
  }

  .inbox-sidebar {
    width: 100% !important;
    height: 50vh;
    border-right: none;
    border-bottom: 1px solid var(--border-light);
  }

  .inbox-content {
    height: 50vh;
  }

  /* 邮件项移动端样式 */
  .email-item {
    padding: var(--spacing-md);
    border-radius: 0;
  }

  .email-item .email-avatar {
    width: 32px;
    height: 32px;
  }

  .email-item .email-content {
    margin-left: var(--spacing-sm);
  }

  .email-item .email-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .email-item .email-sender {
    font-size: var(--font-size-sm);
    max-width: 200px;
  }

  .email-item .email-time {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
  }

  .email-item .email-subject {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-tight);
  }

  .email-item .email-preview {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
    max-height: 2.4em;
    overflow: hidden;
  }

  .email-item .email-actions {
    margin-top: var(--spacing-sm);
    justify-content: flex-end;
  }

  /* 批量操作栏移动端优化 */
  .batch-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }

  .batch-actions .batch-info {
    text-align: center;
  }

  .batch-actions .batch-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  /* 小屏幕邮件列表优化 */
  .inbox-sidebar {
    height: 60vh;
  }

  .inbox-content {
    height: 40vh;
  }

  .email-item {
    padding: var(--spacing-sm);
  }

  .email-item .email-checkbox {
    transform: scale(1.2);
  }

  .email-item .email-subject {
    font-weight: var(--font-weight-medium);
  }

  .email-item .email-preview {
    display: none;
  }

  /* 搜索框移动端优化 */
  .email-search {
    margin-bottom: var(--spacing-sm);
  }

  .email-search .ant-input {
    height: 40px;
    font-size: var(--font-size-base);
  }
}

/* 写邮件页面响应式优化 */
@media (max-width: 768px) {
  .compose-layout {
    padding: var(--spacing-sm);
  }

  .compose-card {
    margin: 0;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
  }

  .compose-header {
    padding: var(--spacing-md);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .compose-title {
    font-size: var(--font-size-lg);
  }

  .compose-actions {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
  }

  .compose-section {
    padding: var(--spacing-md);
  }

  .compose-section-title {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-md);
  }

  /* 收件人输入移动端优化 */
  .recipient-input {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .recipient-input .ant-input {
    height: 44px;
  }

  .recipient-tags {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    max-height: 120px;
    overflow-y: auto;
  }

  /* 附件上传移动端优化 */
  .attachment-upload {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .attachment-upload .ant-btn {
    width: 100%;
    height: 44px;
  }

  .attachment-list {
    grid-template-columns: 1fr;
    gap: var(--spacing-sm);
  }

  .attachment-item {
    padding: var(--spacing-sm);
  }

  /* 编辑器移动端优化 */
  .email-editor {
    min-height: 200px;
  }

  .editor-toolbar {
    flex-wrap: wrap;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm);
  }

  /* 操作按钮移动端优化 */
  .compose-footer {
    flex-direction: column;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .compose-footer .primary-actions {
    order: 1;
    width: 100%;
  }

  .compose-footer .secondary-actions {
    order: 2;
    width: 100%;
    justify-content: center;
  }

  .compose-footer .ant-btn {
    height: 44px;
    font-size: var(--font-size-base);
  }

  .compose-footer .primary-actions .ant-btn {
    flex: 1;
  }
}

@media (max-width: 480px) {
  .compose-layout {
    padding: 0;
  }

  .compose-card {
    border-radius: 0;
    border: none;
    box-shadow: none;
  }

  .compose-header {
    padding: var(--spacing-sm);
    border-bottom: 1px solid var(--border-light);
  }

  .compose-section {
    padding: var(--spacing-sm);
  }

  .compose-footer {
    padding: var(--spacing-sm);
    border-top: 1px solid var(--border-light);
  }
}

/* 文件夹树样式 */
.folder-tree .ant-tree-node-content-wrapper {
  padding: 4px 8px;
  border-radius: 4px;
}

.folder-tree .ant-tree-node-content-wrapper:hover {
  background-color: #f0f0f0;
}

.folder-tree .ant-tree-node-selected .ant-tree-node-content-wrapper {
  background-color: #e6f7ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .desktop-only {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .mobile-only {
    display: none !important;
  }
}

/* 加载动画 */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 邮件列表容器优化 */
.inbox-container {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
}

.inbox-header {
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  padding: var(--spacing-md) var(--spacing-lg);
}

.inbox-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

/* 邮件搜索框优化 */
.email-search-container {
  flex: 1;
  max-width: 400px;
}

.email-search-container .ant-input-search {
  border-radius: var(--radius-lg);
}

.email-search-container .ant-input-search .ant-input {
  border-color: var(--border-color);
  background-color: var(--bg-primary);
}

.email-search-container .ant-input-search .ant-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(160, 82, 45, 0.1);
}

/* 邮件操作按钮组 */
.email-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.email-actions .ant-btn {
  border-radius: var(--radius-md);
}

/* 邮件状态标签 */
.email-status-tags {
  display: flex;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
}

.email-status-tag {
  font-size: var(--font-size-xs);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  background-color: var(--bg-tertiary);
  color: var(--text-tertiary);
  border: 1px solid var(--border-light);
}

.email-status-tag.priority-high {
  background-color: var(--error-light);
  color: var(--error-color);
  border-color: var(--error-color);
}

.email-status-tag.priority-normal {
  background-color: var(--info-light);
  color: var(--info-color);
  border-color: var(--info-color);
}

.email-status-tag.has-attachment {
  background-color: var(--warning-light);
  color: var(--warning-color);
  border-color: var(--warning-color);
}
