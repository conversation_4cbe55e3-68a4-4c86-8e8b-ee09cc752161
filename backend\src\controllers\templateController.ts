import prisma from '../config/database';

// 获取邮件模板列表
export const getTemplates = async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = 1,
    limit = 20,
    sortBy = 'createdAt',
    sortOrder = 'desc',
    isPublic
  }: PaginationParams & { isPublic?: string } = req.query as any;

  // 确保 page 和 limit 是数字
  const pageNum = parseInt(String(page)) || 1;
  const limitNum = parseInt(String(limit)) || 20;
  const skip = (pageNum - 1) * limitNum;
  const userId = req.user!.id;

  const where: any = {
    OR: [
      { userId }, // 用户自己的模板
      { isPublic: true }, // 公共模板
    ],
  };

  if (isPublic !== undefined) {
    where.isPublic = isPublic === 'true';
    if (isPublic === 'false') {
      // 只查询用户自己的私有模板
      where.OR = [{ userId, isPublic: false }];
    }
  }

  const [templates, total] = await Promise.all([
    prisma.emailTemplate.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      include: {
        user: {
          select: {
            id: true,
            username: true,
            displayName: true,
          },
        },
      },
    }),
    prisma.emailTemplate.count({ where }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '获取邮件模板列表成功',
    data: {
      templates,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取邮件模板详情
export const getTemplateById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const template = await prisma.emailTemplate.findFirst({
    where: {
      id: parseInt(id),
      OR: [
        { userId }, // 用户自己的模板
        { isPublic: true }, // 公共模板
      ],
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          displayName: true,
        },
      },
    },
  });

  if (!template) {
    throw new AppError('邮件模板不存在', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: '获取邮件模板详情成功',
    data: template,
  };

  res.json(response);
};

// 创建邮件模板
export const createTemplate = async (req: AuthenticatedRequest, res: Response) => {
  const { name, subject, content, isPublic = false } = req.body;
  const userId = req.user!.id;

  // 检查模板名称是否已存在（用户级别）
  const existingTemplate = await prisma.emailTemplate.findFirst({
    where: {
      userId,
      name,
    },
  });

  if (existingTemplate) {
    throw new AppError('模板名称已存在', 409);
  }

  const template = await prisma.emailTemplate.create({
    data: {
      userId,
      name,
      subject,
      content,
      isPublic,
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          displayName: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '邮件模板创建成功',
    data: template,
  };

  res.status(201).json(response);
};

// 更新邮件模板
export const updateTemplate = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { name, subject, content, isPublic } = req.body;
  const userId = req.user!.id;

  // 检查模板是否存在且属于当前用户
  const existingTemplate = await prisma.emailTemplate.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingTemplate) {
    throw new AppError('邮件模板不存在或无权限修改', 404);
  }

  // 如果修改了名称，检查新名称是否已存在
  if (name && name !== existingTemplate.name) {
    const duplicateTemplate = await prisma.emailTemplate.findFirst({
      where: {
        userId,
        name,
        id: { not: parseInt(id) },
      },
    });

    if (duplicateTemplate) {
      throw new AppError('模板名称已存在', 409);
    }
  }

  const template = await prisma.emailTemplate.update({
    where: { id: parseInt(id) },
    data: {
      name,
      subject,
      content,
      isPublic,
    },
    include: {
      user: {
        select: {
          id: true,
          username: true,
          displayName: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '邮件模板更新成功',
    data: template,
  };

  res.json(response);
};

// 删除邮件模板
export const deleteTemplate = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  // 检查模板是否存在且属于当前用户
  const template = await prisma.emailTemplate.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!template) {
    throw new AppError('邮件模板不存在或无权限删除', 404);
  }

  await prisma.emailTemplate.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '邮件模板删除成功',
  };

  res.json(response);
};
