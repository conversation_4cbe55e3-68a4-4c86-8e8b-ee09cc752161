import logger from '../utils/logger';
import prisma from '../config/database';
import { sendEmail } from '../utils/email';
import { io } from '../index';

/**
 * 子账户邮件转发服务
 */
class SubAccountEmailForwardService {
  
  /**
   * 检查用户是否为子账户，如果是则转发邮件到主账户
   */
  async handleSubAccountEmailForward(email: any): Promise<void> {
    try {
      // 获取邮件接收者信息
      const recipient = await prisma.user.findUnique({
        where: { id: email.userId },
        include: {
          parentUser: true
        }
      });

      if (!recipient) {
        logger.warn(`邮件接收者不存在: userId=${email.userId}`);
        return;
      }

      // 检查是否为子账户
      if (recipient.accountType !== 'sub' || !recipient.parentUser) {
        // 不是子账户，无需转发
        return;
      }

      logger.info(`检测到子账户 ${recipient.email} 收到邮件，准备转发到主账户 ${recipient.parentUser.email}`);

      // 转发邮件到主账户
      await this.forwardEmailToParentAccount(email, recipient, recipient.parentUser);

    } catch (error) {
      logger.error('处理子账户邮件转发失败:', error);
    }
  }

  /**
   * 转发邮件到主账户
   */
  private async forwardEmailToParentAccount(
    originalEmail: any,
    subAccount: any,
    parentAccount: any
  ): Promise<void> {
    try {
      // 创建转发邮件的主题
      const forwardSubject = `[转发自 ${subAccount.email}] ${originalEmail.subject || '(无主题)'}`;

      // 创建转发邮件的内容
      const forwardContent = this.createForwardContent(originalEmail, subAccount);

      // 获取主账户的收件箱文件夹
      const parentInboxFolder = await prisma.folder.findFirst({
        where: {
          userId: parentAccount.id,
          type: 'inbox'
        }
      });

      if (!parentInboxFolder) {
        logger.error(`主账户 ${parentAccount.email} 的收件箱不存在`);
        return;
      }

      // 在数据库中创建转发邮件记录
      const forwardedEmail = await prisma.email.create({
        data: {
          messageId: `forwarded-${originalEmail.messageId || originalEmail.id}-${Date.now()}`,
          subject: forwardSubject,
          senderEmail: `system@${subAccount.email.split('@')[1]}`,
          senderName: '系统转发',
          recipientEmail: parentAccount.email,
          recipientName: parentAccount.displayName || parentAccount.username,
          textContent: forwardContent.text,
          htmlContent: forwardContent.html,
          receivedAt: new Date(),
          isRead: false,
          isDeleted: false,
          isImportant: originalEmail.isImportant || false,
          userId: parentAccount.id,
          folderId: parentInboxFolder.id,
          size: originalEmail.size || 0,
          hasAttachments: originalEmail.hasAttachments || false
        }
      });

      // 如果原邮件有附件，复制附件
      if (originalEmail.hasAttachments) {
        await this.copyEmailAttachments(originalEmail.id, forwardedEmail.id);
      }

      // 通过WebSocket通知主账户有新邮件
      io.to(`user_${parentAccount.id}`).emit('newEmail', {
        email: {
          id: forwardedEmail.id,
          subject: forwardedEmail.subject,
          senderEmail: forwardedEmail.senderEmail,
          senderName: forwardedEmail.senderName,
          receivedAt: forwardedEmail.receivedAt,
          isRead: false,
          isImportant: forwardedEmail.isImportant,
          hasAttachments: forwardedEmail.hasAttachments
        },
        isForwarded: true,
        originalSubAccount: subAccount.email
      });

      logger.info(`邮件已成功转发: ${originalEmail.subject} -> ${parentAccount.email}`);

    } catch (error) {
      logger.error('转发邮件到主账户失败:', error);
      throw error;
    }
  }

  /**
   * 创建转发邮件内容
   */
  private createForwardContent(originalEmail: any, subAccount: any): { text: string; html: string } {
    const forwardInfo = `
---------- 转发邮件 ----------
原收件人: ${subAccount.email}
原发件人: ${originalEmail.senderName || ''} <${originalEmail.senderEmail}>
发送时间: ${originalEmail.receivedAt ? new Date(originalEmail.receivedAt).toLocaleString('zh-CN') : '未知'}
主题: ${originalEmail.subject || '(无主题)'}
---------- 原邮件内容 ----------

`;

    const text = forwardInfo + (originalEmail.textContent || '(无文本内容)');
    
    const htmlForwardInfo = `
<div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0; background-color: #f9f9f9;">
  <h4 style="margin: 0 0 10px 0; color: #666;">转发邮件</h4>
  <p><strong>原收件人:</strong> ${subAccount.email}</p>
  <p><strong>原发件人:</strong> ${originalEmail.senderName || ''} &lt;${originalEmail.senderEmail}&gt;</p>
  <p><strong>发送时间:</strong> ${originalEmail.receivedAt ? new Date(originalEmail.receivedAt).toLocaleString('zh-CN') : '未知'}</p>
  <p><strong>主题:</strong> ${originalEmail.subject || '(无主题)'}</p>
</div>
<div style="margin-top: 20px;">
  <h4 style="color: #666;">原邮件内容:</h4>
  ${originalEmail.htmlContent || originalEmail.textContent || '<p>(无内容)</p>'}
</div>
`;

    return {
      text,
      html: htmlForwardInfo
    };
  }

  /**
   * 复制邮件附件
   */
  private async copyEmailAttachments(originalEmailId: number, forwardedEmailId: number): Promise<void> {
    try {
      const attachments = await prisma.attachment.findMany({
        where: { emailId: originalEmailId }
      });

      for (const attachment of attachments) {
        await prisma.attachment.create({
          data: {
            filename: attachment.filename,
            originalName: attachment.originalName,
            mimeType: attachment.mimeType,
            size: attachment.size,
            path: attachment.path, // 共享同一个文件
            emailId: forwardedEmailId
          }
        });
      }

      logger.info(`已复制 ${attachments.length} 个附件到转发邮件`);
    } catch (error) {
      logger.error('复制邮件附件失败:', error);
    }
  }

  /**
   * 检查子账户是否启用了邮件转发
   */
  async isEmailForwardEnabled(subAccountId: number): Promise<boolean> {
    try {
      const subAccount = await prisma.user.findUnique({
        where: { id: subAccountId }
      });

      if (!subAccount || subAccount.accountType !== 'sub') {
        return false;
      }

      // 检查子账户权限中是否启用了邮件转发
      const permissions = await prisma.subAccountPermission.findMany({
        where: {
          subUserId: subAccountId,
          permissionType: 'email_forward',
          isAllowed: true
        }
      });

      return permissions.length > 0;
    } catch (error) {
      logger.error('检查邮件转发权限失败:', error);
      return false;
    }
  }

  /**
   * 设置子账户邮件转发权限
   */
  async setEmailForwardPermission(subAccountId: number, enabled: boolean): Promise<void> {
    try {
      await prisma.subAccountPermission.upsert({
        where: {
          subUserId_permissionType: {
            subUserId: subAccountId,
            permissionType: 'email_forward'
          }
        },
        update: {
          isAllowed: enabled
        },
        create: {
          subUserId: subAccountId,
          permissionType: 'email_forward',
          isAllowed: enabled
        }
      });

      logger.info(`子账户 ${subAccountId} 邮件转发权限已设置为: ${enabled}`);
    } catch (error) {
      logger.error('设置邮件转发权限失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
export const subAccountEmailForwardService = new SubAccountEmailForwardService();
export default subAccountEmailForwardService;
