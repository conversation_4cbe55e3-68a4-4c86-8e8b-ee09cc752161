import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate, requireAdmin } from '../middleware/auth';
import * as systemUserController from '../controllers/systemUserController';

const router = Router();

// 所有路由都需要认证和管理员权限
router.use(authenticate);
router.use(requireAdmin);

// 获取系统用户列表
router.get('/',
  asyncHandler(systemUserController.getSystemUsers)
);

// 创建系统用户
router.post('/',
  asyncHandler(systemUserController.createSystemUser)
);

// 更新系统用户
router.put('/:id',
  asyncHandler(systemUserController.updateSystemUser)
);

// 删除系统用户
router.delete('/:id',
  asyncHandler(systemUserController.deleteSystemUser)
);

export default router;
