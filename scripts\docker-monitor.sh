#!/bin/bash

# Docker 监控脚本
# 用于监控 Docker 容器状态、资源使用情况和健康检查

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Docker 监控脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境 (默认)"
    echo "  prod    生产环境"
    echo ""
    echo "选项:"
    echo "  -h, --help      显示帮助信息"
    echo "  -s, --status    显示服务状态"
    echo "  -r, --resources 显示资源使用情况"
    echo "  -l, --logs      显示日志"
    echo "  -w, --watch     持续监控模式"
    echo "  --health        显示健康检查状态"
    echo "  --stats         显示实时统计信息"
    echo ""
    echo "示例:"
    echo "  $0 -s           # 显示开发环境状态"
    echo "  $0 -r prod      # 显示生产环境资源使用"
    echo "  $0 -w           # 持续监控开发环境"
}

# 获取 compose 文件
get_compose_file() {
    local env=$1
    if [ "$env" = "prod" ]; then
        echo "docker-compose.prod.yml"
    else
        echo "docker-compose.yml"
    fi
}

# 显示服务状态
show_status() {
    local env=$1
    local compose_file=$(get_compose_file "$env")
    
    log_info "=== $env 环境服务状态 ==="
    docker-compose -f "docker/$compose_file" ps
    
    echo ""
    log_info "=== 容器详细信息 ==="
    docker ps --filter "name=email-system" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 显示资源使用情况
show_resources() {
    local env=$1
    
    log_info "=== $env 环境资源使用情况 ==="
    
    # 获取项目相关容器
    local containers=$(docker ps --filter "name=email-system" --format "{{.Names}}")
    
    if [ -z "$containers" ]; then
        log_warning "没有找到运行中的项目容器"
        return
    fi
    
    echo "容器资源使用情况:"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}" $containers
    
    echo ""
    log_info "=== 系统资源概览 ==="
    docker system df
}

# 显示健康检查状态
show_health() {
    local env=$1
    
    log_info "=== $env 环境健康检查状态 ==="
    
    local containers=$(docker ps --filter "name=email-system" --format "{{.Names}}")
    
    for container in $containers; do
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
        local status_color=""
        
        case $health in
            "healthy")
                status_color=$GREEN
                ;;
            "unhealthy")
                status_color=$RED
                ;;
            "starting")
                status_color=$YELLOW
                ;;
            *)
                status_color=$NC
                ;;
        esac
        
        echo -e "$container: ${status_color}$health${NC}"
        
        # 如果有健康检查，显示详细信息
        if [ "$health" != "no-healthcheck" ]; then
            local last_check=$(docker inspect --format='{{range .State.Health.Log}}{{.Start}} - {{.ExitCode}}{{end}}' "$container" 2>/dev/null | tail -1)
            echo "  最后检查: $last_check"
        fi
    done
}

# 显示日志
show_logs() {
    local env=$1
    local compose_file=$(get_compose_file "$env")
    
    log_info "=== $env 环境服务日志 ==="
    docker-compose -f "docker/$compose_file" logs --tail=50
}

# 显示实时统计信息
show_stats() {
    local env=$1
    
    log_info "=== $env 环境实时统计信息 ==="
    log_info "按 Ctrl+C 退出监控"
    
    local containers=$(docker ps --filter "name=email-system" --format "{{.Names}}")
    
    if [ -z "$containers" ]; then
        log_warning "没有找到运行中的项目容器"
        return
    fi
    
    docker stats $containers
}

# 持续监控模式
watch_mode() {
    local env=$1
    
    log_info "=== 持续监控模式 ($env 环境) ==="
    log_info "按 Ctrl+C 退出监控"
    
    while true; do
        clear
        echo "$(date '+%Y-%m-%d %H:%M:%S') - Email System 监控"
        echo "========================================"
        
        # 显示状态
        show_status "$env"
        
        echo ""
        
        # 显示健康检查
        show_health "$env"
        
        echo ""
        
        # 显示资源使用（简化版）
        local containers=$(docker ps --filter "name=email-system" --format "{{.Names}}")
        if [ -n "$containers" ]; then
            echo "资源使用情况:"
            docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}" $containers
        fi
        
        echo ""
        echo "下次更新: $(date -d '+10 seconds' '+%H:%M:%S')"
        
        sleep 10
    done
}

# 检查服务可用性
check_availability() {
    local env=$1
    
    log_info "=== 检查服务可用性 ==="
    
    if [ "$env" = "prod" ]; then
        local frontend_url="https://mail.blindedby.love"
        local api_url="https://mail.blindedby.love/api/health"
    else
        local frontend_url="http://localhost"
        local api_url="http://localhost/api/health"
    fi
    
    # 检查前端
    if curl -f -s "$frontend_url" > /dev/null; then
        log_success "前端服务可访问: $frontend_url"
    else
        log_error "前端服务不可访问: $frontend_url"
    fi
    
    # 检查 API
    if curl -f -s "$api_url" > /dev/null; then
        log_success "API 服务可访问: $api_url"
    else
        log_error "API 服务不可访问: $api_url"
    fi
}

# 主函数
main() {
    local env="dev"
    local action="status"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -s|--status)
                action="status"
                shift
                ;;
            -r|--resources)
                action="resources"
                shift
                ;;
            -l|--logs)
                action="logs"
                shift
                ;;
            -w|--watch)
                action="watch"
                shift
                ;;
            --health)
                action="health"
                shift
                ;;
            --stats)
                action="stats"
                shift
                ;;
            dev|prod)
                env=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查 Docker 是否运行
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    
    # 执行操作
    case $action in
        status)
            show_status "$env"
            check_availability "$env"
            ;;
        resources)
            show_resources "$env"
            ;;
        health)
            show_health "$env"
            ;;
        logs)
            show_logs "$env"
            ;;
        stats)
            show_stats "$env"
            ;;
        watch)
            watch_mode "$env"
            ;;
    esac
}

# 脚本入口
main "$@"
