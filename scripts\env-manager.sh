#!/bin/bash

# 环境变量管理脚本
# 用于管理不同环境的配置文件

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 显示帮助信息
show_help() {
    echo "环境变量管理脚本"
    echo ""
    echo "用法: $0 <command> [environment]"
    echo ""
    echo "命令:"
    echo "  check [env]     检查环境变量文件"
    echo "  validate [env]  验证环境变量配置"
    echo "  list           列出所有环境变量文件"
    echo "  copy <from> <to> 复制环境变量文件"
    echo "  help           显示此帮助信息"
    echo ""
    echo "环境:"
    echo "  development    开发环境"
    echo "  staging        预发布环境"
    echo "  production     生产环境"
    echo ""
    echo "示例:"
    echo "  $0 check production"
    echo "  $0 validate staging"
    echo "  $0 copy development staging"
}

# 检查环境变量文件
check_env_files() {
    local env=${1:-"all"}
    
    print_info "检查环境变量文件..."
    
    # 后端环境变量文件
    local backend_files=(
        "backend/.env.example:示例文件"
        "backend/.env:开发环境"
        "backend/.env.staging:预发布环境"
        "backend/.env.prod:生产环境"
    )
    
    # 前端环境变量文件
    local frontend_files=(
        "frontend/.env.example:示例文件"
        "frontend/.env:开发环境默认"
        "frontend/.env.development:开发环境"
        "frontend/.env.staging:预发布环境"
        "frontend/.env.production:生产环境"
    )
    
    echo ""
    print_info "后端环境变量文件:"
    for file_info in "${backend_files[@]}"; do
        IFS=':' read -r file desc <<< "$file_info"
        if [ -f "$PROJECT_ROOT/$file" ]; then
            print_success "$file ($desc)"
        else
            print_warning "$file ($desc) - 文件不存在"
        fi
    done
    
    echo ""
    print_info "前端环境变量文件:"
    for file_info in "${frontend_files[@]}"; do
        IFS=':' read -r file desc <<< "$file_info"
        if [ -f "$PROJECT_ROOT/$file" ]; then
            print_success "$file ($desc)"
        else
            print_warning "$file ($desc) - 文件不存在"
        fi
    done
}

# 验证环境变量配置
validate_env() {
    local env=${1:-"production"}
    
    print_info "验证 $env 环境配置..."
    
    # 检查后端配置
    local backend_env_file=""
    case $env in
        "development")
            backend_env_file="backend/.env"
            ;;
        "staging")
            backend_env_file="backend/.env.staging"
            ;;
        "production")
            backend_env_file="backend/.env.prod"
            ;;
        *)
            print_error "未知环境: $env"
            return 1
            ;;
    esac
    
    if [ ! -f "$PROJECT_ROOT/$backend_env_file" ]; then
        print_error "后端环境文件不存在: $backend_env_file"
        return 1
    fi
    
    # 检查前端配置
    local frontend_env_file="frontend/.env.$env"
    if [ ! -f "$PROJECT_ROOT/$frontend_env_file" ]; then
        print_error "前端环境文件不存在: $frontend_env_file"
        return 1
    fi
    
    print_success "环境配置文件验证通过"
    
    # 检查关键配置项
    print_info "检查关键配置项..."
    
    # 检查后端关键配置
    local required_backend_vars=("NODE_ENV" "PORT" "DATABASE_URL" "JWT_SECRET" "FRONTEND_URL")
    for var in "${required_backend_vars[@]}"; do
        if grep -q "^$var=" "$PROJECT_ROOT/$backend_env_file"; then
            print_success "后端: $var 已配置"
        else
            print_warning "后端: $var 未配置或注释"
        fi
    done
    
    # 检查前端关键配置
    local required_frontend_vars=("VITE_API_BASE_URL" "VITE_APP_NAME")
    for var in "${required_frontend_vars[@]}"; do
        if grep -q "^$var=" "$PROJECT_ROOT/$frontend_env_file"; then
            print_success "前端: $var 已配置"
        else
            print_warning "前端: $var 未配置或注释"
        fi
    done
}

# 列出所有环境变量文件
list_env_files() {
    print_info "所有环境变量文件:"
    find "$PROJECT_ROOT" -name ".env*" -type f | sort
}

# 复制环境变量文件
copy_env_file() {
    local from_env=$1
    local to_env=$2
    
    if [ -z "$from_env" ] || [ -z "$to_env" ]; then
        print_error "请指定源环境和目标环境"
        show_help
        return 1
    fi
    
    print_info "复制环境配置: $from_env -> $to_env"
    
    # 复制后端配置
    local from_backend="backend/.env.$from_env"
    local to_backend="backend/.env.$to_env"
    
    if [ "$from_env" = "development" ]; then
        from_backend="backend/.env"
    fi
    if [ "$to_env" = "development" ]; then
        to_backend="backend/.env"
    fi
    
    if [ -f "$PROJECT_ROOT/$from_backend" ]; then
        cp "$PROJECT_ROOT/$from_backend" "$PROJECT_ROOT/$to_backend"
        print_success "后端配置已复制: $from_backend -> $to_backend"
    else
        print_warning "源后端配置文件不存在: $from_backend"
    fi
    
    # 复制前端配置
    local from_frontend="frontend/.env.$from_env"
    local to_frontend="frontend/.env.$to_env"
    
    if [ -f "$PROJECT_ROOT/$from_frontend" ]; then
        cp "$PROJECT_ROOT/$from_frontend" "$PROJECT_ROOT/$to_frontend"
        print_success "前端配置已复制: $from_frontend -> $to_frontend"
    else
        print_warning "源前端配置文件不存在: $from_frontend"
    fi
}

# 主函数
main() {
    case ${1:-""} in
        "check")
            check_env_files "$2"
            ;;
        "validate")
            validate_env "$2"
            ;;
        "list")
            list_env_files
            ;;
        "copy")
            copy_env_file "$2" "$3"
            ;;
        "help"|"--help"|"-h")
            show_help
            ;;
        "")
            print_error "请指定命令"
            show_help
            exit 1
            ;;
        *)
            print_error "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
