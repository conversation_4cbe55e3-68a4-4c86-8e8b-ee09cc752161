#!/bin/bash

# 检查 Dovecot IMAP IDLE 扩展是否启用
# 使用方法: ./check-idle-extension.sh

echo "🔍 检查 Dovecot IMAP IDLE 扩展状态..."
echo "========================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 1. 检查 Dovecot 服务状态
echo -e "\n${BLUE}1. 检查 Dovecot 服务状态${NC}"
echo "--------------------------------"
if systemctl is-active --quiet dovecot; then
    echo -e "${GREEN}✅ Dovecot 服务正在运行${NC}"
else
    echo -e "${RED}❌ Dovecot 服务未运行${NC}"
    echo "请先启动 Dovecot: sudo systemctl start dovecot"
fi

# 2. 检查 Dovecot 配置中的 IMAP 能力
echo -e "\n${BLUE}2. 检查 Dovecot IMAP 能力配置${NC}"
echo "--------------------------------"
if command -v doveconf >/dev/null 2>&1; then
    echo "📋 当前 imap_capability 配置:"
    IMAP_CAP=$(doveconf -h imap_capability 2>/dev/null)
    if [ -n "$IMAP_CAP" ]; then
        echo "   $IMAP_CAP"
        if echo "$IMAP_CAP" | grep -q "IDLE"; then
            echo -e "${GREEN}✅ IDLE 扩展已在配置中启用${NC}"
        else
            echo -e "${YELLOW}⚠️ IDLE 扩展未在 imap_capability 中明确配置${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 未找到 imap_capability 配置（使用默认值）${NC}"
    fi
    
    echo -e "\n📋 启用的协议:"
    PROTOCOLS=$(doveconf -h protocols 2>/dev/null)
    echo "   $PROTOCOLS"
    if echo "$PROTOCOLS" | grep -q "imap"; then
        echo -e "${GREEN}✅ IMAP 协议已启用${NC}"
    else
        echo -e "${RED}❌ IMAP 协议未启用${NC}"
    fi
else
    echo -e "${RED}❌ doveconf 命令不可用${NC}"
fi

# 3. 检查 IMAP 端口监听状态
echo -e "\n${BLUE}3. 检查 IMAP 端口监听状态${NC}"
echo "--------------------------------"
echo "📡 检查端口监听:"
if netstat -tlnp 2>/dev/null | grep -E ":(143|993)" | head -5; then
    echo -e "${GREEN}✅ IMAP 端口正在监听${NC}"
else
    echo -e "${RED}❌ IMAP 端口未监听${NC}"
fi

# 4. 使用 telnet/openssl 测试 IMAP 连接和 IDLE 支持
echo -e "\n${BLUE}4. 测试 IMAP 连接和 IDLE 支持${NC}"
echo "--------------------------------"

# 检查是否有必要的工具
if command -v openssl >/dev/null 2>&1; then
    echo "🔐 使用 OpenSSL 测试 IMAPS (993端口)..."
    
    # 创建临时测试脚本
    cat > /tmp/imap_test.txt << 'EOF'
a001 CAPABILITY
a002 LOGOUT
EOF
    
    echo "📤 发送 CAPABILITY 命令..."
    CAPABILITY_RESULT=$(timeout 10 openssl s_client -connect localhost:993 -quiet -verify_return_error 2>/dev/null < /tmp/imap_test.txt | grep -A 10 "CAPABILITY")
    
    if [ -n "$CAPABILITY_RESULT" ]; then
        echo "📋 服务器能力响应:"
        echo "$CAPABILITY_RESULT"
        
        if echo "$CAPABILITY_RESULT" | grep -q "IDLE"; then
            echo -e "${GREEN}✅ 服务器支持 IDLE 扩展${NC}"
        else
            echo -e "${RED}❌ 服务器不支持 IDLE 扩展${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 无法获取服务器能力信息${NC}"
    fi
    
    # 清理临时文件
    rm -f /tmp/imap_test.txt
    
elif command -v telnet >/dev/null 2>&1; then
    echo "📡 使用 telnet 测试 IMAP (143端口)..."
    echo "注意: 这将测试非加密连接"
    
    # 创建临时测试脚本
    cat > /tmp/imap_test.txt << 'EOF'
a001 CAPABILITY
a002 LOGOUT
EOF
    
    CAPABILITY_RESULT=$(timeout 10 telnet localhost 143 2>/dev/null < /tmp/imap_test.txt | grep -A 5 "CAPABILITY")
    
    if [ -n "$CAPABILITY_RESULT" ]; then
        echo "📋 服务器能力响应:"
        echo "$CAPABILITY_RESULT"
        
        if echo "$CAPABILITY_RESULT" | grep -q "IDLE"; then
            echo -e "${GREEN}✅ 服务器支持 IDLE 扩展${NC}"
        else
            echo -e "${RED}❌ 服务器不支持 IDLE 扩展${NC}"
        fi
    else
        echo -e "${YELLOW}⚠️ 无法获取服务器能力信息${NC}"
    fi
    
    # 清理临时文件
    rm -f /tmp/imap_test.txt
else
    echo -e "${YELLOW}⚠️ 未找到 openssl 或 telnet 工具，跳过连接测试${NC}"
fi

# 5. 检查 Dovecot 日志中的相关信息
echo -e "\n${BLUE}5. 检查 Dovecot 日志${NC}"
echo "--------------------------------"
if [ -f /var/log/dovecot.log ]; then
    echo "📄 检查最近的 Dovecot 日志..."
    echo "最近的 IMAP 相关日志:"
    tail -20 /var/log/dovecot.log | grep -i "imap\|idle" | tail -5
elif [ -f /var/log/mail.log ]; then
    echo "📄 检查系统邮件日志..."
    echo "最近的 Dovecot IMAP 日志:"
    tail -20 /var/log/mail.log | grep -i "dovecot.*imap" | tail -5
else
    echo -e "${YELLOW}⚠️ 未找到 Dovecot 日志文件${NC}"
fi

# 6. 提供配置建议
echo -e "\n${BLUE}6. 配置建议${NC}"
echo "--------------------------------"
echo "如果 IDLE 扩展未启用，请在 /etc/dovecot/dovecot.conf 中添加:"
echo ""
echo "protocol imap {"
echo "  imap_capability = +IDLE +NAMESPACE"
echo "}"
echo ""
echo "然后重启 Dovecot:"
echo "sudo systemctl restart dovecot"

echo -e "\n${GREEN}检查完成！${NC}"
echo "========================================"
