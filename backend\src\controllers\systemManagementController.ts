import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import systemManagementService from '../services/systemManagementService';
import prisma from '../config/database';
import logger from '../utils/logger';

// 获取系统指标
export const getSystemMetrics = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const forceRefresh = req.query.refresh === 'true';
    const metrics = await systemManagementService.getSystemMetrics(forceRefresh);

    const response: ApiResponse = {
      success: true,
      message: '获取系统指标成功',
      data: metrics,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取系统指标失败: ${(error as Error).message}`, 500);
  }
};

// 获取系统警报
export const getSystemAlerts = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { type, resolved, limit } = req.query;
    
    const alerts = systemManagementService.getSystemAlerts(
      type as 'error' | 'warning' | 'info' | undefined,
      resolved === 'true' ? true : resolved === 'false' ? false : undefined,
      limit ? parseInt(String(limit)) : 50
    );

    const response: ApiResponse = {
      success: true,
      message: '获取系统警报成功',
      data: alerts,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取系统警报失败: ${(error as Error).message}`, 500);
  }
};

// 解决系统警报
export const resolveAlert = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { alertId } = req.params;
    
    if (!alertId) {
      throw new AppError('请提供警报ID', 400);
    }

    const resolved = systemManagementService.resolveAlert(alertId);
    
    if (!resolved) {
      throw new AppError('警报不存在或已解决', 404);
    }

    const response: ApiResponse = {
      success: true,
      message: '警报已解决',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`解决警报失败: ${(error as Error).message}`, 500);
  }
};

// 获取系统健康状态
export const getSystemHealth = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const health = await systemManagementService.getSystemHealth();

    const response: ApiResponse = {
      success: true,
      message: '获取系统健康状态成功',
      data: health,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取系统健康状态失败: ${(error as Error).message}`, 500);
  }
};

// 生成系统报告
export const generateSystemReport = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const report = await systemManagementService.generateSystemReport();

    const response: ApiResponse = {
      success: true,
      message: '系统报告生成成功',
      data: report,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`生成系统报告失败: ${(error as Error).message}`, 500);
  }
};

// 记录系统异常（内部API）
export const recordSystemException = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { type, title, message, source, metadata } = req.body;

    if (!type || !title || !message || !source) {
      throw new AppError('请提供完整的异常信息', 400);
    }

    if (!['error', 'warning', 'info'].includes(type)) {
      throw new AppError('无效的异常类型', 400);
    }

    await systemManagementService.recordSystemException(
      type,
      title,
      message,
      source,
      metadata
    );

    const response: ApiResponse = {
      success: true,
      message: '系统异常记录成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`记录系统异常失败: ${(error as Error).message}`, 500);
  }
};

// 获取系统日志（简化版本）
export const getSystemLogs = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { level, limit, source } = req.query;

    // 这里可以实现从日志文件或日志数据库中读取日志
    // 目前返回系统警报作为日志的简化实现
    const alerts = systemManagementService.getSystemAlerts(
      level as 'error' | 'warning' | 'info' | undefined,
      undefined,
      limit ? parseInt(String(limit)) : 100
    );

    // 转换为日志格式
    const logs = alerts.map(alert => ({
      timestamp: alert.timestamp,
      level: alert.type,
      message: `[${alert.source}] ${alert.title}: ${alert.message}`,
      source: alert.source,
      metadata: alert.metadata
    }));

    const response: ApiResponse = {
      success: true,
      message: '获取系统日志成功',
      data: logs,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取系统日志失败: ${(error as Error).message}`, 500);
  }
};

// 清理系统数据
export const cleanupSystemData = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { type, days } = req.body;

    if (!type) {
      throw new AppError('请指定清理类型', 400);
    }

    const daysToKeep = days ? parseInt(String(days)) : 30;
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    let cleanedCount = 0;

    switch (type) {
      case 'deleted_emails':
        // 清理已删除的邮件
        const deletedEmails = await prisma.email.deleteMany({
          where: {
            isDeleted: true,
            updatedAt: { lt: cutoffDate }
          }
        });
        cleanedCount = deletedEmails.count;
        break;

      case 'old_logs':
        // 这里可以实现清理旧日志文件的逻辑
        logger.info(`清理 ${daysToKeep} 天前的日志文件`);
        cleanedCount = 0; // 占位符
        break;

      case 'temp_files':
        // 这里可以实现清理临时文件的逻辑
        logger.info('清理临时文件');
        cleanedCount = 0; // 占位符
        break;

      default:
        throw new AppError('不支持的清理类型', 400);
    }

    // 记录清理操作
    await systemManagementService.recordSystemException(
      'info',
      '系统数据清理',
      `清理了 ${cleanedCount} 条 ${type} 数据`,
      'system_cleanup',
      { type, daysToKeep, cleanedCount }
    );

    const response: ApiResponse = {
      success: true,
      message: `系统数据清理完成，清理了 ${cleanedCount} 条记录`,
      data: { cleanedCount, type, daysToKeep }
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`系统数据清理失败: ${(error as Error).message}`, 500);
  }
};
