import { createBrowserRouter, Navigate } from 'react-router-dom';
import { lazy, Suspense } from 'react';
import { Spin } from 'antd';
import AuthLayout from '../layouts/AuthLayout';
import MainLayout from '../layouts/MainLayout';
import EmailLayout from '../layouts/EmailLayout';
import ProtectedRoute from '../components/ProtectedRoute';

// 懒加载组件
const Login = lazy(() => import('../pages/Login'));
const Register = lazy(() => import('../pages/Register'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const EmailInbox = lazy(() => import('../pages/EmailInbox'));
const EmailStarred = lazy(() => import('../pages/EmailStarred'));
const EmailSent = lazy(() => import('../pages/EmailSent'));
const EmailDrafts = lazy(() => import('../pages/EmailDrafts'));
const EmailTrash = lazy(() => import('../pages/EmailTrash'));
const EmailDetail = lazy(() => import('../pages/EmailDetail'));
const Sent = lazy(() => import('../pages/Sent'));
const Trash = lazy(() => import('../pages/Trash'));
const Starred = lazy(() => import('../pages/Starred'));
const Compose = lazy(() => import('../pages/Compose'));
const Contacts = lazy(() => import('../pages/Contacts'));
const Templates = lazy(() => import('../pages/Templates'));
const LabelManagement = lazy(() => import('../pages/LabelManagement'));
const EmailRules = lazy(() => import('../pages/EmailRules'));
const Settings = lazy(() => import('../pages/Settings'));
const SettingsIndex = lazy(() => import('../pages/Settings/index'));
const DebugAuth = lazy(() => import('../pages/DebugAuth'));
const EmailSignature = lazy(() => import('../pages/Settings/EmailSignature'));
const Profile = lazy(() => import('../pages/Settings/Profile'));
const EmailPreferences = lazy(() => import('../pages/Settings/EmailPreferences'));
const EmailSync = lazy(() => import('../pages/Settings/EmailSync'));

const NotificationSettings = lazy(() => import('../pages/Settings/NotificationSettings'));
const SystemUsers = lazy(() => import('../pages/Settings/SystemUsers'));
const SystemTemplates = lazy(() => import('../pages/Settings/SystemTemplates'));
const AdminSettings = lazy(() => import('../pages/AdminSettings'));
const AdminUserManagement = lazy(() => import('../pages/AdminUserManagementSimple'));
const AdminApiTest = lazy(() => import('../pages/AdminApiTest'));
const EmailTemplates = lazy(() => import('../pages/EmailTemplates'));
const EmailAccounts = lazy(() => import('../pages/EmailAccounts'));
const SecuritySettings = lazy(() => import('../pages/SecuritySettings'));
const TrackingOverview = lazy(() => import('../pages/TrackingOverview'));
const EmailVerificationTest = lazy(() => import('../pages/EmailVerificationTest'));
const ImapConnectionTest = lazy(() => import('../pages/ImapConnectionTest'));
const ColorDemo = lazy(() => import('../pages/ColorDemo'));
const EmailSyncTest = lazy(() => import('../pages/EmailSyncTest'));
const SubAccountManagement = lazy(() => import('../pages/SubAccountManagement'));

// 加载组件
const LoadingComponent = () => (
  <div className="flex items-center justify-center min-h-screen">
    <Spin size="large" />
  </div>
);

// 包装懒加载组件
const withSuspense = (Component: React.ComponentType) => (
  <Suspense fallback={<LoadingComponent />}>
    <Component />
  </Suspense>
);

export const router = createBrowserRouter([
  {
    path: '/',
    element: <Navigate to="/inbox" replace />,
  },
  {
    path: '/login',
    element: (
      <AuthLayout>
        {withSuspense(Login)}
      </AuthLayout>
    ),
  },
  {
    path: '/register',
    element: (
      <AuthLayout>
        {withSuspense(Register)}
      </AuthLayout>
    ),
  },
  {
    path: '/email-verification-test',
    element: withSuspense(EmailVerificationTest),
  },
  {
    path: '/color-demo',
    element: withSuspense(ColorDemo),
  },
  // 统一使用邮箱布局
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <EmailLayout />
      </ProtectedRoute>
    ),
    children: [
      // 邮件相关页面
      {
        path: 'inbox',
        element: withSuspense(EmailInbox),
      },
      {
        path: 'starred',
        element: withSuspense(EmailStarred),
      },
      {
        path: 'sent',
        element: withSuspense(EmailSent),
      },
      {
        path: 'drafts',
        element: withSuspense(EmailDrafts),
      },
      {
        path: 'trash',
        element: withSuspense(EmailTrash),
      },
      {
        path: 'compose',
        element: withSuspense(Compose),
      },
      {
        path: 'email/detail/:emailId',
        element: withSuspense(EmailDetail),
      },

      // 其他功能页面
      {
        path: 'contacts',
        element: withSuspense(Contacts),
      },
      {
        path: 'templates',
        element: withSuspense(Templates),
      },
      {
        path: 'labels',
        element: withSuspense(LabelManagement),
      },
      {
        path: 'rules',
        element: withSuspense(EmailRules),
      },
      {
        path: 'email-accounts',
        element: withSuspense(EmailAccounts),
      },
      {
        path: 'security',
        element: withSuspense(SecuritySettings),
      },
      {
        path: 'tracking',
        element: withSuspense(TrackingOverview),
      },
      {
        path: 'settings',
        element: withSuspense(SettingsIndex),
      },
      {
        path: 'admin',
        element: withSuspense(AdminSettings),
      },
      {
        path: 'admin/users',
        element: withSuspense(AdminUserManagement),
      },
      {
        path: 'admin/api-test',
        element: withSuspense(AdminApiTest),
      },
      {
        path: 'sub-accounts',
        element: withSuspense(SubAccountManagement),
      },

      // 设置子页面
      {
        path: 'settings/profile',
        element: withSuspense(Profile),
      },
      {
        path: 'settings/signature',
        element: withSuspense(EmailSignature),
      },
      {
        path: 'settings/email-preferences',
        element: withSuspense(EmailPreferences),
      },
      {
        path: 'settings/email-sync',
        element: withSuspense(EmailSync),
      },

      {
        path: 'settings/notifications',
        element: withSuspense(NotificationSettings),
      },
      {
        path: 'settings/system-users',
        element: withSuspense(SystemUsers),
      },
      {
        path: 'settings/system-templates',
        element: withSuspense(SystemTemplates),
      },
      {
        path: 'imap-test',
        element: withSuspense(ImapConnectionTest),
      },
      {
        path: 'debug-auth',
        element: withSuspense(DebugAuth),
      },
      {
        path: 'email-sync-test',
        element: withSuspense(EmailSyncTest),
      },
    ],
  },

  {
    path: '*',
    element: <Navigate to="/inbox" replace />,
  },
]);
