import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import logger from '../utils/logger';
import BounceEmailProcessor from '../services/bounceEmailProcessor';
import SystemEmailService from '../services/systemEmailService';

/**
 * 退信邮件处理控制器
 */

/**
 * 手动处理退信邮件（管理员功能）
 */
export const processBounceEmail = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { 
      originalSenderEmail,
      originalRecipientEmail,
      originalSubject,
      bounceReason,
      originalMessageId
    } = req.body;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证必需参数
    if (!originalSenderEmail || !originalRecipientEmail || !originalSubject) {
      throw new AppError('缺少必需参数：originalSenderEmail, originalRecipientEmail, originalSubject', 400);
    }

    // 发送退信通知
    await SystemEmailService.sendBounceNotification(
      originalSenderEmail,
      originalRecipientEmail,
      originalSubject,
      bounceReason || '邮件投递失败',
      originalMessageId
    );

    const response: ApiResponse = {
      success: true,
      message: '退信通知发送成功',
      data: {
        originalSenderEmail,
        originalRecipientEmail,
        originalSubject,
        bounceReason: bounceReason || '邮件投递失败',
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('处理退信邮件失败:', error);
    throw new AppError('处理退信邮件失败', 500);
  }
};

/**
 * 发送测试欢迎邮件（管理员功能）
 */
export const sendTestWelcomeEmail = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userEmail, username } = req.body;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证必需参数
    if (!userEmail || !username) {
      throw new AppError('缺少必需参数：userEmail, username', 400);
    }

    // 发送欢迎邮件
    await SystemEmailService.sendWelcomeEmail(userEmail, username);

    const response: ApiResponse = {
      success: true,
      message: '测试欢迎邮件发送成功',
      data: {
        userEmail,
        username,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('发送测试欢迎邮件失败:', error);
    throw new AppError('发送测试欢迎邮件失败', 500);
  }
};

/**
 * 检查邮件是否为退信邮件（测试功能）
 */
export const checkBounceEmail = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { senderEmail, subject, textContent } = req.body;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证必需参数
    if (!senderEmail || !subject || !textContent) {
      throw new AppError('缺少必需参数：senderEmail, subject, textContent', 400);
    }

    // 检查是否为退信邮件
    const isBounce = BounceEmailProcessor.isBounceEmail({
      senderEmail,
      subject,
      textContent
    });

    const response: ApiResponse = {
      success: true,
      message: '退信邮件检查完成',
      data: {
        isBounce,
        senderEmail,
        subject,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('检查退信邮件失败:', error);
    throw new AppError('检查退信邮件失败', 500);
  }
};

/**
 * 获取系统邮件统计信息
 */
export const getSystemEmailStats = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 这里可以添加统计逻辑，比如：
    // - 今日发送的欢迎邮件数量
    // - 今日处理的退信邮件数量
    // - 系统用户的邮件发送统计等

    const response: ApiResponse = {
      success: true,
      message: '获取系统邮件统计成功',
      data: {
        welcomeEmailsSentToday: 0, // 待实现
        bounceEmailsProcessedToday: 0, // 待实现
        systemEmailsSentToday: 0, // 待实现
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取系统邮件统计失败:', error);
    throw new AppError('获取系统邮件统计失败', 500);
  }
};
