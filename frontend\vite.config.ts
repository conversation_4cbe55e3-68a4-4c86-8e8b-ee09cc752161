import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import { resolve } from "path";

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [react()],

    // 基础路径配置 - 用于静态部署
    base: "./",

    // 路径别名
    resolve: {
      alias: {
        "@": resolve(__dirname, "src"),
        "@/components": resolve(__dirname, "src/components"),
        "@/pages": resolve(__dirname, "src/pages"),
        "@/services": resolve(__dirname, "src/services"),
        "@/utils": resolve(__dirname, "src/utils"),
        "@/types": resolve(__dirname, "src/types"),
        "@/hooks": resolve(__dirname, "src/hooks"),
        "@/store": resolve(__dirname, "src/store"),
      },
    },

    // 开发服务器配置
    server: {
      port: 3000,
      host: true,
      // 代理配置 - 解决跨域问题
      proxy: {
        "/api": {
          target: env.VITE_API_BASE_URL,
          // target: "https://mail.blindedby.love",
          changeOrigin: true,
          secure: true,
          rewrite: (path) => path.replace(/^\/api/, '/api'),
          configure: (proxy, _options) => {
            proxy.on('error', (err, _req, _res) => {
              console.log('proxy error', err);
            });
            proxy.on('proxyReq', (proxyReq, req, _res) => {
              console.log('Sending Request to the Target:', req.method, req.url);
            });
            proxy.on('proxyRes', (proxyRes, req, _res) => {
              console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
            });
          },
        },
        "/socket.io": {
          // target: "https://mail.blindedby.love",
          target: env.VITE_WS_URL,
          changeOrigin: true,
          ws: true,
          secure: true,
        },
      },
    },

    // 预览服务器配置
    preview: {
      port: 3000,
      host: true,
    },

    // 构建配置
    build: {
      target: "es2015",
      outDir: "dist",
      assetsDir: "assets",
      sourcemap: mode === "development",
      minify: mode === "production" ? "terser" : false,

      // 生产环境优化
      rollupOptions: {
        output: {
          // 代码分割
          manualChunks: {
            vendor: ["react", "react-dom"],
            antd: ["antd", "@ant-design/icons"],
            router: ["react-router-dom"],
            utils: ["axios", "zustand", "@tanstack/react-query"],
          },
          // 文件命名
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
        },
      },

      // Terser 压缩配置
      terserOptions: {
        compress: {
          drop_console: mode === "production",
          drop_debugger: mode === "production",
        },
      },

      // 资源大小警告阈值
      chunkSizeWarningLimit: 1000,
    },

    // 环境变量前缀
    envPrefix: "VITE_",

    // CSS 配置
    css: {
      devSourcemap: mode === "development",
      preprocessorOptions: {
        less: {
          javascriptEnabled: true,
        },
      },
    },

    // 依赖优化
    optimizeDeps: {
      include: [
        "react",
        "react-dom",
        "antd",
        "axios",
        "react-router-dom",
        "zustand",
        "@tanstack/react-query",
      ],
    },
  };
});
