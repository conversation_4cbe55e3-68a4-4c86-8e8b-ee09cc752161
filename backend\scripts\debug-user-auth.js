#!/usr/bin/env node

/**
 * 用户认证调试脚本
 * 帮助调试用户登录和角色权限问题
 */

const { PrismaClient } = require('@prisma/client');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

// 从环境变量或默认值获取JWT密钥
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

async function debugUserAuth() {
  try {
    console.log('🔍 用户认证调试工具\n');

    // 1. 检查所有用户的角色
    console.log('📋 当前系统用户列表:');
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true
      },
      orderBy: { id: 'asc' }
    });

    allUsers.forEach(user => {
      const status = user.isActive ? '✅' : '❌';
      const verified = user.emailVerified ? '✅' : '❌';
      const isAdmin = user.role === 'admin' ? '👑' : '';
      console.log(`  ${user.id}: ${user.email} (${user.username}) - 角色: ${user.role} ${isAdmin} - 活跃: ${status} - 验证: ${verified}`);
    });

    // 2. 测试admin用户登录
    console.log('\n🔐 测试admin用户登录...');
    const adminUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: '<EMAIL>' },
          { role: 'admin' }
        ]
      }
    });

    if (!adminUser) {
      console.log('❌ 未找到admin用户');
      return;
    }

    console.log(`✅ 找到admin用户: ${adminUser.email}`);
    console.log(`   - ID: ${adminUser.id}`);
    console.log(`   - 用户名: ${adminUser.username}`);
    console.log(`   - 角色: ${adminUser.role}`);
    console.log(`   - 状态: ${adminUser.isActive ? '活跃' : '禁用'}`);
    console.log(`   - 邮箱验证: ${adminUser.emailVerified ? '已验证' : '未验证'}`);

    // 3. 生成测试JWT令牌
    console.log('\n🎫 生成测试JWT令牌...');
    const payload = {
      userId: adminUser.id,
      email: adminUser.email,
      role: adminUser.role
    };

    const token = jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
    console.log(`✅ JWT令牌已生成`);
    console.log(`   令牌: ${token.substring(0, 50)}...`);

    // 4. 验证JWT令牌
    console.log('\n🔍 验证JWT令牌...');
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      console.log('✅ JWT令牌验证成功');
      console.log(`   解码内容:`, decoded);
    } catch (error) {
      console.log('❌ JWT令牌验证失败:', error.message);
    }

    // 5. 模拟认证中间件流程
    console.log('\n🔄 模拟认证中间件流程...');
    try {
      const decoded = jwt.verify(token, JWT_SECRET);
      
      const user = await prisma.user.findUnique({
        where: { id: decoded.userId },
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          avatarUrl: true,
          isActive: true,
          role: true,
          createdAt: true,
          updatedAt: true,
        },
      });

      if (!user) {
        console.log('❌ 认证中间件: 用户不存在');
        return;
      }

      if (!user.isActive) {
        console.log('❌ 认证中间件: 用户账户已禁用');
        return;
      }

      console.log('✅ 认证中间件: 验证成功');
      console.log('   返回的用户信息:');
      console.log(`   - ID: ${user.id}`);
      console.log(`   - 邮箱: ${user.email}`);
      console.log(`   - 用户名: ${user.username}`);
      console.log(`   - 显示名: ${user.displayName || '未设置'}`);
      console.log(`   - 角色: ${user.role} ${user.role === 'admin' ? '👑' : ''}`);
      console.log(`   - 状态: ${user.isActive ? '活跃' : '禁用'}`);

      // 6. 检查管理员权限
      console.log('\n👑 检查管理员权限...');
      if (user.role === 'admin') {
        console.log('✅ 用户具有管理员权限');
        console.log('   前端应该显示"管理员设置"菜单');
      } else {
        console.log('❌ 用户不具有管理员权限');
        console.log(`   当前角色: ${user.role}`);
      }

    } catch (error) {
      console.log('❌ 认证流程失败:', error.message);
    }

    // 7. 提供测试建议
    console.log('\n💡 测试建议:');
    console.log('1. 使用以下信息登录前端:');
    console.log(`   邮箱: ${adminUser.email}`);
    console.log(`   密码: admin123 (如果是默认密码)`);
    console.log('');
    console.log('2. 登录后检查浏览器开发者工具:');
    console.log('   - Network标签: 查看/auth/me请求的响应');
    console.log('   - Console标签: 查看是否有JavaScript错误');
    console.log('   - Application标签: 查看localStorage中的token');
    console.log('');
    console.log('3. 检查前端用户状态:');
    console.log('   - 在浏览器控制台运行: localStorage.getItem("accessToken")');
    console.log('   - 检查用户store状态');

  } catch (error) {
    console.error('❌ 调试过程中出现错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行调试脚本
if (require.main === module) {
  debugUserAuth().catch(console.error);
}

module.exports = { debugUserAuth };
