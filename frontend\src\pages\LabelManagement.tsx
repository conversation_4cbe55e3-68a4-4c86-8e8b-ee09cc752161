import { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  ColorPicker,
  Space,
  message,
  Popconfirm,
  Tag,
  Typography,
  Statistic,
  Row,
  Col,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  TagOutlined,
} from "@ant-design/icons";
import {
  getLabels,
  createLabel,
  updateLabel,
  deleteLabel,
} from "../services/labelApi";
import type { Label } from "../services/labelApi";
import type { ColumnsType } from "antd/es/table";

const { Title } = Typography;

const LabelManagement: React.FC = () => {
  const [labels, setLabels] = useState<Label[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingLabel, setEditingLabel] = useState<Label | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();

  // 加载标签列表
  const loadLabels = async (page = 1, limit = 20) => {
    try {
      setLoading(true);
      const response = await getLabels({
        page,
        limit,
        sortBy: "name",
        sortOrder: "asc",
      });

      setLabels(response.data || []);
      setPagination({
        current: response.pagination.page,
        pageSize: response.pagination.limit,
        total: response.pagination.total,
      });
    } catch (error) {
      console.error("加载标签列表失败:", error);
      message.error("加载标签列表失败");
      setLabels([]); // 确保在错误时设置为空数组
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadLabels();
  }, []);

  // 创建或更新标签
  const handleSaveLabel = async (values: { name: string; color: string }) => {
    try {
      if (editingLabel) {
        // 更新标签
        await updateLabel(editingLabel.id, values);
        message.success("标签更新成功");
      } else {
        // 创建标签
        await createLabel(values);
        message.success("标签创建成功");
      }

      setModalVisible(false);
      setEditingLabel(null);
      form.resetFields();
      loadLabels(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error(editingLabel ? "标签更新失败" : "标签创建失败");
    }
  };

  // 删除标签
  const handleDeleteLabel = async (labelId: number) => {
    try {
      await deleteLabel(labelId);
      message.success("标签删除成功");
      loadLabels(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error("标签删除失败");
    }
  };

  // 打开编辑模态框
  const openEditModal = (label?: Label) => {
    setEditingLabel(label || null);
    if (label) {
      form.setFieldsValue({
        name: label.name,
        color: label.color,
      });
    } else {
      form.resetFields();
    }
    setModalVisible(true);
  };

  // 表格列定义
  const columns: ColumnsType<Label> = [
    {
      title: "标签",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: Label) => (
        <Tag
          color={record.color}
          style={{ fontSize: "14px", padding: "4px 8px" }}
        >
          {name}
        </Tag>
      ),
    },
    {
      title: "颜色",
      dataIndex: "color",
      key: "color",
      width: 100,
      render: (color: string) => (
        <div className="flex items-center space-x-2">
          <div
            className="w-6 h-6 rounded border"
            style={{ backgroundColor: color }}
          />
          <span className="text-xs text-gray-500">{color}</span>
        </div>
      ),
    },
    {
      title: "邮件数量",
      dataIndex: "_count",
      key: "emailCount",
      width: 120,
      render: (count: { emails: number }) => <span>{count?.emails || 0}</span>,
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "操作",
      key: "actions",
      width: 120,
      render: (_, record: Label) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          />
          <Popconfirm
            title="确定要删除这个标签吗？"
            description="删除后将从所有邮件中移除该标签"
            onConfirm={() => handleDeleteLabel(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" size="small" icon={<DeleteOutlined />} danger />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 统计信息
  const totalEmails = (labels || []).reduce(
    (sum, label) => sum + (label._count?.emails || 0),
    0
  );

  return (
    <div className="p-6">
      <div className="mb-6">
        <Title level={2}>
          <TagOutlined className="mr-2" />
          标签管理
        </Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={8}>
          <Card>
            <Statistic
              title="总标签数"
              value={pagination.total}
              prefix={<TagOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已使用标签"
              value={
                (labels || []).filter((label) => (label._count?.emails || 0) > 0).length
              }
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic title="标记邮件总数" value={totalEmails} />
          </Card>
        </Col>
      </Row>

      {/* 标签表格 */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <Title level={4} className="mb-0">
            标签列表
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openEditModal()}
          >
            新建标签
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={labels}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadLabels(page, pageSize);
            },
          }}
        />
      </Card>

      {/* 创建/编辑标签模态框 */}
      <Modal
        title={editingLabel ? "编辑标签" : "新建标签"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingLabel(null);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveLabel}
          initialValues={{ color: "#1890ff" }}
        >
          <Form.Item
            label="标签名称"
            name="name"
            rules={[
              { required: true, message: "请输入标签名称" },
              { max: 50, message: "标签名称不能超过50个字符" },
            ]}
          >
            <Input placeholder="输入标签名称..." />
          </Form.Item>

          <Form.Item
            label="标签颜色"
            name="color"
            rules={[{ required: true, message: "请选择标签颜色" }]}
          >
            <ColorPicker
              showText
              format="hex"
              presets={[
                {
                  label: "推荐颜色",
                  colors: [
                    "#f50",
                    "#2db7f5",
                    "#87d068",
                    "#108ee9",
                    "#f56a00",
                    "#eb2f96",
                    "#52c41a",
                    "#13c2c2",
                    "#722ed1",
                    "#fa541c",
                    "#faad14",
                    "#a0d911",
                  ],
                },
              ]}
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: "100%", justifyContent: "flex-end" }}>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingLabel ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default LabelManagement;
