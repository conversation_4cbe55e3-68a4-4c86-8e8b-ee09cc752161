import { create } from "zustand";
import { persist } from "zustand/middleware";
import type { User, LoginData, RegisterData } from "../types";
import * as authApi from "../services/authApi";

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;

  // Actions
  login: (data: LoginData) => Promise<void>;
  register: (data: RegisterData) => Promise<void>;
  logout: () => void;
  getCurrentUser: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set) => ({
      user: null,
      isAuthenticated: false,
      loading: false,
      error: null,

      login: async (data: LoginData) => {
        try {
          console.log('🔑 AuthStore: 开始登录流程', data);
          set({ loading: true, error: null });

          console.log('📡 AuthStore: 调用登录API');
          const response = await authApi.login(data);
          const { user, accessToken, refreshToken } = response;

          console.log('✅ AuthStore: 登录API成功，用户信息:', user);

          // 保存token到localStorage
          localStorage.setItem("accessToken", accessToken);
          localStorage.setItem("refreshToken", refreshToken);
          console.log('💾 AuthStore: Token已保存到localStorage');

          set({
            user,
            isAuthenticated: true,
            loading: false,
            error: null,
          });

          console.log('🎉 AuthStore: 用户状态已更新，登录完成');
        } catch (error: unknown) {
          const errorMessage = error instanceof Error
            ? error.message
            : (error as { response?: { data?: { message?: string } } })?.response?.data?.message || "登录失败";

          set({
            loading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      register: async (data: RegisterData) => {
        try {
          set({ loading: true, error: null });

          const response = await authApi.register(data);
          const { user, accessToken, refreshToken } = response;

          // 保存token到localStorage
          localStorage.setItem("accessToken", accessToken);
          localStorage.setItem("refreshToken", refreshToken);

          set({
            user,
            isAuthenticated: true,
            loading: false,
            error: null,
          });
        } catch (error: unknown) {
          const errorMessage = error instanceof Error
            ? error.message
            : (error as { response?: { data?: { message?: string } } })?.response?.data?.message || "注册失败";

          set({
            loading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: () => {
        // 清除本地存储
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");

        // 调用后端登出API
        authApi.logout().catch(console.error);

        set({
          user: null,
          isAuthenticated: false,
          loading: false,
          error: null,
        });
      },

      getCurrentUser: async () => {
        try {
          const token = localStorage.getItem("accessToken");
          console.log('🔑 AuthStore: 检查本地token:', !!token);

          if (!token) {
            console.log('⚠️ AuthStore: 没有token，跳过用户状态恢复');
            return;
          }

          console.log('📡 AuthStore: 开始获取当前用户信息');
          set({ loading: true });
          const user = await authApi.getCurrentUser();

          console.log('✅ AuthStore: 用户信息获取成功:', user);
          set({
            user,
            isAuthenticated: true,
            loading: false,
          });
        } catch (error: unknown) {
          // 如果获取用户信息失败，清除认证状态
          localStorage.removeItem("accessToken");
          localStorage.removeItem("refreshToken");

          const errorMessage = error instanceof Error
            ? error.message
            : (error as { response?: { data?: { message?: string } } })?.response?.data?.message || "获取用户信息失败";

          set({
            user: null,
            isAuthenticated: false,
            loading: false,
            error: errorMessage,
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ loading });
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
