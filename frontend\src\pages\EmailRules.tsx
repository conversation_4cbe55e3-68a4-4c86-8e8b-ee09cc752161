import { useState, useEffect } from "react";
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  message,
  Popconfirm,
  Tag,
  Typography,
  Statistic,
  Row,
  Col,
  Divider,
  InputNumber,
} from "antd";
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ExperimentOutlined,
  FilterOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import {
  getEmailRules,
  createEmailRule,
  updateEmailRule,
  deleteEmailRule,
  toggleEmailRuleStatus,
  testEmailRule,
  getAllRuleTemplates,
  getRuleTemplatesByCategory,
  createRuleFromTemplate,
  createSentCopyRule,
} from "../services/emailRuleApi";
import type {
  EmailRule,
  EmailRuleCondition,
  EmailRuleAction,
  EmailRuleTemplate,
} from "../services/emailRuleApi";
import EmailRuleTemplates from "../components/EmailRuleTemplates";

const { Title, Text } = Typography;
const { Option } = Select;

const EmailRules: React.FC = () => {
  const [rules, setRules] = useState<EmailRule[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<EmailRule | null>(null);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [testingRule, setTestingRule] = useState<EmailRule | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [form] = Form.useForm();
  const [testForm] = Form.useForm();

  // 字段选项
  const fieldOptions = [
    { value: "from", label: "发件人" },
    { value: "to", label: "收件人" },
    { value: "subject", label: "主题" },
    { value: "content", label: "内容" },
    { value: "hasAttachment", label: "包含附件" },
    { value: "headers", label: "邮件头部" },
  ];

  // 操作符选项
  const operatorOptions = [
    { value: "contains", label: "包含" },
    { value: "equals", label: "等于" },
    { value: "startsWith", label: "开始于" },
    { value: "endsWith", label: "结束于" },
    { value: "regex", label: "正则表达式" },
  ];

  // 动作类型选项
  const actionTypeOptions = [
    { value: "move", label: "移动到文件夹" },
    { value: "moveToFolder", label: "移动到指定文件夹" },
    { value: "addLabel", label: "添加标签" },
    { value: "addFlag", label: "添加邮件标志" },
    { value: "markRead", label: "标记为已读" },
    { value: "markStarred", label: "标记为星标" },
    { value: "delete", label: "删除邮件" },
    { value: "forward", label: "转发邮件" },
    { value: "stopProcessing", label: "停止处理后续规则" },
  ];

  // 加载规则列表
  const loadRules = async (page = 1, limit = 20) => {
    try {
      setLoading(true);
      const response = await getEmailRules({
        page,
        limit,
        sortBy: "priority",
        sortOrder: "asc",
      });

      setRules(response.data || []);
      setPagination({
        current: response.pagination?.page || 1,
        pageSize: response.pagination?.limit || 20,
        total: response.pagination?.total || 0,
      });
    } catch (error) {
      message.error("加载规则列表失败");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRules();
  }, []);

  // 创建或更新规则
  const handleSaveRule = async (values: any) => {
    try {
      const { name, conditions, actions, isActive, priority } = values;

      if (editingRule) {
        await updateEmailRule(editingRule.id, {
          name,
          conditions,
          actions,
          isActive,
          priority,
        });
        message.success("规则更新成功");
      } else {
        await createEmailRule({
          name,
          conditions,
          actions,
          isActive,
          priority,
        });
        message.success("规则创建成功");
      }

      setModalVisible(false);
      setEditingRule(null);
      form.resetFields();
      loadRules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error(editingRule ? "规则更新失败" : "规则创建失败");
    }
  };

  // 删除规则
  const handleDeleteRule = async (ruleId: number) => {
    try {
      await deleteEmailRule(ruleId);
      message.success("规则删除成功");
      loadRules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error("规则删除失败");
    }
  };

  // 切换规则状态
  const handleToggleRule = async (ruleId: number) => {
    try {
      await toggleEmailRuleStatus(ruleId);
      message.success("规则状态已更新");
      loadRules(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error("更新规则状态失败");
    }
  };

  // 测试规则
  const handleTestRule = async (values: any) => {
    try {
      const { conditions, emailId } = values;
      const result = await testEmailRule({ conditions, emailId });

      if (result.matched) {
        message.success(
          `规则测试成功！找到匹配的邮件${
            result.email ? `：${result.email.subject}` : ""
          }`
        );
      } else {
        message.info("规则测试完成，未找到匹配的邮件");
      }

      setTestModalVisible(false);
      testForm.resetFields();
    } catch (error) {
      message.error("规则测试失败");
    }
  };

  // 打开编辑模态框
  const openEditModal = (rule?: EmailRule) => {
    setEditingRule(rule || null);
    if (rule) {
      form.setFieldsValue({
        name: rule.name,
        conditions: rule.conditions,
        actions: rule.actions,
        isActive: rule.isActive,
        priority: rule.priority,
      });
    } else {
      form.resetFields();
    }
    setModalVisible(true);
  };

  // 打开测试模态框
  const openTestModal = (rule: EmailRule) => {
    setTestingRule(rule);
    testForm.setFieldsValue({
      conditions: rule.conditions,
    });
    setTestModalVisible(true);
  };

  // 表格列定义
  const columns: ColumnsType<EmailRule> = [
    {
      title: "规则名称",
      dataIndex: "name",
      key: "name",
      render: (name: string, record: EmailRule) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-xs text-gray-500">优先级: {record.priority}</div>
        </div>
      ),
    },
    {
      title: "条件",
      dataIndex: "conditions",
      key: "conditions",
      render: (conditions: EmailRuleCondition[]) => (
        <div className="space-y-1">
          {conditions.slice(0, 2).map((condition, index) => (
            <Tag key={index} color="blue" className="text-xs">
              {fieldOptions.find((f) => f.value === condition.field)?.label}{" "}
              {
                operatorOptions.find((o) => o.value === condition.operator)
                  ?.label
              }{" "}
              "{condition.value}"
            </Tag>
          ))}
          {conditions.length > 2 && (
            <Tag color="default" className="text-xs">
              +{conditions.length - 2} 更多
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: "动作",
      dataIndex: "actions",
      key: "actions",
      render: (actions: EmailRuleAction[]) => (
        <div className="space-y-1">
          {actions.slice(0, 2).map((action, index) => (
            <Tag key={index} color="green" className="text-xs">
              {actionTypeOptions.find((a) => a.value === action.type)?.label}
            </Tag>
          ))}
          {actions.length > 2 && (
            <Tag color="default" className="text-xs">
              +{actions.length - 2} 更多
            </Tag>
          )}
        </div>
      ),
    },
    {
      title: "状态",
      dataIndex: "isActive",
      key: "isActive",
      width: 100,
      render: (isActive: boolean, record: EmailRule) => (
        <Switch
          checked={isActive}
          onChange={() => handleToggleRule(record.id)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      ),
    },
    {
      title: "创建时间",
      dataIndex: "createdAt",
      key: "createdAt",
      width: 180,
      render: (date: string) => new Date(date).toLocaleString(),
    },
    {
      title: "操作",
      key: "operations",
      width: 150,
      render: (_, record: EmailRule) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<ExperimentOutlined />}
            onClick={() => openTestModal(record)}
            title="测试规则"
          />
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => openEditModal(record)}
          />
          <Popconfirm
            title="确定要删除这个规则吗？"
            onConfirm={() => handleDeleteRule(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="text" size="small" icon={<DeleteOutlined />} danger />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <Title level={2}>
          <FilterOutlined className="mr-2" />
          邮件规则
        </Title>
        <Text type="secondary">自动化处理邮件，根据条件执行相应动作</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={16} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="总规则数"
              value={pagination.total}
              prefix={<FilterOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="启用规则"
              value={(rules || []).filter((rule) => rule.isActive).length}
              prefix={<PlayCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="禁用规则"
              value={(rules || []).filter((rule) => !rule.isActive).length}
              prefix={<PauseCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均优先级"
              value={
                (rules || []).length > 0
                  ? Math.round(
                      (rules || []).reduce((sum, rule) => sum + rule.priority, 0) /
                        (rules || []).length
                    )
                  : 0
              }
            />
          </Card>
        </Col>
      </Row>



      {/* 规则模板 */}
      <div className="mb-6">
        <EmailRuleTemplates onRuleCreated={loadRules} />
      </div>

      <Divider />

      {/* 规则表格 */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <Title level={4} className="mb-0">
            规则列表
          </Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openEditModal()}
          >
            新建规则
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={rules || []}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadRules(page, pageSize);
            },
          }}
        />
      </Card>

      {/* 创建/编辑规则模态框 */}
      <Modal
        title={editingRule ? "编辑规则" : "新建规则"}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingRule(null);
          form.resetFields();
        }}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveRule}
          initialValues={{
            isActive: true,
            priority: 0,
            conditions: [
              {
                field: "from",
                operator: "contains",
                value: "",
                caseSensitive: false,
              },
            ],
            actions: [{ type: "markRead" }],
          }}
        >
          <Row gutter={16}>
            <Col span={16}>
              <Form.Item
                label="规则名称"
                name="name"
                rules={[
                  { required: true, message: "请输入规则名称" },
                  { max: 100, message: "规则名称不能超过100个字符" },
                ]}
              >
                <Input placeholder="输入规则名称..." />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item
                label="优先级"
                name="priority"
                tooltip="数字越小优先级越高"
              >
                <InputNumber min={0} max={999} style={{ width: "100%" }} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item
                label="启用状态"
                name="isActive"
                valuePropName="checked"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Divider>规则条件</Divider>
          <Form.List name="conditions">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8} align="middle">
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, "field"]}
                        rules={[{ required: true, message: "请选择字段" }]}
                      >
                        <Select placeholder="选择字段">
                          {fieldOptions.map((option) => (
                            <Option key={option.value} value={option.value}>
                              {option.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={5}>
                      <Form.Item
                        {...restField}
                        name={[name, "operator"]}
                        rules={[{ required: true, message: "请选择操作符" }]}
                      >
                        <Select placeholder="选择操作符">
                          {operatorOptions.map((option) => (
                            <Option key={option.value} value={option.value}>
                              {option.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        name={[name, "value"]}
                        rules={[{ required: true, message: "请输入值" }]}
                      >
                        <Input placeholder="输入匹配值..." />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...restField}
                        name={[name, "caseSensitive"]}
                        valuePropName="checked"
                      >
                        <Switch
                          size="small"
                          checkedChildren="区分大小写"
                          unCheckedChildren="忽略大小写"
                        />
                      </Form.Item>
                    </Col>
                    <Col span={2}>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(name)}
                        disabled={fields.length === 1}
                      />
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() =>
                      add({
                        field: "from",
                        operator: "contains",
                        value: "",
                        caseSensitive: false,
                      })
                    }
                    block
                    icon={<PlusOutlined />}
                  >
                    添加条件
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>

          <Divider>执行动作</Divider>
          <Form.List name="actions">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8} align="middle">
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        name={[name, "type"]}
                        rules={[{ required: true, message: "请选择动作类型" }]}
                      >
                        <Select placeholder="选择动作类型">
                          {actionTypeOptions.map((option) => (
                            <Option key={option.value} value={option.value}>
                              {option.label}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        {...restField}
                        name={[name, "value"]}
                        dependencies={[["actions", name, "type"]]}
                      >
                        <Form.Item noStyle shouldUpdate>
                          {({ getFieldValue }) => {
                            const actionType = getFieldValue([
                              "actions",
                              name,
                              "type",
                            ]);

                            if (actionType === "move") {
                              return (
                                <Select placeholder="选择目标文件夹">
                                  {/* TODO: 加载文件夹列表 */}
                                  <Option value={1}>收件箱</Option>
                                  <Option value={2}>已发送</Option>
                                  <Option value={3}>草稿箱</Option>
                                  <Option value={4}>垃圾箱</Option>
                                </Select>
                              );
                            }

                            if (actionType === "addLabel") {
                              return (
                                <Select placeholder="选择标签">
                                  {/* TODO: 加载标签列表 */}
                                  <Option value={1}>重要</Option>
                                  <Option value={2}>工作</Option>
                                  <Option value={3}>个人</Option>
                                </Select>
                              );
                            }

                            if (actionType === "forward") {
                              return (
                                <Input placeholder="输入转发邮箱地址..." />
                              );
                            }

                            return (
                              <Input
                                disabled
                                placeholder="此动作无需额外参数"
                              />
                            );
                          }}
                        </Form.Item>
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(name)}
                        disabled={fields.length === 1}
                      />
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add({ type: "markRead" })}
                    block
                    icon={<PlusOutlined />}
                  >
                    添加动作
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>

          <Form.Item>
            <Space style={{ width: "100%", justifyContent: "flex-end" }}>
              <Button onClick={() => setModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingRule ? "更新" : "创建"}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 测试规则模态框 */}
      <Modal
        title="测试规则"
        open={testModalVisible}
        onCancel={() => {
          setTestModalVisible(false);
          setTestingRule(null);
          testForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={testForm} layout="vertical" onFinish={handleTestRule}>
          <Form.Item
            label="邮件ID（可选）"
            name="emailId"
            tooltip="留空将自动查找匹配的邮件"
          >
            <Input placeholder="输入特定邮件ID进行测试..." />
          </Form.Item>

          <Form.Item label="规则条件" name="conditions">
            <div className="bg-gray-50 p-4 rounded border">
              {testingRule?.conditions.map((condition, index) => (
                <Tag key={index} color="blue" className="mb-2">
                  {fieldOptions.find((f) => f.value === condition.field)?.label}{" "}
                  {
                    operatorOptions.find((o) => o.value === condition.operator)
                      ?.label
                  }{" "}
                  "{condition.value}"
                  {condition.caseSensitive && " (区分大小写)"}
                </Tag>
              ))}
            </div>
          </Form.Item>

          <Form.Item>
            <Space style={{ width: "100%", justifyContent: "flex-end" }}>
              <Button onClick={() => setTestModalVisible(false)}>取消</Button>
              <Button type="primary" htmlType="submit">
                开始测试
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EmailRules;
