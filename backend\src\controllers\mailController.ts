import { AuthenticatedRequest, AppError, ApiResponse } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';
import { execSync } from 'child_process';
import { generateMailPassword } from '../utils/mailPasswordUtils';

/**
 * 邮件控制器
 * 处理邮件相关的API请求
 */

/**
 * 使用服务器端doveadm生成正确的邮件密码
 * 直接使用mailPasswordUtils中的统一实现
 */
function generateServerMailPassword(plainPassword: string): string {
  try {
    logger.info('开始生成邮件密码');
    const mailPassword = generateMailPassword(plainPassword);
    logger.info('邮件密码生成成功');
    return mailPassword;
  } catch (error: any) {
    logger.error('邮件密码生成失败:', error?.message || error);
    throw new AppError('邮件密码生成失败，请稍后重试', 500);
  }
}

/**
 * 设置用户邮件密码
 * POST /api/mail/setup-password
 */
export const setupMailPassword = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { password } = req.body;
    const userId = req.user!.id;

    if (!password || password.length < 6) {
      throw new AppError('密码长度至少为6位', 400);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        emailVerified: true,
        isMailActive: true,
        mailPassword: true
      }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    if (!user.emailVerified) {
      throw new AppError('请先验证邮箱', 400);
    }

    // 生成服务器端邮件密码
    logger.info(`Generating mail password for user: ${user.email}`);
    const mailPassword = generateServerMailPassword(password);

    // 更新用户邮件密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        mailPassword: mailPassword,
        isMailActive: true
      }
    });

    // 确保虚拟别名存在
    const domain = user.email.split('@')[1];
    let virtualDomain = await prisma.virtualDomain.findFirst({
      where: { name: domain }
    });

    if (!virtualDomain) {
      virtualDomain = await prisma.virtualDomain.create({
        data: {
          name: domain,
          active: 1
        }
      });
    }

    // 检查虚拟别名
    const existingAlias = await prisma.virtualAlias.findFirst({
      where: { source: user.email }
    });

    if (!existingAlias) {
      await prisma.virtualAlias.create({
        data: {
          source: user.email,
          destination: user.email,
          domainId: virtualDomain.id,
          active: 1
        }
      });
    }

    logger.info(`Mail password setup completed for user: ${user.email}`);

    const response: ApiResponse = {
      success: true,
      message: '邮件密码设置成功',
      data: {
        isMailActive: true,
        hasMailPassword: true
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('Setup mail password error:', error);
    throw new AppError('邮件密码设置失败', 500);
  }
};

/**
 * 检查用户邮件状态
 * GET /api/mail/status
 */
export const getMailStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        email: true,
        emailVerified: true,
        isMailActive: true,
        mailPassword: true
      }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    const response: ApiResponse = {
      success: true,
      message: '获取邮件状态成功',
      data: {
        email: user.email,
        emailVerified: user.emailVerified,
        isMailActive: user.isMailActive,
        hasMailPassword: !!user.mailPassword,
        needsPasswordSetup: !user.mailPassword && user.emailVerified
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('Get mail status error:', error);
    throw new AppError('获取邮件状态失败', 500);
  }
};

/**
 * 重置用户邮件密码
 * POST /api/mail/reset-password
 */
export const resetMailPassword = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user!.id;

    if (!newPassword || newPassword.length < 6) {
      throw new AppError('新密码长度至少为6位', 400);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        mailPassword: true
      }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    if (!user.mailPassword) {
      throw new AppError('请先设置邮件密码', 400);
    }

    // 这里可以添加当前密码验证逻辑
    // 暂时跳过，因为我们的密码验证比较复杂

    // 生成新的邮件密码
    logger.info(`Resetting mail password for user: ${user.email}`);
    const newMailPassword = generateServerMailPassword(newPassword);

    // 更新密码
    await prisma.user.update({
      where: { id: userId },
      data: {
        mailPassword: newMailPassword
      }
    });

    logger.info(`Mail password reset completed for user: ${user.email}`);

    const response: ApiResponse = {
      success: true,
      message: '邮件密码重置成功',
      data: {
        hasMailPassword: true
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('Reset mail password error:', error);
    throw new AppError('邮件密码重置失败', 500);
  }
};
