#!/bin/bash

# 快速检查 IMAP IDLE 扩展状态
# 使用方法: ./quick-idle-check.sh [host] [port] [user] [password]

HOST=${1:-"localhost"}
PORT=${2:-"993"}
USER=${3:-"<EMAIL>"}
PASSWORD=${4:-"HOUsc@0202"}

echo "🔍 快速检查 IMAP IDLE 扩展..."
echo "主机: $HOST:$PORT"
echo "用户: $USER"
echo "================================"

# 检查工具可用性
if ! command -v openssl >/dev/null 2>&1; then
    echo "❌ 需要 openssl 工具"
    exit 1
fi

# 创建临时测试文件
TEMP_FILE=$(mktemp)
cat > "$TEMP_FILE" << EOF
a001 CAPABILITY
a002 LOGIN $USER $PASSWORD
a003 CAPABILITY
a004 SELECT INBOX
a005 IDLE
a006 DONE
a007 LOGOUT
EOF

echo "📡 连接并测试 IDLE 支持..."

# 执行测试
RESULT=$(timeout 15 openssl s_client -connect "$HOST:$PORT" -quiet 2>/dev/null < "$TEMP_FILE")

# 清理临时文件
rm -f "$TEMP_FILE"

# 分析结果
echo "📋 服务器响应分析:"
echo "================================"

if echo "$RESULT" | grep -q "CAPABILITY"; then
    echo "✅ 服务器响应正常"
    
    # 检查IDLE支持
    if echo "$RESULT" | grep -i "capability" | grep -q "IDLE"; then
        echo "✅ 服务器支持 IDLE 扩展"
        
        # 检查IDLE命令是否成功
        if echo "$RESULT" | grep -q "IDLE"; then
            echo "✅ IDLE 命令执行成功"
        else
            echo "⚠️ IDLE 命令可能未正确执行"
        fi
    else
        echo "❌ 服务器不支持 IDLE 扩展"
    fi
    
    # 显示服务器能力
    echo ""
    echo "📋 服务器能力:"
    echo "$RESULT" | grep -i "capability" | head -1
    
else
    echo "❌ 无法获取服务器响应"
    echo "可能的原因:"
    echo "- 服务器未运行"
    echo "- 连接被拒绝"
    echo "- 认证失败"
    echo "- SSL/TLS 问题"
fi

echo ""
echo "🔧 如需启用 IDLE 扩展，请在 dovecot.conf 中添加:"
echo "protocol imap {"
echo "  imap_capability = +IDLE +NAMESPACE"
echo "}"
