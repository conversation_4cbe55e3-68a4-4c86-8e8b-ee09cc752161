import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';

// Mock API calls
vi.mock('../../services/authApi', () => ({
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  getCurrentUser: vi.fn(),
  refreshToken: vi.fn(),
}));

vi.mock('../../services/emailApi', () => ({
  getEmails: vi.fn(),
  getEmailById: vi.fn(),
  sendEmail: vi.fn(),
}));

import * as authApi from '../../services/authApi';
import * as emailApi from '../../services/emailApi';

describe('Authentication Flow Integration', () => {
  beforeEach(async () => {
    vi.clearAllMocks();

    // Reset localStorage mocks
    vi.mocked(localStorage.getItem).mockReturnValue(null);
    vi.mocked(localStorage.setItem).mockImplementation(() => {});
    vi.mocked(localStorage.removeItem).mockImplementation(() => {});
    vi.mocked(localStorage.clear).mockImplementation(() => {});

    // Reset auth store state
    const { useAuthStore } = await import('../../store/authStore');
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      loading: false,
      error: null,
    });

    // Mock successful API responses by default
    vi.mocked(authApi.getCurrentUser).mockRejectedValue(new Error('Not authenticated'));
    vi.mocked(emailApi.getEmails).mockResolvedValue({
      data: [],
      pagination: {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false,
      },
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('should complete full authentication flow', async () => {
    // Mock successful login
    vi.mocked(authApi.login).mockResolvedValue({
      user: {
        id: 1,
        email: '<EMAIL>',
        username: 'testuser',
        role: 'user' as const,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
    });

    // Test the authentication flow using the store directly
    const { useAuthStore } = await import('../../store/authStore');
    const store = useAuthStore.getState();

    // Perform login
    await store.login({
      email: '<EMAIL>',
      password: 'password123',
    });

    // Verify API was called
    expect(authApi.login).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123',
    });

    // Verify tokens are stored
    expect(localStorage.setItem).toHaveBeenCalledWith('accessToken', 'mock-access-token');
    expect(localStorage.setItem).toHaveBeenCalledWith('refreshToken', 'mock-refresh-token');

    // Verify store state
    const state = useAuthStore.getState();
    expect(state.isAuthenticated).toBe(true);
    expect(state.user).toEqual({
      id: 1,
      email: '<EMAIL>',
      username: 'testuser',
      role: 'user',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    });
  });

  it('should handle login failure gracefully', async () => {
    // Mock login failure
    vi.mocked(authApi.login).mockRejectedValue({
      response: {
        data: {
          message: '邮箱或密码错误',
        },
      },
    });

    // Test the authentication flow using the store directly
    const { useAuthStore } = await import('../../store/authStore');
    const store = useAuthStore.getState();

    // Attempt login with invalid credentials
    await expect(store.login({
      email: '<EMAIL>',
      password: 'wrongpassword',
    })).rejects.toThrow();

    // Verify API was called
    expect(authApi.login).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'wrongpassword',
    });

    // Verify no access tokens are stored
    expect(localStorage.setItem).not.toHaveBeenCalledWith('accessToken', expect.anything());
    expect(localStorage.setItem).not.toHaveBeenCalledWith('refreshToken', expect.anything());

    // Verify store state remains unauthenticated
    const state = useAuthStore.getState();
    expect(state.isAuthenticated).toBe(false);
    expect(state.user).toBeNull();
    expect(state.error).toBe('邮箱或密码错误');
  });

  it('should handle registration flow', async () => {
    // Mock successful registration
    vi.mocked(authApi.register).mockResolvedValue({
      user: {
        id: 2,
        email: '<EMAIL>',
        username: 'newuser',
        role: 'user' as const,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      },
      accessToken: 'mock-access-token',
      refreshToken: 'mock-refresh-token',
    });

    // Test the registration flow using the store directly
    const { useAuthStore } = await import('../../store/authStore');
    const store = useAuthStore.getState();

    // Perform registration
    await store.register({
      email: '<EMAIL>',
      username: 'newuser',
      password: 'password123',
    });

    // Verify API was called
    expect(authApi.register).toHaveBeenCalledWith({
      email: '<EMAIL>',
      username: 'newuser',
      password: 'password123',
    });

    // Verify tokens are stored
    expect(localStorage.setItem).toHaveBeenCalledWith('accessToken', 'mock-access-token');
    expect(localStorage.setItem).toHaveBeenCalledWith('refreshToken', 'mock-refresh-token');

    // Verify store state
    const state = useAuthStore.getState();
    expect(state.isAuthenticated).toBe(true);
    expect(state.user).toEqual({
      id: 2,
      email: '<EMAIL>',
      username: 'newuser',
      role: 'user',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    });
  });

  it('should handle logout flow', async () => {
    // Start with authenticated state
    vi.mocked(localStorage.getItem).mockImplementation((key) => {
      if (key === 'accessToken') return 'mock-access-token';
      if (key === 'refreshToken') return 'mock-refresh-token';
      return null;
    });

    vi.mocked(authApi.getCurrentUser).mockResolvedValue({
      id: 1,
      email: '<EMAIL>',
      username: 'testuser',
      role: 'user' as const,
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    });

    // Test logout functionality using the store directly
    const { useAuthStore } = await import('../../store/authStore');
    const store = useAuthStore.getState();

    vi.mocked(authApi.logout).mockResolvedValue();
    await store.logout();

    // Should call logout API
    expect(authApi.logout).toHaveBeenCalled();

    // Should clear tokens
    expect(localStorage.removeItem).toHaveBeenCalledWith('accessToken');
    expect(localStorage.removeItem).toHaveBeenCalledWith('refreshToken');
  });

  it.skip('should handle token refresh on API errors', async () => {
    // Token refresh functionality is not yet implemented in the store
    // This test is skipped until the feature is implemented
  });
});
