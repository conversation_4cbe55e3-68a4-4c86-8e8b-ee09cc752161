import api from '../config/api';
import type { ApiResponse, PaginatedResponse } from '../types';

export interface SecuritySettings {
  passwordExpiryDays?: number;
  lastPasswordChange?: string;
  twoFactorEnabled: boolean;
  loginNotification: boolean;
  suspiciousActivity: boolean;
  maxActiveSessions: number;
  sessionTimeout: number;
  hasBackupCodes: boolean;
  ipWhitelist: string[];
}

export interface SecurityLog {
  id: number;
  action: string;
  ipAddress: string;
  userAgent?: string;
  location?: string;
  success: boolean;
  details?: string;
  createdAt: string;
}

export interface UserSession {
  id: string;
  createdAt: string;
  expiresAt: string;
  lastUsedAt?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface TwoFactorSetup {
  secret: string;
  backupCodes: string[];
  qrCodeUrl: string;
}

// 获取安全设置
export const getSecuritySettings = async (): Promise<SecuritySettings> => {
  const response = await api.get<ApiResponse<SecuritySettings>>('/security/settings');
  return response.data.data!;
};

// 更新安全设置
export const updateSecuritySettings = async (settings: Partial<SecuritySettings>): Promise<SecuritySettings> => {
  const response = await api.put<ApiResponse<SecuritySettings>>('/security/settings', settings);
  return response.data.data!;
};

// 启用两步验证
export const enableTwoFactor = async (password: string): Promise<TwoFactorSetup> => {
  const response = await api.post<ApiResponse<TwoFactorSetup>>('/security/2fa/enable', { password });
  return response.data.data!;
};

// 确认启用两步验证
export const confirmTwoFactor = async (code: string): Promise<void> => {
  await api.post('/security/2fa/confirm', { code });
};

// 禁用两步验证
export const disableTwoFactor = async (password: string, code: string): Promise<void> => {
  await api.post('/security/2fa/disable', { password, code });
};

// 生成新的备用代码
export const generateNewBackupCodes = async (password: string): Promise<string[]> => {
  const response = await api.post<ApiResponse<{ backupCodes: string[] }>>('/security/2fa/backup-codes', { password });
  return response.data.data!.backupCodes;
};

// 获取安全日志
export const getSecurityLogs = async (params?: {
  page?: number;
  limit?: number;
}): Promise<PaginatedResponse<SecurityLog>> => {
  const response = await api.get<ApiResponse<PaginatedResponse<SecurityLog>>>('/security/logs', {
    params,
  });
  return response.data.data!;
};

// 获取活跃会话
export const getActiveSessions = async (): Promise<UserSession[]> => {
  const response = await api.get<ApiResponse<UserSession[]>>('/security/sessions');
  return response.data.data!;
};

// 终止会话
export const terminateSession = async (sessionId: string): Promise<void> => {
  await api.delete(`/security/sessions/${sessionId}`);
};

// 终止所有其他会话
export const terminateAllOtherSessions = async (): Promise<void> => {
  await api.delete('/security/sessions');
};

// 修改密码
export const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
  await api.post('/auth/change-password', { currentPassword, newPassword });
};

// 安全事件类型映射
export const SECURITY_ACTION_LABELS: Record<string, string> = {
  'login': '登录',
  'logout': '登出',
  'user_registered': '用户注册',
  'password_changed': '密码修改',
  '2fa_enabled': '启用两步验证',
  '2fa_disabled': '禁用两步验证',
  '2fa_setup_initiated': '开始设置两步验证',
  'backup_codes_regenerated': '重新生成备用代码',
  'security_settings_updated': '安全设置更新',
  'session_terminated': '会话终止',
  'all_sessions_terminated': '终止所有会话',
};

// 获取安全事件的友好显示名称
export const getSecurityActionLabel = (action: string): string => {
  return SECURITY_ACTION_LABELS[action] || action;
};

// 格式化IP地址显示
export const formatIPAddress = (ip: string): string => {
  if (ip === '::1' || ip === '127.0.0.1') {
    return '本地';
  }
  return ip;
};

// 解析用户代理字符串
export const parseUserAgent = (userAgent?: string): { browser: string; os: string } => {
  if (!userAgent) {
    return { browser: '未知', os: '未知' };
  }

  let browser = '未知';
  let os = '未知';

  // 简单的用户代理解析
  if (userAgent.includes('Chrome')) browser = 'Chrome';
  else if (userAgent.includes('Firefox')) browser = 'Firefox';
  else if (userAgent.includes('Safari')) browser = 'Safari';
  else if (userAgent.includes('Edge')) browser = 'Edge';

  if (userAgent.includes('Windows')) os = 'Windows';
  else if (userAgent.includes('Mac')) os = 'macOS';
  else if (userAgent.includes('Linux')) os = 'Linux';
  else if (userAgent.includes('Android')) os = 'Android';
  else if (userAgent.includes('iOS')) os = 'iOS';

  return { browser, os };
};
