import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, PaginationParams, EmailSearchParams, EmailData, AppError } from '../types';
import prisma from '../config/database';
import { io } from '../index';
import logger from '../utils/logger';
import { sendEmail as sendEmailService, validateEmailData, saveEmailToDatabase, saveDraft, updateDraft } from '../services/emailService';
import { fetchNewEmails, markEmailAsRead, markEmailAsUnread, syncDatabaseStatusToIMAP } from '../services/imapService';
import { syncUserEmailsViaImapFlow, testImapPermissions } from '../services/imapFlowService';
import { onDemandEmailSyncService } from '../services/onDemandEmailSyncService';
import { emailSyncManager } from '../services/emailSyncManager';
import realTimeEmailService from '../services/realTimeEmailService';
// 获取邮件列表
export const getEmails = async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = 1,
    limit = 20,
    sortBy = 'receivedAt',
    sortOrder = 'desc',
    folderId,
    folderType,
    isRead,
    isStarred
  }: PaginationParams & { folderId?: string; folderType?: string; isRead?: string; isStarred?: string } = req.query as any;

  // 确保 page 和 limit 是数字
  const pageNum = parseInt(String(page));
  const limitNum = parseInt(String(limit));
  const skip = (pageNum - 1) * limitNum;
  const userId = req.user!.id;

  // 构建查询条件
  const where: any = {
    userId,
  };

  // 特殊处理垃圾箱：显示已删除的邮件
  if (folderType === 'trash') {
    where.isDeleted = true;
  } else {
    where.isDeleted = false;
  }

  if (folderId) {
    where.folderId = parseInt(folderId);
  } else if (folderType && folderType !== 'trash') {
    // 根据文件夹类型查询（垃圾箱除外，因为垃圾箱是通过isDeleted字段控制的）
    const folder = await prisma.folder.findFirst({
      where: {
        userId,
        type: folderType,
      },
    });

    if (folder) {
      where.folderId = folder.id;
    } else {
      // 如果找不到对应类型的文件夹，返回空结果
      const response: ApiResponse = {
        success: true,
        message: '获取邮件列表成功',
        data: {
          emails: [],
          pagination: {
            page: pageNum,
            limit: limitNum,
            total: 0,
            totalPages: 0,
            hasNext: false,
            hasPrev: false,
          },
        },
      };
      return res.json(response);
    }
  }

  if (isRead !== undefined) {
    where.isRead = isRead === 'true';
  }

  if (isStarred !== undefined) {
    where.isStarred = isStarred === 'true';
  }

  const [emails, total] = await Promise.all([
    prisma.email.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      include: {
        folder: {
          select: { id: true, name: true, type: true },
        },
        labels: {
          include: {
            label: {
              select: { id: true, name: true, color: true },
            },
          },
        },
      },
    }),
    prisma.email.count({ where }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '获取邮件列表成功',
    data: {
      emails,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取邮件详情
export const getEmailById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const email = await prisma.email.findFirst({
    where: {
      id,
      userId,
      isDeleted: false,
    },
    include: {
      folder: {
        select: { id: true, name: true, type: true },
      },
      labels: {
        include: {
          label: {
            select: { id: true, name: true, color: true },
          },
        },
      },
    },
  });

  if (!email) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  // 如果邮件未读，标记为已读
  if (!email.isRead) {
    await prisma.email.update({
      where: { id },
      data: { isRead: true },
    });
  }

  const response: ApiResponse = {
    success: true,
    message: '获取邮件详情成功',
    data: email,
  };

  res.json(response);
};

// 发送邮件
export const sendEmail = async (req: AuthenticatedRequest, res: Response) => {
  const {
    to,
    cc,
    bcc,
    subject,
    content,
    attachments,
    isDraft = false,
    trackingOptions = {}
  } = req.body;
  const userId = req.user!.id;

  try {
    // 构建邮件数据
    const emailData: EmailData = {
      to: to.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      cc: cc?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      bcc: bcc?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      subject,
      html: content,
      attachments: attachments || [],
    };

    // 验证邮件数据
    const validation = validateEmailData(emailData);
    if (!validation.isValid) {
      throw new AppError(`邮件数据验证失败: ${validation.errors.join(', ')}`, 400);
    }

    if (isDraft) {
      // 保存草稿
      const draft = await saveDraft(userId, emailData);

      const response: ApiResponse = {
        success: true,
        message: '草稿保存成功',
        data: draft,
      };

      return res.json(response);
    }

    // 发送邮件
    const user = req.user!;
    const messageId = await sendEmailService(emailData, {
      email: user.email,
      name: user.displayName || user.username,
    });

    // 保存到数据库
    const savedEmail = await saveEmailToDatabase(userId, emailData, messageId, 'sent', trackingOptions);

    // 触发收件人的实时邮件同步
    const allRecipients = [
      ...emailData.to.map(r => r.email),
      ...(emailData.cc || []).map(r => r.email),
      ...(emailData.bcc || []).map(r => r.email)
    ];

    // 为每个收件人触发实时同步
    for (const recipientEmail of allRecipients) {
      realTimeEmailService.triggerEmailSyncForRecipient(recipientEmail);
    }

    const response: ApiResponse = {
      success: true,
      message: '邮件发送成功',
      data: savedEmail,
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(`邮件发送失败: ${(error as Error).message}`, 500);
  }
};

// 更新邮件状态
export const updateEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { isRead, isStarred, folderId } = req.body;
  const userId = req.user!.id;

  // 先获取邮件信息（用于IMAP同步）
  const emailInfo = await prisma.email.findFirst({
    where: {
      id,
      userId,
      isDeleted: false,
    },
    select: {
      messageId: true,
      subject: true,
      isRead: true, // 获取当前状态
    },
  });

  if (!emailInfo) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  const updateData: any = {};
  if (isRead !== undefined) updateData.isRead = isRead;
  if (isStarred !== undefined) updateData.isStarred = isStarred;
  if (folderId !== undefined) updateData.folderId = folderId;

  const email = await prisma.email.updateMany({
    where: {
      id,
      userId,
      isDeleted: false,
    },
    data: updateData,
  });

  if (email.count === 0) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  // 先返回响应给前端
  const response: ApiResponse = {
    success: true,
    message: '邮件状态更新成功',
  };

  res.json(response);

  // 重新启用IMAP同步（已优化超时控制）
  if (isRead !== undefined && emailInfo.messageId) {
    const userEmail = req.user!.email;

    logger.info(`📝 邮件状态已在数据库中更新: ${emailInfo.messageId} (用户: ${userEmail}, 已读: ${isRead})`);

    // 异步同步到IMAP服务器（不阻塞响应）
    setImmediate(async () => {
      try {
        logger.info(`🔄 开始异步同步邮件状态到IMAP: ${emailInfo.messageId}`);

        if (isRead) {
          await markEmailAsRead(emailInfo.messageId, userEmail);
        } else {
          await markEmailAsUnread(emailInfo.messageId, userEmail);
        }

        logger.info(`✅ 邮件 ${emailInfo.messageId} 已读状态已同步到IMAP (用户: ${userEmail}, 已读: ${isRead})`);
      } catch (error) {
        logger.error(`❌ 异步同步邮件状态到IMAP失败 (${emailInfo.messageId}):`, error);
        // 不抛出错误，避免影响用户体验
      }
    });
  }
};

// 删除邮件
export const deleteEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const email = await prisma.email.updateMany({
    where: {
      id,
      userId,
    },
    data: {
      isDeleted: true,
    },
  });

  if (email.count === 0) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  // 通过WebSocket通知邮件删除
  io.to(`user_${userId}`).emit('emailDeleted', {
    emailId: id,
  });

  const response: ApiResponse = {
    success: true,
    message: '邮件删除成功',
  };

  res.json(response);
};

// 批量操作邮件
export const batchUpdateEmails = async (req: AuthenticatedRequest, res: Response) => {
  const { emailIds, action, value } = req.body;
  const userId = req.user!.id;

  const updateData: any = {};
  
  switch (action) {
    case 'markRead':
      updateData.isRead = true;
      break;
    case 'markUnread':
      updateData.isRead = false;
      break;
    case 'star':
      updateData.isStarred = true;
      break;
    case 'unstar':
      updateData.isStarred = false;
      break;
    case 'move':
      updateData.folderId = value;
      break;
    case 'delete':
      updateData.isDeleted = true;
      break;
    default:
      return res.status(400).json({
        success: false,
        message: '无效的操作类型',
      });
  }

  const updatedEmails = await prisma.email.updateMany({
    where: {
      id: { in: emailIds },
      userId,
    },
    data: updateData,
  });

  // 先返回响应给前端
  const response: ApiResponse = {
    success: true,
    message: '批量操作成功',
  };

  res.json(response);

  // 异步同步到IMAP（不阻塞响应）
  if (action === 'markRead' || action === 'markUnread') {
    const userEmail = req.user!.email;
    const isRead = action === 'markRead';

    setImmediate(async () => {
      try {
        // 获取需要同步的邮件信息
        const emailsToSync = await prisma.email.findMany({
          where: {
            id: { in: emailIds },
            userId,
          },
          select: {
            messageId: true,
            subject: true,
          },
        });

        logger.info(`🔄 开始批量异步同步 ${emailsToSync.length} 封邮件状态到IMAP`);

        // 异步同步到IMAP
        await Promise.all(
          emailsToSync.map(async (email) => {
            if (email.messageId) {
              try {
                if (isRead) {
                  await markEmailAsRead(email.messageId, userEmail);
                } else {
                  await markEmailAsUnread(email.messageId, userEmail);
                }
                logger.info(`✅ 批量操作：邮件 ${email.messageId} 已读状态已同步到IMAP`);
              } catch (error) {
                logger.error(`❌ 批量操作：同步邮件 ${email.messageId} 状态到IMAP失败:`, error);
              }
            }
          })
        );

        logger.info(`✅ 批量异步同步完成`);
      } catch (error) {
        logger.error('❌ 批量异步同步邮件状态到IMAP失败:', error);
      }
    });
  }
};

// 搜索邮件
export const searchEmails = async (req: AuthenticatedRequest, res: Response) => {
  const {
    query,
    from,
    to,
    subject,
    dateFrom,
    dateTo,
    folderType,
    isStarred,
    page = 1,
    limit = 20
  }: EmailSearchParams & PaginationParams = req.body;

  const userId = req.user!.id;
  // 确保 page 和 limit 是数字
  const pageNum = parseInt(String(page)) || 1;
  const limitNum = parseInt(String(limit)) || 20;
  const skip = (pageNum - 1) * limitNum;

  const where: any = {
    userId,
  };

  // 特殊处理垃圾箱：显示已删除的邮件
  if (folderType === 'trash') {
    where.isDeleted = true;
  } else {
    where.isDeleted = false;
  }

  // 星标邮件过滤
  if (isStarred !== undefined) {
    where.isStarred = isStarred;
  }

  // 构建搜索条件
  if (query) {
    where.OR = [
      { subject: { contains: query } },
      { contentText: { contains: query } } || null,
      { contentHtml: { contains: query } } || null,
    ];
  }

  if (from) {
    where.senderEmail = { contains: from };
  }

  if (subject) {
    where.subject = { contains: subject };
  }

  if (dateFrom || dateTo) {
    where.receivedAt = {};
    if (dateFrom) where.receivedAt.gte = new Date(dateFrom);
    if (dateTo) where.receivedAt.lte = new Date(dateTo);
  }

  const [emails, total] = await Promise.all([
    prisma.email.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { receivedAt: 'desc' },
      include: {
        folder: {
          select: { id: true, name: true, type: true },
        },
      },
    }),
    prisma.email.count({ where }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '邮件搜索成功',
    data: {
      emails,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 标记为已读/未读
export const markAsRead = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { isRead = true } = req.body;
  const userId = req.user!.id;

  // 获取邮件信息
  const emailInfo = await prisma.email.findFirst({
    where: {
      id,
      userId,
      isDeleted: false,
    },
    select: {
      messageId: true,
      subject: true,
    },
  });

  if (!emailInfo) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  // 更新数据库
  const email = await prisma.email.updateMany({
    where: {
      id,
      userId,
      isDeleted: false,
    },
    data: { isRead },
  });

  if (email.count === 0) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  // 同步状态到IMAP服务器
  if (emailInfo.messageId) {
    try {
      const userEmail = req.user!.email;
      if (isRead) {
        await markEmailAsRead(emailInfo.messageId, userEmail);
      } else {
        await markEmailAsUnread(emailInfo.messageId, userEmail);
      }
      logger.info(`✅ 邮件 ${emailInfo.messageId} 已读状态已同步到IMAP (用户: ${userEmail}, 已读: ${isRead})`);
    } catch (error) {
      logger.error('同步邮件状态到IMAP失败:', error);
      // 不抛出错误，避免影响前端操作
    }
  }

  // 通过WebSocket通知邮件状态更新
  io.to(`user_${userId}`).emit('emailRead', {
    emailId: id,
    isRead,
  });

  const response: ApiResponse = {
    success: true,
    message: `邮件已标记为${isRead ? '已读' : '未读'}`,
  };

  res.json(response);
};

// 标记为星标/取消星标
export const toggleStar = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { isStarred } = req.body;
  const userId = req.user!.id;

  const email = await prisma.email.updateMany({
    where: {
      id,
      userId,
      isDeleted: false,
    },
    data: { isStarred },
  });

  if (email.count === 0) {
    return res.status(404).json({
      success: false,
      message: '邮件不存在',
    });
  }

  const response: ApiResponse = {
    success: true,
    message: `邮件${isStarred ? '已加星标' : '已取消星标'}`,
  };

  res.json(response);
};

// 同步邮件
export const syncEmails = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 使用 syncUserEmailsViaImapFlow 来同步并保存邮件到数据库
    const syncResult = await syncUserEmailsViaImapFlow(userEmail);

    const response: ApiResponse = {
      success: syncResult.success,
      message: syncResult.success
        ? `同步完成，获取到 ${syncResult.newEmails} 封新邮件`
        : `邮件同步失败: ${syncResult.error}`,
      data: {
        count: syncResult.newEmails,
        success: syncResult.success,
        error: syncResult.error,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`邮件同步失败: ${(error as Error).message}`, 500);
  }
};

// 检查现有未读邮件（用户上线时）
export const checkExistingUnreadEmails = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 手动触发检查现有未读邮件
    await onDemandEmailSyncService.manualCheckExistingEmails(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `已触发检查用户 ${userEmail} 的现有未读邮件`,
      data: {
        userEmail,
        timestamp: new Date().toISOString()
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`检查现有未读邮件失败: ${(error as Error).message}`, 500);
  }
};

// 获取用户邮件统计信息
export const getUserEmailStats = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 触发统计信息获取
    await onDemandEmailSyncService.getUserEmailStats(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `已获取用户 ${userEmail} 的邮件统计信息，请查看日志`,
      data: {
        userEmail,
        timestamp: new Date().toISOString()
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取邮件统计失败: ${(error as Error).message}`, 500);
  }
};

// 测试邮件状态同步到IMAP
export const testEmailStatusSync = async (req: AuthenticatedRequest, res: Response) => {
  const { messageId, isRead } = req.body;
  const userEmail = req.user!.email;

  try {
    if (isRead) {
      await markEmailAsRead(messageId, userEmail);
    } else {
      await markEmailAsUnread(messageId, userEmail);
    }

    const response: ApiResponse = {
      success: true,
      message: `邮件 ${messageId} 状态已同步到IMAP (已读: ${isRead})`,
      data: {
        messageId,
        isRead,
        userEmail,
        timestamp: new Date().toISOString()
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`测试邮件状态同步失败: ${(error as Error).message}`, 500);
  }
};

// 调试邮件MessageId
export const debugEmailMessageId = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.params;
  const userId = req.user!.id;

  try {
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
      select: {
        id: true,
        messageId: true,
        subject: true,
        senderEmail: true,
        isRead: true,
        receivedAt: true,
      },
    });

    if (!email) {
      return res.status(404).json({
        success: false,
        message: '邮件不存在',
      });
    }

    const response: ApiResponse = {
      success: true,
      message: '邮件信息获取成功',
      data: {
        email,
        messageIdLength: email.messageId?.length || 0,
        messageIdHasAngleBrackets: email.messageId?.includes('<') && email.messageId?.includes('>'),
        cleanMessageId: email.messageId?.replace(/[<>]/g, ''),
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取邮件信息失败: ${(error as Error).message}`, 500);
  }
};

// 测试IMAP权限
export const testImapPermissionsController = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应，避免超时
    const response: ApiResponse = {
      success: true,
      message: '权限测试已启动，请查看后端日志',
      data: {
        userEmail,
        status: 'testing',
        timestamp: new Date().toISOString()
      },
    };

    res.json(response);

    // 异步执行权限测试
    setImmediate(async () => {
      try {
        logger.info(`🧪 开始异步IMAP权限测试: ${userEmail}`);
        const result = await testImapPermissions(userEmail);
        logger.info(`🧪 IMAP权限测试结果:`, result);
      } catch (error) {
        logger.error(`🧪 异步IMAP权限测试失败:`, error);
      }
    });

  } catch (error) {
    throw new AppError(`IMAP权限测试失败: ${(error as Error).message}`, 500);
  }
};

// 简化的邮件状态同步测试
export const testSimpleEmailSync = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.body;
  const userEmail = req.user!.email;
  const userId = req.user!.id;

  try {
    // 获取邮件信息
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
      select: {
        id: true,
        messageId: true,
        subject: true,
        isRead: true,
      },
    });

    if (!email) {
      return res.status(404).json({
        success: false,
        message: '邮件不存在',
      });
    }

    logger.info(`🧪 开始简化测试邮件同步: ${email.messageId}`);

    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '开始测试邮件同步',
      data: {
        emailId,
        messageId: email.messageId,
        currentStatus: email.isRead ? '已读' : '未读',
      },
    };

    res.json(response);

    // 异步测试同步
    setImmediate(async () => {
      try {
        logger.info(`🔄 测试：尝试将邮件标记为已读: ${email.messageId}`);
        await markEmailAsRead(email.messageId, userEmail);
        logger.info(`✅ 测试：邮件同步完成`);
      } catch (error) {
        logger.error(`❌ 测试：邮件同步失败:`, error);
      }
    });

  } catch (error) {
    throw new AppError(`测试邮件同步失败: ${(error as Error).message}`, 500);
  }
};

// 获取用户邮件ID列表（调试用）
export const getEmailIds = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    const emails = await prisma.email.findMany({
      where: {
        userId,
        isDeleted: false,
      },
      select: {
        id: true,
        messageId: true,
        subject: true,
        isRead: true,
        senderEmail: true,
      },
      orderBy: {
        receivedAt: 'desc',
      },
      take: 10, // 只取前10封邮件
    });

    const response: ApiResponse = {
      success: true,
      message: `找到 ${emails.length} 封邮件`,
      data: emails,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取邮件ID失败: ${(error as Error).message}`, 500);
  }
};

// 详细的IMAP同步调试
export const debugImapSync = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.body;
  const userEmail = req.user!.email;
  const userId = req.user!.id;

  try {
    // 获取邮件信息
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
      select: {
        id: true,
        messageId: true,
        subject: true,
        isRead: true,
        imapUid: true,
        imapFlags: true,
      },
    });

    if (!email) {
      return res.status(404).json({
        success: false,
        message: '邮件不存在',
      });
    }

    logger.info(`🔍 开始详细IMAP同步调试:`);
    logger.info(`📧 邮件信息: ID=${email.id}, MessageId=${email.messageId}`);
    logger.info(`📊 数据库状态: isRead=${email.isRead}, imapUid=${email.imapUid}`);
    logger.info(`🏷️ 数据库标志: ${email.imapFlags || '无'}`);

    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '开始详细IMAP同步调试',
      data: {
        emailId,
        messageId: email.messageId,
        currentStatus: email.isRead ? '已读' : '未读',
        imapUid: email.imapUid,
        imapFlags: email.imapFlags,
      },
    };

    res.json(response);

    // 异步执行详细调试
    setImmediate(async () => {
      try {
        logger.info(`🔄 开始异步IMAP调试...`);

        // 这里我们可以添加更详细的IMAP检查
        // 比如直接检查文件系统、验证MessageId等

        await markEmailAsRead(email.messageId, userEmail);
        logger.info(`✅ IMAP同步调试完成`);
      } catch (error) {
        logger.error(`❌ IMAP同步调试失败:`, error);
      }
    });

  } catch (error) {
    throw new AppError(`IMAP同步调试失败: ${(error as Error).message}`, 500);
  }
};

// 检查数据库中的MessageId格式
export const checkMessageIds = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    const emails = await prisma.email.findMany({
      where: {
        userId,
        isDeleted: false,
      },
      select: {
        id: true,
        messageId: true,
        subject: true,
        isRead: true,
        imapUid: true,
        imapFlags: true,
      },
      orderBy: {
        receivedAt: 'desc',
      },
      take: 5,
    });

    logger.info(`📊 数据库中的邮件MessageId信息:`);
    emails.forEach((email, index) => {
      logger.info(`${index + 1}. ID: ${email.id}`);
      logger.info(`   MessageId: "${email.messageId}"`);
      logger.info(`   Subject: "${email.subject}"`);
      logger.info(`   IsRead: ${email.isRead}`);
      logger.info(`   ImapUid: ${email.imapUid}`);
      logger.info(`   ImapFlags: ${email.imapFlags || '无'}`);
      logger.info(`   MessageId长度: ${email.messageId?.length || 0}`);
      logger.info(`---`);
    });

    const response: ApiResponse = {
      success: true,
      message: `检查了 ${emails.length} 封邮件的MessageId`,
      data: emails.map(email => ({
        id: email.id,
        messageId: email.messageId,
        messageIdLength: email.messageId?.length || 0,
        subject: email.subject?.substring(0, 50) + '...',
        isRead: email.isRead,
        imapUid: email.imapUid,
        imapFlags: email.imapFlags,
      })),
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`检查MessageId失败: ${(error as Error).message}`, 500);
  }
};

// 检查邮件文件系统状态
export const checkEmailFiles = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    const fs = require('fs').promises;
    const path = require('path');

    // 构建邮件目录路径
    const domain = userEmail.split('@')[1];
    const username = userEmail.split('@')[0];
    const mailDir = `/var/mail/vhosts/${domain}/${username}`;
    const curDir = path.join(mailDir, 'cur');

    logger.info(`🔍 检查邮件文件系统: ${curDir}`);

    try {
      const files = await fs.readdir(curDir);
      const emailFiles = files.filter((file: string) => !file.startsWith('.'));

      const fileInfo = emailFiles.slice(0, 10).map((file: string) => {
        const hasSeenFlag = file.includes(':2,S');
        const hasReplyFlag = file.includes('R');
        const hasFlaggedFlag = file.includes('F');

        return {
          filename: file,
          hasSeenFlag,
          hasReplyFlag,
          hasFlaggedFlag,
          flags: file.split(':2,')[1] || '无标志'
        };
      });

      const summary = {
        totalFiles: emailFiles.length,
        readFiles: emailFiles.filter((f: string) => f.includes(':2,S')).length,
        unreadFiles: emailFiles.filter((f: string) => !f.includes(':2,S')).length,
      };

      logger.info(`📊 文件系统统计: 总计=${summary.totalFiles}, 已读=${summary.readFiles}, 未读=${summary.unreadFiles}`);

      const response: ApiResponse = {
        success: true,
        message: '文件系统检查完成',
        data: {
          mailDir: curDir,
          summary,
          files: fileInfo,
        },
      };

      res.json(response);

    } catch (dirError) {
      logger.error(`访问邮件目录失败: ${curDir}`, dirError);

      const response: ApiResponse = {
        success: false,
        message: '无法访问邮件目录',
        data: {
          mailDir: curDir,
          error: dirError.message,
          note: '可能是权限问题或目录不存在'
        },
      };

      res.json(response);
    }

  } catch (error) {
    throw new AppError(`检查邮件文件失败: ${(error as Error).message}`, 500);
  }
};

// 强制IMAP同步测试
export const forceImapSync = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.body;
  const userEmail = req.user!.email;
  const userId = req.user!.id;

  try {
    // 获取邮件信息
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
      select: {
        id: true,
        messageId: true,
        subject: true,
        isRead: true,
      },
    });

    if (!email) {
      return res.status(404).json({
        success: false,
        message: '邮件不存在',
      });
    }

    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '强制IMAP同步已启动',
      data: {
        emailId,
        messageId: email.messageId,
        currentStatus: email.isRead ? '已读' : '未读',
      },
    };

    res.json(response);

    // 立即执行IMAP同步（不使用setImmediate）
    try {
      logger.info(`🚀 强制IMAP同步开始: ${email.messageId}`);
      logger.info(`📧 邮件信息: ID=${email.id}, Subject="${email.subject}"`);
      logger.info(`👤 用户: ${userEmail}`);
      logger.info(`📊 当前状态: ${email.isRead ? '已读' : '未读'}`);

      // 强制标记为已读
      await markEmailAsRead(email.messageId, userEmail);

      logger.info(`✅ 强制IMAP同步完成`);
    } catch (syncError) {
      logger.error(`❌ 强制IMAP同步失败:`, syncError);
    }

  } catch (error) {
    throw new AppError(`强制IMAP同步失败: ${(error as Error).message}`, 500);
  }
};

// 直接IMAP操作测试
export const directImapTest = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '直接IMAP测试已启动',
      data: { userEmail },
    };

    res.json(response);

    // 执行直接IMAP测试
    try {
      logger.info(`🧪 开始直接IMAP测试: ${userEmail}`);

      try {
        const { getOrCreateConnection } = require('../services/imapFlowService');
        logger.info(`✅ 导入 getOrCreateConnection 成功`);

        const { openMailbox, searchEmails } = require('../utils/imapFlowHelper');
        logger.info(`✅ 导入 openMailbox, searchEmails 成功`);

        // 获取IMAP连接
        logger.info(`🔄 正在获取IMAP连接...`);
        const client = await getOrCreateConnection(userEmail);
        logger.info(`✅ IMAP连接获取成功`);

        // 打开收件箱
        logger.info(`🔄 正在打开收件箱...`);
        const mailbox = await openMailbox(client, 'INBOX', { readOnly: false });
        logger.info(`✅ 收件箱打开成功: ${mailbox.exists} 封邮件`);

        // 搜索所有邮件
        logger.info(`🔄 正在搜索邮件...`);
        const allUids = await searchEmails(client, { all: true });
        logger.info(`✅ 搜索成功: 找到 ${allUids.length} 封邮件`);

        if (allUids.length > 0) {
          const testUid = allUids[0];
          logger.info(`🎯 测试邮件 UID: ${testUid}`);

          try {
            // 获取邮件标志
            logger.info(`🔄 正在获取邮件标志...`);
            const messages = client.fetch(testUid.toString(), {
              flags: true,
              uid: true
            });

            let currentFlags: string[] = [];
            for await (const msg of messages) {
              currentFlags = Array.from(msg.flags || []);
              break;
            }

            logger.info(`📋 当前标志: [${currentFlags.join(', ')}]`);

            // 尝试添加 \Seen 标志（带超时控制）
            try {
              logger.info(`🔧 尝试添加 \\Seen 标志到 UID ${testUid}...`);

              // 添加超时控制
              const flagTimeout = 5000; // 5秒超时
              const flagTimeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error(`标志操作超时 (${flagTimeout}ms)`)), flagTimeout);
              });

              const flagPromise = client.messageFlagsAdd([testUid], ['\\Seen']);

              logger.info(`🔄 等待标志操作响应...`);
              const result = await Promise.race([flagPromise, flagTimeoutPromise]);
              logger.info(`🔧 messageFlagsAdd 结果:`, result);

              // 等待一下让操作生效
              logger.info(`⏳ 等待操作生效...`);
              await new Promise(resolve => setTimeout(resolve, 1000));

              // 再次检查标志（也添加超时）
              try {
                logger.info(`🔄 正在检查操作后的标志...`);
                const fetchTimeout = 3000; // 3秒超时
                const fetchTimeoutPromise = new Promise((_, reject) => {
                  setTimeout(() => reject(new Error(`获取标志超时 (${fetchTimeout}ms)`)), fetchTimeout);
                });

                const fetchPromise = (async () => {
                  const afterMessages = client.fetch(testUid.toString(), {
                    flags: true,
                    uid: true
                  });

                  let newFlags: string[] = [];
                  for await (const msg of afterMessages) {
                    newFlags = Array.from(msg.flags || []);
                    break;
                  }
                  return newFlags;
                })();

                const newFlags = await Promise.race([fetchPromise, fetchTimeoutPromise]) as string[];
                logger.info(`📋 操作后标志: [${newFlags.join(', ')}]`);

                const success = newFlags.includes('\\Seen');
                logger.info(`${success ? '✅' : '❌'} 标志操作${success ? '成功' : '失败'}`);

              } catch (fetchError) {
                logger.error(`❌ 检查操作后标志失败:`, fetchError);
              }

            } catch (flagError) {
              logger.error(`❌ 标志操作失败:`, flagError);
            }
          } catch (fetchError) {
            logger.error(`❌ 获取邮件信息失败:`, fetchError);
          }
        } else {
          logger.warn(`⚠️ 邮箱中没有邮件可供测试`);
        }

        logger.info(`✅ 直接IMAP测试完成`);

      } catch (importError) {
        logger.error(`❌ 导入模块失败:`, importError);
      }

    } catch (testError) {
      logger.error(`❌ 直接IMAP测试失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`直接IMAP测试失败: ${(error as Error).message}`, 500);
  }
};

// 简化的IMAP连接测试
export const simpleImapTest = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '简化IMAP测试已启动',
      data: { userEmail },
    };

    res.json(response);

    // 执行简化IMAP测试
    try {
      logger.info(`🧪 开始简化IMAP连接测试: ${userEmail}`);

      const { getOrCreateConnection } = require('../services/imapFlowService');
      logger.info(`✅ 导入 getOrCreateConnection 成功`);

      // 获取IMAP连接
      logger.info(`🔄 正在获取IMAP连接...`);
      const client = await getOrCreateConnection(userEmail);
      logger.info(`✅ IMAP连接获取成功`);

      // 检查连接状态
      logger.info(`🔍 检查连接状态...`);
      logger.info(`📊 连接可用性: ${client.usable}`);
      logger.info(`📊 连接状态: ${client.authenticated ? '已认证' : '未认证'}`);

      if (!client.usable) {
        logger.error(`❌ IMAP连接不可用`);
        return;
      }

      // 尝试简单的操作 - 获取能力
      try {
        logger.info(`🔄 正在获取服务器能力...`);
        const capabilities = Array.from(client.capabilities || []);
        logger.info(`✅ 服务器能力: [${capabilities.slice(0, 5).join(', ')}...]`);
      } catch (capError) {
        logger.error(`❌ 获取服务器能力失败:`, capError);
      }

      // 尝试列出邮箱
      try {
        logger.info(`🔄 正在列出邮箱...`);
        const mailboxes = await client.list();
        logger.info(`✅ 找到 ${mailboxes.length} 个邮箱`);
        mailboxes.slice(0, 3).forEach((mb: any) => {
          logger.info(`📁 邮箱: ${mb.path} (分隔符: ${mb.delimiter})`);
        });
      } catch (listError) {
        logger.error(`❌ 列出邮箱失败:`, listError);
      }

      logger.info(`✅ 简化IMAP测试完成`);

    } catch (testError) {
      logger.error(`❌ 简化IMAP测试失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`简化IMAP测试失败: ${(error as Error).message}`, 500);
  }
};

// 最基础的IMAP连接测试
export const basicImapTest = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '基础IMAP测试已启动',
      data: { userEmail },
    };

    res.json(response);

    // 执行最基础的IMAP测试
    try {
      logger.info(`🧪 开始最基础IMAP连接测试: ${userEmail}`);

      const { getOrCreateConnection } = require('../services/imapFlowService');

      // 获取IMAP连接
      logger.info(`🔄 正在获取IMAP连接...`);
      const client = await getOrCreateConnection(userEmail);
      logger.info(`✅ IMAP连接获取成功`);

      // 只检查连接状态，不进行任何操作
      logger.info(`🔍 检查连接基本状态...`);
      logger.info(`📊 连接对象存在: ${!!client}`);
      logger.info(`📊 连接可用性: ${client.usable}`);
      logger.info(`📊 连接认证状态: ${client.authenticated ? '已认证' : '未认证'}`);

      if (client.usable) {
        logger.info(`✅ IMAP连接状态正常`);
      } else {
        logger.warn(`⚠️ IMAP连接不可用`);
      }

      logger.info(`✅ 基础IMAP测试完成`);

    } catch (testError) {
      logger.error(`❌ 基础IMAP测试失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`基础IMAP测试失败: ${(error as Error).message}`, 500);
  }
};

// 直接文件系统标志操作测试
export const fileSystemFlagTest = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '文件系统标志测试已启动',
      data: { userEmail },
    };

    res.json(response);

    // 执行文件系统标志测试
    try {
      logger.info(`🧪 开始文件系统标志测试: ${userEmail}`);

      const fs = require('fs').promises;
      const path = require('path');

      // 构建邮件目录路径
      const domain = userEmail.split('@')[1];
      const username = userEmail.split('@')[0];
      const mailDir = `/var/mail/vhosts/${domain}/${username}`;
      const curDir = path.join(mailDir, 'cur');

      logger.info(`📁 邮件目录: ${curDir}`);

      // 检查多个可能的邮件存储位置
      const possiblePaths = [
        curDir,
        path.join(mailDir, 'new'),
        `/var/mail/${username}`,
        `/var/spool/mail/${username}`,
        `/home/<USER>/Maildir/cur`,
        `/home/<USER>/Maildir/new`,
      ];

      logger.info(`🔍 检查可能的邮件存储位置:`);

      let foundFiles = 0;
      let foundPath = '';

      for (const checkPath of possiblePaths) {
        try {
          logger.info(`📂 检查: ${checkPath}`);
          const files = await fs.readdir(checkPath);
          const emailFiles = files.filter((file: string) => !file.startsWith('.'));
          logger.info(`   找到 ${emailFiles.length} 个文件`);

          if (emailFiles.length > 0 && foundFiles === 0) {
            foundFiles = emailFiles.length;
            foundPath = checkPath;
            logger.info(`✅ 找到邮件文件位置: ${checkPath}`);

            // 显示前几个文件名
            emailFiles.slice(0, 3).forEach((file: string) => {
              logger.info(`   📄 ${file}`);
            });
          }

        } catch (pathError) {
          logger.info(`   ❌ 无法访问: ${pathError.message}`);
        }
      }

      if (foundFiles === 0) {
        logger.warn(`⚠️ 在所有可能位置都没有找到邮件文件`);
        return;
      }

      // 使用找到的路径进行测试
      try {
        const files = await fs.readdir(foundPath);
        const emailFiles = files.filter((file: string) => !file.startsWith('.'));

        logger.info(`📊 在 ${foundPath} 找到 ${emailFiles.length} 个邮件文件`);

        if (emailFiles.length > 0) {
          // 选择第一个邮件文件进行测试
          const testFile = emailFiles[0];
          const oldPath = path.join(foundPath, testFile);

          logger.info(`🎯 测试文件: ${testFile}`);

          // 检查文件是否已有 :2,S 标志
          const hasSeenFlag = testFile.includes(':2,S');
          logger.info(`📋 当前状态: ${hasSeenFlag ? '已读' : '未读'}`);

          if (!hasSeenFlag) {
            // 添加 :2,S 标志
            let newFileName: string;
            if (testFile.includes(':2,')) {
              // 已有其他标志，添加S
              newFileName = testFile.replace(':2,', ':2,S');
            } else {
              // 没有标志，添加 :2,S
              newFileName = testFile + ':2,S';
            }

            const newPath = path.join(foundPath, newFileName);

            logger.info(`🔄 重命名文件: ${testFile} -> ${newFileName}`);

            try {
              await fs.rename(oldPath, newPath);
              logger.info(`✅ 文件重命名成功，邮件已标记为已读`);

              // 验证文件是否存在
              try {
                await fs.access(newPath);
                logger.info(`✅ 验证：新文件存在`);
              } catch (accessError) {
                logger.error(`❌ 验证失败：新文件不存在`);
              }

            } catch (renameError) {
              logger.error(`❌ 文件重命名失败:`, renameError);
            }

          } else {
            logger.info(`ℹ️ 文件已经是已读状态`);
          }

        } else {
          logger.warn(`⚠️ 邮件目录中没有邮件文件`);
        }

      } catch (dirError) {
        logger.error(`❌ 访问邮件目录失败:`, dirError);
      }

      logger.info(`✅ 文件系统标志测试完成`);

    } catch (testError) {
      logger.error(`❌ 文件系统标志测试失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`文件系统标志测试失败: ${(error as Error).message}`, 500);
  }
};

// SSH远程文件系统检查
export const sshFileSystemCheck = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: 'SSH文件系统检查已启动',
      data: { userEmail },
    };

    res.json(response);

    // 执行SSH文件系统检查
    try {
      logger.info(`🧪 开始SSH文件系统检查: ${userEmail}`);

      const { exec } = require('child_process');
      const util = require('util');
      const execAsync = util.promisify(exec);

      // 构建邮件目录路径
      const domain = userEmail.split('@')[1];
      const username = userEmail.split('@')[0];
      const mailDir = `/var/mail/vhosts/${domain}/${username}`;

      logger.info(`📁 检查邮件目录: ${mailDir}`);

      // 检查邮件文件
      const commands = [
        `ls -la ${mailDir}/cur/ | head -10`,
        `ls -la ${mailDir}/new/ | head -10`,
        `find ${mailDir} -name "*" -type f | head -5`,
        `ls -la ${mailDir}/cur/ | grep ":2,S" | wc -l`,
        `ls -la ${mailDir}/cur/ | grep -v ":2,S" | wc -l`,
      ];

      for (const command of commands) {
        try {
          logger.info(`🔄 执行命令: ${command}`);
          const { stdout, stderr } = await execAsync(command);

          if (stdout) {
            logger.info(`✅ 输出:`);
            stdout.split('\n').forEach((line: string) => {
              if (line.trim()) {
                logger.info(`   ${line}`);
              }
            });
          }

          if (stderr) {
            logger.warn(`⚠️ 错误: ${stderr}`);
          }

        } catch (cmdError: any) {
          logger.error(`❌ 命令执行失败: ${command}`, cmdError.message);
        }
      }

      logger.info(`✅ SSH文件系统检查完成`);

    } catch (testError) {
      logger.error(`❌ SSH文件系统检查失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`SSH文件系统检查失败: ${(error as Error).message}`, 500);
  }
};

// 替代IMAP标志操作方法
export const alternativeImapFlag = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '替代IMAP标志操作已启动',
      data: { userEmail },
    };

    res.json(response);

    // 执行替代IMAP标志操作
    try {
      logger.info(`🧪 开始替代IMAP标志操作: ${userEmail}`);

      const { getOrCreateConnection } = require('../services/imapFlowService');
      const { openMailbox, searchEmails } = require('../utils/imapFlowHelper');

      // 获取IMAP连接
      const client = await getOrCreateConnection(userEmail);
      logger.info(`✅ IMAP连接获取成功`);

      // 打开收件箱
      const mailbox = await openMailbox(client, 'INBOX', { readOnly: false });
      logger.info(`✅ 收件箱打开成功: ${mailbox.exists} 封邮件`);

      // 搜索所有邮件
      const allUids = await searchEmails(client, { all: true });
      logger.info(`✅ 搜索成功: 找到 ${allUids.length} 封邮件`);

      if (allUids.length > 0) {
        const testUid = allUids[0];
        logger.info(`🎯 测试邮件 UID: ${testUid}`);

        // 尝试不同的标志操作方法
        const methods = [
          {
            name: 'messageFlagsAdd',
            action: () => client.messageFlagsAdd([testUid], ['\\Seen'])
          },
          {
            name: 'messageFlagsSet',
            action: () => client.messageFlagsSet([testUid], ['\\Seen'])
          },
          {
            name: 'messageFlagsSet with uid',
            action: () => client.messageFlagsSet([testUid], ['\\Seen'], { uid: true })
          },
          {
            name: 'store command',
            action: () => client.store([testUid], '+FLAGS', ['\\Seen'])
          }
        ];

        for (const method of methods) {
          try {
            logger.info(`🔧 尝试方法: ${method.name}`);

            // 添加1秒超时
            const methodTimeout = 1000;
            const methodTimeoutPromise = new Promise((_, reject) => {
              setTimeout(() => reject(new Error(`${method.name} 超时`)), methodTimeout);
            });

            const result = await Promise.race([method.action(), methodTimeoutPromise]);
            logger.info(`✅ ${method.name} 成功:`, result);

            // 如果成功，检查标志
            try {
              const messages = client.fetch(testUid.toString(), { flags: true, uid: true });
              let newFlags: string[] = [];
              for await (const msg of messages) {
                newFlags = Array.from(msg.flags || []);
                break;
              }
              logger.info(`📋 操作后标志: [${newFlags.join(', ')}]`);

              if (newFlags.includes('\\Seen')) {
                logger.info(`🎉 成功！${method.name} 方法有效`);
                break;
              }
            } catch (checkError) {
              logger.warn(`⚠️ 检查标志失败:`, checkError);
            }

          } catch (methodError) {
            logger.warn(`❌ ${method.name} 失败:`, methodError.message);
          }
        }
      }

      logger.info(`✅ 替代IMAP标志操作完成`);

    } catch (testError) {
      logger.error(`❌ 替代IMAP标志操作失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`替代IMAP标志操作失败: ${(error as Error).message}`, 500);
  }
};

// 手动IMAP同步测试
export const manualImapSync = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;
  const { messageId, action } = req.body; // action: 'read' | 'unread'

  try {
    // 立即返回响应
    const response: ApiResponse = {
      success: true,
      message: '手动IMAP同步已启动',
      data: { userEmail, messageId, action },
    };

    res.json(response);

    // 执行手动IMAP同步
    try {
      logger.info(`🧪 开始手动IMAP同步: ${messageId} -> ${action}`);

      const { markEmailAsRead, markEmailAsUnread } = require('../services/imapFlowService');

      if (action === 'read') {
        await markEmailAsRead(messageId, userEmail);
        logger.info(`✅ 手动同步成功: 邮件已标记为已读`);
      } else if (action === 'unread') {
        await markEmailAsUnread(messageId, userEmail);
        logger.info(`✅ 手动同步成功: 邮件已标记为未读`);
      } else {
        logger.error(`❌ 无效的操作: ${action}`);
      }

    } catch (testError) {
      logger.error(`❌ 手动IMAP同步失败:`, testError);
    }

  } catch (error) {
    throw new AppError(`手动IMAP同步失败: ${(error as Error).message}`, 500);
  }
};

// 重置用户邮件为未读状态（测试用）
export const resetEmailsToUnread = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;
  const userId = req.user!.id;

  try {
    // 更新数据库中的所有邮件为未读
    const result = await prisma.email.updateMany({
      where: {
        userId: userId,
        isDeleted: false,
      },
      data: {
        isRead: false,
      },
    });

    logger.info(`📧 用户 ${userEmail} 的 ${result.count} 封邮件已重置为未读状态`);

    const response: ApiResponse = {
      success: true,
      message: `成功将 ${result.count} 封邮件重置为未读状态`,
      data: {
        userEmail,
        updatedCount: result.count,
        timestamp: new Date().toISOString()
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`重置邮件状态失败: ${(error as Error).message}`, 500);
  }
};

// 保存草稿
export const saveDraftEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { to, cc, bcc, subject, content, attachments } = req.body;
  const userId = req.user!.id;

  try {
    const emailData: Partial<EmailData> = {
      to: to?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      cc: cc?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      bcc: bcc?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      subject,
      html: content,
      attachments: attachments || [],
    };

    const draft = await saveDraft(userId, emailData);

    const response: ApiResponse = {
      success: true,
      message: '草稿保存成功',
      data: draft,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`保存草稿失败: ${(error as Error).message}`, 500);
  }
};

// 更新草稿
export const updateDraftEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { to, cc, bcc, subject, content, attachments } = req.body;
  const userId = req.user!.id;

  try {
    const emailData: Partial<EmailData> = {
      to: to?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      cc: cc?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      bcc: bcc?.map((recipient: any) => ({
        email: recipient.email,
        name: recipient.name,
      })),
      subject,
      html: content,
      attachments: attachments || [],
    };

    const updatedDraft = await updateDraft(id, userId, emailData);

    const response: ApiResponse = {
      success: true,
      message: '草稿更新成功',
      data: updatedDraft,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新草稿失败: ${(error as Error).message}`, 500);
  }
};

// 同步邮件状态到IMAP
export const syncStatusToIMAP = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    const syncResult = await syncDatabaseStatusToIMAP(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `状态同步完成，同步 ${syncResult.syncedCount} 封邮件`,
      data: {
        syncedCount: syncResult.syncedCount,
        errorCount: syncResult.errorCount,
        syncedEmails: syncResult.syncedEmails,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`邮件状态同步失败: ${(error as Error).message}`, 500);
  }
};

// 获取邮件同步状态
export const getSyncStatus = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    const isActive = emailSyncManager.isUserSyncActive(userEmail);
    const allStatus = emailSyncManager.getSyncStatus();
    const userStatus = allStatus.find(s => s.userEmail === userEmail);

    const response: ApiResponse = {
      success: true,
      message: '获取同步状态成功',
      data: {
        userEmail,
        isActive,
        intervalMinutes: userStatus?.intervalMinutes || 5,
        totalActiveSyncs: emailSyncManager.getActiveSyncCount(),
        allStatus: allStatus,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取同步状态失败: ${(error as Error).message}`, 500);
  }
};

// 启动邮件同步
export const startSync = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;
  const { intervalMinutes = 5 } = req.body;

  try {
    await emailSyncManager.startUserSync(userEmail, intervalMinutes);

    const response: ApiResponse = {
      success: true,
      message: `邮件同步已启动，间隔: ${intervalMinutes} 分钟`,
      data: {
        userEmail,
        intervalMinutes,
        isActive: true,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动邮件同步失败: ${(error as Error).message}`, 500);
  }
};

// 停止邮件同步
export const stopSync = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;

  try {
    emailSyncManager.stopUserSync(userEmail);

    const response: ApiResponse = {
      success: true,
      message: '邮件同步已停止',
      data: {
        userEmail,
        isActive: false,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止邮件同步失败: ${(error as Error).message}`, 500);
  }
};

// 重启邮件同步
export const restartSync = async (req: AuthenticatedRequest, res: Response) => {
  const userEmail = req.user!.email;
  const { intervalMinutes } = req.body;

  try {
    await emailSyncManager.restartUserSync(userEmail, intervalMinutes);

    const response: ApiResponse = {
      success: true,
      message: `邮件同步已重启，间隔: ${intervalMinutes || 5} 分钟`,
      data: {
        userEmail,
        intervalMinutes: intervalMinutes || 5,
        isActive: true,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`重启邮件同步失败: ${(error as Error).message}`, 500);
  }
};

// 启动所有用户的邮件同步（管理员功能）
export const startAllSync = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 检查用户权限（可选：只允许管理员执行）
    // if (req.user!.role !== 'admin') {
    //   throw new AppError('权限不足', 403);
    // }

    await emailSyncManager.startAllUserSync();

    const response: ApiResponse = {
      success: true,
      message: '所有用户邮件同步已启动',
      data: {
        activeSyncCount: emailSyncManager.getActiveSyncCount(),
        syncStatus: emailSyncManager.getSyncStatus(),
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动所有用户邮件同步失败: ${(error as Error).message}`, 500);
  }
};

// 停止所有用户的邮件同步（管理员功能）
export const stopAllSync = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 检查用户权限（可选：只允许管理员执行）
    // if (req.user!.role !== 'admin') {
    //   throw new AppError('权限不足', 403);
    // }

    emailSyncManager.stopAllSync();

    const response: ApiResponse = {
      success: true,
      message: '所有用户邮件同步已停止',
      data: {
        activeSyncCount: 0,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止所有用户邮件同步失败: ${(error as Error).message}`, 500);
  }
};
