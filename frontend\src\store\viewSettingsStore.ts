import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import api from '../config/api';

export type ViewMode = 'list' | 'split';

interface ViewSettingsState {
  // 视图模式设置
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;

  // 分栏模式下的列表宽度
  listWidth: number;
  setListWidth: (width: number) => void;

  // 紧凑模式设置
  isCompactMode: boolean;
  setIsCompactMode: (compact: boolean) => void;

  // 重置所有设置
  resetSettings: () => void;

  // 同步设置到服务器
  syncToServer: () => Promise<void>;

  // 从服务器加载设置
  loadFromServer: () => Promise<void>;
}

const DEFAULT_SETTINGS = {
  viewMode: 'list' as ViewMode,
  listWidth: 400,
  isCompactMode: false,
};

export const useViewSettingsStore = create<ViewSettingsState>()(
  persist(
    (set, get) => ({
      ...DEFAULT_SETTINGS,

      setViewMode: async (mode: ViewMode) => {
        set({ viewMode: mode });
        // 同步到服务器
        try {
          await get().syncToServer();
        } catch (error) {
          console.error('同步视图模式到服务器失败:', error);
        }
      },

      setListWidth: async (width: number) => {
        // 限制宽度范围
        const minWidth = 300;
        const maxWidth = window.innerWidth * 0.7;
        const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, width));
        set({ listWidth: constrainedWidth });
        // 同步到服务器
        try {
          await get().syncToServer();
        } catch (error) {
          console.error('同步列表宽度到服务器失败:', error);
        }
      },

      setIsCompactMode: async (compact: boolean) => {
        set({ isCompactMode: compact });
        // 同步到服务器
        try {
          await get().syncToServer();
        } catch (error) {
          console.error('同步紧凑模式到服务器失败:', error);
        }
      },

      resetSettings: async () => {
        set(DEFAULT_SETTINGS);
        // 同步到服务器
        try {
          await get().syncToServer();
        } catch (error) {
          console.error('重置设置到服务器失败:', error);
        }
      },

      syncToServer: async () => {
        const state = get();
        try {
          await api.put('/user-preferences', {
            viewMode: state.viewMode,
            listWidth: state.listWidth,
            compactMode: state.isCompactMode,
          });
        } catch (error) {
          console.error('同步设置到服务器失败:', error);
          throw error;
        }
      },

      loadFromServer: async () => {
        try {
          const response = await api.get('/user-preferences');
          const preferences = response.data.data;

          if (preferences) {
            set({
              viewMode: preferences.viewMode || DEFAULT_SETTINGS.viewMode,
              listWidth: preferences.listWidth || DEFAULT_SETTINGS.listWidth,
              isCompactMode: preferences.compactMode || DEFAULT_SETTINGS.isCompactMode,
            });
          }
        } catch (error) {
          console.error('从服务器加载设置失败:', error);
          // 使用默认设置
          set(DEFAULT_SETTINGS);
        }
      },
    }),
    {
      name: 'email-view-settings', // localStorage key
      version: 1,
    }
  )
);

// 计算紧凑模式的辅助函数
export const calculateCompactMode = (viewMode: ViewMode, listWidth: number): boolean => {
  if (viewMode === "list") {
    // 列表模式：基于窗口宽度
    return window.innerWidth <= 768;
  } else {
    // 分栏模式：基于列表面板宽度
    return listWidth <= 600;
  }
};
