import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export type ViewMode = 'list' | 'split';

interface ViewSettingsState {
  // 视图模式设置
  viewMode: ViewMode;
  setViewMode: (mode: ViewMode) => void;
  
  // 分栏模式下的列表宽度
  listWidth: number;
  setListWidth: (width: number) => void;
  
  // 紧凑模式设置
  isCompactMode: boolean;
  setIsCompactMode: (compact: boolean) => void;
  
  // 重置所有设置
  resetSettings: () => void;
}

const DEFAULT_SETTINGS = {
  viewMode: 'split' as ViewMode,
  listWidth: 400,
  isCompactMode: false,
};

export const useViewSettingsStore = create<ViewSettingsState>()(
  persist(
    (set, get) => ({
      ...DEFAULT_SETTINGS,
      
      setViewMode: (mode: ViewMode) => {
        set({ viewMode: mode });
      },
      
      setListWidth: (width: number) => {
        // 限制宽度范围
        const minWidth = 300;
        const maxWidth = window.innerWidth * 0.7;
        const constrainedWidth = Math.max(minWidth, Math.min(maxWidth, width));
        set({ listWidth: constrainedWidth });
      },
      
      setIsCompactMode: (compact: boolean) => {
        set({ isCompactMode: compact });
      },
      
      resetSettings: () => {
        set(DEFAULT_SETTINGS);
      },
    }),
    {
      name: 'email-view-settings', // localStorage key
      version: 1,
    }
  )
);

// 计算紧凑模式的辅助函数
export const calculateCompactMode = (viewMode: ViewMode, listWidth: number): boolean => {
  if (viewMode === "list") {
    // 列表模式：基于窗口宽度
    return window.innerWidth <= 768;
  } else {
    // 分栏模式：基于列表面板宽度
    return listWidth <= 600;
  }
};
