{"name": "email-system-backend", "version": "1.0.0", "description": "Backend API for Email System", "main": "dist/index.js", "scripts": {"build": "npm run clean && tsc --noCheck --skipLib<PERSON><PERSON><PERSON>", "build:check": "npm run clean && tsc", "build:dev": "npm run clean && tsc --noCheck --skipLib<PERSON><PERSON><PERSON>", "build:staging": "npm run clean && cross-env NODE_ENV=staging tsc --noCheck --skipLibCheck", "build:prod": "npm run clean && cross-env NODE_ENV=production tsc --noCheck --skipLibCheck --removeComments", "start": "node dist/index.js", "start:dev": "cross-env NODE_ENV=development node dist/index.js", "start:staging": "cross-env NODE_ENV=staging node dist/index.js", "start:prod": "cross-env NODE_ENV=production node dist/index.js", "start:prod:simple": "node dist/index.js", "dev": "nodemon", "test": "cross-env NODE_ENV=test DATABASE_URL=\"mysql://mailadmin:HOUsc@<EMAIL>:3306/mailserver_test\" jest", "test:watch": "cross-env NODE_ENV=test DATABASE_URL=\"mysql://mailadmin:HOUsc@<EMAIL>:3306/mailserver_test\" jest --watch", "test:coverage": "cross-env NODE_ENV=test DATABASE_URL=\"mysql://mailadmin:HOUsc@<EMAIL>:3306/mailserver_test\" jest --coverage", "test:e2e": "cross-env NODE_ENV=test DATABASE_URL=\"mysql://mailadmin:HOUsc@<EMAIL>:3306/mailserver_test\" jest --config jest.e2e.config.js", "test:setup": "node scripts/setup-test-db.js", "test:init": "npm run test:setup && npm run test", "test:clean": "cross-env NODE_ENV=test node -e \"require('./dist/test/seedData').cleanTestSeedData()\"", "test:password": "node scripts/test-password-generation.js", "test:password:ssh": "node scripts/test-password-generation.js --real-ssh", "test:password-strategy": "node scripts/test-password-strategy.js", "test:system-emails": "node scripts/test-system-emails.js", "test:user-management": "node scripts/test-user-management.js", "test:system-monitoring": "node scripts/test-system-monitoring.js", "test:delete-user": "node scripts/test-delete-user.js", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "type-check": "tsc --noEmit", "clean": "<PERSON><PERSON><PERSON> dist", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:migrate:prod": "prisma migrate deploy", "db:seed": "ts-node prisma/seed.ts", "db:init-admin": "node scripts/init-admin.js", "db:studio": "prisma studio", "db:reset": "prisma migrate reset --force", "docker:build": "docker build -t email-system-backend .", "docker:run": "docker run -p 5000:5000 email-system-backend"}, "keywords": ["email", "nodejs", "express", "typescript"], "author": "", "license": "MIT", "dependencies": {"@prisma/client": "^6.10.1", "@types/express": "^5.0.3", "@types/mailparser": "^3.4.6", "@types/node-cron": "^3.0.11", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.1.0", "imapflow": "^1.0.191", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "libmime": "^5.3.7", "mailparser": "^3.7.3", "morgan": "^1.10.0", "multer": "^2.0.1", "mysql2": "^3.14.2", "node-cron": "^4.2.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.3", "otplib": "^12.0.1", "prisma": "^6.10.1", "recharts": "^3.1.0", "redis": "^5.5.6", "socket.io": "^4.8.1", "winston": "^3.17.0"}, "devDependencies": {"@types/bcryptjs": "^3.0.0", "@types/cors": "^2.8.19", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/libmime": "^5.0.3", "@types/morgan": "^1.9.10", "@types/multer": "^1.4.13", "@types/node": "^24.0.3", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "cross-env": "^7.0.3", "eslint": "^8.57.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "rimraf": "^5.0.5", "supertest": "^6.3.4", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}