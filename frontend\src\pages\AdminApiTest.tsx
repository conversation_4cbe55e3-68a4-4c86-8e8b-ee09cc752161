import React, { useState } from 'react';
import { <PERSON>, Button, <PERSON>pography, Alert, Space, Divider } from 'antd';
import { ApiOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import api from '../config/api';

const { Title, Text, Paragraph } = Typography;

interface TestResult {
  name: string;
  success: boolean;
  message: string;
  data?: any;
}

const AdminApiTest: React.FC = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<TestResult[]>([]);

  const runTests = async () => {
    setTesting(true);
    setResults([]);
    
    const testResults: TestResult[] = [];

    // 测试1: 获取用户列表
    try {
      const response = await api.get('/admin/users', { 
        params: { page: 1, limit: 10 } 
      });
      testResults.push({
        name: '获取用户列表',
        success: true,
        message: `成功获取 ${response.data.data?.length || 0} 个用户`,
        data: response.data
      });
    } catch (error: any) {
      testResults.push({
        name: '获取用户列表',
        success: false,
        message: error.response?.data?.message || error.message || '请求失败'
      });
    }

    // 测试2: 检查API基础路径
    try {
      const response = await api.get('/health');
      testResults.push({
        name: '健康检查',
        success: true,
        message: '服务器连接正常',
        data: response.data
      });
    } catch (error: any) {
      testResults.push({
        name: '健康检查',
        success: false,
        message: error.response?.data?.message || error.message || '服务器连接失败'
      });
    }

    // 测试3: 检查认证状态
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        testResults.push({
          name: '认证检查',
          success: false,
          message: '未找到访问令牌，请先登录'
        });
      } else {
        testResults.push({
          name: '认证检查',
          success: true,
          message: '访问令牌存在',
          data: { tokenLength: token.length }
        });
      }
    } catch (error: any) {
      testResults.push({
        name: '认证检查',
        success: false,
        message: '认证检查失败'
      });
    }

    setResults(testResults);
    setTesting(false);
  };

  const getResultIcon = (success: boolean) => {
    return success ? 
      <CheckCircleOutlined style={{ color: '#52c41a' }} /> : 
      <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
  };

  return (
    <div style={{ 
      padding: '24px',
      minHeight: '100vh',
      backgroundColor: '#f5f5f5'
    }}>
      <Card>
        <Title level={3}>
          <ApiOutlined style={{ marginRight: '8px' }} />
          管理员API测试
        </Title>
        
        <Paragraph>
          此页面用于测试管理员API的连接状态和功能。点击下方按钮开始测试。
        </Paragraph>

        <Alert
          message="测试说明"
          description="此测试将检查API连接、认证状态和基本功能。请确保您已经登录并具有管理员权限。"
          type="info"
          showIcon
          style={{ marginBottom: '24px' }}
        />

        <Space style={{ marginBottom: '24px' }}>
          <Button 
            type="primary" 
            onClick={runTests}
            loading={testing}
            size="large"
          >
            {testing ? '测试中...' : '开始测试'}
          </Button>
        </Space>

        {results.length > 0 && (
          <>
            <Divider />
            <Title level={4}>测试结果</Title>
            
            {results.map((result, index) => (
              <Card 
                key={index}
                size="small" 
                style={{ marginBottom: '16px' }}
                title={
                  <Space>
                    {getResultIcon(result.success)}
                    <Text strong>{result.name}</Text>
                  </Space>
                }
              >
                <Paragraph>
                  <Text type={result.success ? 'success' : 'danger'}>
                    {result.message}
                  </Text>
                </Paragraph>
                
                {result.data && (
                  <details>
                    <summary style={{ cursor: 'pointer', marginBottom: '8px' }}>
                      <Text type="secondary">查看详细数据</Text>
                    </summary>
                    <pre style={{ 
                      background: '#f5f5f5', 
                      padding: '12px', 
                      borderRadius: '4px',
                      fontSize: '12px',
                      overflow: 'auto',
                      maxHeight: '200px'
                    }}>
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </Card>
            ))}

            <Divider />
            
            <Alert
              message={`测试完成: ${results.filter(r => r.success).length}/${results.length} 项通过`}
              type={results.every(r => r.success) ? 'success' : 'warning'}
              showIcon
            />
          </>
        )}

        <Divider />
        
        <Title level={4}>调试信息</Title>
        <Card size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            <div>
              <Text strong>API基础URL: </Text>
              <Text code>{import.meta.env.VITE_API_BASE_URL || '未设置'}</Text>
            </div>
            <div>
              <Text strong>当前环境: </Text>
              <Text code>{import.meta.env.MODE}</Text>
            </div>
            <div>
              <Text strong>访问令牌: </Text>
              <Text code>
                {localStorage.getItem('accessToken') ? '已设置' : '未设置'}
              </Text>
            </div>
            <div>
              <Text strong>当前URL: </Text>
              <Text code>{window.location.href}</Text>
            </div>
          </Space>
        </Card>
      </Card>
    </div>
  );
};

export default AdminApiTest;
