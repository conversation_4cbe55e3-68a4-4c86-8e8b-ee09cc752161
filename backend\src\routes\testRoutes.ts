import express from 'express';
import { testEmailParsing, getSampleEmailContent, testEmailCleaning } from '../controllers/testController';
import { authenticateToken } from '../middleware/auth';

const router = express.Router();

/**
 * 测试路由 - 仅在开发环境中启用
 */

// 测试邮件内容解析
router.post('/email-parsing', authenticateToken, testEmailParsing);

// 获取示例邮件内容
router.get('/sample-emails', authenticateToken, getSampleEmailContent);

// 测试邮件内容清理
router.post('/email-cleaning', authenticateToken, testEmailCleaning);

export default router;
