import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';

// 获取文件夹列表
export const getFolders = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  const folders = await prisma.folder.findMany({
    where: { userId },
    orderBy: [
      { type: 'asc' }, // 系统文件夹优先
      { createdAt: 'asc' },
    ],
    include: {
      _count: {
        select: {
          emails: {
            where: {
              isDeleted: false,
            },
          },
        },
      },
      children: {
        include: {
          _count: {
            select: {
              emails: {
                where: {
                  isDeleted: false,
                },
              },
            },
          },
        },
      },
    },
  });

  // 构建树形结构
  const folderTree = folders.filter(folder => !folder.parentId);
  
  const response: ApiResponse = {
    success: true,
    message: '获取文件夹列表成功',
    data: folderTree,
  };

  res.json(response);
};

// 获取文件夹详情
export const getFolderById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const folder = await prisma.folder.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
    include: {
      _count: {
        select: {
          emails: {
            where: {
              isDeleted: false,
            },
          },
        },
      },
      parent: {
        select: {
          id: true,
          name: true,
        },
      },
      children: {
        include: {
          _count: {
            select: {
              emails: {
                where: {
                  isDeleted: false,
                },
              },
            },
          },
        },
      },
    },
  });

  if (!folder) {
    throw new AppError('文件夹不存在', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: '获取文件夹详情成功',
    data: folder,
  };

  res.json(response);
};

// 创建文件夹
export const createFolder = async (req: AuthenticatedRequest, res: Response) => {
  const { name, type = 'custom', parentId } = req.body;
  const userId = req.user!.id;

  // 检查同级文件夹名称是否重复
  const existingFolder = await prisma.folder.findFirst({
    where: {
      userId,
      name,
      parentId: parentId || null,
    },
  });

  if (existingFolder) {
    throw new AppError('同级目录下已存在同名文件夹', 409);
  }

  // 如果指定了父文件夹，检查父文件夹是否存在且属于当前用户
  if (parentId) {
    const parentFolder = await prisma.folder.findFirst({
      where: {
        id: parentId,
        userId,
      },
    });

    if (!parentFolder) {
      throw new AppError('父文件夹不存在', 404);
    }
  }

  const folder = await prisma.folder.create({
    data: {
      userId,
      name,
      type,
      parentId,
    },
    include: {
      _count: {
        select: {
          emails: {
            where: {
              isDeleted: false,
            },
          },
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '文件夹创建成功',
    data: folder,
  };

  res.status(201).json(response);
};

// 更新文件夹
export const updateFolder = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { name, parentId } = req.body;
  const userId = req.user!.id;

  // 检查文件夹是否存在且属于当前用户
  const existingFolder = await prisma.folder.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingFolder) {
    throw new AppError('文件夹不存在', 404);
  }

  // 不允许修改系统文件夹
  if (existingFolder.type !== 'custom') {
    throw new AppError('不能修改系统文件夹', 400);
  }

  // 检查同级文件夹名称是否重复
  if (name && name !== existingFolder.name) {
    const duplicateFolder = await prisma.folder.findFirst({
      where: {
        userId,
        name,
        parentId: parentId !== undefined ? parentId : existingFolder.parentId,
        id: { not: parseInt(id) },
      },
    });

    if (duplicateFolder) {
      throw new AppError('同级目录下已存在同名文件夹', 409);
    }
  }

  // 如果指定了父文件夹，检查父文件夹是否存在且不是自己或子文件夹
  if (parentId) {
    const parentFolder = await prisma.folder.findFirst({
      where: {
        id: parentId,
        userId,
      },
    });

    if (!parentFolder) {
      throw new AppError('父文件夹不存在', 404);
    }

    // 防止循环引用
    if (parentId === parseInt(id)) {
      throw new AppError('不能将文件夹移动到自己下面', 400);
    }

    // TODO: 检查是否会形成循环引用（需要递归检查子文件夹）
  }

  const folder = await prisma.folder.update({
    where: { id: parseInt(id) },
    data: {
      name,
      parentId,
    },
    include: {
      _count: {
        select: {
          emails: {
            where: {
              isDeleted: false,
            },
          },
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '文件夹更新成功',
    data: folder,
  };

  res.json(response);
};

// 删除文件夹
export const deleteFolder = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  // 检查文件夹是否存在且属于当前用户
  const folder = await prisma.folder.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
    include: {
      _count: {
        select: {
          emails: true,
          children: true,
        },
      },
    },
  });

  if (!folder) {
    throw new AppError('文件夹不存在', 404);
  }

  // 不允许删除系统文件夹
  if (folder.type !== 'custom') {
    throw new AppError('不能删除系统文件夹', 400);
  }

  // 检查文件夹是否为空
  if (folder._count.emails > 0) {
    throw new AppError('文件夹不为空，无法删除', 400);
  }

  if (folder._count.children > 0) {
    throw new AppError('文件夹包含子文件夹，无法删除', 400);
  }

  await prisma.folder.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '文件夹删除成功',
  };

  res.json(response);
};
