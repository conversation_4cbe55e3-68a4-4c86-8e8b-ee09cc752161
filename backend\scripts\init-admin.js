#!/usr/bin/env node

/**
 * 管理员用户初始化脚本
 * 用于在空数据库中创建第一个管理员用户
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const readline = require('readline');

// 尝试导入密码管理器，如果失败则使用bcrypt降级
let createUserPassword;
try {
  createUserPassword = require('../dist/utils/passwordManager').createUserPassword;
} catch (error) {
  console.warn('无法加载密码管理器，将使用bcrypt降级方案');
  createUserPassword = null;
}

const prisma = new PrismaClient();

// 创建命令行接口
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// 提示用户输入
function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

// 隐藏密码输入
function questionPassword(prompt) {
  return new Promise((resolve) => {
    process.stdout.write(prompt);
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    let password = '';
    process.stdin.on('data', function(char) {
      char = char + '';
      
      switch(char) {
        case '\n':
        case '\r':
        case '\u0004':
          process.stdin.setRawMode(false);
          process.stdin.pause();
          process.stdout.write('\n');
          resolve(password);
          break;
        case '\u0003':
          process.exit();
          break;
        case '\u007f': // backspace
          if (password.length > 0) {
            password = password.slice(0, -1);
            process.stdout.write('\b \b');
          }
          break;
        default:
          password += char;
          process.stdout.write('*');
          break;
      }
    });
  });
}

// 验证邮箱格式
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// 验证用户名格式
function isValidUsername(username) {
  const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
  return usernameRegex.test(username);
}

// 创建默认域名
async function createDefaultDomain(domainName) {
  return await prisma.virtualDomain.upsert({
    where: { name: domainName },
    update: {},
    create: {
      name: domainName,
      active: 1,
    },
  });
}

// 创建管理员用户
async function createAdminUser(userData, domainId) {
  let passwordData;

  if (createUserPassword) {
    // 使用系统密码管理器
    try {
      passwordData = await createUserPassword(userData.password);
      if (passwordData.error) {
        console.warn('密码管理器警告:', passwordData.error);
        throw new Error('密码生成失败');
      }
    } catch (error) {
      console.warn('密码管理器失败，降级使用bcrypt:', error.message);
      passwordData = {
        webPassword: await bcrypt.hash(userData.password, 12),
        mailPassword: null
      };
    }
  } else {
    // 降级使用bcrypt
    passwordData = {
      webPassword: await bcrypt.hash(userData.password, 12),
      mailPassword: null
    };
  }

  return await prisma.user.create({
    data: {
      email: userData.email,
      username: userData.username,
      password: passwordData.webPassword,
      mailPassword: passwordData.mailPassword,
      displayName: userData.displayName,
      role: 'admin',
      isActive: true,
      emailVerified: true,
      domainId: domainId,
    },
    select: {
      id: true,
      email: true,
      username: true,
      displayName: true,
      role: true,
      createdAt: true,
    },
  });
}

// 创建默认文件夹
async function createDefaultFolders(userId) {
  const folders = [
    { name: '收件箱', type: 'inbox' },
    { name: '发件箱', type: 'sent' },
    { name: '草稿箱', type: 'draft' },
    { name: '垃圾箱', type: 'trash' },
  ];

  for (const folderData of folders) {
    await prisma.folder.create({
      data: {
        userId: userId,
        name: folderData.name,
        type: folderData.type,
      },
    });
  }
}

// 主函数
async function main() {
  console.log('🚀 管理员用户初始化脚本');
  console.log('================================\n');

  try {
    // 检查是否已有管理员用户
    const existingAdmin = await prisma.user.findFirst({
      where: { role: 'admin' }
    });

    if (existingAdmin) {
      console.log('⚠️  系统中已存在管理员用户:');
      console.log(`   邮箱: ${existingAdmin.email}`);
      console.log(`   用户名: ${existingAdmin.username}`);
      
      const confirm = await question('\n是否继续创建新的管理员用户? (y/N): ');
      if (confirm.toLowerCase() !== 'y' && confirm.toLowerCase() !== 'yes') {
        console.log('操作已取消。');
        return;
      }
    }

    // 收集用户信息
    console.log('请输入管理员用户信息:\n');
    
    let email;
    do {
      email = await question('邮箱地址: ');
      if (!isValidEmail(email)) {
        console.log('❌ 邮箱格式无效，请重新输入。');
      }
    } while (!isValidEmail(email));

    let username;
    do {
      username = await question('用户名 (3-20个字符，仅限字母数字下划线): ');
      if (!isValidUsername(username)) {
        console.log('❌ 用户名格式无效，请重新输入。');
      }
    } while (!isValidUsername(username));

    const displayName = await question('显示名称 (可选): ') || '系统管理员';

    let password;
    let confirmPassword;
    do {
      password = await questionPassword('密码 (至少8位): ');
      if (password.length < 8) {
        console.log('❌ 密码长度至少8位，请重新输入。');
        continue;
      }
      
      confirmPassword = await questionPassword('确认密码: ');
      if (password !== confirmPassword) {
        console.log('❌ 两次输入的密码不一致，请重新输入。');
      }
    } while (password.length < 8 || password !== confirmPassword);

    // 检查用户是否已存在
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: email },
          { username: username },
        ],
      },
    });

    if (existingUser) {
      console.log('❌ 邮箱或用户名已存在，请使用其他邮箱或用户名。');
      return;
    }

    console.log('\n🔄 正在创建管理员用户...');

    // 创建域名
    const domainName = email.split('@')[1];
    const domain = await createDefaultDomain(domainName);
    console.log(`✓ 域名创建/确认: ${domain.name}`);

    // 创建管理员用户
    const adminUser = await createAdminUser({
      email,
      username,
      password,
      displayName,
    }, domain.id);
    console.log(`✓ 管理员用户创建成功: ${adminUser.email}`);

    // 创建默认文件夹
    await createDefaultFolders(adminUser.id);
    console.log('✓ 默认文件夹创建完成');

    console.log('\n✅ 管理员用户初始化完成！');
    console.log('================================');
    console.log('📋 账户信息:');
    console.log(`👑 ${adminUser.displayName}`);
    console.log(`   邮箱: ${adminUser.email}`);
    console.log(`   用户名: ${adminUser.username}`);
    console.log(`   角色: ${adminUser.role}`);
    console.log(`   创建时间: ${adminUser.createdAt}`);
    console.log('\n⚠️  请妥善保管登录信息！');

  } catch (error) {
    console.error('❌ 创建管理员用户失败:', error);
    process.exit(1);
  } finally {
    rl.close();
    await prisma.$disconnect();
  }
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n\n操作已取消。');
  rl.close();
  await prisma.$disconnect();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
