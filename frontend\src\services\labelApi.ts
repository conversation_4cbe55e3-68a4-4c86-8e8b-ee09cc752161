import api from "../config/api";
import type { ApiResponse, PaginatedResponse } from "../types";

// 标签接口
export interface Label {
  id: number;
  userId: number;
  name: string;
  color: string;
  createdAt: string;
  _count?: {
    emails: number;
  };
}

// 创建标签参数
export interface CreateLabelParams {
  name: string;
  color: string;
}

// 更新标签参数
export interface UpdateLabelParams {
  name?: string;
  color?: string;
}

// 邮件标签操作参数
export interface EmailLabelParams {
  emailId: string;
  labelId: number;
}

// 批量邮件标签操作参数
export interface BatchEmailLabelParams {
  emailIds: string[];
  labelIds: number[];
}

// 获取标签列表
export const getLabels = async (params?: {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}): Promise<PaginatedResponse<Label>> => {
  const response = await api.get<ApiResponse<PaginatedResponse<Label>>>(
    "/labels",
    {
      params,
    }
  );
  return response.data.data!;
};

// 获取标签详情
export const getLabelById = async (id: number): Promise<Label> => {
  const response = await api.get<ApiResponse<Label>>(`/labels/${id}`);
  return response.data.data!;
};

// 创建标签
export const createLabel = async (
  params: CreateLabelParams
): Promise<Label> => {
  const response = await api.post<ApiResponse<Label>>("/labels", params);
  return response.data.data!;
};

// 更新标签
export const updateLabel = async (
  id: number,
  params: UpdateLabelParams
): Promise<Label> => {
  const response = await api.put<ApiResponse<Label>>(`/labels/${id}`, params);
  return response.data.data!;
};

// 删除标签
export const deleteLabel = async (id: number): Promise<void> => {
  await api.delete(`/labels/${id}`);
};

// 为邮件添加标签
export const addLabelToEmail = async (
  params: EmailLabelParams
): Promise<void> => {
  await api.post("/labels/email/add", params);
};

// 从邮件移除标签
export const removeLabelFromEmail = async (
  params: EmailLabelParams
): Promise<void> => {
  await api.post("/labels/email/remove", params);
};

// 批量为邮件添加标签
export const batchAddLabelsToEmails = async (
  params: BatchEmailLabelParams
): Promise<void> => {
  await api.post("/labels/email/batch-add", params);
};

// 获取所有标签（不分页，用于选择器）
export const getAllLabels = async (): Promise<Label[]> => {
  const response = await getLabels({ limit: 1000 });
  return response.data;
};
