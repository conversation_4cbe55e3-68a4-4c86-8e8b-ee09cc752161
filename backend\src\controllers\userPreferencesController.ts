import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';

// 默认用户偏好设置
const DEFAULT_PREFERENCES = {
  // 基础设置
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24h',
  
  // 邮件设置
  emailsPerPage: 20,
  autoRefresh: true,
  autoRefreshInterval: 30,
  showPreviewPane: true,
  markAsReadOnPreview: false,
  confirmBeforeDelete: true,
  defaultReplyAction: 'reply',
  includeOriginalMessage: true,
  quotingStyle: 'top',
  
  // 界面设置
  theme: {
    mode: 'light',
    primaryColor: '#1890ff',
    fontSize: 14,
    fontFamily: 'system-ui'
  },
  compactMode: false,
  showAvatars: true,
  sidebarCollapsed: false,
  viewMode: 'list', // 新增：默认视图模式
  listWidth: 400,   // 新增：列表宽度
  
  // 通知设置
  enableNotifications: true,
  notificationSound: true,
  desktopNotifications: false,
  emailNotifications: true,
  
  // 编辑器设置
  richTextEditor: true,
  autoSave: true,
  autoSaveInterval: 60,
  spellCheck: true,
  
  // 安全设置
  sessionTimeout: 24 * 60, // 24小时，单位分钟
  requirePasswordForSensitiveActions: true,
};

// 获取用户偏好设置
export const getUserPreferences = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true },
    });

    let preferences = DEFAULT_PREFERENCES;
    
    if (user?.preferences) {
      try {
        const userPrefs = JSON.parse(user.preferences);
        preferences = { ...DEFAULT_PREFERENCES, ...userPrefs };
      } catch (error) {
        logger.warn(`解析用户 ${userId} 的偏好设置失败，使用默认设置:`, error);
      }
    }

    const response: ApiResponse = {
      success: true,
      message: '获取用户偏好设置成功',
      data: preferences,
    };

    res.json(response);
  } catch (error) {
    logger.error('获取用户偏好设置失败:', error);
    throw new AppError('获取用户偏好设置失败', 500);
  }
};

// 更新用户偏好设置
export const updateUserPreferences = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const updates = req.body;

  try {
    // 获取当前偏好设置
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true },
    });

    let currentPreferences = DEFAULT_PREFERENCES;
    
    if (user?.preferences) {
      try {
        currentPreferences = JSON.parse(user.preferences);
      } catch (error) {
        logger.warn(`解析用户 ${userId} 的偏好设置失败，使用默认设置:`, error);
      }
    }

    // 合并更新
    const updatedPreferences = { ...currentPreferences, ...updates };

    // 保存到数据库
    await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: JSON.stringify(updatedPreferences),
      },
    });

    logger.info(`用户 ${userId} 的偏好设置已更新`);

    const response: ApiResponse = {
      success: true,
      message: '用户偏好设置更新成功',
      data: updatedPreferences,
    };

    res.json(response);
  } catch (error) {
    logger.error('更新用户偏好设置失败:', error);
    throw new AppError('更新用户偏好设置失败', 500);
  }
};

// 重置用户偏好设置为默认值
export const resetUserPreferences = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: JSON.stringify(DEFAULT_PREFERENCES),
      },
    });

    logger.info(`用户 ${userId} 的偏好设置已重置为默认值`);

    const response: ApiResponse = {
      success: true,
      message: '用户偏好设置已重置为默认值',
      data: DEFAULT_PREFERENCES,
    };

    res.json(response);
  } catch (error) {
    logger.error('重置用户偏好设置失败:', error);
    throw new AppError('重置用户偏好设置失败', 500);
  }
};

// 获取特定偏好设置项
export const getPreferenceSetting = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { key } = req.params;

  try {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true },
    });

    let preferences = DEFAULT_PREFERENCES;
    
    if (user?.preferences) {
      try {
        preferences = JSON.parse(user.preferences);
      } catch (error) {
        logger.warn(`解析用户 ${userId} 的偏好设置失败，使用默认设置:`, error);
      }
    }

    const value = (preferences as any)[key];
    
    if (value === undefined) {
      throw new AppError(`偏好设置项 '${key}' 不存在`, 404);
    }

    const response: ApiResponse = {
      success: true,
      message: '获取偏好设置项成功',
      data: { [key]: value },
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取偏好设置项失败:', error);
    throw new AppError('获取偏好设置项失败', 500);
  }
};

// 更新特定偏好设置项
export const updatePreferenceSetting = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { key } = req.params;
  const { value } = req.body;

  try {
    // 获取当前偏好设置
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { preferences: true },
    });

    let preferences = DEFAULT_PREFERENCES;
    
    if (user?.preferences) {
      try {
        preferences = JSON.parse(user.preferences);
      } catch (error) {
        logger.warn(`解析用户 ${userId} 的偏好设置失败，使用默认设置:`, error);
      }
    }

    // 更新特定设置项
    (preferences as any)[key] = value;

    // 保存到数据库
    await prisma.user.update({
      where: { id: userId },
      data: {
        preferences: JSON.stringify(preferences),
      },
    });

    logger.info(`用户 ${userId} 的偏好设置项 '${key}' 已更新`);

    const response: ApiResponse = {
      success: true,
      message: '偏好设置项更新成功',
      data: { [key]: value },
    };

    res.json(response);
  } catch (error) {
    logger.error('更新偏好设置项失败:', error);
    throw new AppError('更新偏好设置项失败', 500);
  }
};
