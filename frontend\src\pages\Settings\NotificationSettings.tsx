import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  Button,
  message,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Select,
  TimePicker,
  Checkbox
} from 'antd';
import {
  BellOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

interface NotificationSettings {
  emailNotifications: boolean;
  browserNotifications: boolean;
  soundNotifications: boolean;
  desktopNotifications: boolean;
  notifyNewEmail: boolean;
  notifyImportantEmail: boolean;
  notifyMentions: boolean;
  notifyReplies: boolean;
  quietHoursEnabled: boolean;
  quietHoursStart: string;
  quietHoursEnd: string;
  weekendNotifications: boolean;
  notificationSound: string;
  notificationFrequency: 'immediate' | 'digest' | 'hourly' | 'daily';
  digestTime: string;
}

const NotificationSettingsPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState<NotificationSettings | null>(null);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      // 模拟加载通知设置
      const mockSettings: NotificationSettings = {
        emailNotifications: true,
        browserNotifications: true,
        soundNotifications: false,
        desktopNotifications: true,
        notifyNewEmail: true,
        notifyImportantEmail: true,
        notifyMentions: true,
        notifyReplies: true,
        quietHoursEnabled: true,
        quietHoursStart: '22:00',
        quietHoursEnd: '08:00',
        weekendNotifications: false,
        notificationSound: 'default',
        notificationFrequency: 'immediate',
        digestTime: '09:00'
      };
      setSettings(mockSettings);
      form.setFieldsValue({
        ...mockSettings,
        quietHoursStart: dayjs(mockSettings.quietHoursStart, 'HH:mm'),
        quietHoursEnd: dayjs(mockSettings.quietHoursEnd, 'HH:mm'),
        digestTime: dayjs(mockSettings.digestTime, 'HH:mm')
      });
    } catch (error) {
      message.error('加载通知设置失败');
    }
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 转换时间格式
      const processedValues = {
        ...values,
        quietHoursStart: values.quietHoursStart?.format('HH:mm'),
        quietHoursEnd: values.quietHoursEnd?.format('HH:mm'),
        digestTime: values.digestTime?.format('HH:mm')
      };
      
      setSettings(processedValues);
      message.success('通知设置保存成功');
    } catch (error) {
      message.error('保存通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info('已重置为默认设置');
  };

  const requestNotificationPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission();
      if (permission === 'granted') {
        message.success('浏览器通知权限已开启');
      } else {
        message.warning('浏览器通知权限被拒绝');
      }
    } else {
      message.error('您的浏览器不支持通知功能');
    }
  };

  return (
    <Card>
            <Title level={3} style={{ marginBottom: '24px' }}>
              <BellOutlined style={{ marginRight: '8px' }} />
              通知设置
            </Title>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={settings}
            >
              {/* 通知类型 */}
              <Row gutter={[24, 24]}>
                <Col span={24}>
                  <Title level={4}>通知类型</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="emailNotifications"
                    label="邮件通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Text type="secondary">通过邮件接收通知</Text>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="browserNotifications"
                    label="浏览器通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <div>
                    <Text type="secondary">在浏览器中显示通知</Text>
                    <br />
                    <Button 
                      type="link" 
                      size="small" 
                      onClick={requestNotificationPermission}
                      style={{ padding: 0 }}
                    >
                      请求通知权限
                    </Button>
                  </div>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="soundNotifications"
                    label="声音通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Text type="secondary">播放通知声音</Text>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="desktopNotifications"
                    label="桌面通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Text type="secondary">显示桌面弹窗通知</Text>
                </Col>

                <Divider />

                {/* 通知内容 */}
                <Col span={24}>
                  <Title level={4}>通知内容</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="notifyNewEmail"
                    label="新邮件通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="notifyImportantEmail"
                    label="重要邮件通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="notifyMentions"
                    label="提及通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="notifyReplies"
                    label="回复通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Divider />

                {/* 通知频率 */}
                <Col span={24}>
                  <Title level={4}>通知频率</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="notificationFrequency"
                    label="通知频率"
                  >
                    <Select style={{ width: '100%' }}>
                      <Select.Option value="immediate">立即通知</Select.Option>
                      <Select.Option value="digest">摘要通知</Select.Option>
                      <Select.Option value="hourly">每小时</Select.Option>
                      <Select.Option value="daily">每日</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="digestTime"
                    label="摘要发送时间"
                    dependencies={['notificationFrequency']}
                  >
                    <TimePicker
                      format="HH:mm"
                      style={{ width: '100%' }}
                      disabled={form.getFieldValue('notificationFrequency') === 'immediate'}
                    />
                  </Form.Item>
                </Col>

                <Divider />

                {/* 免打扰时间 */}
                <Col span={24}>
                  <Title level={4}>免打扰时间</Title>
                </Col>

                <Col span={8}>
                  <Form.Item
                    name="quietHoursEnabled"
                    label="启用免打扰"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item
                    name="quietHoursStart"
                    label="开始时间"
                    dependencies={['quietHoursEnabled']}
                  >
                    <TimePicker
                      format="HH:mm"
                      style={{ width: '100%' }}
                      disabled={!form.getFieldValue('quietHoursEnabled')}
                    />
                  </Form.Item>
                </Col>

                <Col span={8}>
                  <Form.Item
                    name="quietHoursEnd"
                    label="结束时间"
                    dependencies={['quietHoursEnabled']}
                  >
                    <TimePicker
                      format="HH:mm"
                      style={{ width: '100%' }}
                      disabled={!form.getFieldValue('quietHoursEnabled')}
                    />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="weekendNotifications"
                    label="周末通知"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                  <Text type="secondary">在周末接收通知</Text>
                </Col>

                <Divider />

                {/* 通知声音 */}
                <Col span={24}>
                  <Title level={4}>通知声音</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="notificationSound"
                    label="通知铃声"
                    dependencies={['soundNotifications']}
                  >
                    <Select 
                      style={{ width: '100%' }}
                      disabled={!form.getFieldValue('soundNotifications')}
                    >
                      <Select.Option value="default">默认</Select.Option>
                      <Select.Option value="chime">铃声</Select.Option>
                      <Select.Option value="ding">叮咚</Select.Option>
                      <Select.Option value="pop">弹出</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                {/* 操作按钮 */}
                <Col span={24}>
                  <Divider />
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SaveOutlined />}
                      loading={loading}
                      size="large"
                    >
                      保存设置
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={handleReset}
                      size="large"
                    >
                      重置默认
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
    </Card>
  );
};

export default NotificationSettingsPage;
