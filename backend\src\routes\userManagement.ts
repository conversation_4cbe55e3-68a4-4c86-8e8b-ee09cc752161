import { Router } from 'express';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, requireAdmin } from '../middleware/auth';
import * as userManagementController from '../controllers/userManagementController';

const router = Router();

// 所有路由都需要认证和管理员权限
router.use(authenticate);
router.use(requireAdmin);

/**
 * 获取所有用户列表（管理员功能）
 * GET /api/user-management/users
 * Query参数：
 * - page: 页码
 * - limit: 每页数量
 * - search: 搜索关键词
 * - role: 角色筛选
 * - status: 状态筛选
 * - sortBy: 排序字段
 * - sortOrder: 排序方向
 */
router.get('/users',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userManagementController.getAllUsers)
);

/**
 * 获取用户详细信息（管理员功能）
 * GET /api/user-management/users/:userId
 */
router.get('/users/:userId',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userManagementController.getUserDetails)
);

/**
 * 更新用户权限和状态（管理员功能）
 * PUT /api/user-management/users/:userId/permissions
 * Body参数：
 * - role: 用户角色 ('user' | 'admin' | 'moderator')
 * - isActive: 是否激活
 * - emailVerified: 邮箱是否验证
 */
router.put('/users/:userId/permissions',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userManagementController.updateUserPermissions)
);

/**
 * 批量更新用户状态（管理员功能）
 * POST /api/user-management/users/batch-update
 * Body参数：
 * - userIds: 用户ID数组
 * - action: 操作类型 ('activate' | 'deactivate' | 'verify_email' | 'unverify_email' | 'set_role')
 * - value: 操作值（当action为set_role时需要）
 */
router.post('/users/batch-update',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userManagementController.batchUpdateUsers)
);

/**
 * 删除用户（管理员功能）
 * DELETE /api/user-management/users/:userId
 */
router.delete('/users/:userId',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userManagementController.deleteUser)
);

export default router;
