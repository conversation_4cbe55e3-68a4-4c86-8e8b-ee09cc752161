/**
 * 管理员权限验证中间件
 */

import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';

/**
 * 验证用户是否为管理员
 */
export const requireAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的请求'
      });
    }

    // 检查用户是否为管理员
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        role: true,
        isActive: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }

    if (user.role !== 'admin' && user.role !== 'super_admin') {
      logger.warn(`非管理员用户尝试访问管理员功能: ${user.email}`);
      return res.status(403).json({
        success: false,
        message: '需要管理员权限'
      });
    }

    // 用户是管理员，继续处理请求
    next();
  } catch (error) {
    logger.error('管理员权限验证失败:', error);
    res.status(500).json({
      success: false,
      message: '权限验证失败'
    });
  }
};

/**
 * 验证用户是否为超级管理员（可选的更高级权限）
 */
export const requireSuperAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的请求'
      });
    }

    // 检查用户是否为超级管理员
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        role: true,
        isActive: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }

    if (user.role !== 'super_admin') {
      logger.warn(`非超级管理员用户尝试访问超级管理员功能: ${user.email}`);
      return res.status(403).json({
        success: false,
        message: '需要超级管理员权限'
      });
    }

    // 用户是超级管理员，继续处理请求
    next();
  } catch (error) {
    logger.error('超级管理员权限验证失败:', error);
    res.status(500).json({
      success: false,
      message: '权限验证失败'
    });
  }
};

/**
 * 验证用户是否可以访问指定用户的资源
 * 管理员可以访问所有用户资源，普通用户只能访问自己的资源
 */
export const requireOwnershipOrAdmin = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的请求'
      });
    }

    // 从请求参数中获取目标用户ID
    const targetUserId = req.params.userId || req.body.userId || req.query.userId;
    
    if (!targetUserId) {
      return res.status(400).json({
        success: false,
        message: '缺少用户ID参数'
      });
    }

    // 检查当前用户权限
    const currentUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        role: true,
        isActive: true
      }
    });

    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '当前用户不存在'
      });
    }

    if (!currentUser.isActive) {
      return res.status(403).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }

    // 如果是管理员，允许访问
    if (currentUser.role === 'admin' || currentUser.role === 'super_admin') {
      return next();
    }

    // 如果是访问自己的资源，允许访问
    if (currentUser.id === parseInt(targetUserId)) {
      return next();
    }

    // 否则拒绝访问
    logger.warn(`用户 ${currentUser.email} 尝试访问其他用户资源: ${targetUserId}`);
    return res.status(403).json({
      success: false,
      message: '无权访问该资源'
    });

  } catch (error) {
    logger.error('资源访问权限验证失败:', error);
    res.status(500).json({
      success: false,
      message: '权限验证失败'
    });
  }
};
