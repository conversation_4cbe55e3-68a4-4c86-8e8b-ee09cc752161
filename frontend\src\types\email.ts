// 邮件相关的类型定义

export type EmailPriority = 'low' | 'normal' | 'high';
export type EmailFolderType = 'inbox' | 'sent' | 'draft' | 'trash' | 'custom';
export type EmailStatus = 'draft' | 'sent' | 'delivered' | 'read' | 'replied' | 'forwarded';

// 邮件收件人类型
export interface EmailRecipient {
  email: string;
  name?: string;
}

// 邮件附件类型
export interface EmailAttachment {
  id?: string;
  filename: string;
  contentType: string;
  size: number;
  path?: string;
  url?: string;
  cid?: string; // Content-ID for inline attachments
  isInline?: boolean;
}

// 邮件数据类型（用于发送）
export interface EmailData {
  to: EmailRecipient[];
  cc?: EmailRecipient[];
  bcc?: EmailRecipient[];
  subject: string;
  content: string;
  htmlContent?: string;
  attachments?: EmailAttachment[];
  priority?: EmailPriority;
  replyTo?: string;
  inReplyTo?: string;
  references?: string;
  headers?: Record<string, string>;
  trackingOptions?: TrackingOptions;
  scheduleAt?: string;
  templateId?: number;
  templateVariables?: Record<string, unknown>;
}

// 邮件实体类型
export interface Email {
  id: string;
  messageId: string;
  userId: number;
  folderId: number;
  subject?: string;
  senderEmail: string;
  senderName?: string;
  recipients: EmailRecipient[];
  ccRecipients?: EmailRecipient[];
  bccRecipients?: EmailRecipient[];
  contentText?: string;
  contentHtml?: string;
  attachments?: EmailAttachment[];
  isRead: boolean;
  isStarred: boolean;
  isDeleted: boolean;
  isImportant?: boolean;
  priority?: EmailPriority;
  status?: EmailStatus;
  receivedAt?: string;
  sentAt?: string;
  readAt?: string;
  createdAt: string;
  updatedAt: string;
  folder?: Folder;
  labels?: EmailLabel[];
  tracking?: EmailTracking;
  thread?: EmailThread;
  inReplyTo?: string;
  references?: string;
  size?: number; // in bytes
}

// 邮件文件夹类型
export interface Folder {
  id: number;
  userId: number;
  name: string;
  type: EmailFolderType;
  parentId?: number;
  color?: string;
  icon?: string;
  isSystem: boolean;
  isVisible: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  children?: Folder[];
  _count?: {
    emails: number;
    unreadEmails: number;
  };
}

// 邮件标签类型
export interface Label {
  id: number;
  userId: number;
  name: string;
  color: string;
  description?: string;
  isSystem: boolean;
  isVisible: boolean;
  sortOrder: number;
  createdAt: string;
  updatedAt: string;
  _count?: {
    emails: number;
  };
}

export interface EmailLabel {
  emailId: string;
  labelId: number;
  label: Label;
  createdAt: string;
}

// 邮件搜索参数类型
export interface EmailSearchParams {
  query?: string;
  from?: string;
  to?: string;
  cc?: string;
  bcc?: string;
  subject?: string;
  body?: string;
  dateFrom?: string;
  dateTo?: string;
  hasAttachment?: boolean;
  attachmentType?: string;
  isRead?: boolean;
  isStarred?: boolean;
  isImportant?: boolean;
  folderId?: number;
  folderType?: EmailFolderType;
  labelIds?: number[];
  priority?: EmailPriority;
  status?: EmailStatus;
  size?: {
    min?: number;
    max?: number;
  };
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 邮件模板类型
export interface EmailTemplate {
  id: number;
  userId: number;
  name: string;
  description?: string;
  subject: string;
  content: string;
  htmlContent?: string;
  isPublic: boolean;
  isSystem: boolean;
  category?: string;
  variables?: TemplateVariable[];
  createdAt: string;
  updatedAt: string;
  user?: {
    id: number;
    username: string;
    displayName?: string;
  };
  _count?: {
    usage: number;
  };
}

export interface TemplateVariable {
  name: string;
  type: 'text' | 'number' | 'date' | 'boolean' | 'email' | 'url';
  description?: string;
  defaultValue?: string;
  required?: boolean;
  options?: string[]; // for select type
}

// 邮件草稿类型
export interface EmailDraft {
  id?: string;
  to: EmailRecipient[];
  cc?: EmailRecipient[];
  bcc?: EmailRecipient[];
  subject: string;
  content: string;
  htmlContent?: string;
  attachments?: EmailAttachment[];
  priority?: EmailPriority;
  replyTo?: string;
  inReplyTo?: string;
  references?: string;
  scheduleAt?: string;
  autoSaveAt?: string;
  createdAt?: string;
  updatedAt?: string;
}

// 邮件线程类型
export interface EmailThread {
  id: string;
  subject: string;
  participantEmails: string[];
  participantNames: string[];
  messageCount: number;
  unreadCount: number;
  lastMessageAt: string;
  isStarred: boolean;
  isImportant: boolean;
  labels: Label[];
  createdAt: string;
  updatedAt: string;
}

// 邮件跟踪类型
export interface TrackingOptions {
  trackOpens?: boolean;
  trackClicks?: boolean;
  trackReplies?: boolean;
  requestReceipt?: boolean;
}

export interface EmailTracking {
  id: string;
  emailId: string;
  trackingId: string;
  isDelivered: boolean;
  deliveredAt?: string;
  isOpened: boolean;
  firstOpenedAt?: string;
  lastOpenedAt?: string;
  openCount: number;
  isReplied: boolean;
  repliedAt?: string;
  clickCount: number;
  uniqueClicks: number;
  events: TrackingEvent[];
}

export interface TrackingEvent {
  id: string;
  trackingId: string;
  eventType: 'delivery' | 'open' | 'click' | 'reply' | 'bounce' | 'spam';
  ipAddress?: string;
  userAgent?: string;
  location?: string;
  clickedUrl?: string;
  metadata?: Record<string, unknown>;
  createdAt: string;
}

// 邮件规则类型
export type EmailRuleConditionField = 'from' | 'to' | 'cc' | 'bcc' | 'subject' | 'body' | 'attachment' | 'size';
export type EmailRuleConditionOperator = 'contains' | 'equals' | 'startsWith' | 'endsWith' | 'regex' | 'greaterThan' | 'lessThan';

export interface EmailRuleCondition {
  field: EmailRuleConditionField;
  operator: EmailRuleConditionOperator;
  value: string;
  caseSensitive?: boolean;
}

export type EmailRuleActionType = 
  | 'move' 
  | 'addLabel' 
  | 'removeLabel'
  | 'markRead' 
  | 'markUnread'
  | 'markStarred' 
  | 'markUnstarred'
  | 'markImportant'
  | 'markUnimportant'
  | 'delete' 
  | 'forward' 
  | 'reply'
  | 'archive'
  | 'stopProcessing';

export interface EmailRuleAction {
  type: EmailRuleActionType;
  value?: string | number;
  folderId?: number;
  labelId?: number;
  forwardTo?: string;
  replyTemplate?: string;
}

export interface EmailRule {
  id: number;
  userId: number;
  name: string;
  description?: string;
  conditions: EmailRuleCondition[];
  actions: EmailRuleAction[];
  isActive: boolean;
  priority: number;
  isSystem: boolean;
  createdAt: string;
  updatedAt: string;
  _count?: {
    appliedEmails: number;
  };
}

// 邮件统计类型
export interface EmailStats {
  totalEmails: number;
  unreadEmails: number;
  sentEmails: number;
  draftEmails: number;
  deletedEmails: number;
  starredEmails: number;
  importantEmails: number;
  spamEmails: number;
  storageUsed: number; // in bytes
  averageEmailSize: number; // in bytes
  emailsToday: number;
  emailsThisWeek: number;
  emailsThisMonth: number;
  topSenders: Array<{
    email: string;
    name?: string;
    count: number;
  }>;
  topRecipients: Array<{
    email: string;
    name?: string;
    count: number;
  }>;
}

// 邮件批量操作类型
export interface EmailBulkAction {
  action: 'markRead' | 'markUnread' | 'star' | 'unstar' | 'delete' | 'archive' | 'move' | 'addLabel' | 'removeLabel';
  emailIds: string[];
  params?: {
    folderId?: number;
    labelId?: number;
  };
}

export interface EmailBulkActionResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors: Array<{
    emailId: string;
    error: string;
  }>;
}
