import { useEffect } from 'react';
import { RouterProvider } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ConfigProvider, Spin } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import { router } from './router';
import { useAuthStore } from './store/authStore';
import './styles/global.css';

// 创建 React Query 客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

// Ant Design 主题配置 - 优化的温馨邮箱配色
const theme = {
  token: {
    colorPrimary: '#FF7F50', /* 珊瑚粉 */
    colorPrimaryHover: '#FF6347', /* 番茄红 */
    colorPrimaryActive: '#FF0000', /* 樱桃红 */
    colorBgBase: '#ffffff',
    colorBgContainer: '#E6F0FF', /* 非常淡的天蓝色 */
    colorBgElevated: '#ffffff',
    colorBorder: '#e2e8f0', /* 清新边框 */
    colorBorderSecondary: '#f7fafc',
    colorText: '#1a202c', /* 深蓝灰 */
    colorTextSecondary: '#2d3748', /* 中蓝灰 */
    colorTextTertiary: '#4a5568', /* 浅蓝灰 */
    colorSuccess: '#48bb78', /* 清新绿 */
    colorWarning: '#ed8936', /* 温暖橙 */
    colorError: '#FF0000', /* 樱桃红 */
    colorInfo: '#4299e1', /* 天蓝 */
    borderRadius: 8,
    borderRadiusLG: 12,
    borderRadiusSM: 6,
  },
  components: {
    Layout: {
      bodyBg: '#E6F0FF', /* 非常淡的天蓝色 */
      headerBg: '#ffffff',
      siderBg: '#E8F5E9', /* 薄荷绿 */
    },
    Menu: {
      itemBg: 'transparent',
      itemSelectedBg: '#FF7F50', /* 珊瑚粉 */
      itemSelectedColor: '#ffffff',
      itemHoverBg: '#F0F8FF', /* 爱丽丝蓝 */
      itemHoverColor: '#1a202c', /* 深蓝灰 */
    },
    Button: {
      primaryShadow: '0 2px 8px rgba(255, 127, 80, 0.15)', /* 珊瑚粉阴影 */
    },
    Input: {
      hoverBorderColor: '#FF7F50', /* 珊瑚粉 */
      focusBorderColor: '#FF7F50', /* 珊瑚粉 */
      activeBorderColor: '#FF6347', /* 番茄红 */
    },
    Card: {
      colorBgContainer: '#ffffff',
      colorBorderSecondary: '#e2e8f0',
    },
    Table: {
      colorBgContainer: '#ffffff',
      headerBg: '#F0F8FF', /* 爱丽丝蓝 */
      rowHoverBg: '#E0F2FE', /* 淡天蓝 */
    },
  },
};

function App() {
  const { getCurrentUser, loading } = useAuthStore();

  useEffect(() => {
    // 应用启动时尝试恢复用户状态
    console.log('🚀 App: 应用启动，尝试恢复用户状态');
    getCurrentUser().then(() => {
      console.log('✅ App: 用户状态恢复完成');
    }).catch((error) => {
      console.error('❌ App: 用户状态恢复失败:', error);
    });
  }, [getCurrentUser]);

  return (
    <QueryClientProvider client={queryClient}>
      <ConfigProvider locale={zhCN} theme={theme}>
        {loading ? (
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100vh'
          }}>
            <Spin size="large" tip="加载中..." />
          </div>
        ) : (
          <RouterProvider router={router} />
        )}
      </ConfigProvider>
    </QueryClientProvider>
  );
}

export default App;
