import api from '../config/api';
import type { ApiResponse } from '../types';

export interface SendVerificationCodeData {
  email: string;
  type?: 'REGISTRATION' | 'PASSWORD_RESET' | 'EMAIL_CHANGE';
}

export interface VerifyCodeData {
  email: string;
  code: string;
  type?: 'REGISTRATION' | 'PASSWORD_RESET' | 'EMAIL_CHANGE';
}

export interface VerificationStatus {
  email: string;
  type: string;
  hasActiveCode: boolean;
  expiresAt?: string;
  attempts?: number;
}

export interface ResendCodeData {
  type?: 'REGISTRATION' | 'PASSWORD_RESET' | 'EMAIL_CHANGE';
}

export interface VerifyCurrentCodeData {
  code: string;
}

// 发送验证码
export const sendVerificationCode = async (data: SendVerificationCodeData): Promise<{
  email: string;
  type: string;
  expiresIn: number;
}> => {
  const response = await api.post<ApiResponse<{
    email: string;
    type: string;
    expiresIn: number;
  }>>('/verification/send-code', data);
  return response.data.data!;
};

// 验证验证码
export const verifyCode = async (data: VerifyCodeData): Promise<{
  email: string;
  type: string;
  verified: boolean;
}> => {
  const response = await api.post<ApiResponse<{
    email: string;
    type: string;
    verified: boolean;
  }>>('/verification/verify-code', data);
  return response.data.data!;
};

// 获取验证码状态
export const getVerificationStatus = async (
  email: string,
  type: 'REGISTRATION' | 'PASSWORD_RESET' | 'EMAIL_CHANGE' = 'REGISTRATION'
): Promise<VerificationStatus> => {
  const response = await api.get<ApiResponse<VerificationStatus>>(
    `/verification/status?email=${encodeURIComponent(email)}&type=${type}`
  );
  return response.data.data!;
};

// 重新发送验证码（需要认证）
export const resendVerificationCode = async (data: ResendCodeData): Promise<{
  email: string;
  type: string;
  expiresIn: number;
}> => {
  const response = await api.post<ApiResponse<{
    email: string;
    type: string;
    expiresIn: number;
  }>>('/verification/resend', data);
  return response.data.data!;
};

// 验证当前用户邮箱（需要认证）
export const verifyCurrentUserEmail = async (data: VerifyCurrentCodeData): Promise<{
  email: string;
  verified: boolean;
}> => {
  const response = await api.post<ApiResponse<{
    email: string;
    verified: boolean;
  }>>('/verification/verify-current', data);
  return response.data.data!;
};
