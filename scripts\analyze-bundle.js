#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function analyzeFrontendBundle() {
  log('分析前端打包文件...', 'blue')
  
  const frontendDir = path.join(__dirname, '../frontend')
  const distDir = path.join(frontendDir, 'dist')
  
  if (!fs.existsSync(distDir)) {
    log('前端构建文件不存在，正在构建...', 'yellow')
    execSync('npm run build', { cwd: frontendDir, stdio: 'inherit' })
  }
  
  // 分析打包文件大小
  const assets = []
  
  function analyzeDirectory(dir, prefix = '') {
    const files = fs.readdirSync(dir)
    
    files.forEach(file => {
      const filePath = path.join(dir, file)
      const stat = fs.statSync(filePath)
      
      if (stat.isDirectory()) {
        analyzeDirectory(filePath, `${prefix}${file}/`)
      } else {
        const size = stat.size
        const sizeKB = (size / 1024).toFixed(2)
        const sizeMB = (size / (1024 * 1024)).toFixed(2)
        
        assets.push({
          name: `${prefix}${file}`,
          size,
          sizeKB: parseFloat(sizeKB),
          sizeMB: parseFloat(sizeMB),
        })
      }
    })
  }
  
  analyzeDirectory(distDir)
  
  // 按大小排序
  assets.sort((a, b) => b.size - a.size)
  
  log('\n前端打包文件分析结果:', 'green')
  log('=' * 60, 'green')
  
  let totalSize = 0
  assets.forEach(asset => {
    totalSize += asset.size
    const sizeDisplay = asset.sizeMB > 1 
      ? `${asset.sizeMB} MB` 
      : `${asset.sizeKB} KB`
    
    const color = asset.sizeMB > 1 ? 'red' : asset.sizeKB > 500 ? 'yellow' : 'reset'
    log(`${asset.name.padEnd(40)} ${sizeDisplay}`, color)
  })
  
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2)
  log('=' * 60, 'green')
  log(`总大小: ${totalSizeMB} MB`, 'green')
  
  // 检查大文件
  const largeFiles = assets.filter(asset => asset.sizeMB > 1)
  if (largeFiles.length > 0) {
    log('\n⚠️  发现大文件 (>1MB):', 'yellow')
    largeFiles.forEach(file => {
      log(`  - ${file.name}: ${file.sizeMB} MB`, 'yellow')
    })
    log('\n建议优化这些文件以提高加载性能', 'yellow')
  }
  
  // 检查JS文件
  const jsFiles = assets.filter(asset => asset.name.endsWith('.js'))
  const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0)
  const totalJSSizeMB = (totalJSSize / (1024 * 1024)).toFixed(2)
  
  log(`\nJavaScript 文件总大小: ${totalJSSizeMB} MB`, 'blue')
  
  if (totalJSSizeMB > 2) {
    log('⚠️  JavaScript 文件过大，建议进行代码分割', 'yellow')
  }
  
  // 检查CSS文件
  const cssFiles = assets.filter(asset => asset.name.endsWith('.css'))
  const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0)
  const totalCSSSizeKB = (totalCSSSize / 1024).toFixed(2)
  
  log(`CSS 文件总大小: ${totalCSSSizeKB} KB`, 'blue')
  
  // 生成报告
  const report = {
    timestamp: new Date().toISOString(),
    totalSize: totalSizeMB,
    totalJSSize: totalJSSizeMB,
    totalCSSSize: totalCSSSizeKB,
    assets: assets.slice(0, 20), // 只保留前20个最大的文件
    largeFiles: largeFiles.length,
    recommendations: []
  }
  
  if (largeFiles.length > 0) {
    report.recommendations.push('优化大文件以提高加载性能')
  }
  
  if (totalJSSizeMB > 2) {
    report.recommendations.push('进行JavaScript代码分割')
  }
  
  if (totalSizeMB > 5) {
    report.recommendations.push('总体打包文件过大，需要全面优化')
  }
  
  // 保存报告
  const reportPath = path.join(__dirname, '../reports/bundle-analysis.json')
  const reportsDir = path.dirname(reportPath)
  
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true })
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  log(`\n报告已保存到: ${reportPath}`, 'green')
}

function analyzeBackendBundle() {
  log('\n分析后端打包文件...', 'blue')
  
  const backendDir = path.join(__dirname, '../backend')
  const distDir = path.join(backendDir, 'dist')
  
  if (!fs.existsSync(distDir)) {
    log('后端构建文件不存在，正在构建...', 'yellow')
    execSync('npm run build', { cwd: backendDir, stdio: 'inherit' })
  }
  
  // 分析node_modules大小
  const nodeModulesDir = path.join(backendDir, 'node_modules')
  if (fs.existsSync(nodeModulesDir)) {
    const nodeModulesSize = execSync(`du -sh ${nodeModulesDir}`, { encoding: 'utf8' }).trim()
    log(`node_modules 大小: ${nodeModulesSize.split('\t')[0]}`, 'blue')
  }
  
  // 分析dist目录大小
  const distSize = execSync(`du -sh ${distDir}`, { encoding: 'utf8' }).trim()
  log(`构建文件大小: ${distSize.split('\t')[0]}`, 'blue')
}

function main() {
  log('开始分析打包文件...', 'green')
  
  try {
    analyzeFrontendBundle()
    analyzeBackendBundle()
    
    log('\n✅ 分析完成！', 'green')
    log('查看详细报告: reports/bundle-analysis.json', 'blue')
    
  } catch (error) {
    log(`❌ 分析失败: ${error.message}`, 'red')
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}

module.exports = {
  analyzeFrontendBundle,
  analyzeBackendBundle,
}
