import { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Form,
  Input,
  Button,
  Alert,
  Steps,
  Space,
  Typography,
  Divider,
  message,
  Result,
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import api from '../config/api';
import type { ApiResponse } from '../types';

const { Title, Text } = Typography;
const { Step } = Steps;

interface InvitationInfo {
  id: number;
  email: string;
  parentUser: {
    email: string;
    displayName?: string;
  };
  permissions: any;
  quotaLimit?: number;
  expiresAt: string;
  status: string;
}

const AcceptSubAccountInvitation: React.FC = () => {
  const { invitationToken } = useParams<{ invitationToken: string }>();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [invitationInfo, setInvitationInfo] = useState<InvitationInfo | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  // 验证邀请令牌
  const validateInvitation = async () => {
    try {
      setLoading(true);
      // 这里需要一个验证邀请的API端点
      // 暂时模拟验证过程
      if (!invitationToken || invitationToken.length < 32) {
        throw new Error('无效的邀请链接');
      }

      // 模拟邀请信息
      const mockInvitation: InvitationInfo = {
        id: 1,
        email: '<EMAIL>',
        parentUser: {
          email: '<EMAIL>',
          displayName: '主账户用户'
        },
        permissions: {
          emailSend: true,
          emailReceive: true,
          emailDelete: false,
          folderManage: true,
          contactManage: true,
          templateManage: false,
          ruleManage: false
        },
        quotaLimit: **********, // 1GB
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        status: 'pending'
      };

      setInvitationInfo(mockInvitation);
      setCurrentStep(1);
    } catch (error) {
      setError((error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  // 接受邀请
  const handleAcceptInvitation = async (values: any) => {
    try {
      setLoading(true);
      await api.post(`/sub-accounts/invitations/${invitationToken}/accept`, values);
      
      setSuccess(true);
      setCurrentStep(2);
      message.success('子账户创建成功！');
      
      // 3秒后跳转到登录页面
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error: any) {
      message.error(error.response?.data?.message || '接受邀请失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (invitationToken) {
      validateInvitation();
    } else {
      setError('缺少邀请令牌');
    }
  }, [invitationToken]);

  const getPermissionText = (permissions: any) => {
    const permissionLabels: { [key: string]: string } = {
      emailSend: '发送邮件',
      emailReceive: '接收邮件',
      emailDelete: '删除邮件',
      folderManage: '管理文件夹',
      contactManage: '管理联系人',
      templateManage: '管理模板',
      ruleManage: '管理规则'
    };

    return Object.entries(permissions)
      .filter(([_, allowed]) => allowed)
      .map(([key, _]) => permissionLabels[key])
      .join('、');
  };

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card style={{ width: 500 }}>
          <Result
            status="error"
            title="邀请无效"
            subTitle={error}
            extra={
              <Button type="primary" onClick={() => navigate('/login')}>
                返回登录
              </Button>
            }
          />
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card style={{ width: 500 }}>
          <Result
            status="success"
            title="子账户创建成功！"
            subTitle="您的子账户已成功创建，即将跳转到登录页面..."
            extra={
              <Button type="primary" onClick={() => navigate('/login')}>
                立即登录
              </Button>
            }
          />
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card style={{ width: 600, maxWidth: '90vw' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2}>接受子账户邀请</Title>
          <Text type="secondary">
            您被邀请成为子账户用户，请完成以下步骤
          </Text>
        </div>

        <Steps current={currentStep} style={{ marginBottom: 32 }}>
          <Step title="验证邀请" icon={<MailOutlined />} />
          <Step title="创建账户" icon={<UserOutlined />} />
          <Step title="完成" icon={<CheckCircleOutlined />} />
        </Steps>

        {currentStep === 0 && (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <div style={{ fontSize: 16, marginBottom: 16 }}>
              正在验证邀请信息...
            </div>
            <Button loading={loading}>验证中</Button>
          </div>
        )}

        {currentStep === 1 && invitationInfo && (
          <div>
            {/* 邀请信息 */}
            <Alert
              message="邀请信息"
              description={
                <div>
                  <div><strong>邀请邮箱:</strong> {invitationInfo.email}</div>
                  <div><strong>邀请人:</strong> {invitationInfo.parentUser.displayName || invitationInfo.parentUser.email}</div>
                  <div><strong>过期时间:</strong> {new Date(invitationInfo.expiresAt).toLocaleString()}</div>
                  {invitationInfo.quotaLimit && (
                    <div><strong>存储配额:</strong> {Math.round(invitationInfo.quotaLimit / (1024 * 1024))} MB</div>
                  )}
                  <div><strong>权限:</strong> {getPermissionText(invitationInfo.permissions)}</div>
                </div>
              }
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            {/* 创建账户表单 */}
            <Form
              form={form}
              layout="vertical"
              onFinish={handleAcceptInvitation}
              autoComplete="off"
            >
              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { 
                    pattern: /^[a-zA-Z0-9_]{3,20}$/, 
                    message: '用户名只能包含字母、数字、下划线，长度3-20位' 
                  }
                ]}
              >
                <Input 
                  prefix={<UserOutlined />} 
                  placeholder="请输入用户名"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="显示名称"
                name="displayName"
                extra="可选，用于显示的友好名称"
              >
                <Input 
                  placeholder="请输入显示名称（可选）"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="密码"
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 8, message: '密码长度至少8位' },
                  {
                    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
                    message: '密码必须包含大小写字母和数字'
                  }
                ]}
              >
                <Input.Password 
                  prefix={<LockOutlined />} 
                  placeholder="请输入密码"
                  size="large"
                />
              </Form.Item>

              <Form.Item
                label="确认密码"
                name="confirmPassword"
                dependencies={['password']}
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password 
                  prefix={<LockOutlined />} 
                  placeholder="请再次输入密码"
                  size="large"
                />
              </Form.Item>

              <Divider />

              <Form.Item>
                <Space style={{ width: '100%', justifyContent: 'center' }}>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    size="large"
                    style={{ minWidth: 120 }}
                  >
                    接受邀请
                  </Button>
                  <Button 
                    size="large"
                    onClick={() => navigate('/login')}
                  >
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>

            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                接受邀请即表示您同意成为 {invitationInfo.parentUser.displayName || invitationInfo.parentUser.email} 的子账户用户
              </Text>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default AcceptSubAccountInvitation;
