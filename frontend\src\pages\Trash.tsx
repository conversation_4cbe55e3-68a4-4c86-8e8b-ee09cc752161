import React, { useState, useEffect } from 'react';
import {
  Layout,
  List,
  Button,
  Badge,
  Tooltip,
  message,
  Modal,
  Checkbox,
  Space,
  Dropdown,
  Menu,
  Empty,
  Spin,
  Avatar,
} from 'antd';
import {
  DeleteOutlined,
  ReloadOutlined,
  RollbackOutlined,
  EyeOutlined,
  DownloadOutlined,
  Arrow<PERSON><PERSON>tOutlined,
  ShareAltOutlined,
  PaperClipOutlined,
  ExclamationCircleOutlined,
  ArrowDownOutlined,
  ClockCircleOutlined,
  UserOutlined,
  MoreOutlined,
} from '@ant-design/icons';
import { useEmailStore } from '../store/emailStore';
import type { Email, EmailSearchParams } from '../types';
import EmailSearch from '../components/EmailSearch';
import AttachmentPreview from '../components/AttachmentPreview';

const { Sider, Content } = Layout;

const Trash = () => {
  const {
    emails,
    selectedEmail,
    loading,
    pagination,
    fetchEmails,
    setSelectedEmail,
    updateEmail,
    deleteEmail,
    syncEmails,
    searchEmails,
  } = useEmailStore();

  const [selectedEmailIds, setSelectedEmailIds] = useState<string[]>([]);
  const [hoveredEmailId, setHoveredEmailId] = useState<string | null>(null);
  const [currentSearchParams, setCurrentSearchParams] = useState<EmailSearchParams>({});

  useEffect(() => {
    // 获取垃圾箱邮件
    fetchEmails({ folderType: 'trash' });
  }, [fetchEmails]);

  const handleEmailClick = async (email: Email) => {
    setSelectedEmail(email);
    
    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      await updateEmail(email.id, { isRead: true });
    }
  };

  const handleRestore = async (emailIds: string[]) => {
    try {
      for (const emailId of emailIds) {
        await updateEmail(emailId, { isDeleted: false });
      }
      message.success(`成功恢复 ${emailIds.length} 封邮件`);
      setSelectedEmailIds([]);
      // 重新获取垃圾箱邮件
      fetchEmails({ folderType: 'trash' });
    } catch (error) {
      message.error('恢复邮件失败');
    }
  };

  const handlePermanentDelete = async (emailIds: string[]) => {
    Modal.confirm({
      title: '永久删除邮件',
      content: `确定要永久删除这 ${emailIds.length} 封邮件吗？此操作不可恢复。`,
      icon: <ExclamationCircleOutlined />,
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          for (const emailId of emailIds) {
            await deleteEmail(emailId);
          }
          message.success(`成功永久删除 ${emailIds.length} 封邮件`);
          setSelectedEmailIds([]);
          // 重新获取垃圾箱邮件
          fetchEmails({ folderType: 'trash' });
        } catch (error) {
          message.error('删除邮件失败');
        }
      },
    });
  };

  const handleSearch = (params: EmailSearchParams) => {
    const searchParams = { ...params, folderType: 'trash' };
    setCurrentSearchParams(searchParams);
    searchEmails(searchParams);
  };

  const handleSyncEmails = async () => {
    try {
      await syncEmails();
      message.success('邮件同步成功');
      // 重新获取垃圾箱邮件
      fetchEmails({ folderType: 'trash' });
    } catch (error) {
      message.error('邮件同步失败');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });
    } else if (diffDays < 7) {
      return `${diffDays}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmailIds(emails.map(email => email.id));
    } else {
      setSelectedEmailIds([]);
    }
  };

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmailIds(prev => [...prev, emailId]);
    } else {
      setSelectedEmailIds(prev => prev.filter(id => id !== emailId));
    }
  };

  const getActionMenu = (email: Email) => (
    <Menu>
      <Menu.Item
        key="restore"
        icon={<RollbackOutlined />}
        onClick={() => handleRestore([email.id])}
      >
        恢复邮件
      </Menu.Item>
      <Menu.Item 
        key="delete" 
        icon={<DeleteOutlined />}
        danger
        onClick={() => handlePermanentDelete([email.id])}
      >
        永久删除
      </Menu.Item>
    </Menu>
  );

  return (
    <Layout className="h-full bg-secondary trash-layout">
      {/* 邮件列表 */}
      <Sider width={420} className="bg-primary shadow-sm trash-sidebar">
        {/* 列表头部 */}
        <div className="p-6 border-b border-light">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold text-primary m-0">垃圾箱</h2>
              <Badge
                count={emails.length}
                className="ml-3"
                showZero={false}
              />
            </div>
            <Tooltip title="刷新邮件" placement="bottom">
              <Button
                type="text"
                icon={<ReloadOutlined spin={loading} />}
                onClick={handleSyncEmails}
                disabled={loading}
                className="header-action-btn"
              />
            </Tooltip>
          </div>

          {/* 搜索组件 */}
          <div className="mb-4">
            <EmailSearch onSearch={handleSearch} loading={loading} />
          </div>

          {/* 批量操作 */}
          {selectedEmailIds.length > 0 && (
            <div className="mb-4 p-3 bg-light rounded-lg">
              <div className="flex items-center justify-between">
                <span className="text-sm text-secondary">
                  已选择 {selectedEmailIds.length} 封邮件
                </span>
                <Space>
                  <Button
                    size="small"
                    icon={<RollbackOutlined />}
                    onClick={() => handleRestore(selectedEmailIds)}
                  >
                    恢复
                  </Button>
                  <Button
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handlePermanentDelete(selectedEmailIds)}
                  >
                    永久删除
                  </Button>
                </Space>
              </div>
            </div>
          )}
        </div>

        {/* 邮件列表内容 */}
        <div className="flex-1 overflow-hidden">
          {loading && emails.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Spin size="large" />
            </div>
          ) : emails.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="垃圾箱为空"
              />
            </div>
          ) : (
            <div className="email-list">
              {/* 全选框 */}
              <div className="px-6 py-3 border-b border-light bg-light">
                <Checkbox
                  checked={selectedEmailIds.length === emails.length && emails.length > 0}
                  indeterminate={selectedEmailIds.length > 0 && selectedEmailIds.length < emails.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                >
                  全选
                </Checkbox>
              </div>

              {emails.map((email) => (
                <div
                  key={email.id}
                  className={`
                    email-item relative cursor-pointer transition-all duration-200 group
                    ${!email.isRead ? 'unread' : ''}
                    ${selectedEmail?.id === email.id ? 'selected' : ''}
                    ${hoveredEmailId === email.id ? 'hovered' : ''}
                  `}
                  onClick={() => handleEmailClick(email)}
                  onMouseEnter={() => setHoveredEmailId(email.id)}
                  onMouseLeave={() => setHoveredEmailId(null)}
                >
                  {/* 未读指示器 */}
                  {!email.isRead && (
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-primary rounded-r"></div>
                  )}

                  <div className="flex items-center p-4 pl-6">
                    {/* 选择框 */}
                    <Checkbox
                      checked={selectedEmailIds.includes(email.id)}
                      onChange={(e) => {
                        e.stopPropagation();
                        handleSelectEmail(email.id, e.target.checked);
                      }}
                      className="mr-3"
                    />

                    {/* 发件人头像 */}
                    <Avatar
                      size="small"
                      icon={<UserOutlined />}
                      className="mr-3 flex-shrink-0"
                    />

                    {/* 邮件信息 */}
                    <div className="flex-1 min-w-0">
                      {/* 第一行：发件人和时间 */}
                      <div className="flex items-center justify-between mb-1">
                        <span className={`
                          text-sm truncate max-w-48
                          ${!email.isRead ? 'font-semibold text-primary' : 'text-secondary'}
                        `}>
                          {email.senderName || email.senderEmail}
                        </span>
                        <div className="flex items-center space-x-2 flex-shrink-0 ml-2">
                          <span className="text-xs text-tertiary">
                            {formatDate(email.receivedAt)}
                          </span>
                          {/* 操作按钮 */}
                          <Dropdown overlay={getActionMenu(email)} trigger={['click']}>
                            <Button
                              type="text"
                              size="small"
                              icon={<MoreOutlined />}
                              onClick={(e) => e.stopPropagation()}
                              className="opacity-0 group-hover:opacity-100 transition-opacity p-1 w-6 h-6"
                            />
                          </Dropdown>
                        </div>
                      </div>

                      {/* 第二行：主题 */}
                      <div className="flex items-center mb-1">
                        <span className={`
                          text-sm truncate flex-1
                          ${!email.isRead ? 'font-medium text-primary' : 'text-secondary'}
                        `}>
                          {email.subject || '(无主题)'}
                        </span>
                        {email.attachments && email.attachments.length > 0 && (
                          <PaperClipOutlined className="text-tertiary ml-2 flex-shrink-0" />
                        )}
                      </div>

                      {/* 第三行：内容预览 */}
                      <div className="text-xs text-tertiary truncate">
                        {email.contentText?.substring(0, 100) || '(无内容)'}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </Sider>

      {/* 邮件详情 */}
      <Content className="flex flex-col">
        {selectedEmail ? (
          <div className="flex flex-col h-full">
            {/* 邮件头部 */}
            <div className="p-6 border-b border-light bg-primary">
              <div className="flex items-center justify-between mb-4">
                <Button
                  type="text"
                  icon={<ArrowLeftOutlined />}
                  onClick={() => setSelectedEmail(null)}
                  className="mr-4"
                >
                  返回列表
                </Button>
                <Space>
                  <Button
                    icon={<RollbackOutlined />}
                    onClick={() => handleRestore([selectedEmail.id])}
                  >
                    恢复邮件
                  </Button>
                  <Button
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handlePermanentDelete([selectedEmail.id])}
                  >
                    永久删除
                  </Button>
                </Space>
              </div>

              <h1 className="text-xl font-semibold text-primary mb-2">
                {selectedEmail.subject || '(无主题)'}
              </h1>

              <div className="text-sm text-secondary space-y-1">
                <div>
                  <span className="font-medium">发件人：</span>
                  {selectedEmail.senderName ?
                    `${selectedEmail.senderName} <${selectedEmail.senderEmail}>` :
                    selectedEmail.senderEmail
                  }
                </div>
                <div>
                  <span className="font-medium">收件人：</span>
                  {selectedEmail.recipients?.map(r => r.name ? `${r.name} <${r.email}>` : r.email).join(', ')}
                </div>
                {selectedEmail.ccRecipients && selectedEmail.ccRecipients.length > 0 && (
                  <div>
                    <span className="font-medium">抄送：</span>
                    {selectedEmail.ccRecipients.map(r => r.name ? `${r.name} <${r.email}>` : r.email).join(', ')}
                  </div>
                )}
                <div>
                  <span className="font-medium">时间：</span>
                  {selectedEmail.receivedAt ? new Date(selectedEmail.receivedAt).toLocaleString('zh-CN') : ''}
                </div>
              </div>
            </div>

            {/* 邮件内容 */}
            <div className="flex-1 overflow-auto p-6">
              {selectedEmail.contentHtml ? (
                <div
                  className="email-content"
                  dangerouslySetInnerHTML={{ __html: selectedEmail.contentHtml }}
                />
              ) : (
                <div className="whitespace-pre-wrap text-secondary">
                  {selectedEmail.contentText || '(无内容)'}
                </div>
              )}

              {/* 附件预览 */}
              {selectedEmail.attachments && selectedEmail.attachments.length > 0 && (
                <div className="mt-6 pt-6 border-t border-light">
                  <h3 className="text-lg font-medium text-primary mb-4">附件</h3>
                  <AttachmentPreview attachments={selectedEmail.attachments} />
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <Empty
              image={Empty.PRESENTED_IMAGE_SIMPLE}
              description="选择一封邮件查看详情"
            />
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default Trash;
