#!/usr/bin/env node

/**
 * 测试数据库设置脚本
 * 用于初始化测试数据库结构和种子数据
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🔧 开始设置测试数据库...');

// 检查环境
if (process.env.NODE_ENV !== 'test') {
  console.log('⚠️  设置测试环境变量...');
  process.env.NODE_ENV = 'test';
}

// 加载测试环境配置
const envTestPath = path.join(__dirname, '../.env.test');
if (fs.existsSync(envTestPath)) {
  require('dotenv').config({ path: envTestPath });
  console.log('✅ 已加载测试环境配置');
} else {
  console.error('❌ 找不到 .env.test 文件');
  process.exit(1);
}

// 检查数据库URL
const dbUrl = process.env.DATABASE_URL;
if (!dbUrl || !dbUrl.includes('_test')) {
  console.error('❌ 数据库URL必须包含 "_test" 后缀');
  console.error('当前URL:', dbUrl);
  process.exit(1);
}

try {
  console.log('📊 数据库URL:', dbUrl);
  
  // 1. 推送数据库结构
  console.log('🔄 推送数据库结构...');
  execSync('npx prisma db push', { 
    stdio: 'inherit',
    env: { ...process.env, DATABASE_URL: dbUrl }
  });
  
  // 2. 生成Prisma客户端
  console.log('🔄 生成Prisma客户端...');
  execSync('npx prisma generate', { 
    stdio: 'inherit',
    env: { ...process.env, DATABASE_URL: dbUrl }
  });
  
  // 3. 创建种子数据
  console.log('🌱 创建种子数据...');
  const { createTestSeedData } = require('../dist/test/seedData');
  
  // 确保编译了TypeScript
  if (!fs.existsSync(path.join(__dirname, '../dist'))) {
    console.log('🔄 编译TypeScript...');
    execSync('npm run build', { stdio: 'inherit' });
  }
  
  // 运行种子数据创建
  createTestSeedData().then(() => {
    console.log('✅ 测试数据库设置完成！');
    console.log('');
    console.log('📋 现在您可以运行测试：');
    console.log('   npm run test');
    console.log('   npm run test:watch');
    console.log('   npm run test:coverage');
    process.exit(0);
  }).catch((error) => {
    console.error('❌ 创建种子数据失败:', error);
    process.exit(1);
  });
  
} catch (error) {
  console.error('❌ 设置测试数据库失败:', error.message);
  process.exit(1);
}
