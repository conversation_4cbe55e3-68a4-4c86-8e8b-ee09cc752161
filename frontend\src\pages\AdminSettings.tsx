import React, { useState } from 'react';
import { Card, Tabs, <PERSON>po<PERSON>, Alert, Button, Space } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  SettingOutlined,
  DatabaseOutlined,
  UserOutlined,
  MailOutlined,
  SecurityScanOutlined,
  FileTextOutlined,
  MonitorOutlined,
  SwapOutlined as SyncOutlined,
} from '@ant-design/icons';
import AdminEmailSync from '../components/AdminEmailSync';
import AdminRuleOperations from '../components/AdminRuleOperations';
import SystemTemplates from './Settings/SystemTemplates';
import UserManagement from '../components/UserManagement';
import SystemMonitoring from '../components/SystemMonitoring';
import EmailAccountMonitor from '../components/EmailAccountMonitor';
import MultiAccountSync from '../components/MultiAccountSync';
import SystemManagement from '../components/SystemManagement';
import DataMigration from '../components/DataMigration';
import { useAuthStore } from '../store/authStore';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const AdminSettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('email-sync');
  const { user } = useAuthStore();
  const navigate = useNavigate();

  // 检查是否为管理员
  const isAdmin = user?.role === 'admin';

  if (!isAdmin) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="权限不足"
          description="只有管理员才能访问管理员设置页面"
          type="warning"
          showIcon
        />
      </div>
    );
  }

  return (
    <div style={{
      padding: '24px',
      height: '100vh',
      overflow: 'auto',
      backgroundColor: '#f5f5f5'
    }}>
      <Card style={{ height: 'calc(100vh - 48px)', overflow: 'hidden' }}>
        <div style={{ marginBottom: '24px' }}>
          <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '12px' }}>
            <SettingOutlined />
            管理员设置
          </Title>
          <Text type="secondary">
            管理员专用功能，包括用户邮件同步、系统规则管理等
          </Text>
        </div>

        <Alert
          message="管理员权限"
          description="此页面包含管理员专用功能，请谨慎操作。所有操作都会被记录在系统日志中。"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 快速访问按钮 */}
        <div style={{ marginBottom: 24, textAlign: 'center' }}>
          <Space size="large">
            <Button
              type="primary"
              size="large"
              icon={<UserOutlined />}
              onClick={() => navigate('/admin/users')}
              style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                borderRadius: '8px',
                height: '48px',
                fontSize: '16px',
                fontWeight: 'bold'
              }}
            >
              用户管理中心
            </Button>

            <Button
              size="large"
              icon={<SettingOutlined />}
              onClick={() => navigate('/admin/api-test')}
              style={{
                background: 'linear-gradient(135deg, #52c41a 0%, #389e0d 100%)',
                border: 'none',
                borderRadius: '8px',
                height: '48px',
                fontSize: '16px',
                fontWeight: 'bold',
                color: 'white'
              }}
            >
              API测试
            </Button>
          </Space>
        </div>

        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size="large"
          tabPosition="top"
          style={{
            height: 'calc(100vh - 200px)',
            overflow: 'hidden'
          }}
        >
          <TabPane
            tab={
              <span>
                <DatabaseOutlined />
                邮件同步管理
              </span>
            }
            key="email-sync"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <AdminEmailSync />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <MailOutlined />
                多账户同步
              </span>
            }
            key="multi-account-sync"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <MultiAccountSync />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <MailOutlined />
                邮件规则管理
              </span>
            }
            key="email-rules"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <AdminRuleOperations />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <UserOutlined />
                用户管理
              </span>
            }
            key="user-management"
          >
            <div style={{
              height: 'calc(100vh - 250px)',
              overflow: 'auto',
              padding: '16px 0'
            }}>
              <Card>
                <Title level={4}>用户管理功能</Title>
                <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                  <Card
                    size="small"
                    hoverable
                    onClick={() => navigate('/admin/users')}
                    style={{ cursor: 'pointer' }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <UserOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
                      <div>
                        <Title level={5} style={{ margin: 0 }}>用户账户管理</Title>
                        <Text type="secondary">管理所有用户账户，包括编辑信息、重置密码、权限设置等</Text>
                      </div>
                    </div>
                  </Card>

                  <Card size="small">
                    <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                      <SecurityScanOutlined style={{ fontSize: '24px', color: '#52c41a' }} />
                      <div>
                        <Title level={5} style={{ margin: 0 }}>子账户权限管理</Title>
                        <Text type="secondary">为主账户启用/禁用子账户功能，设置子账户数量限制</Text>
                      </div>
                    </div>
                  </Card>
                </div>
              </Card>

              {/* 保留原有的用户管理组件 */}
              <div style={{ marginTop: '24px' }}>
                <UserManagement />
              </div>
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <FileTextOutlined />
                系统邮件模板
              </span>
            }
            key="system-templates"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <SystemTemplates />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <MonitorOutlined />
                邮箱账户监控
              </span>
            }
            key="email-account-monitor"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <EmailAccountMonitor />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <SettingOutlined />
                系统管理
              </span>
            }
            key="system-management"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <SystemManagement />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <SyncOutlined />
                数据迁移
              </span>
            }
            key="data-migration"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <DataMigration />
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <SecurityScanOutlined />
                系统监控
              </span>
            }
            key="system-monitoring"
          >
            <div style={{ height: 'calc(100vh - 250px)', overflow: 'auto', padding: '16px 0' }}>
              <SystemMonitoring />
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default AdminSettings;
