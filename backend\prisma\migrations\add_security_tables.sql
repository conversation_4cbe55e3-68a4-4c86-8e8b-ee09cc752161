-- 添加安全相关表
-- 创建用户安全设置表
CREATE TABLE IF NOT EXISTS `user_security_settings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `password_expiry_days` int DEFAULT NULL,
  `last_password_change` datetime(3) DEFAULT NULL,
  `two_factor_enabled` tinyint(1) NOT NULL DEFAULT '0',
  `two_factor_secret` varchar(191) DEFAULT NULL,
  `backup_codes` text,
  `login_notification` tinyint(1) NOT NULL DEFAULT '1',
  `suspicious_activity` tinyint(1) NOT NULL DEFAULT '1',
  `max_active_sessions` int NOT NULL DEFAULT '5',
  `session_timeout` int NOT NULL DEFAULT '24',
  `ip_whitelist` text,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_security_settings_user_id_key` (`user_id`),
  KEY `user_security_settings_user_id_fkey` (`user_id`),
  CONSTRAINT `user_security_settings_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建安全日志表
CREATE TABLE IF NOT EXISTS `security_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `action` varchar(191) NOT NULL,
  `ip_address` varchar(191) NOT NULL,
  `user_agent` text,
  `location` varchar(191) DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT '1',
  `details` text,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `security_logs_user_id_created_at_idx` (`user_id`,`created_at`),
  KEY `security_logs_action_created_at_idx` (`action`,`created_at`),
  KEY `security_logs_user_id_fkey` (`user_id`),
  CONSTRAINT `security_logs_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建可疑活动表
CREATE TABLE IF NOT EXISTS `suspicious_activities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int DEFAULT NULL,
  `type` varchar(191) NOT NULL,
  `description` text NOT NULL,
  `ip_address` varchar(191) NOT NULL,
  `user_agent` text,
  `severity` enum('LOW','MEDIUM','HIGH','CRITICAL') NOT NULL DEFAULT 'MEDIUM',
  `status` enum('PENDING','REVIEWED','RESOLVED','FALSE_POSITIVE') NOT NULL DEFAULT 'PENDING',
  `details` json DEFAULT NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  KEY `suspicious_activities_user_id_created_at_idx` (`user_id`,`created_at`),
  KEY `suspicious_activities_type_created_at_idx` (`type`,`created_at`),
  KEY `suspicious_activities_status_idx` (`status`),
  KEY `suspicious_activities_user_id_fkey` (`user_id`),
  CONSTRAINT `suspicious_activities_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建用户会话表
CREATE TABLE IF NOT EXISTS `user_sessions` (
  `id` varchar(191) NOT NULL,
  `user_id` int NOT NULL,
  `ip_address` varchar(191) DEFAULT NULL,
  `user_agent` text,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `expires_at` datetime(3) NOT NULL,
  `last_used_at` datetime(3) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `user_sessions_user_id_expires_at_idx` (`user_id`,`expires_at`),
  KEY `user_sessions_user_id_fkey` (`user_id`),
  CONSTRAINT `user_sessions_user_id_fkey` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
