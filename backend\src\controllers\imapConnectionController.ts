import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import logger from '../utils/logger';
import prisma from '../config/database';
import realTimeEmailService from '../services/realTimeEmailService';
import { getActiveAppPasswordForUser } from '../services/simpleAppPasswordService';
import { getUserImapConfig } from '../services/imapService';
import { testImapConnection } from '../services/imapFlowService';
import { ImapFlow } from 'imapflow';

/**
 * IMAP连接状态控制器
 * 用于测试和查看用户的IMAP连接状态
 */

/**
 * 获取用户IMAP连接状态
 * GET /api/imap-connection/status
 */
export const getImapConnectionStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const userEmail = user.email;

    logger.info(`开始获取用户 ${userEmail} 的IMAP连接状态`);

    // 1. 检查实时邮件服务中的连接状态
    let realTimeStatus;
    let isUserOnline = false;
    let hasActiveConnection = false;

    try {
      logger.info('获取实时邮件服务状态...');
      realTimeStatus = realTimeEmailService.getStatus();
      isUserOnline = realTimeStatus.onlineUsers.includes(userEmail);
      hasActiveConnection = realTimeStatus.watchedUsers.includes(userEmail);
      logger.info(`实时邮件服务状态获取成功: 在线=${isUserOnline}, 连接=${hasActiveConnection}`);
    } catch (realTimeError) {
      logger.error('获取实时邮件服务状态失败:', realTimeError);
      // 使用默认值继续执行
      realTimeStatus = {
        isInitialized: false,
        onlineUsers: [],
        activeWatchers: 0,
        watchedUsers: [],
        globalImapIdleActive: false
      };
    }

    // 2. 检查用户的应用专用密码
    let appPasswordStatus = 'none';
    let appPassword = null;
    try {
      logger.info(`获取用户 ${userEmail} 的应用专用密码...`);
      appPassword = await getActiveAppPasswordForUser(user.id, 'imap');
      appPasswordStatus = appPassword ? 'available' : 'none';
      logger.info(`应用专用密码状态: ${appPasswordStatus}`);
    } catch (error) {
      logger.error(`获取应用专用密码失败:`, error);
      appPasswordStatus = 'error';
    }

    // 3. 获取用户的IMAP配置
    let imapConfig = null;
    let imapConfigStatus = 'error';
    try {
      logger.info(`获取用户 ${userEmail} 的IMAP配置...`);
      imapConfig = await getUserImapConfig(userEmail);
      imapConfigStatus = 'available';
      logger.info(`IMAP配置获取成功: ${imapConfig.host}:${imapConfig.port}`);
    } catch (error) {
      logger.error(`获取用户 ${userEmail} IMAP配置失败:`, error);
      imapConfigStatus = 'error';
    }

    // 4. 检查用户的邮箱账户配置
    let emailAccount = null;
    try {
      logger.info(`查询用户 ${userEmail} 的邮箱账户配置...`);
      emailAccount = await prisma.emailAccount.findFirst({
        where: {
          user: { email: userEmail },
          isActive: true,
          syncEnabled: true
        },
        select: {
          id: true,
          imapHost: true,
          imapPort: true,
          imapSecure: true,
          imapUsername: true,
          lastConnectionTest: true,
          connectionStatus: true,
          lastError: true
        }
      });
      logger.info(`邮箱账户查询完成: ${emailAccount ? `找到账户ID ${emailAccount.id}` : '未找到账户'}`);
    } catch (dbError) {
      logger.error(`查询邮箱账户失败:`, dbError);
      emailAccount = null;
    }

    const response: ApiResponse = {
      success: true,
      message: '获取IMAP连接状态成功',
      data: {
        user: {
          id: user.id,
          email: userEmail,
          username: user.username
        },
        realTimeService: {
          isUserOnline,
          hasActiveConnection,
          totalOnlineUsers: realTimeStatus.onlineUsers.length,
          totalActiveConnections: realTimeStatus.activeWatchers
        },
        appPassword: {
          status: appPasswordStatus,
          hasPassword: !!appPassword
        },
        imapConfig: {
          status: imapConfigStatus,
          config: imapConfig ? {
            host: imapConfig.host,
            port: imapConfig.port,
            tls: imapConfig.tls,
            user: imapConfig.user,
            passwordLength: imapConfig.password ? imapConfig.password.length : 0
          } : null
        },
        emailAccount: emailAccount ? {
          id: emailAccount.id,
          imapHost: emailAccount.imapHost,
          imapPort: emailAccount.imapPort,
          imapSecure: emailAccount.imapSecure,
          imapUsername: emailAccount.imapUsername,
          lastConnectionTest: emailAccount.lastConnectionTest,
          connectionStatus: emailAccount.connectionStatus,
          lastError: emailAccount.lastError
        } : null,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('获取IMAP连接状态失败:', error);
    logger.error('错误堆栈:', error.stack);

    // 返回更详细的错误信息
    const response: ApiResponse = {
      success: false,
      message: `获取IMAP连接状态失败: ${error.message}`,
      data: {
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      }
    };

    res.status(500).json(response);
  }
};

/**
 * 测试IMAP连接
 * POST /api/imap-connection/test
 */
export const testImapConnection = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const userEmail = user.email;

    logger.info(`开始测试用户 ${userEmail} 的IMAP连接`);

    // 获取IMAP配置
    const imapConfig = await getUserImapConfig(userEmail);

    // 使用ImapFlow测试连接
    const testResult = await testImapConnection(userEmail);

    const response: ApiResponse = {
      success: true,
      message: 'IMAP连接测试完成',
      data: {
        userEmail,
        testResult,
        config: {
          host: imapConfig.host,
          port: imapConfig.port,
          tls: imapConfig.tls,
          user: imapConfig.user
        },
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    logger.error(`用户 ${req.user!.email} IMAP连接测试失败:`, error);
    throw new AppError(`IMAP连接测试失败: ${error.message}`, 500);
  }
};

/**
 * 强制启动用户的实时邮件监听
 * POST /api/imap-connection/start-monitoring
 */
export const startUserMonitoring = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const userEmail = user.email;

    logger.info(`手动启动用户 ${userEmail} 的实时邮件监听`);

    // 启动用户的实时监听
    await realTimeEmailService.onUserOnline(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `用户 ${userEmail} 的实时邮件监听已启动`,
      data: {
        userEmail,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    logger.error(`启动用户 ${req.user!.email} 实时监听失败:`, error);
    throw new AppError(`启动实时监听失败: ${error.message}`, 500);
  }
};

/**
 * 停止用户的实时邮件监听
 * POST /api/imap-connection/stop-monitoring
 */
export const stopUserMonitoring = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const user = req.user!;
    const userEmail = user.email;

    logger.info(`手动停止用户 ${userEmail} 的实时邮件监听`);

    // 停止用户的实时监听
    realTimeEmailService.onUserOffline(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `用户 ${userEmail} 的实时邮件监听已停止`,
      data: {
        userEmail,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    logger.error(`停止用户 ${req.user!.email} 实时监听失败:`, error);
    throw new AppError(`停止实时监听失败: ${error.message}`, 500);
  }
};
