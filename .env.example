# 生产环境配置示例
# 复制此文件为 .env 并填入实际值

# 数据库配置
DB_ROOT_PASSWORD=your-strong-root-password
DB_NAME=mailserver
DB_USER=emailuser
DB_PASSWORD=your-strong-db-password

# Redis 配置
REDIS_PASSWORD=your-strong-redis-password

# JWT 配置
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long

# 前端 URL
FRONTEND_URL=https://blindedby.love

# 邮件服务配置
SMTP_HOST=smtp.blindedby.love
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-smtp-password

# IMAP 配置
IMAP_HOST=imap.blindedby.love
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-imap-password

# SSL 证书配置
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# 域名配置
DOMAIN=blindedby.love
MAIL_DOMAIN=mail.blindedby.love

# 备份配置
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# 监控配置
ENABLE_MONITORING=true
GRAFANA_ADMIN_PASSWORD=your-grafana-password
PROMETHEUS_RETENTION=15d

# 日志配置
LOG_LEVEL=info
MAX_LOG_SIZE=100M
LOG_RETENTION_DAYS=30
