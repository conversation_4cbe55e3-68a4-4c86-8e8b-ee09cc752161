#!/usr/bin/env node

/**
 * 应用 app_passwords 表迁移
 * 这个脚本会创建 app_passwords 表并验证其结构
 */

const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

// 数据库配置
const dbConfig = {
  host: 'localhost',
  user: 'mailuser',
  password: 'HOUsc@0202',
  database: 'mailserver',
  charset: 'utf8mb4'
};

// 日志函数
const log = {
  info: (msg) => console.log(`\x1b[34m[INFO]\x1b[0m ${msg}`),
  success: (msg) => console.log(`\x1b[32m[SUCCESS]\x1b[0m ${msg}`),
  warn: (msg) => console.log(`\x1b[33m[WARN]\x1b[0m ${msg}`),
  error: (msg) => console.log(`\x1b[31m[ERROR]\x1b[0m ${msg}`)
};

async function main() {
  let connection;
  
  try {
    log.info('连接到数据库...');
    connection = await mysql.createConnection(dbConfig);
    log.success('数据库连接成功');

    // 检查表是否已存在
    log.info('检查 app_passwords 表是否存在...');
    const [tables] = await connection.execute(
      "SHOW TABLES LIKE 'app_passwords'"
    );

    if (tables.length > 0) {
      log.warn('app_passwords 表已存在，跳过创建');
      
      // 显示表结构
      log.info('当前表结构:');
      const [columns] = await connection.execute('DESCRIBE app_passwords');
      console.table(columns);
      
      // 检查数据
      const [rows] = await connection.execute('SELECT COUNT(*) as count FROM app_passwords');
      log.info(`表中现有记录数: ${rows[0].count}`);
      
      return;
    }

    // 读取迁移文件
    log.info('读取迁移文件...');
    const migrationPath = path.join(__dirname, '../prisma/migrations/add_app_passwords.sql');
    
    if (!fs.existsSync(migrationPath)) {
      throw new Error(`迁移文件不存在: ${migrationPath}`);
    }

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    log.success('迁移文件读取成功');

    // 执行迁移
    log.info('执行迁移 SQL...');
    await connection.execute(migrationSQL);
    log.success('app_passwords 表创建成功');

    // 验证表结构
    log.info('验证表结构...');
    const [columns] = await connection.execute('DESCRIBE app_passwords');
    console.table(columns);

    // 验证索引
    log.info('验证索引...');
    const [indexes] = await connection.execute('SHOW INDEX FROM app_passwords');
    console.table(indexes.map(idx => ({
      Key_name: idx.Key_name,
      Column_name: idx.Column_name,
      Non_unique: idx.Non_unique,
      Index_type: idx.Index_type
    })));

    // 验证外键约束
    log.info('验证外键约束...');
    const [constraints] = await connection.execute(`
      SELECT 
        CONSTRAINT_NAME,
        COLUMN_NAME,
        REFERENCED_TABLE_NAME,
        REFERENCED_COLUMN_NAME
      FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
      WHERE TABLE_SCHEMA = 'mailserver' 
        AND TABLE_NAME = 'app_passwords' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    `);
    
    if (constraints.length > 0) {
      console.table(constraints);
      log.success('外键约束验证成功');
    } else {
      log.warn('未找到外键约束');
    }

    log.success('app_passwords 表迁移完成！');

  } catch (error) {
    log.error('迁移失败:');
    console.error(error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      log.info('数据库连接已关闭');
    }
  }
}

// 运行脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
