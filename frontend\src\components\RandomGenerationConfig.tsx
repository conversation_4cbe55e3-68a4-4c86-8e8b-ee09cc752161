import React, { useState } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Switch,
  Space,
  Button,
  Divider,
  Card,
  Tooltip,
  message
} from 'antd';
import {
  SettingOutlined,
  QuestionCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { RandomGenerationConfig } from '../utils/randomGenerator';

interface RandomGenerationConfigProps {
  visible: boolean;
  onCancel: () => void;
  onSave: (config: RandomGenerationConfig) => void;
  initialConfig?: RandomGenerationConfig;
}

const RandomGenerationConfigModal: React.FC<RandomGenerationConfigProps> = ({
  visible,
  onCancel,
  onSave,
  initialConfig
}) => {
  const [form] = Form.useForm();

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      onSave(values);
      message.success('配置保存成功');
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const resetToDefault = () => {
    form.setFieldsValue({
      usernamePrefix: 'sub',
      usernameSuffix: '',
      usernameLength: 8,
      emailDomain: 'example.com',
      includeNumbers: true,
      includeSpecialChars: false
    });
  };

  return (
    <Modal
      title={
        <Space>
          <SettingOutlined />
          随机生成配置
        </Space>
      }
      open={visible}
      onCancel={onCancel}
      footer={[
        <Button key="reset" onClick={resetToDefault}>
          重置默认
        </Button>,
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存配置
        </Button>
      ]}
      width={600}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={initialConfig || {
          usernamePrefix: 'sub',
          usernameSuffix: '',
          usernameLength: 8,
          emailDomain: 'example.com',
          includeNumbers: true,
          includeSpecialChars: false
        }}
      >
        <Card title="用户名生成规则" size="small" style={{ marginBottom: 16 }}>
          <Form.Item
            label={
              <Space>
                用户名前缀
                <Tooltip title="生成的用户名将以此开头">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            name="usernamePrefix"
          >
            <Input placeholder="sub" />
          </Form.Item>

          <Form.Item
            label={
              <Space>
                用户名后缀
                <Tooltip title="生成的用户名将以此结尾">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            name="usernameSuffix"
          >
            <Input placeholder="留空表示无后缀" />
          </Form.Item>

          <Form.Item
            label={
              <Space>
                用户名总长度
                <Tooltip title="包括前缀和后缀的总长度">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            name="usernameLength"
          >
            <InputNumber min={3} max={20} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item label="字符类型">
            <Space direction="vertical">
              <Form.Item name="includeNumbers" valuePropName="checked" noStyle>
                <Switch checkedChildren="包含" unCheckedChildren="不含" />
                <span style={{ marginLeft: 8 }}>数字 (0-9)</span>
              </Form.Item>
              <Form.Item name="includeSpecialChars" valuePropName="checked" noStyle>
                <Switch checkedChildren="包含" unCheckedChildren="不含" />
                <span style={{ marginLeft: 8 }}>特殊字符 (_)</span>
              </Form.Item>
            </Space>
          </Form.Item>
        </Card>

        <Card title="邮箱生成规则" size="small">
          <Form.Item
            label={
              <Space>
                邮箱域名
                <Tooltip title="生成的邮箱地址的域名部分">
                  <QuestionCircleOutlined />
                </Tooltip>
              </Space>
            }
            name="emailDomain"
            rules={[
              { required: true, message: '请输入邮箱域名' },
              { pattern: /^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, message: '请输入有效的域名格式' }
            ]}
          >
            <Input placeholder="example.com" />
          </Form.Item>
        </Card>
      </Form>
    </Modal>
  );
};

export default RandomGenerationConfigModal;
