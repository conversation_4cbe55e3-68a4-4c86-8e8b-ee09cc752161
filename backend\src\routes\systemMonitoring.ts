import { Router } from 'express';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate, requireAdmin } from '../middleware/auth';
import * as systemMonitoringController from '../controllers/systemMonitoringController';

const router = Router();

// 所有路由都需要认证和管理员权限
router.use(authenticate);
router.use(requireAdmin);

/**
 * 获取系统概览信息
 * GET /api/system-monitoring/overview
 */
router.get('/overview',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(systemMonitoringController.getSystemOverview)
);

/**
 * 获取邮件服务器状态
 * GET /api/system-monitoring/mail-server
 */
router.get('/mail-server',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(systemMonitoringController.getMailServerStatus)
);

/**
 * 获取邮件统计信息
 * GET /api/system-monitoring/email-stats
 * Query参数：
 * - period: 统计周期 ('24h' | '7d' | '30d')
 */
router.get('/email-stats',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(systemMonitoringController.getEmailStatistics)
);

/**
 * 获取系统日志
 * GET /api/system-monitoring/logs
 * Query参数：
 * - type: 日志类型 ('app' | 'mail' | 'security')
 * - level: 日志级别 ('all' | 'error' | 'warn' | 'info')
 * - limit: 返回数量
 * - offset: 偏移量
 */
router.get('/logs',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(systemMonitoringController.getSystemLogs)
);

export default router;
