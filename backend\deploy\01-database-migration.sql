-- 邮件Token系统数据库迁移脚本
-- 执行前请备份数据库

USE mailserver;

-- 1. 创建邮件令牌表
CREATE TABLE IF NOT EXISTS mail_tokens (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  token_hash VARCHAR(64) NOT NULL COMMENT 'SHA256哈希，不存储原始令牌',
  purpose ENUM('imap', 'smtp') NOT NULL DEFAULT 'imap',
  is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL,
  revoked_at TIMESTAMP NULL,
  last_used_at TIMESTAMP NULL,
  
  INDEX idx_user_id (user_id),
  INDEX idx_token_hash (token_hash),
  INDEX idx_expires_at (expires_at),
  INDEX idx_purpose (purpose),
  INDEX idx_active_tokens (user_id, is_revoked, expires_at),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 2. 创建令牌使用日志表（可选，用于审计）
CREATE TABLE IF NOT EXISTS mail_token_usage_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL,
  token_hash VARCHAR(64) NOT NULL,
  action ENUM('generate', 'verify', 'revoke', 'expire', 'cleanup') NOT NULL,
  ip_address VARCHAR(45),
  user_agent TEXT,
  success BOOLEAN NOT NULL DEFAULT TRUE,
  error_message TEXT,
  metadata JSON,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_user_id (user_id),
  INDEX idx_token_hash (token_hash),
  INDEX idx_created_at (created_at),
  INDEX idx_action (action),
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. 添加用户表的Token相关字段（可选）
-- 检查并添加字段，避免重复添加
SET @sql = '';
SELECT COUNT(*) INTO @col_exists
FROM information_schema.columns
WHERE table_schema = 'mailserver'
AND table_name = 'users'
AND column_name = 'mail_token_enabled';

SET @sql = IF(@col_exists = 0,
  'ALTER TABLE users ADD COLUMN mail_token_enabled BOOLEAN DEFAULT TRUE COMMENT ''是否启用邮件Token认证''',
  'SELECT ''Column mail_token_enabled already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SELECT COUNT(*) INTO @col_exists
FROM information_schema.columns
WHERE table_schema = 'mailserver'
AND table_name = 'users'
AND column_name = 'mail_token_last_generated';

SET @sql = IF(@col_exists = 0,
  'ALTER TABLE users ADD COLUMN mail_token_last_generated TIMESTAMP NULL COMMENT ''最后生成Token的时间''',
  'SELECT ''Column mail_token_last_generated already exists'' as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 创建Token清理的存储过程
DELIMITER //

DROP PROCEDURE IF EXISTS CleanupExpiredMailTokens //

CREATE PROCEDURE CleanupExpiredMailTokens()
BEGIN
  DECLARE deleted_count INT DEFAULT 0;
  DECLARE EXIT HANDLER FOR SQLEXCEPTION
  BEGIN
    ROLLBACK;
    RESIGNAL;
  END;

  START TRANSACTION;

  -- 删除过期的令牌
  DELETE FROM mail_tokens
  WHERE expires_at < NOW();

  SET deleted_count = ROW_COUNT();

  -- 记录清理日志
  INSERT INTO mail_token_usage_logs (user_id, token_hash, action, success, metadata)
  VALUES (0, 'system', 'cleanup', TRUE, JSON_OBJECT('deleted_count', deleted_count));

  COMMIT;

  SELECT CONCAT('清理了 ', deleted_count, ' 个过期令牌') AS result;
END //

DELIMITER ;

-- 5. 创建定时清理事件（每小时执行一次）
SET GLOBAL event_scheduler = ON;

-- 删除已存在的事件
DROP EVENT IF EXISTS cleanup_expired_mail_tokens;

-- 创建新的清理事件
CREATE EVENT cleanup_expired_mail_tokens
ON SCHEDULE EVERY 1 HOUR
STARTS CURRENT_TIMESTAMP
DO
  CALL CleanupExpiredMailTokens();

-- 6. 验证表创建
SELECT 
  TABLE_NAME,
  TABLE_ROWS,
  CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'mailserver' 
AND TABLE_NAME IN ('mail_tokens', 'mail_token_usage_logs');

-- 7. 显示索引信息
SHOW INDEX FROM mail_tokens;

COMMIT;
