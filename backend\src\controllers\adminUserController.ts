import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';
import { generateBothPasswords } from '../utils/passwordUtils';

/**
 * 获取所有用户列表（管理员功能）
 */
export const getAllUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      role, 
      accountType, 
      isActive 
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);
    
    // 构建查询条件
    const where: any = {};
    
    if (search) {
      where.OR = [
        { email: { contains: search as string } },
        { username: { contains: search as string } },
        { displayName: { contains: search as string } }
      ];
    }
    
    if (role) {
      where.role = role;
    }
    
    if (accountType) {
      where.accountType = accountType;
    }
    
    if (isActive !== undefined) {
      where.isActive = isActive === 'true';
    }

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          role: true,
          accountType: true,
          parentUserId: true,
          isActive: true,
          isSubAccountEnabled: true,
          maxSubAccounts: true,
          emailVerified: true,
          createdAt: true,
          lastLoginAt: true,
          _count: {
            select: {
              subAccounts: { where: { isActive: true } },
              emails: { where: { isDeleted: false } }
            }
          }
        },
        skip,
        take: Number(limit),
        orderBy: { createdAt: 'desc' }
      }),
      prisma.user.count({ where })
    ]);

    const response: ApiResponse = {
      success: true,
      message: '获取用户列表成功',
      data: users,
      pagination: {
        page: Number(page),
        limit: Number(limit),
        total,
        pages: Math.ceil(total / Number(limit))
      }
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取用户列表失败: ${(error as Error).message}`, 500);
  }
};

/**
 * 更新用户基本信息（管理员功能）
 */
export const updateUser = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const { 
      email, 
      username, 
      displayName, 
      role, 
      accountType, 
      isActive, 
      isSubAccountEnabled,
      maxSubAccounts 
    } = req.body;

    const userIdNum = parseInt(userId);
    if (isNaN(userIdNum)) {
      throw new AppError('无效的用户ID', 400);
    }

    // 检查用户是否存在
    const existingUser = await prisma.user.findUnique({
      where: { id: userIdNum }
    });

    if (!existingUser) {
      throw new AppError('用户不存在', 404);
    }

    // 检查邮箱和用户名是否已被其他用户使用
    if (email && email !== existingUser.email) {
      const emailExists = await prisma.user.findFirst({
        where: { 
          email, 
          id: { not: userIdNum } 
        }
      });
      if (emailExists) {
        throw new AppError('邮箱已被使用', 409);
      }
    }

    if (username && username !== existingUser.username) {
      const usernameExists = await prisma.user.findFirst({
        where: { 
          username, 
          id: { not: userIdNum } 
        }
      });
      if (usernameExists) {
        throw new AppError('用户名已被使用', 409);
      }
    }

    // 更新用户信息
    const updateData: any = {};
    
    if (email) updateData.email = email;
    if (username) updateData.username = username;
    if (displayName !== undefined) updateData.displayName = displayName;
    if (role) updateData.role = role;
    if (accountType) updateData.accountType = accountType;
    if (isActive !== undefined) updateData.isActive = isActive;
    if (isSubAccountEnabled !== undefined) updateData.isSubAccountEnabled = isSubAccountEnabled;
    if (maxSubAccounts !== undefined) updateData.maxSubAccounts = maxSubAccounts;

    const updatedUser = await prisma.user.update({
      where: { id: userIdNum },
      data: updateData,
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
        accountType: true,
        isActive: true,
        isSubAccountEnabled: true,
        maxSubAccounts: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // 记录操作日志
    logger.info(`管理员 ${req.user!.email} 更新了用户 ${updatedUser.email} 的信息`);

    const response: ApiResponse = {
      success: true,
      message: '用户信息更新成功',
      data: updatedUser
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新用户信息失败: ${(error as Error).message}`, 500);
  }
};

/**
 * 重置用户密码（管理员功能）
 */
export const resetUserPassword = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const { newPassword } = req.body;

    const userIdNum = parseInt(userId);
    if (isNaN(userIdNum)) {
      throw new AppError('无效的用户ID', 400);
    }

    if (!newPassword || newPassword.length < 6) {
      throw new AppError('密码长度至少6位', 400);
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userIdNum },
      select: { id: true, email: true, username: true }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 生成新密码哈希
    const { webPassword, mailPassword } = await generateBothPasswords(newPassword);

    // 更新密码
    await prisma.user.update({
      where: { id: userIdNum },
      data: {
        password: webPassword,
        mailPassword: mailPassword
      }
    });

    // 记录操作日志
    logger.info(`管理员 ${req.user!.email} 重置了用户 ${user.email} 的密码`);

    const response: ApiResponse = {
      success: true,
      message: '用户密码重置成功'
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`重置用户密码失败: ${(error as Error).message}`, 500);
  }
};

/**
 * 切换用户子账户功能状态（管理员功能）
 */
export const toggleUserSubAccountFeature = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const { enabled, maxSubAccounts } = req.body;

    const userIdNum = parseInt(userId);
    if (isNaN(userIdNum)) {
      throw new AppError('无效的用户ID', 400);
    }

    if (typeof enabled !== 'boolean') {
      throw new AppError('请提供有效的启用状态', 400);
    }

    // 检查用户是否存在且为主账户
    const user = await prisma.user.findUnique({
      where: { id: userIdNum },
      select: { 
        id: true, 
        email: true, 
        accountType: true,
        isSubAccountEnabled: true,
        maxSubAccounts: true
      }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    if (user.accountType !== 'main') {
      throw new AppError('只能为主账户启用子账户功能', 400);
    }

    // 更新子账户功能状态
    const updateData: any = { isSubAccountEnabled: enabled };
    
    if (maxSubAccounts !== undefined && maxSubAccounts > 0) {
      updateData.maxSubAccounts = maxSubAccounts;
    }

    await prisma.user.update({
      where: { id: userIdNum },
      data: updateData
    });

    // 记录操作日志
    logger.info(`管理员 ${req.user!.email} ${enabled ? '启用' : '禁用'}了用户 ${user.email} 的子账户功能`);

    const response: ApiResponse = {
      success: true,
      message: `用户子账户功能已${enabled ? '启用' : '禁用'}`,
      data: { enabled, maxSubAccounts: maxSubAccounts || user.maxSubAccounts }
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`切换子账户功能失败: ${(error as Error).message}`, 500);
  }
};

/**
 * 删除用户（管理员功能）
 */
export const deleteUser = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const { force = false } = req.body;

    const userIdNum = parseInt(userId);
    if (isNaN(userIdNum)) {
      throw new AppError('无效的用户ID', 400);
    }

    // 不能删除自己
    if (userIdNum === req.user!.id) {
      throw new AppError('不能删除自己的账户', 400);
    }

    // 检查用户是否存在
    const user = await prisma.user.findUnique({
      where: { id: userIdNum },
      include: {
        subAccounts: { where: { isActive: true } },
        _count: {
          select: {
            emails: { where: { isDeleted: false } },
            contacts: true
          }
        }
      }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 检查是否有子账户或数据
    if (!force && (user.subAccounts.length > 0 || user._count.emails > 0 || user._count.contacts > 0)) {
      throw new AppError('用户还有关联数据，请先处理或使用强制删除', 400);
    }

    // 删除用户（级联删除相关数据）
    await prisma.user.delete({
      where: { id: userIdNum }
    });

    // 记录操作日志
    logger.warn(`管理员 ${req.user!.email} 删除了用户 ${user.email}`);

    const response: ApiResponse = {
      success: true,
      message: '用户删除成功'
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`删除用户失败: ${(error as Error).message}`, 500);
  }
};
