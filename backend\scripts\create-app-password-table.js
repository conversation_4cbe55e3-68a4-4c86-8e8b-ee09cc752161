const mysql = require('mysql2/promise');

async function createAppPasswordTable() {
  let connection;
  
  try {
    console.log('🔧 创建应用专用密码表...');

    // 连接数据库
    connection = await mysql.createConnection({
      host: 'mail.blindedby.love',
      user: 'mailuser',
      password: 'HOUsc@0202',
      database: 'mailserver'
    });

    console.log('✅ 数据库连接成功');

    // 检查表是否已存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'app_passwords'");
    
    if (tables.length > 0) {
      console.log('ℹ️  app_passwords表已存在');
    } else {
      console.log('📝 创建app_passwords表...');
      
      await connection.execute(`
        CREATE TABLE app_passwords (
          id int NOT NULL AUTO_INCREMENT,
          user_id int NOT NULL,
          name varchar(191) NOT NULL,
          password varchar(191) NOT NULL,
          purpose varchar(191) NOT NULL DEFAULT 'imap',
          is_active boolean NOT NULL DEFAULT true,
          last_used_at datetime(3) NULL,
          expires_at datetime(3) NULL,
          created_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updated_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          PRIMARY KEY (id),
          UNIQUE KEY app_passwords_user_id_name_key (user_id, name),
          KEY app_passwords_user_id_fkey (user_id),
          CONSTRAINT app_passwords_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      
      console.log('✅ app_passwords表创建成功');
    }

    // 检查是否有用户需要创建默认应用专用密码
    const [users] = await connection.execute(`
      SELECT u.id, u.email 
      FROM users u 
      LEFT JOIN app_passwords ap ON u.id = ap.user_id AND ap.purpose = 'imap' AND ap.is_active = 1
      WHERE u.isActive = 1 AND ap.id IS NULL
      LIMIT 5
    `);

    if (users.length > 0) {
      console.log(`\n📋 发现 ${users.length} 个用户需要创建默认IMAP应用专用密码:`);
      
      for (const user of users) {
        console.log(`   - ${user.email} (ID: ${user.id})`);
        
        // 为用户创建默认应用专用密码
        const testPassword = '$6$testsalt$testhash'; // 临时测试密码
        
        await connection.execute(`
          INSERT INTO app_passwords (user_id, name, password, purpose, is_active)
          VALUES (?, ?, ?, ?, ?)
        `, [user.id, 'Default IMAP', testPassword, 'imap', true]);
        
        console.log(`     ✅ 已创建默认IMAP应用专用密码`);
      }
    } else {
      console.log('\n✅ 所有活跃用户都已有IMAP应用专用密码');
    }

    // 显示统计信息
    const [stats] = await connection.execute(`
      SELECT 
        COUNT(*) as total_passwords,
        COUNT(DISTINCT user_id) as users_with_passwords,
        SUM(CASE WHEN purpose = 'imap' THEN 1 ELSE 0 END) as imap_passwords,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) as active_passwords
      FROM app_passwords
    `);

    console.log('\n📊 应用专用密码统计:');
    console.log(`   总密码数: ${stats[0].total_passwords}`);
    console.log(`   有密码的用户数: ${stats[0].users_with_passwords}`);
    console.log(`   IMAP密码数: ${stats[0].imap_passwords}`);
    console.log(`   活跃密码数: ${stats[0].active_passwords}`);

    console.log('\n🎉 应用专用密码表设置完成！');
    console.log('\n🔧 下一步:');
    console.log('1. 重启后端服务');
    console.log('2. 尝试用户登录');
    console.log('3. 测试IMAP连接');

  } catch (error) {
    console.error('❌ 创建应用专用密码表失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
createAppPasswordTable().catch(console.error);
