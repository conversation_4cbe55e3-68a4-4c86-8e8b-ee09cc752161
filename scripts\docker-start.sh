#!/bin/bash

# Docker 快速启动脚本
# 用于快速启动开发环境或生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "Docker 快速启动脚本"
    echo ""
    echo "用法: $0 [选项] [环境]"
    echo ""
    echo "环境:"
    echo "  dev     开发环境 (默认)"
    echo "  prod    生产环境"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -b, --build    强制重新构建镜像"
    echo "  -d, --down     停止并删除容器"
    echo "  -l, --logs     显示日志"
    echo "  -s, --status   显示服务状态"
    echo ""
    echo "示例:"
    echo "  $0                    # 启动开发环境"
    echo "  $0 prod               # 启动生产环境"
    echo "  $0 -b dev             # 重新构建并启动开发环境"
    echo "  $0 -d                 # 停止开发环境"
    echo "  $0 -l prod            # 查看生产环境日志"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
}

# 检查环境变量文件
check_env_file() {
    local env=$1
    local env_file=".env"
    
    if [ "$env" = "prod" ]; then
        env_file=".env.production"
    fi
    
    if [ ! -f "$env_file" ]; then
        log_warning "环境变量文件 $env_file 不存在"
        if [ -f ".env.example" ]; then
            log_info "复制 .env.example 为 $env_file"
            cp .env.example "$env_file"
            log_warning "请编辑 $env_file 文件并设置正确的环境变量"
        fi
    fi
}

# 获取 compose 文件
get_compose_file() {
    local env=$1
    if [ "$env" = "prod" ]; then
        echo "docker-compose.prod.yml"
    else
        echo "docker-compose.yml"
    fi
}

# 启动服务
start_services() {
    local env=$1
    local build=$2
    local compose_file=$(get_compose_file "$env")
    
    log_info "启动 $env 环境服务..."
    
    if [ "$build" = "true" ]; then
        log_info "重新构建镜像..."
        docker-compose -f "docker/$compose_file" build --no-cache
    fi
    
    docker-compose -f "docker/$compose_file" up -d
    
    log_success "服务启动完成"
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 5
    
    # 显示状态
    show_status "$env"
}

# 停止服务
stop_services() {
    local env=$1
    local compose_file=$(get_compose_file "$env")
    
    log_info "停止 $env 环境服务..."
    docker-compose -f "docker/$compose_file" down
    log_success "服务已停止"
}

# 显示服务状态
show_status() {
    local env=$1
    local compose_file=$(get_compose_file "$env")
    
    log_info "服务状态:"
    docker-compose -f "docker/$compose_file" ps
    
    echo ""
    log_info "访问地址:"
    if [ "$env" = "prod" ]; then
        echo "  前端: https://mail.blindedby.love"
        echo "  API:  https://mail.blindedby.love/api"
    else
        echo "  前端: http://localhost"
        echo "  API:  http://localhost/api"
    fi
}

# 显示日志
show_logs() {
    local env=$1
    local compose_file=$(get_compose_file "$env")
    
    log_info "显示 $env 环境日志..."
    docker-compose -f "docker/$compose_file" logs -f --tail=50
}

# 主函数
main() {
    local env="dev"
    local action="start"
    local build="false"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -b|--build)
                build="true"
                shift
                ;;
            -d|--down)
                action="stop"
                shift
                ;;
            -l|--logs)
                action="logs"
                shift
                ;;
            -s|--status)
                action="status"
                shift
                ;;
            dev|prod)
                env=$1
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 检查 Docker
    check_docker
    
    # 检查环境变量文件
    check_env_file "$env"
    
    # 执行操作
    case $action in
        start)
            start_services "$env" "$build"
            ;;
        stop)
            stop_services "$env"
            ;;
        status)
            show_status "$env"
            ;;
        logs)
            show_logs "$env"
            ;;
    esac
}

# 脚本入口
main "$@"
