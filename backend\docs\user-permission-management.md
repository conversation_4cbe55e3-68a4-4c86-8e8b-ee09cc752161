# 用户权限管理功能

本文档介绍了新实现的用户权限管理功能，允许管理员对系统中的用户进行权限设置和管理。

## 功能概述

### 用户角色系统
系统支持三种用户角色：

| 角色 | 权限描述 | 功能范围 |
|------|----------|----------|
| **user** (普通用户) | 基础邮件功能 | 收发邮件、管理联系人、创建模板和规则 |
| **moderator** (协调员) | 部分管理功能 | 普通用户功能 + 部分系统管理权限 |
| **admin** (管理员) | 完整管理权限 | 所有功能 + 用户管理 + 系统设置 |

### 用户状态管理
- **激活状态** (`isActive`): 控制用户是否可以登录和使用系统
- **邮箱验证** (`emailVerified`): 控制用户邮箱是否已验证

## API 接口

### 1. 获取用户列表
```http
GET /api/user-management/users
Authorization: Bearer <admin-token>
```

**查询参数：**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 20)
- `search`: 搜索关键词 (用户名、邮箱、显示名)
- `role`: 角色筛选 ('user' | 'admin' | 'moderator')
- `status`: 状态筛选 ('active' | 'inactive')
- `sortBy`: 排序字段 (默认: 'createdAt')
- `sortOrder`: 排序方向 ('asc' | 'desc', 默认: 'desc')

**响应示例：**
```json
{
  "success": true,
  "message": "获取用户列表成功",
  "data": {
    "users": [
      {
        "id": 1,
        "email": "<EMAIL>",
        "username": "testuser",
        "displayName": "测试用户",
        "role": "user",
        "isActive": true,
        "emailVerified": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z",
        "stats": {
          "emailCount": 10,
          "folderCount": 5,
          "contactCount": 3
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. 更新用户权限
```http
PUT /api/user-management/users/:userId/permissions
Authorization: Bearer <admin-token>
Content-Type: application/json
```

**请求体：**
```json
{
  "role": "moderator",
  "isActive": true,
  "emailVerified": true
}
```

**响应示例：**
```json
{
  "success": true,
  "message": "用户权限更新成功",
  "data": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "testuser",
    "role": "moderator",
    "isActive": true,
    "emailVerified": true
  }
}
```

### 3. 批量更新用户
```http
POST /api/user-management/users/batch-update
Authorization: Bearer <admin-token>
Content-Type: application/json
```

**请求体：**
```json
{
  "userIds": [1, 2, 3],
  "action": "verify_email"
}
```

**支持的批量操作：**
- `activate`: 激活用户
- `deactivate`: 禁用用户
- `verify_email`: 验证邮箱
- `unverify_email`: 取消邮箱验证
- `set_role`: 设置角色 (需要提供 `value` 字段)

**设置角色示例：**
```json
{
  "userIds": [1, 2, 3],
  "action": "set_role",
  "value": "moderator"
}
```

### 4. 获取用户详情
```http
GET /api/user-management/users/:userId
Authorization: Bearer <admin-token>
```

## 前端组件

### UserManagement 组件
位置：`frontend/src/components/UserManagement.tsx`

**主要功能：**
1. **用户列表展示**
   - 分页表格显示所有用户
   - 显示用户基本信息、角色、状态、统计数据
   - 支持搜索和筛选

2. **权限编辑**
   - 单个用户权限编辑模态框
   - 角色选择、状态切换
   - 实时保存更改

3. **批量操作**
   - 多选用户进行批量操作
   - 支持批量激活/禁用、验证邮箱、设置角色

4. **搜索和筛选**
   - 按用户名、邮箱、显示名搜索
   - 按角色和状态筛选
   - 排序功能

### 集成到管理员设置
用户权限管理已集成到管理员设置页面 (`/admin`) 作为一个独立的Tab。

## 权限控制

### 后端权限检查
- 所有用户管理API都需要管理员权限
- 使用 `requireAdmin` 中间件进行权限验证
- 管理员不能修改自己的权限
- 批量操作会自动排除管理员自己的ID

### 前端权限控制
- 只有管理员可以访问用户管理功能
- 普通用户无法看到用户管理相关菜单
- 批量操作时自动禁用管理员用户的选择

## 安全特性

1. **操作日志**
   - 所有权限更改都会记录详细日志
   - 包含操作者、目标用户、更改内容、时间戳

2. **权限验证**
   - 严格的角色验证
   - 防止权限提升攻击
   - 管理员自我保护机制

3. **数据验证**
   - 严格的输入验证
   - 角色值白名单检查
   - 用户ID有效性验证

## 使用场景

### 1. 新用户管理
- 查看新注册用户
- 验证用户邮箱
- 激活用户账户

### 2. 权限调整
- 提升用户为协调员
- 降级问题用户权限
- 临时禁用用户账户

### 3. 批量管理
- 批量验证邮箱
- 批量激活新用户
- 批量设置角色

### 4. 用户监控
- 查看用户活动统计
- 监控用户数据使用情况
- 识别异常用户行为

## 测试

### 运行测试
```bash
npm run test:user-management
```

### 测试覆盖
- ✅ 获取用户列表
- ✅ 更新用户权限
- ✅ 批量更新用户
- ✅ 权限检查
- ✅ 用户角色分布统计

## 注意事项

1. **管理员保护**：管理员无法修改自己的权限，防止意外锁定
2. **数据一致性**：权限更改会立即生效，但用户需要重新登录才能获得新权限
3. **日志记录**：所有权限操作都会记录在系统日志中
4. **性能考虑**：大量用户时建议使用分页和筛选功能
5. **备份建议**：重要权限更改前建议备份用户数据

## 故障排除

### 常见问题

1. **权限更新失败**
   - 检查目标用户是否存在
   - 确认管理员权限
   - 查看系统日志获取详细错误

2. **批量操作部分失败**
   - 检查用户ID列表是否有效
   - 确认操作类型和参数正确
   - 查看返回的影响用户数量

3. **前端显示异常**
   - 检查API响应格式
   - 确认用户权限
   - 刷新页面重新加载数据
