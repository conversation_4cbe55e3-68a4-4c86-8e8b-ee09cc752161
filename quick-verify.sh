#!/bin/bash

# 邮件系统快速验证脚本
# 用于快速检查系统是否正常运行

echo "🚀 邮件系统快速验证开始..."
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
PASSED=0
TOTAL=0

# 验证函数
verify_step() {
    local step_name="$1"
    local command="$2"
    local expected_pattern="$3"
    
    echo -n "📋 验证 $step_name... "
    TOTAL=$((TOTAL + 1))
    
    if eval "$command" 2>/dev/null | grep -q "$expected_pattern"; then
        echo -e "${GREEN}✅ 通过${NC}"
        PASSED=$((PASSED + 1))
        return 0
    else
        echo -e "${RED}❌ 失败${NC}"
        return 1
    fi
}

# 1. 检查后端服务
echo "🔧 检查后端服务..."
verify_step "后端健康检查" "curl -s http://localhost:3001/api/health" "healthy"

# 2. 检查数据库连接
echo "🗄️  检查数据库..."
verify_step "数据库连接" "curl -s http://localhost:3001/api/health/detailed" "database.*healthy"

# 3. 检查关键API端点
echo "🌐 检查API端点..."
verify_step "用户认证API" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3001/api/auth/login" "405\|400"
verify_step "邮箱账户API" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3001/api/email-accounts" "401"
verify_step "子账户API" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3001/api/sub-accounts" "401"
verify_step "系统管理API" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3001/api/system-management/health" "401\|403"

# 4. 检查前端服务（如果运行）
echo "🎨 检查前端服务..."
if curl -s http://localhost:3000 >/dev/null 2>&1; then
    verify_step "前端服务" "curl -s -o /dev/null -w '%{http_code}' http://localhost:3000" "200"
else
    echo "⚠️  前端服务未运行（可选）"
fi

# 5. 检查数据库表结构
echo "📊 检查数据库结构..."
if command -v mysql >/dev/null 2>&1; then
    # 这里需要根据实际的数据库配置调整
    echo "💡 数据库结构检查需要手动验证（请参考验证指南）"
else
    echo "💡 MySQL客户端未安装，跳过数据库结构检查"
fi

# 输出结果
echo ""
echo "📊 验证结果:"
echo "================================"
echo -e "通过: ${GREEN}$PASSED${NC}/$TOTAL"

SUCCESS_RATE=$((PASSED * 100 / TOTAL))
echo "成功率: $SUCCESS_RATE%"

if [ $SUCCESS_RATE -ge 80 ]; then
    echo -e "${GREEN}🎉 系统验证通过！${NC}"
    echo ""
    echo "✅ 邮件系统架构重构成功完成！"
    echo "✅ 核心功能正常运行"
    echo "✅ API端点响应正常"
    echo ""
    echo "📖 完整验证请参考: VERIFICATION_GUIDE.md"
    exit 0
elif [ $SUCCESS_RATE -ge 60 ]; then
    echo -e "${YELLOW}⚠️  系统部分功能正常${NC}"
    echo ""
    echo "💡 建议检查失败的验证项"
    echo "📖 详细验证请参考: VERIFICATION_GUIDE.md"
    exit 1
else
    echo -e "${RED}❌ 系统存在问题${NC}"
    echo ""
    echo "🔧 请检查以下项目:"
    echo "   1. 后端服务是否正常启动"
    echo "   2. 数据库连接是否正常"
    echo "   3. 环境配置是否正确"
    echo ""
    echo "📖 详细排查请参考: VERIFICATION_GUIDE.md"
    exit 2
fi
