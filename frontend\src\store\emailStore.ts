import { create } from 'zustand';
import type { Em<PERSON>, Folder, EmailData, EmailSearchParams } from '../types';
import * as emailApi from '../services/emailApi';
import websocketService from '../services/websocketService';
import { message } from 'antd';

interface EmailState {
  emails: Email[];
  selectedEmail: Email | null;
  folders: Folder[];
  currentFolder: Folder | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  currentQueryParams: any; // 保存当前的查询参数

  // Actions
  fetchEmails: (params?: any) => Promise<void>;
  fetchEmailById: (id: string) => Promise<void>;
  sendEmail: (data: EmailData) => Promise<void>;
  saveDraft: (data: Partial<EmailData>) => Promise<void>;
  updateEmail: (id: string, data: Partial<Email>) => Promise<void>;
  deleteEmail: (id: string) => Promise<void>;
  batchUpdateEmails: (emailIds: string[], action: string, value?: any) => Promise<void>;
  searchEmails: (params: EmailSearchParams) => Promise<void>;
  syncEmails: () => Promise<void>;
  
  fetchFolders: () => Promise<void>;
  setCurrentFolder: (folder: Folder | null) => void;
  setSelectedEmail: (email: Email | null) => void;
  clearError: () => void;
  setLoading: (loading: boolean) => void;

  // WebSocket相关
  initializeWebSocket: () => void;
  cleanupWebSocket: () => void;
  handleNewEmail: (email: Email) => void;
  handleEmailRead: (emailId: string) => void;
  handleEmailDeleted: (emailId: string) => void;
}

export const useEmailStore = create<EmailState>((set, get) => ({
  emails: [],
  selectedEmail: null,
  folders: [],
  currentFolder: null,
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  },
  currentQueryParams: {},

  fetchEmails: async (params = {}) => {
    try {
      set({ loading: true, error: null, currentQueryParams: params });

      const response = await emailApi.getEmails(params);

      set({
        emails: response.emails,
        pagination: response.pagination,
        loading: false,
      });
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '获取邮件列表失败',
      });
    }
  },

  fetchEmailById: async (id: string) => {
    try {
      set({ loading: true, error: null });
      
      const email = await emailApi.getEmailById(id);
      
      set({
        selectedEmail: email,
        loading: false,
      });
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '获取邮件详情失败',
      });
    }
  },

  sendEmail: async (data: EmailData) => {
    try {
      set({ loading: true, error: null });
      
      const email = await emailApi.sendEmail(data);
      
      // 更新邮件列表
      const { emails } = get();
      set({
        emails: [email, ...emails],
        loading: false,
      });
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '发送邮件失败',
      });
      throw error;
    }
  },

  saveDraft: async (data: Partial<EmailData>) => {
    try {
      set({ loading: true, error: null });
      
      const draft = await emailApi.saveDraft(data);
      
      // 更新邮件列表
      const { emails } = get();
      set({
        emails: [draft, ...emails],
        loading: false,
      });
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '保存草稿失败',
      });
      throw error;
    }
  },

  updateEmail: async (id: string, data: Partial<Email>) => {
    try {
      await emailApi.updateEmail(id, data);
      
      // 更新本地状态
      const { emails, selectedEmail } = get();
      const updatedEmails = emails.map(email => 
        email.id === id ? { ...email, ...data } : email
      );
      
      set({
        emails: updatedEmails,
        selectedEmail: selectedEmail?.id === id 
          ? { ...selectedEmail, ...data } 
          : selectedEmail,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.message || '更新邮件失败',
      });
      throw error;
    }
  },

  deleteEmail: async (id: string) => {
    try {
      await emailApi.deleteEmail(id);
      
      // 从本地状态中移除
      const { emails, selectedEmail } = get();
      const filteredEmails = emails.filter(email => email.id !== id);
      
      set({
        emails: filteredEmails,
        selectedEmail: selectedEmail?.id === id ? null : selectedEmail,
      });
    } catch (error: any) {
      set({
        error: error.response?.data?.message || '删除邮件失败',
      });
      throw error;
    }
  },

  batchUpdateEmails: async (emailIds: string[], action: string, value?: any) => {
    try {
      set({ loading: true, error: null });
      
      await emailApi.batchUpdateEmails(emailIds, action, value);
      
      // 重新获取邮件列表
      await get().fetchEmails();
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '批量操作失败',
      });
      throw error;
    }
  },

  searchEmails: async (params: EmailSearchParams) => {
    try {
      set({ loading: true, error: null });
      
      const response = await emailApi.searchEmails(params);
      
      set({
        emails: response.emails,
        pagination: response.pagination,
        loading: false,
      });
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '搜索邮件失败',
      });
    }
  },

  syncEmails: async () => {
    try {
      set({ loading: true, error: null });

      await emailApi.syncEmails();

      // 重新获取邮件列表，使用当前的查询参数
      const currentParams = get().currentQueryParams;
      await get().fetchEmails(currentParams);
    } catch (error: any) {
      set({
        loading: false,
        error: error.response?.data?.message || '同步邮件失败',
      });
    }
  },

  fetchFolders: async () => {
    try {
      const folders = await emailApi.getFolders();
      set({ folders });
    } catch (error: any) {
      set({
        error: error.response?.data?.message || '获取文件夹失败',
      });
    }
  },

  setCurrentFolder: (folder: Folder | null) => {
    set({ currentFolder: folder });
  },

  setSelectedEmail: (email: Email | null) => {
    set({ selectedEmail: email });
  },

  clearError: () => {
    set({ error: null });
  },

  setLoading: (loading: boolean) => {
    set({ loading });
  },

  // WebSocket相关方法
  initializeWebSocket: () => {
    console.log('🔧 初始化邮件存储的WebSocket监听');

    const handleNewEmail = (data: any) => {
      console.log('📧 邮件存储收到单个新邮件:', data);
      get().handleNewEmail(data);
    };

    const handleEmailRead = (data: any) => {
      console.log('👁️ 邮件存储收到邮件已读事件:', data);
      get().handleEmailRead(data.emailId);
    };

    const handleEmailDeleted = (data: any) => {
      console.log('🗑️ 邮件存储收到邮件删除事件:', data);
      get().handleEmailDeleted(data.emailId);
    };

    const handleNewEmails = (data: { count: number; emails: any[] }) => {
      console.log('📬 邮件存储收到新邮件通知:', data);
      console.log(`📊 邮件数量: ${data.count}, 当前文件夹: ${get().currentQueryParams.folderType}`);

      // 显示通知
      message.success(`收到 ${data.count} 封新邮件`);

      // 自动刷新邮件列表（如果当前在收件箱）
      const currentParams = get().currentQueryParams;
      console.log('📋 当前查询参数:', currentParams);

      if (!currentParams.folderType || currentParams.folderType === 'inbox') {
        console.log('🔄 开始刷新收件箱邮件列表...');
        get().fetchEmails(currentParams).then(() => {
          console.log('✅ 邮件列表刷新完成');
        }).catch((error) => {
          console.error('❌ 邮件列表刷新失败:', error);
        });
      } else {
        console.log(`ℹ️ 当前不在收件箱 (${currentParams.folderType})，不自动刷新`);
      }
    };

    // 监听WebSocket事件
    console.log('👂 注册WebSocket事件监听器');
    websocketService.on('newEmail', handleNewEmail);
    websocketService.on('newEmails', handleNewEmails);
    websocketService.on('emailRead', handleEmailRead);
    websocketService.on('emailDeleted', handleEmailDeleted);
    console.log('✅ WebSocket事件监听器注册完成');
  },

  cleanupWebSocket: () => {
    // 移除WebSocket事件监听
    websocketService.off('newEmail');
    websocketService.off('newEmails');
    websocketService.off('emailRead');
    websocketService.off('emailDeleted');
  },

  handleNewEmail: (email: Email) => {
    const { emails, currentFolder } = get();

    // 如果新邮件属于当前文件夹，则添加到列表顶部
    if (!currentFolder || email.folderId === currentFolder.id) {
      set({
        emails: [email, ...emails],
        pagination: {
          ...get().pagination,
          total: get().pagination.total + 1,
        },
      });
    }
  },

  handleEmailRead: (emailId: string) => {
    const { emails, selectedEmail } = get();

    // 更新邮件列表中的已读状态
    const updatedEmails = emails.map(email =>
      email.id === emailId ? { ...email, isRead: true } : email
    );

    set({
      emails: updatedEmails,
      selectedEmail: selectedEmail?.id === emailId
        ? { ...selectedEmail, isRead: true }
        : selectedEmail,
    });
  },

  handleEmailDeleted: (emailId: string) => {
    const { emails, selectedEmail } = get();

    // 从邮件列表中移除已删除的邮件
    const filteredEmails = emails.filter(email => email.id !== emailId);

    set({
      emails: filteredEmails,
      selectedEmail: selectedEmail?.id === emailId ? null : selectedEmail,
      pagination: {
        ...get().pagination,
        total: Math.max(0, get().pagination.total - 1),
      },
    });
  },


}));
