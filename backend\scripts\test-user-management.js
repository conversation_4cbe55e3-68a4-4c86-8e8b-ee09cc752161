#!/usr/bin/env node

/**
 * 用户管理功能测试脚本
 * 测试用户权限管理API的各项功能
 */

const { PrismaClient } = require('@prisma/client');

// 尝试导入用户管理控制器
let userManagementController;
try {
  userManagementController = require('../dist/controllers/userManagementController');
} catch (error) {
  console.error('无法加载用户管理控制器，请先编译项目：npm run build');
  process.exit(1);
}

const prisma = new PrismaClient();

// 模拟请求和响应对象
function createMockReqRes(user, params = {}, body = {}) {
  const req = {
    user,
    params,
    body,
    query: {}
  };

  let responseData = null;
  let statusCode = 200;
  
  const res = {
    json: (data) => {
      responseData = data;
      return res;
    },
    status: (code) => {
      statusCode = code;
      return res;
    },
    getResponseData: () => responseData,
    getStatusCode: () => statusCode
  };

  return { req, res };
}

// 测试获取用户列表
async function testGetAllUsers() {
  console.log('\n🧪 测试获取用户列表...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    if (!adminUser) {
      console.log('❌ 没有找到管理员用户');
      return false;
    }

    console.log(`✓ 使用管理员用户: ${adminUser.email}`);

    // 创建模拟请求
    const { req, res } = createMockReqRes(adminUser);
    req.query = { page: '1', limit: '10' };

    // 调用API
    await userManagementController.getAllUsers(req, res);

    // 获取响应数据
    const responseData = res.getResponseData();
    
    if (!responseData || !responseData.success) {
      console.log('❌ API调用失败');
      return false;
    }

    console.log('✅ 获取用户列表成功');
    console.log(`📊 返回 ${responseData.data.users.length} 个用户`);
    console.log(`📄 分页信息: 第${responseData.data.pagination.page}页，共${responseData.data.pagination.total}条`);

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试更新用户权限
async function testUpdateUserPermissions() {
  console.log('\n🧪 测试更新用户权限...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    // 获取一个普通用户
    const testUser = await prisma.user.findFirst({
      where: { 
        role: 'user',
        id: { not: adminUser?.id }
      },
      select: { id: true, email: true, role: true, isActive: true }
    });

    if (!adminUser || !testUser) {
      console.log('❌ 没有找到合适的测试用户');
      return false;
    }

    console.log(`✓ 管理员: ${adminUser.email}`);
    console.log(`✓ 测试用户: ${testUser.email}`);

    // 创建模拟请求 - 更新用户为moderator角色
    const { req, res } = createMockReqRes(
      adminUser,
      { userId: testUser.id.toString() },
      { role: 'moderator', isActive: true }
    );

    // 调用API
    await userManagementController.updateUserPermissions(req, res);

    // 获取响应数据
    const responseData = res.getResponseData();
    
    if (!responseData || !responseData.success) {
      console.log('❌ 更新用户权限失败');
      return false;
    }

    console.log('✅ 用户权限更新成功');
    console.log(`📝 用户 ${responseData.data.email} 的角色已更新为: ${responseData.data.role}`);

    // 恢复原始角色
    const { req: restoreReq, res: restoreRes } = createMockReqRes(
      adminUser,
      { userId: testUser.id.toString() },
      { role: 'user' }
    );

    await userManagementController.updateUserPermissions(restoreReq, restoreRes);
    console.log('✓ 已恢复用户原始角色');

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试批量更新用户
async function testBatchUpdateUsers() {
  console.log('\n🧪 测试批量更新用户...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    // 获取一些普通用户
    const testUsers = await prisma.user.findMany({
      where: { 
        role: 'user',
        id: { not: adminUser?.id }
      },
      select: { id: true, email: true },
      take: 2
    });

    if (!adminUser || testUsers.length === 0) {
      console.log('❌ 没有找到合适的测试用户');
      return false;
    }

    console.log(`✓ 管理员: ${adminUser.email}`);
    console.log(`✓ 测试用户: ${testUsers.map(u => u.email).join(', ')}`);

    // 创建模拟请求 - 批量验证邮箱
    const { req, res } = createMockReqRes(
      adminUser,
      {},
      { 
        userIds: testUsers.map(u => u.id),
        action: 'verify_email'
      }
    );

    // 调用API
    await userManagementController.batchUpdateUsers(req, res);

    // 获取响应数据
    const responseData = res.getResponseData();
    
    if (!responseData || !responseData.success) {
      console.log('❌ 批量更新用户失败');
      return false;
    }

    console.log('✅ 批量更新用户成功');
    console.log(`📝 成功更新 ${responseData.data.affectedCount} 个用户`);
    console.log(`🔧 执行操作: ${responseData.data.action}`);

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试权限检查
async function testPermissionCheck() {
  console.log('\n🧪 测试权限检查...');
  
  try {
    // 获取普通用户
    const normalUser = await prisma.user.findFirst({
      where: { role: 'user' },
      select: { id: true, email: true, role: true }
    });

    if (!normalUser) {
      console.log('❌ 没有找到普通用户');
      return false;
    }

    console.log(`✓ 使用普通用户: ${normalUser.email}`);

    // 创建模拟请求 - 普通用户尝试获取用户列表
    const { req, res } = createMockReqRes(normalUser);

    try {
      await userManagementController.getAllUsers(req, res);
      console.log('❌ 权限检查失败：普通用户不应该能访问用户管理功能');
      return false;
    } catch (error) {
      if (error.message.includes('权限不足')) {
        console.log('✅ 权限检查正常：普通用户被正确拒绝');
        return true;
      } else {
        console.log('❌ 权限检查异常:', error.message);
        return false;
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 检查数据库中的用户角色分布
async function checkUserRoleDistribution() {
  console.log('\n📊 检查用户角色分布...');
  
  try {
    const roleStats = await prisma.user.groupBy({
      by: ['role'],
      _count: {
        id: true
      }
    });

    console.log('用户角色统计:');
    roleStats.forEach(stat => {
      const roleNames = {
        admin: '管理员',
        moderator: '协调员',
        user: '普通用户'
      };
      const roleName = roleNames[stat.role] || stat.role;
      console.log(`  ${roleName}: ${stat._count.id} 人`);
    });

    const totalUsers = roleStats.reduce((sum, stat) => sum + stat._count.id, 0);
    console.log(`总用户数: ${totalUsers}`);

    return roleStats.length > 0;

  } catch (error) {
    console.error('❌ 检查用户角色分布失败:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 用户管理功能测试');
  console.log('================================\n');

  try {
    // 检查用户角色分布
    const roleCheckOk = await checkUserRoleDistribution();
    if (!roleCheckOk) {
      console.log('\n❌ 用户角色检查失败');
      return;
    }

    // 测试获取用户列表
    const getUsersOk = await testGetAllUsers();

    // 测试更新用户权限
    const updatePermissionsOk = await testUpdateUserPermissions();

    // 测试批量更新用户
    const batchUpdateOk = await testBatchUpdateUsers();

    // 测试权限检查
    const permissionCheckOk = await testPermissionCheck();

    // 总结
    console.log('\n📊 测试结果总结:');
    console.log('================================');
    console.log(`用户角色分布: ${roleCheckOk ? '✅' : '❌'}`);
    console.log(`获取用户列表: ${getUsersOk ? '✅' : '❌'}`);
    console.log(`更新用户权限: ${updatePermissionsOk ? '✅' : '❌'}`);
    console.log(`批量更新用户: ${batchUpdateOk ? '✅' : '❌'}`);
    console.log(`权限检查: ${permissionCheckOk ? '✅' : '❌'}`);

    if (getUsersOk && updatePermissionsOk && batchUpdateOk && permissionCheckOk) {
      console.log('\n🎉 所有测试通过！用户管理功能正常工作。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查配置和日志。');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n\n操作已取消。');
  await prisma.$disconnect();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
