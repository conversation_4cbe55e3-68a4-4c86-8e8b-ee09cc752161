/* 邮箱布局样式 */
.email-layout {
  height: 100vh;
  background-color: var(--bg-secondary);
}

/* 头部工具栏 */
.email-header {
  height: 64px;
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  padding: 16px 20px 16px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
  height: 100%;
}

/* 新建邮件按钮 */
.compose-btn {
  height: 40px;
  border-radius: 20px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.compose-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

/* Header标题样式 */
.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 16px;
}

/* 复选框圆角调整 */
.header-title .ant-checkbox-inner,
.email-checkbox .ant-checkbox-inner {
  border-radius: 4px !important;
}

.header-logo-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.header-logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 头部操作按钮 */
.header-action-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.header-action-btn:hover {
  background-color: var(--color-gray-1);
  transform: scale(1.05);
}

/* 主题切换开关 */
.theme-switch {
  background-color: var(--color-gray-3);
}

.theme-switch.ant-switch-checked {
  background-color: var(--color-primary);
}

/* 用户头像 */
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-avatar {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-menu:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--color-primary);
}

/* 在线状态指示器 */
.online-indicator .ant-badge-dot {
  background-color: #52c41a !important;
  box-shadow: 0 0 0 2px #ffffff;
  animation: pulse-online 2s infinite;
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
}

@keyframes pulse-online {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.user-menu:hover .online-indicator .ant-badge-dot {
  animation-duration: 1s;
  transform: scale(1.1);
}

/* 主内容区 */
.email-content {
  flex: 1;
  overflow: hidden;
  background-color: var(--bg-secondary);
}

.email-container {
  height: 100%;
  display: flex;
  background-color: var(--bg-primary);
}

/* 侧边栏样式 */
.email-sidebar {
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  width: 260px !important; /* 调小导航栏宽度 */
}

.email-sidebar.ant-layout-sider-collapsed {
  width: 64px !important;
}

.sidebar-content {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
}


.sidebar-logo-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.sidebar-logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-logo-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.sidebar-logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

/* 侧边栏标题样式 - 与header高度一致 */
.sidebar-title {
  height: 64px; /* 与header高度一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 0 16px;
  margin: 0px 0px 16px 0px; /* 抵消sidebar-header的padding */
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  cursor: pointer;
}

.sidebar-logo-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.sidebar-logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.sidebar-logo:hover {
  background-color: var(--color-gray-1);
}

.logo-icon {
  font-size: 24px;
  color: var(--color-primary);
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.compose-btn {
  width: 100%;
  height: 40px;
  border-radius: 20px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
  transition: all 0.3s ease;
}

.compose-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

/* 新建邮件菜单项样式 - 使用高亮主题色并保持距离 */
.compose-menu-item.ant-menu-item {
  background-color: var(--primary-hover) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
  margin: 1px 4px 20px 4px !important; /* 底部增加更大间距 */
  height: 36px !important;
  line-height: 36px !important;
  box-shadow: 0 2px 4px rgba(255, 127, 80, 0.3);
  transform: translateY(-1px); /* 轻微上移效果 */
}

/* 新建邮件按钮后面添加分隔线效果 */
.compose-menu-item.ant-menu-item::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background-color: var(--border-color);
}

.compose-menu-item.ant-menu-item:hover {
  background-color: var(--primary-hover) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
}

.compose-menu-item.ant-menu-item-selected {
  background-color: var(--primary-hover) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
}

.compose-menu-item.ant-menu-item-selected:hover {
  background-color: var(--primary-hover) !important;
  color: #ffffff !important;
  transform: translateY(-1px);
}

.menu-section {
  padding: 8px;
}

/* 移除Ant Design菜单默认右边框 */
.primary-menu.ant-menu,
.secondary-menu.ant-menu,
.settings-menu.ant-menu {
  border-inline-end: none !important;
  border-right: none !important;
}

.menu-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 8px 16px;
}

.settings-section {
  margin-top: auto;
  border-top: 1px solid var(--border-color);
  padding-top: 8px;
}

.menu-item-with-badge {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 菜单样式调整 - 更亮的选中状态和hover效果 */
.primary-menu .ant-menu-item-selected {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
  border-radius: 8px !important;
}

.primary-menu .ant-menu-item:hover:not(.ant-menu-item-selected):not(.compose-menu-item) {
  background-color: var(--color-gray-2) !important;
  border-radius: 8px !important;
}

/* 主要菜单选中状态不受hover影响 */
.primary-menu .ant-menu-item-selected:hover {
  background-color: var(--primary-color) !important;
  color: #ffffff !important;
}

/* 确保选中状态下角标仍然显示 */
.primary-menu .ant-menu-item-selected .ant-badge {
  display: inline-block !important;
  visibility: visible !important;
}

.primary-menu .ant-menu-item-selected .ant-badge-count {
  background-color: #ffffff !important;
  color: var(--primary-color) !important;
  border: 1px solid var(--primary-color) !important;
  display: inline-block !important;
  visibility: visible !important;
}

/* 确保展开状态下角标正确显示 */
.menu-item-with-badge .ant-badge {
  display: inline-block !important;
  visibility: visible !important;
}

.menu-item-with-badge .ant-badge-count {
  display: inline-block !important;
  visibility: visible !important;
}

.secondary-menu .ant-menu-item-selected {
  background-color: rgba(255, 127, 80, 0.12) !important;
  color: #B8512F !important;
  border-radius: 8px !important;
}

.secondary-menu .ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: var(--color-gray-2) !important;
  border-radius: 8px !important;
}

/* 选中状态不受hover影响 */
.secondary-menu .ant-menu-item-selected:hover {
  background-color: rgba(255, 127, 80, 0.12) !important;
  color: #B8512F !important;
}

.settings-menu .ant-menu-item-selected {
  background-color: rgba(255, 127, 80, 0.12) !important;
  color: #B8512F !important;
  border-radius: 8px !important;
}

.settings-menu .ant-menu-item:hover:not(.ant-menu-item-selected) {
  background-color: var(--color-gray-2) !important;
  border-radius: 8px !important;
}

/* 选中状态不受hover影响 */
.settings-menu .ant-menu-item-selected:hover {
  background-color: rgba(255, 127, 80, 0.12) !important;
  color: #B8512F !important;
}

/* 菜单项间距调整 */
.primary-menu .ant-menu-item,
.secondary-menu .ant-menu-item,
.settings-menu .ant-menu-item {
  margin: 1px 4px;
  height: 36px !important;
  line-height: 36px !important;
  display: flex !important;
  align-items: center !important;
}

/* 修复图标对齐问题 */
.primary-menu .ant-menu-item .ant-menu-item-icon,
.secondary-menu .ant-menu-item .ant-menu-item-icon,
.settings-menu .ant-menu-item .ant-menu-item-icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 收起状态下的角标样式 */
.ant-layout-sider-collapsed .ant-badge {
  position: relative !important;
  display: inline-block !important;
}

.ant-layout-sider-collapsed .ant-badge-count {
  min-width: 16px !important;
  height: 16px !important;
  line-height: 16px !important;
  font-size: 10px !important;
  padding: 0 4px !important;
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  z-index: 10 !important;
}

/* 收起状态下菜单项间距调整 */
.ant-layout-sider-collapsed .menu-section {
  padding: 4px;
}

.ant-layout-sider-collapsed .ant-menu-item {
  margin: 2px 4px !important;
  padding: 0 !important;
  width: 40px !important;
  height: 40px !important;
  line-height: 40px !important;
  text-align: center !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 收起状态下图标居中 */
.ant-layout-sider-collapsed .ant-menu-item .ant-menu-item-icon {
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

.ant-layout-sider-collapsed .ant-menu-item .anticon {
  font-size: 16px !important;
  margin: 0 !important;
}

/* 确保角标和图标始终显示 */
.ant-layout-sider-collapsed .ant-menu-item .ant-badge {
  display: inline-block !important;
  position: relative !important;
}

.ant-layout-sider-collapsed .ant-menu-item:hover .ant-badge {
  display: inline-block !important;
}

/* 收起状态下角标的特殊定位 */
.ant-layout-sider-collapsed .ant-menu-item .ant-badge .ant-badge-count {
  position: absolute !important;
  top: -8px !important;
  right: -8px !important;
  transform: none !important;
}

/* 确保所有图标在悬浮时都显示 */
.ant-menu-item .anticon {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.ant-menu-item:hover .anticon {
  display: inline-block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 头部工具栏更新 */
.sidebar-toggle-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-toggle-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 两栏布局 */
.email-two-column {
  display: flex;
  height: 100%;
  width: 100%;
}

/* 三栏布局 */
.email-three-column {
  display: flex;
  height: 100%;
  width: 100%;
}

/* 左侧导航栏 */
.email-sidebar {
  width: 280px;
  min-width: 280px;
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

/* 中间邮件列表 */
.email-list-panel {
  width: 400px;
  min-width: 400px;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .email-sidebar {
    width: 240px;
    min-width: 240px;
  }
  
  .email-list-panel {
    width: 360px;
    min-width: 360px;
  }
}

@media (max-width: 768px) {
  .email-header {
    padding: 0 16px;
  }

  .compose-btn {
    height: 36px;
    font-size: 14px;
  }

  .header-action-btn {
    width: 32px;
    height: 32px;
  }

  .user-avatar {
    width: 28px !important;
    height: 28px !important;
  }

  .online-indicator .ant-badge-dot {
    width: 6px !important;
    height: 6px !important;
  }

  /* 移动端隐藏侧边栏，改为抽屉模式 */
  .email-sidebar {
    display: none;
  }

  .email-list-panel {
    width: 100%;
    min-width: auto;
  }

  .email-detail-panel {
    display: none; /* 移动端隐藏详情面板，点击邮件时全屏显示 */
  }

  /* 移动端邮件项使用紧凑布局（三行） */
  .email-item-layout {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    min-height: auto;
  }

  .email-item-layout .email-item-row-1 {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .email-item-layout .email-item-row-2 {
    width: 100%;
    padding-left: 56px;
  }

  .email-item-layout .email-item-row-3 {
    width: 100%;
    padding-left: 56px;
  }

  .email-sender {
    min-width: 120px !important;
    max-width: 120px !important;
    width: 120px !important; /* 移动端固定宽度 */
    flex: 0 0 120px !important; /* 不伸缩，固定120px */
  }

  .email-subject-content {
    font-size: 13px !important;
  }

  .email-subject {
    font-size: 13px !important;
    font-weight: 500;
  }

  .email-content-preview {
    font-size: 11px !important;
    color: var(--text-secondary);
  }

  .email-attachments {
    margin-right: 4px !important;
  }

  .email-date {
    min-width: 60px !important;
    font-size: 11px !important;
    margin-left: auto;
  }
}

@media (max-width: 480px) {
  .email-header {
    padding: 0 12px;
  }
  
  .compose-btn span {
    display: none; /* 只显示图标 */
  }
  
  .header-action-btn {
    width: 28px;
    height: 28px;
  }
  
  .user-avatar {
    width: 24px !important;
    height: 24px !important;
  }
  
  .online-indicator .ant-badge-dot {
    width: 5px !important;
    height: 5px !important;
  }
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #1f1f1f;
  --bg-secondary: #141414;
  --bg-tertiary: #262626;
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --border-color: #434343;
}

[data-theme="dark"] .email-header {
  background-color: var(--bg-primary);
  border-bottom-color: var(--border-color);
}

[data-theme="dark"] .header-action-btn:hover {
  background-color: var(--bg-tertiary);
}

[data-theme="dark"] .user-menu:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 侧边栏样式 */
.sidebar-header {
  padding: 0;
  border-bottom: 1px solid var(--border-color);
}

.folder-menu {
  border: none;
  background: transparent;
}

.folder-menu .ant-menu-item {
  margin: 4px 8px;
  border-radius: 8px;
  height: 40px;
  line-height: 40px;
}

.folder-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 邮件容器样式 */
.email-container {
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  overflow: hidden;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
  max-width: 600px;
  margin: 0 auto;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.header-right {
  display: flex;
  align-items: center;
}

/* 批量操作样式 */
.batch-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background-color: var(--primary-light);
  border-radius: 6px;
  border: 1px solid var(--primary-color);
}

.selected-count {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 500;
}

/* 邮件内容区域 */
.email-content {
  flex: 1;
  width: 100%;
  min-height: 0; /* 重要：允许flex子项收缩 */
  display: flex;
  overflow: hidden;
}

/* 邮件主体容器 - 包含列表、拖拽条、详情面板 */
.email-main-container {
  display: flex;
  flex-direction: row;
  flex: 1; /* 占用剩余空间 */
  overflow: hidden;
  min-height: 0; /* 重要：允许flex子项收缩 */
  width: 100%;
}

/* 邮件列表面板 */
.email-list-panel {
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  border-right: 1px solid var(--border-color);
  min-width: 300px;
  max-width: none; /* 移除最大宽度限制 */
  height: 100%;
  min-height: 0; /* 重要：允许flex子项收缩 */
  flex-shrink: 0; /* 防止收缩 */
  flex-grow: 0; /* 防止自动增长 */
  overflow: hidden; /* 防止内容溢出 */
}

.email-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：允许flex子项收缩 */
  overflow: hidden; /* 防止内容溢出 */
}

.email-list-items {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0; /* 重要：允许滚动 */

  /* 滚动条样式 - 仅在滚动时显示 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
  transition: scrollbar-color 0.3s ease;
}

/* 鼠标悬停或正在滚动时显示滚动条 */
.email-list-items:hover,
.email-list-items:focus-within,
.email-list-items.scrolling {
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

/* Webkit浏览器滚动条样式 */
.email-list-items::-webkit-scrollbar {
  width: 6px;
}

.email-list-items::-webkit-scrollbar-track {
  background: transparent;
}

.email-list-items::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.email-list-items:hover::-webkit-scrollbar-thumb,
.email-list-items:focus-within::-webkit-scrollbar-thumb,
.email-list-items.scrolling::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
}

.email-list-items::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

/* 滚动时的动画效果 */
.email-list-items.scrolling::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.4);
}

.email-list-items .ant-spin-container {
  min-height: 100%;
}

/* 邮件项容器样式已移除 */

/* 搜索栏样式 */
.search-bar {
  padding: 12px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.search-bar .ant-space-compact {
  flex: 1;
}

.search-bar .ant-select {
  border-radius: 6px 0 0 6px !important;
}

.search-bar .ant-input-affix-wrapper {
  border-radius: 0 !important;
  border-left: none !important;
}

.search-bar .ant-btn {
  border-radius: 0 6px 6px 0 !important;
  border-left: none !important;
}

/* 高级搜索面板样式 */
.advanced-search-panel {
  padding: 16px 20px;
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 16px;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-label {
  font-size: 12px;
  color: var(--text-secondary);
  white-space: nowrap;
  min-width: 50px;
}

/* 分页器样式 - 固定在底部 */
.email-pagination {
  padding: 12px 20px;
  border-top: 1px solid var(--border-color);
  background-color: var(--bg-primary);
  flex-shrink: 0; /* 防止收缩 */
  display: flex;
  justify-content: center;
  z-index: 10;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  /* 确保分页器始终可见 */
  position: relative;
  bottom: 0;
}

/* 邮件项布局 - 紧凑版 */
.email-item {
  padding: 0 16px; /* 减少内边距使其更紧凑 */
  border-bottom: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.email-item:hover {
  background-color: var(--color-gray-1);
}

.email-item.selected {
  position: relative;
}

.email-item.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: #FF7F50; /* 使用主题色橙色 */
  z-index: 1;
}

.email-item.unread .email-subject {
  font-weight: 600;
}

/* 邮件项主要布局 - 宽屏模式 */
.email-item-layout {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  min-height: 32px; /* 确保一致的行高 */
}

.email-item-layout.compact {
  flex-direction: column;
  align-items: flex-start;
  gap: 2px; /* 减少间距使其更紧凑 */
  min-height: auto;
}

/* 邮件项各个部分 */
.email-checkbox {
  flex-shrink: 0;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 改为左对齐 */
}

.email-read-status {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* 改为左对齐 */
}

.email-delivery-status {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.email-sender {
  flex-shrink: 0;
  min-width: 200px;
  max-width: 200px;
  width: 200px; /* 固定宽度确保一致性 */
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 合并的主题内容区域 - 宽屏模式 */
.email-subject-content {
  flex: 1; /* 占用剩余空间 */
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
  min-width: 0; /* 允许flex收缩 */
}

.email-subject-content .content-separator {
  margin: 0 8px;
  flex-shrink: 0;
  overflow: visible;
  white-space: nowrap;
}

/* 紧凑模式保持分离的主题和内容 */
.email-subject {
  flex: 0 1 200px; /* 固定基础宽度，允许收缩 */
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.4;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
  min-width: 120px; /* 最小宽度 */
}

.email-content-preview {
  flex: 1; /* 占用剩余空间 */
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 12px;
  min-width: 0; /* 允许flex收缩 */
}

.email-attachments {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 60px; /* 增加最小宽度 */
  max-width: 120px; /* 限制最大宽度 */
}

/* 紧凑模式的附件样式 */
.email-attachments.compact {
  min-width: 20px;
  max-width: 20px;
}

.attachment-icon {
  color: var(--color-gray-5);
  font-size: 14px;
}

.attachment-info {
  font-size: 11px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.email-system-icons {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 20px;
}

.email-date {
  flex-shrink: 0;
  min-width: 80px;
  font-size: 12px;
  color: var(--text-secondary);
  text-align: right;
}

.email-star {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.email-star:hover {
  transform: scale(1.1);
}

.email-star.starred {
  color: #FF7F50; /* 使用主题色 */
}

.email-star.unstarred {
  color: var(--color-gray-4);
}

/* 邮件详情样式 */
.email-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.detail-header {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
}

.detail-header h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
}

.email-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sender-info {
  flex: 1;
}

.sender-name {
  font-weight: 500;
  color: var(--text-primary);
}

.email-date {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 2px;
}

.detail-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
  line-height: 1.6;
}



/* 邮件头像容器 */
.email-avatar-container {
  position: relative;
  display: inline-block;
}

.email-status-icon {
  position: absolute;
  bottom: -2px;
  right: -2px;
  background-color: var(--bg-primary);
  border-radius: 50%;
  padding: 2px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.read-icon {
  color: var(--color-gray-5);
  font-size: 12px;
}

.unread-icon {
  color: var(--color-primary);
  font-size: 12px;
}

/* 视图模式样式 - 左右分栏 */
.email-container.split .email-main-container {
  flex-direction: row;
}

.email-container.split .email-detail-panel {
  display: flex;
  flex: 1;
  min-width: 300px;
}

.email-container.split .resize-handle {
  display: block;
}

.email-container.list .email-main-container {
  flex-direction: column;
}

.email-container.list .email-detail-panel {
  display: none;
}

.email-container.list .email-list-panel {
  width: 100% !important;
  border-right: none;
}

.email-container.list .resize-handle {
  display: none;
}

/* 拖拽分隔条样式 - 垂直分隔条（左右分栏用） */
.resize-handle {
  width: 4px;
  height: 100%;
  background-color: transparent;
  cursor: col-resize;
  flex-shrink: 0;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background-color: var(--primary-color);
}

.resize-handle:active {
  background-color: var(--primary-color);
}

/* 拖拽时的全局样式 */
body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing * {
  cursor: col-resize !important;
}

/* 邮件详情面板样式 */
.email-detail-panel {
  flex: 1; /* 占据剩余空间 */
  background-color: var(--bg-primary);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-width: 300px;
  height: 100%;
  min-height: 0;
}

.email-detail {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 24px;
  overflow-y: auto;
}

.detail-header {
  margin-bottom: 24px;
  border-bottom: 1px solid var(--border-color);
  padding-bottom: 16px;
}

.detail-header h2 {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
}

.email-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sender-info {
  flex: 1;
}

.sender-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.email-date {
  font-size: 12px;
  color: var(--text-secondary);
}

.detail-content {
  flex: 1;
  line-height: 1.6;
}

.no-email-selected {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 拖拽分隔条样式 */
.resize-handle {
  width: 4px;
  background-color: transparent;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.3s ease;
}

.resize-handle:hover {
  background-color: var(--color-primary);
}

.resize-handle:active {
  background-color: var(--color-primary);
}

/* 拖拽时的全局样式 */
body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

/* 紧凑模式布局 - 窄屏幕（三行） */
.email-item-layout.compact .email-item-row-1 {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.email-item-layout.compact .email-item-row-2 {
  width: 100%;
  padding-left: 56px; /* 重新调整对齐位置 */
}

.email-item-layout.compact .email-item-row-3 {
  width: 100%;
  padding-left: 56px; /* 重新调整对齐位置 */
}

.email-item-layout.compact .email-sender {
  min-width: 150px;
  max-width: 150px;
  width: 150px; /* 紧凑模式固定宽度 */
  flex: 0 0 150px; /* 不伸缩，固定150px */
}

.email-item-layout.compact .email-date {
  min-width: auto;
  margin-left: auto;
}

.email-item-layout.compact .email-subject {
  font-size: 13px;
  font-weight: 500;
}

.email-item-layout.compact .email-content-preview {
  font-size: 11px;
  line-height: 1.3;
  color: var(--text-secondary);
}

/* 系统标识图标 */
.email-system-icons {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

.system-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

.system-icon.priority-high {
  color: #ff4d4f;
}

.system-icon.priority-low {
  color: var(--color-gray-5);
}

.system-icon.encrypted {
  color: #52c41a;
}

.system-icon.forwarded {
  color: var(--color-primary);
}

.system-icon.replied {
  color: var(--color-primary);
}

/* 邮件列表容器 */
.email-list-items {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow-y: auto;
  min-height: 0; /* 重要：允许flex子项收缩 */
}

/* 重复定义已移除，使用上面的定义 */

/* 重复定义已移除，使用上面的定义 */

/* 重复定义已移除，使用上面的定义 */

.email-content {
  flex: 1;
  display: flex;
  min-height: 0; /* 重要：允许flex子项收缩 */
  overflow: hidden;
}

/* 中等屏幕适配 */
@media (max-width: 1024px) and (min-width: 769px) {
  .email-item-layout .email-sender {
    min-width: 160px;
    max-width: 160px;
    width: 160px; /* 中等屏幕固定宽度 */
  }

  .email-item-layout .email-subject-content {
    font-size: 13px;
  }

  .email-item-layout .email-subject {
    font-size: 13px;
  }

  .email-item-layout .email-content-preview {
    font-size: 11px;
  }

  .email-item-layout .email-date {
    min-width: 70px;
    font-size: 11px;
  }
}

/* 当列表面板宽度较小时强制使用紧凑模式 */
@media (max-width: 600px) {
  .email-list-panel .email-item-layout:not(.compact) {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 4px !important;
    min-height: auto !important;
  }

  .email-list-panel .email-item-layout:not(.compact) > * {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .email-list-panel .email-item-layout:not(.compact) .email-subject,
  .email-list-panel .email-item-layout:not(.compact) .email-content-preview {
    width: 100%;
    padding-left: 56px;
  }
}
