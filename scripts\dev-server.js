#!/usr/bin/env node

/**
 * 开发服务器管理脚本
 * 用于启动、停止和重启开发环境的后端服务
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// 颜色定义
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// 日志函数
const log = {
  info: (msg) => console.log(`${colors.blue}[INFO]${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}[SUCCESS]${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}[WARNING]${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}[ERROR]${colors.reset} ${msg}`),
  debug: (msg) => console.log(`${colors.magenta}[DEBUG]${colors.reset} ${msg}`)
};

// PID 文件路径
const PID_FILE = path.join(__dirname, '..', 'backend', '.dev-server.pid');

// 保存 PID
function savePid(pid) {
  try {
    fs.writeFileSync(PID_FILE, pid.toString());
    log.debug(`PID ${pid} 已保存到 ${PID_FILE}`);
  } catch (error) {
    log.error(`保存 PID 失败: ${error.message}`);
  }
}

// 读取 PID
function readPid() {
  try {
    if (fs.existsSync(PID_FILE)) {
      const pid = parseInt(fs.readFileSync(PID_FILE, 'utf8').trim());
      return isNaN(pid) ? null : pid;
    }
  } catch (error) {
    log.error(`读取 PID 失败: ${error.message}`);
  }
  return null;
}

// 删除 PID 文件
function removePidFile() {
  try {
    if (fs.existsSync(PID_FILE)) {
      fs.unlinkSync(PID_FILE);
      log.debug(`PID 文件已删除: ${PID_FILE}`);
    }
  } catch (error) {
    log.error(`删除 PID 文件失败: ${error.message}`);
  }
}

// 检查进程是否存在
function isProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (error) {
    return false;
  }
}

// 强制终止进程树
function killProcessTree(pid) {
  return new Promise((resolve) => {
    if (process.platform === 'win32') {
      // Windows 系统
      exec(`taskkill /F /T /PID ${pid}`, (error) => {
        if (error) {
          log.warning(`终止进程 ${pid} 时出现警告: ${error.message}`);
        }
        resolve();
      });
    } else {
      // Unix 系统
      exec(`pkill -TERM -P ${pid}`, (error) => {
        if (error) {
          log.warning(`终止进程 ${pid} 时出现警告: ${error.message}`);
        }
        setTimeout(() => {
          try {
            process.kill(pid, 'SIGKILL');
          } catch (e) {
            // 进程可能已经终止
          }
          resolve();
        }, 2000);
      });
    }
  });
}

// 启动开发服务器
async function startServer() {
  log.info('启动开发服务器...');
  
  // 检查是否已有服务器在运行
  const existingPid = readPid();
  if (existingPid && isProcessRunning(existingPid)) {
    log.warning(`开发服务器已在运行 (PID: ${existingPid})`);
    log.info('如需重启，请先运行: npm run dev:stop');
    return;
  }
  
  // 清理旧的 PID 文件
  removePidFile();
  
  // 切换到后端目录
  const backendDir = path.join(__dirname, '..', 'backend');
  
  // 启动 nodemon
  const child = spawn('npx', ['nodemon'], {
    cwd: backendDir,
    stdio: 'inherit',
    shell: true
  });
  
  // 保存主进程 PID
  savePid(child.pid);
  
  // 处理进程事件
  child.on('error', (error) => {
    log.error(`启动失败: ${error.message}`);
    removePidFile();
  });
  
  child.on('exit', (code, signal) => {
    if (signal) {
      log.info(`开发服务器被信号 ${signal} 终止`);
    } else {
      log.info(`开发服务器退出，退出码: ${code}`);
    }
    removePidFile();
  });
  
  // 处理 Ctrl+C
  process.on('SIGINT', async () => {
    log.info('收到中断信号，正在停止开发服务器...');
    await stopServer();
    process.exit(0);
  });
  
  log.success(`开发服务器已启动 (PID: ${child.pid})`);
  log.info('按 Ctrl+C 停止服务器');
}

// 停止开发服务器
async function stopServer() {
  log.info('停止开发服务器...');
  
  const pid = readPid();
  if (!pid) {
    log.warning('没有找到运行中的开发服务器');
    return;
  }
  
  if (!isProcessRunning(pid)) {
    log.warning(`进程 ${pid} 不存在，清理 PID 文件`);
    removePidFile();
    return;
  }
  
  log.info(`正在终止进程 ${pid}...`);
  await killProcessTree(pid);
  
  // 等待一段时间确保进程完全终止
  setTimeout(() => {
    if (isProcessRunning(pid)) {
      log.warning(`进程 ${pid} 仍在运行，尝试强制终止`);
      try {
        process.kill(pid, 'SIGKILL');
      } catch (error) {
        log.error(`强制终止失败: ${error.message}`);
      }
    }
    removePidFile();
    log.success('开发服务器已停止');
  }, 3000);
}

// 重启开发服务器
async function restartServer() {
  log.info('重启开发服务器...');
  await stopServer();
  
  // 等待一段时间确保进程完全终止
  setTimeout(() => {
    startServer();
  }, 2000);
}

// 显示服务器状态
function showStatus() {
  const pid = readPid();
  if (!pid) {
    log.info('开发服务器未运行');
    return;
  }
  
  if (isProcessRunning(pid)) {
    log.success(`开发服务器正在运行 (PID: ${pid})`);
  } else {
    log.warning(`PID 文件存在但进程 ${pid} 不存在`);
    removePidFile();
  }
}

// 显示帮助信息
function showHelp() {
  console.log(`
${colors.cyan}开发服务器管理脚本${colors.reset}

用法:
  node scripts/dev-server.js <command>

命令:
  start     启动开发服务器
  stop      停止开发服务器
  restart   重启开发服务器
  status    显示服务器状态
  help      显示此帮助信息

示例:
  node scripts/dev-server.js start
  node scripts/dev-server.js stop
  node scripts/dev-server.js restart
  node scripts/dev-server.js status
`);
}

// 主函数
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'start':
      startServer();
      break;
    case 'stop':
      stopServer();
      break;
    case 'restart':
      restartServer();
      break;
    case 'status':
      showStatus();
      break;
    case 'help':
    case '--help':
    case '-h':
      showHelp();
      break;
    default:
      log.error('未知命令');
      showHelp();
      process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  startServer,
  stopServer,
  restartServer,
  showStatus
};
