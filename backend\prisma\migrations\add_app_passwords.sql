-- 添加应用专用密码表
CREATE TABLE IF NOT EXISTS `app_passwords` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL,
  `name` varchar(191) NOT NULL,
  `password` varchar(191) NOT NULL,
  `purpose` varchar(191) NOT NULL DEFAULT 'imap',
  `is_active` boolean NOT NULL DEFAULT true,
  `last_used_at` datetime(3) NULL,
  `expires_at` datetime(3) NULL,
  `created_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  `updated_at` datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (`id`),
  UNIQUE KEY `app_passwords_user_id_name_key` (`user_id`, `name`),
  KEY `app_passwords_user_id_fkey` (`user_id`),
  CONSTRAINT `app_passwords_user_id_fkey` FOR<PERSON><PERSON>N KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
