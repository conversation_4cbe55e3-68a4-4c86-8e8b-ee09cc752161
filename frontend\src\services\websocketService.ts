import { io, Socket } from 'socket.io-client';
// import type { WebSocketEvent } from '../types';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners: Map<string, Function[]> = new Map();

  connect(userId: number) {
    if (this.socket?.connected) {
      console.log('WebSocket已连接，跳过重复连接');
      return;
    }

    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3001';
    console.log(`🔌 尝试连接WebSocket: ${wsUrl}, 用户ID: ${userId}`);

    this.socket = io(wsUrl, {
      transports: ['websocket', 'polling'],
      autoConnect: true,
      withCredentials: true,
      forceNew: true,
    });

    this.socket.on('connect', () => {
      console.log('✅ WebSocket连接已建立');
      console.log(`🆔 Socket ID: ${this.socket?.id}`);
      this.reconnectAttempts = 0;

      // 加入用户房间
      console.log(`🏠 发送join事件，用户ID: ${userId}`);
      this.socket?.emit('join', userId.toString());
    });

    // 监听新邮件通知
    this.socket.on('newEmails', (data) => {
      console.log('📧 收到新邮件通知:', data);
      console.log(`📊 邮件数量: ${data.count}`);
      this.emit('newEmails', data);
    });

    this.socket.on('disconnect', (reason) => {
      console.log('❌ WebSocket连接断开:', reason);

      if (reason === 'io server disconnect') {
        // 服务器主动断开，需要重新连接
        console.log('🔄 服务器主动断开，准备重新连接');
        this.reconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('❌ WebSocket连接错误:', error);
      this.reconnect();
    });

    // 监听新邮件事件
    this.socket.on('newEmail', (data) => {
      this.emit('newEmail', data);
    });

    // 监听邮件状态更新事件
    this.socket.on('emailRead', (data) => {
      this.emit('emailRead', data);
    });

    // 监听邮件删除事件
    this.socket.on('emailDeleted', (data) => {
      this.emit('emailDeleted', data);
    });

    // 监听通知事件
    this.socket.on('notification', (data) => {
      this.emit('notification', data);
    });
  }

  disconnect() {
    if (this.socket) {
      console.log('🔌 断开WebSocket连接');
      this.socket.disconnect();
      this.socket = null;
    }
    this.eventListeners.clear();
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  private reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('WebSocket重连次数已达上限');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
    
    setTimeout(() => {
      if (this.socket && !this.socket.connected) {
        this.socket.connect();
      }
    }, delay);
  }

  // 事件监听
  on(event: string, callback: Function) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)?.push(callback);
  }

  // 移除事件监听
  off(event: string, callback?: Function) {
    if (!callback) {
      this.eventListeners.delete(event);
      return;
    }

    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  // 触发事件
  private emit(event: string, data: any) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }

  // 发送消息
  send(event: string, data: any) {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    } else {
      console.warn('WebSocket未连接，无法发送消息');
    }
  }
}

// 创建单例实例
const websocketService = new WebSocketService();

export default websocketService;
