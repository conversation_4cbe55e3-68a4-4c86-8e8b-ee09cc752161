import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  Button,
  message,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Select,
  InputNumber,
  Radio
} from 'antd';
import {
  MailOutlined,
  SaveOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;

interface EmailPreferences {
  autoRefresh: boolean;
  refreshInterval: number;
  showPreview: boolean;
  markAsReadOnPreview: boolean;
  defaultReplyAll: boolean;
  autoSaveInterval: number;
  showImages: boolean;
  composeMode: 'html' | 'text';
  defaultPriority: 'low' | 'normal' | 'high';
  autoSignature: boolean;
  confirmBeforeSend: boolean;
  confirmBeforeDelete: boolean;
  pageSize: number;
  sortBy: 'date' | 'sender' | 'subject';
  sortOrder: 'asc' | 'desc';
}

const EmailPreferencesPage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [preferences, setPreferences] = useState<EmailPreferences | null>(null);

  useEffect(() => {
    loadPreferences();
  }, []);

  const loadPreferences = async () => {
    try {
      // 模拟加载邮件偏好设置
      const mockPreferences: EmailPreferences = {
        autoRefresh: true,
        refreshInterval: 30,
        showPreview: true,
        markAsReadOnPreview: false,
        defaultReplyAll: false,
        autoSaveInterval: 60,
        showImages: false,
        composeMode: 'html',
        defaultPriority: 'normal',
        autoSignature: true,
        confirmBeforeSend: false,
        confirmBeforeDelete: true,
        pageSize: 20,
        sortBy: 'date',
        sortOrder: 'desc'
      };
      setPreferences(mockPreferences);
      form.setFieldsValue(mockPreferences);
    } catch (error) {
      message.error('加载邮件偏好设置失败');
    }
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 模拟保存邮件偏好设置
      setPreferences(values);
      message.success('邮件偏好设置保存成功');
    } catch (error) {
      message.error('保存邮件偏好设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = () => {
    form.resetFields();
    message.info('已重置为默认设置');
  };

  return (
    <Card>
            <Title level={3} style={{ marginBottom: '24px' }}>
              <MailOutlined style={{ marginRight: '8px' }} />
              邮件偏好设置
            </Title>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={preferences}
            >
              {/* 邮件刷新设置 */}
              <Row gutter={[24, 24]}>
                <Col span={24}>
                  <Title level={4}>邮件刷新</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="autoRefresh"
                    label="自动刷新邮件"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="refreshInterval"
                    label="刷新间隔（秒）"
                    dependencies={['autoRefresh']}
                  >
                    <InputNumber
                      min={10}
                      max={300}
                      disabled={!form.getFieldValue('autoRefresh')}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>

                <Divider />

                {/* 邮件显示设置 */}
                <Col span={24}>
                  <Title level={4}>邮件显示</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="showPreview"
                    label="显示邮件预览"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="markAsReadOnPreview"
                    label="预览时标记为已读"
                    valuePropName="checked"
                    dependencies={['showPreview']}
                  >
                    <Switch disabled={!form.getFieldValue('showPreview')} />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="showImages"
                    label="自动显示图片"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="pageSize"
                    label="每页显示邮件数"
                  >
                    <Select style={{ width: '100%' }}>
                      <Select.Option value={10}>10</Select.Option>
                      <Select.Option value={20}>20</Select.Option>
                      <Select.Option value={50}>50</Select.Option>
                      <Select.Option value={100}>100</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Divider />

                {/* 邮件排序设置 */}
                <Col span={24}>
                  <Title level={4}>邮件排序</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="sortBy"
                    label="排序方式"
                  >
                    <Select style={{ width: '100%' }}>
                      <Select.Option value="date">按日期</Select.Option>
                      <Select.Option value="sender">按发件人</Select.Option>
                      <Select.Option value="subject">按主题</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="sortOrder"
                    label="排序顺序"
                  >
                    <Radio.Group>
                      <Radio value="desc">降序</Radio>
                      <Radio value="asc">升序</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>

                <Divider />

                {/* 写邮件设置 */}
                <Col span={24}>
                  <Title level={4}>写邮件</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="composeMode"
                    label="默认编辑模式"
                  >
                    <Radio.Group>
                      <Radio value="html">富文本</Radio>
                      <Radio value="text">纯文本</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="defaultPriority"
                    label="默认优先级"
                  >
                    <Select style={{ width: '100%' }}>
                      <Select.Option value="low">低优先级</Select.Option>
                      <Select.Option value="normal">普通</Select.Option>
                      <Select.Option value="high">高优先级</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="defaultReplyAll"
                    label="默认回复全部"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="autoSignature"
                    label="自动添加签名"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="autoSaveInterval"
                    label="自动保存间隔（秒）"
                  >
                    <InputNumber
                      min={30}
                      max={300}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </Col>

                <Divider />

                {/* 确认设置 */}
                <Col span={24}>
                  <Title level={4}>确认操作</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="confirmBeforeSend"
                    label="发送前确认"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="confirmBeforeDelete"
                    label="删除前确认"
                    valuePropName="checked"
                  >
                    <Switch />
                  </Form.Item>
                </Col>

                {/* 操作按钮 */}
                <Col span={24}>
                  <Divider />
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SaveOutlined />}
                      loading={loading}
                      size="large"
                    >
                      保存设置
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={handleReset}
                      size="large"
                    >
                      重置默认
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
    </Card>
  );
};

export default EmailPreferencesPage;
