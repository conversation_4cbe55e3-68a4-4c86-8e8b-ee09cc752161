import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  List,
  Modal,
  Form,
  Input,
  message,
  Space,
  Typography,
  Row,
  Col,
  Tag,
  Popconfirm,
  Select,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CopyOutlined,
  MailOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import RichTextEditor from '../components/RichTextEditor';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  content: string;
  category: string;
  isDefault: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount: number;
}

const EmailTemplatesPage: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<EmailTemplate | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewTemplate, setPreviewTemplate] = useState<EmailTemplate | null>(null);
  const [filterCategory, setFilterCategory] = useState<string>('all');

  const categories = [
    { label: '全部', value: 'all' },
    { label: '商务邮件', value: 'business' },
    { label: '客户服务', value: 'service' },
    { label: '营销推广', value: 'marketing' },
    { label: '内部通知', value: 'internal' },
    { label: '其他', value: 'other' }
  ];

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    setLoading(true);
    try {
      // 模拟数据 - 实际应该从API获取
      const mockTemplates: EmailTemplate[] = [
        {
          id: '1',
          name: '会议邀请模板',
          subject: '会议邀请：{会议主题}',
          content: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6;">
              <h3>会议邀请</h3>
              <p>尊敬的 {收件人姓名}：</p>
              <p>您好！我们诚挚邀请您参加以下会议：</p>
              <ul>
                <li><strong>会议主题：</strong>{会议主题}</li>
                <li><strong>会议时间：</strong>{会议时间}</li>
                <li><strong>会议地点：</strong>{会议地点}</li>
                <li><strong>会议议程：</strong>{会议议程}</li>
              </ul>
              <p>请您确认是否能够参加，如有任何问题，请随时联系我们。</p>
              <p>谢谢！</p>
            </div>
          `,
          category: 'business',
          isDefault: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01',
          usageCount: 25
        },
        {
          id: '2',
          name: '客户问候模板',
          subject: '感谢您的支持',
          content: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6;">
              <h3>感谢您的支持</h3>
              <p>亲爱的 {客户姓名}：</p>
              <p>感谢您一直以来对我们的信任和支持！</p>
              <p>我们将继续为您提供优质的服务，如有任何需要，请随时联系我们。</p>
              <p>祝您工作顺利，生活愉快！</p>
            </div>
          `,
          category: 'service',
          isDefault: false,
          createdAt: '2024-01-02',
          updatedAt: '2024-01-02',
          usageCount: 18
        },
        {
          id: '3',
          name: '产品推广模板',
          subject: '新产品发布通知',
          content: `
            <div style="font-family: Arial, sans-serif; line-height: 1.6;">
              <h3>新产品发布</h3>
              <p>尊敬的客户：</p>
              <p>我们很高兴地向您介绍我们的新产品：{产品名称}</p>
              <p><strong>产品特点：</strong></p>
              <ul>
                <li>{特点1}</li>
                <li>{特点2}</li>
                <li>{特点3}</li>
              </ul>
              <p><strong>特别优惠：</strong>{优惠信息}</p>
              <p>了解更多详情，请访问我们的网站或联系我们。</p>
            </div>
          `,
          category: 'marketing',
          isDefault: false,
          createdAt: '2024-01-03',
          updatedAt: '2024-01-03',
          usageCount: 12
        }
      ];
      setTemplates(mockTemplates);
    } catch (error) {
      message.error('加载模板失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateTemplate = () => {
    setEditingTemplate(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditTemplate = (template: EmailTemplate) => {
    setEditingTemplate(template);
    form.setFieldsValue({
      name: template.name,
      subject: template.subject,
      content: template.content,
      category: template.category
    });
    setIsModalVisible(true);
  };

  const handleSaveTemplate = async (values: any) => {
    setLoading(true);
    try {
      if (editingTemplate) {
        // 更新模板
        const updatedTemplates = templates.map(tpl =>
          tpl.id === editingTemplate.id
            ? { ...tpl, ...values, updatedAt: new Date().toISOString() }
            : tpl
        );
        setTemplates(updatedTemplates);
        message.success('模板更新成功');
      } else {
        // 创建新模板
        const newTemplate: EmailTemplate = {
          id: Date.now().toString(),
          name: values.name,
          subject: values.subject,
          content: values.content,
          category: values.category,
          isDefault: false,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          usageCount: 0
        };
        setTemplates([...templates, newTemplate]);
        message.success('模板创建成功');
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('保存模板失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteTemplate = async (id: string) => {
    try {
      const updatedTemplates = templates.filter(tpl => tpl.id !== id);
      setTemplates(updatedTemplates);
      message.success('模板删除成功');
    } catch (error) {
      message.error('删除模板失败');
    }
  };

  const handlePreview = (template: EmailTemplate) => {
    setPreviewTemplate(template);
    setPreviewVisible(true);
  };

  const handleUseTemplate = (template: EmailTemplate) => {
    // 跳转到写邮件页面，并传递模板内容
    const params = new URLSearchParams({
      subject: template.subject,
      content: template.content
    });
    navigate(`/compose?${params.toString()}`);
  };

  const handleCopyTemplate = async (template: EmailTemplate) => {
    try {
      const newTemplate: EmailTemplate = {
        ...template,
        id: Date.now().toString(),
        name: `${template.name} (副本)`,
        isDefault: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        usageCount: 0
      };
      setTemplates([...templates, newTemplate]);
      message.success('模板复制成功');
    } catch (error) {
      message.error('复制模板失败');
    }
  };

  const filteredTemplates = templates.filter(template => 
    filterCategory === 'all' || template.category === filterCategory
  );

  return (
    <div style={{ padding: '24px' }}>
      <Row gutter={[24, 24]}>
        <Col span={24}>
          <Card>
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
              <Title level={3} style={{ margin: 0 }}>邮件模板管理</Title>
              <Space>
                <Select
                  value={filterCategory}
                  onChange={setFilterCategory}
                  style={{ width: '120px' }}
                  options={categories}
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateTemplate}
                >
                  创建模板
                </Button>
              </Space>
            </div>

            <List
              loading={loading}
              dataSource={filteredTemplates}
              renderItem={(template) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => handlePreview(template)}
                    >
                      预览
                    </Button>,
                    <Button
                      type="text"
                      icon={<MailOutlined />}
                      onClick={() => handleUseTemplate(template)}
                    >
                      使用
                    </Button>,
                    <Button
                      type="text"
                      icon={<CopyOutlined />}
                      onClick={() => handleCopyTemplate(template)}
                    >
                      复制
                    </Button>,
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditTemplate(template)}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      title="确定要删除这个模板吗？"
                      onConfirm={() => handleDeleteTemplate(template.id)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        disabled={template.isDefault}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ]}
                >
                  <List.Item.Meta
                    avatar={<FileTextOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                    title={
                      <Space>
                        <span>{template.name}</span>
                        {template.isDefault && (
                          <Tag color="blue">默认</Tag>
                        )}
                        <Tag color="green">{categories.find(c => c.value === template.category)?.label}</Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: '8px' }}>
                          <Text strong>主题：</Text>
                          <Text type="secondary">{template.subject}</Text>
                        </div>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Text type="secondary">
                            创建时间：{new Date(template.createdAt).toLocaleString()}
                          </Text>
                          <Text type="secondary">
                            使用次数：{template.usageCount}
                          </Text>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 创建/编辑模板模态框 */}
      <Modal
        title={editingTemplate ? '编辑模板' : '创建模板'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveTemplate}
        >
          <Form.Item
            name="name"
            label="模板名称"
            rules={[{ required: true, message: '请输入模板名称' }]}
          >
            <Input placeholder="请输入模板名称" />
          </Form.Item>

          <Form.Item
            name="category"
            label="模板分类"
            rules={[{ required: true, message: '请选择模板分类' }]}
          >
            <Select
              placeholder="请选择模板分类"
              options={categories.filter(c => c.value !== 'all')}
            />
          </Form.Item>

          <Form.Item
            name="subject"
            label="邮件主题"
            rules={[{ required: true, message: '请输入邮件主题' }]}
          >
            <Input placeholder="请输入邮件主题，可使用 {变量名} 作为占位符" />
          </Form.Item>

          <Form.Item
            name="content"
            label="邮件内容"
            rules={[{ required: true, message: '请输入邮件内容' }]}
          >
            <RichTextEditor
              placeholder="请输入邮件内容，可使用 {变量名} 作为占位符..."
              height={300}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                保存
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title="模板预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="use" 
            type="primary" 
            onClick={() => {
              if (previewTemplate) {
                handleUseTemplate(previewTemplate);
              }
            }}
          >
            使用模板
          </Button>
        ]}
        width={600}
      >
        {previewTemplate && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <Text strong>主题：</Text>
              <Text>{previewTemplate.subject}</Text>
            </div>
            <Divider />
            <div style={{ marginBottom: '16px' }}>
              <Text strong>内容：</Text>
            </div>
            <div
              style={{
                border: '1px solid #d9d9d9',
                borderRadius: '6px',
                padding: '16px',
                backgroundColor: '#fafafa',
                maxHeight: '400px',
                overflow: 'auto'
              }}
              dangerouslySetInnerHTML={{ __html: previewTemplate.content }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default EmailTemplatesPage;
