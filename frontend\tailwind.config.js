/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#FFFEF7',
          100: '#FFFDD0',
          200: '#FFF9E6',
          300: '#F5DEB3',
          400: '#D2B48C',
          500: '#A0522D',
          600: '#8B4513',
          700: '#CD5C5C',
          800: '#B22222',
          900: '#8B0000',
        },
        warm: {
          cream: '#FFFDD0',
          beige: '#FFF9E6',
          tan: '#D2B48C',
          brown: '#A0522D',
          darkBrown: '#8B4513',
          brick: '#CD5C5C',
          darkBrick: '#B22222',
        },
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
  corePlugins: {
    preflight: false, // 禁用Tailwind的基础样式重置，避免与Ant Design冲突
  },
}
