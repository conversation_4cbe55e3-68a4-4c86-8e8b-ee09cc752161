import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Typography,
  Tag,
  Popconfirm,
  InputNumber,
  Tooltip,
  Badge
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  TeamOutlined,
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuthStore } from '../store/authStore';

const { Title, Text } = Typography;
const { Option } = Select;

interface User {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  role: string;
  accountType: string;
  parentUserId?: number;
  isActive: boolean;
  isSubAccountEnabled?: boolean;
  maxSubAccounts?: number;
  emailVerified: boolean;
  createdAt: string;
  lastLoginAt?: string;
  _count: {
    subAccounts: number;
    emails: number;
  };
}

const AdminUserManagement: React.FC = () => {
  const { user: currentUser } = useAuthStore();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [subAccountModalVisible, setSubAccountModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchText, setSearchText] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('');
  const [accountTypeFilter, setAccountTypeFilter] = useState<string>('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const [editForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [subAccountForm] = Form.useForm();

  // 加载用户列表
  const loadUsers = async (page = 1, pageSize = 20) => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
      });

      if (searchText) params.append('search', searchText);
      if (roleFilter) params.append('role', roleFilter);
      if (accountTypeFilter) params.append('accountType', accountTypeFilter);

      const response = await fetch(`/api/admin/users?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取用户列表失败');
      }

      const data = await response.json();
      setUsers(data.data || []);
      setPagination({
        current: data.pagination?.page || 1,
        pageSize: data.pagination?.limit || 20,
        total: data.pagination?.total || 0,
      });
    } catch (error) {
      message.error('加载用户列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, [searchText, roleFilter, accountTypeFilter]);

  // 编辑用户
  const handleEdit = (user: User) => {
    setSelectedUser(user);
    editForm.setFieldsValue({
      email: user.email,
      username: user.username,
      displayName: user.displayName,
      role: user.role,
      accountType: user.accountType,
      isActive: user.isActive,
    });
    setEditModalVisible(true);
  };

  // 保存用户编辑
  const handleSaveEdit = async (values: any) => {
    if (!selectedUser) return;

    try {
      const response = await fetch(`/api/admin/users/${selectedUser.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '更新用户失败');
      }

      message.success('用户信息更新成功');
      setEditModalVisible(false);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  // 重置密码
  const handleResetPassword = (user: User) => {
    setSelectedUser(user);
    passwordForm.resetFields();
    setPasswordModalVisible(true);
  };

  // 保存密码重置
  const handleSavePassword = async (values: any) => {
    if (!selectedUser) return;

    try {
      const response = await fetch(`/api/admin/users/${selectedUser.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '重置密码失败');
      }

      message.success('密码重置成功');
      setPasswordModalVisible(false);
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  // 管理子账户功能
  const handleManageSubAccount = (user: User) => {
    setSelectedUser(user);
    subAccountForm.setFieldsValue({
      enabled: user.isSubAccountEnabled || false,
      maxSubAccounts: user.maxSubAccounts || 5,
    });
    setSubAccountModalVisible(true);
  };

  // 保存子账户设置
  const handleSaveSubAccount = async (values: any) => {
    if (!selectedUser) return;

    try {
      const response = await fetch(`/api/admin/users/${selectedUser.id}/sub-account-feature`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '更新子账户设置失败');
      }

      message.success('子账户设置更新成功');
      setSubAccountModalVisible(false);
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  // 删除用户
  const handleDelete = async (user: User) => {
    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || '删除用户失败');
      }

      message.success('用户删除成功');
      loadUsers(pagination.current, pagination.pageSize);
    } catch (error) {
      message.error((error as Error).message);
    }
  };

  // 获取角色标签
  const getRoleTag = (role: string) => {
    const roleConfig = {
      'super_admin': { color: 'red', text: '超级管理员' },
      'admin': { color: 'orange', text: '管理员' },
      'user': { color: 'blue', text: '普通用户' },
      'moderator': { color: 'green', text: '版主' },
    };
    const config = roleConfig[role as keyof typeof roleConfig] || { color: 'default', text: role };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 获取账户类型标签
  const getAccountTypeTag = (accountType: string) => {
    return accountType === 'main' ? 
      <Tag color="blue">主账户</Tag> : 
      <Tag color="green">子账户</Tag>;
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.email}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            @{record.username}
            {record.displayName && ` (${record.displayName})`}
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => getRoleTag(role),
      filters: [
        { text: '超级管理员', value: 'super_admin' },
        { text: '管理员', value: 'admin' },
        { text: '普通用户', value: 'user' },
        { text: '版主', value: 'moderator' },
      ],
    },
    {
      title: '账户类型',
      dataIndex: 'accountType',
      key: 'accountType',
      render: (accountType) => getAccountTypeTag(accountType),
      filters: [
        { text: '主账户', value: 'main' },
        { text: '子账户', value: 'sub' },
      ],
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Badge 
            status={record.isActive ? 'success' : 'error'} 
            text={record.isActive ? '激活' : '禁用'} 
          />
          {record.emailVerified && <Badge status="success" text="邮箱已验证" />}
          {record.isSubAccountEnabled && <Badge status="processing" text="子账户功能" />}
        </Space>
      ),
    },
    {
      title: '统计',
      key: 'stats',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Text style={{ fontSize: '12px' }}>
            子账户: {record._count.subAccounts}
          </Text>
          <Text style={{ fontSize: '12px' }}>
            邮件: {record._count.emails}
          </Text>
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑用户">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Tooltip title="重置密码">
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => handleResetPassword(record)}
            />
          </Tooltip>
          {record.accountType === 'main' && (
            <Tooltip title="子账户管理">
              <Button
                type="text"
                icon={<TeamOutlined />}
                onClick={() => handleManageSubAccount(record)}
              />
            </Tooltip>
          )}
          {record.id !== currentUser?.id && (
            <Popconfirm
              title="确定要删除这个用户吗？"
              description="删除后无法恢复，请谨慎操作。"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Tooltip title="删除用户">
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Tooltip>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{
      padding: '24px',
      minHeight: '100vh',
      overflow: 'auto'
    }}>
      <Card style={{ minHeight: 'auto' }}>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={3} style={{ margin: 0 }}>
            <UserOutlined style={{ marginRight: '8px' }} />
            用户管理
          </Title>
          <Button
            type="primary"
            icon={<ReloadOutlined />}
            onClick={() => loadUsers(1, pagination.pageSize)}
            loading={loading}
          >
            刷新
          </Button>
        </div>

        {/* 搜索和筛选 */}
        <div style={{ marginBottom: '16px', display: 'flex', gap: '16px', flexWrap: 'wrap' }}>
          <Input
            placeholder="搜索用户（邮箱、用户名、显示名）"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            style={{ width: '300px' }}
            allowClear
          />
          <Select
            placeholder="角色筛选"
            value={roleFilter}
            onChange={setRoleFilter}
            style={{ width: '150px' }}
            allowClear
          >
            <Option value="super_admin">超级管理员</Option>
            <Option value="admin">管理员</Option>
            <Option value="user">普通用户</Option>
            <Option value="moderator">版主</Option>
          </Select>
          <Select
            placeholder="账户类型"
            value={accountTypeFilter}
            onChange={setAccountTypeFilter}
            style={{ width: '120px' }}
            allowClear
          >
            <Option value="main">主账户</Option>
            <Option value="sub">子账户</Option>
          </Select>
        </div>

        {/* 用户表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              loadUsers(page, pageSize);
            },
          }}
        />
      </Card>

      {/* 编辑用户模态框 */}
      <Modal
        title="编辑用户"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleSaveEdit}
        >
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="displayName"
            label="显示名"
          >
            <Input />
          </Form.Item>
          <Form.Item
            name="role"
            label="角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select>
              <Option value="user">普通用户</Option>
              <Option value="moderator">版主</Option>
              <Option value="admin">管理员</Option>
              <Option value="super_admin">超级管理员</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="accountType"
            label="账户类型"
            rules={[{ required: true, message: '请选择账户类型' }]}
          >
            <Select>
              <Option value="main">主账户</Option>
              <Option value="sub">子账户</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="isActive"
            label="账户状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="激活" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setEditModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 重置密码模态框 */}
      <Modal
        title="重置用户密码"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleSavePassword}
        >
          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                重置密码
              </Button>
              <Button onClick={() => setPasswordModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 子账户功能管理模态框 */}
      <Modal
        title="子账户功能管理"
        open={subAccountModalVisible}
        onCancel={() => setSubAccountModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={subAccountForm}
          layout="vertical"
          onFinish={handleSaveSubAccount}
        >
          <Form.Item
            name="enabled"
            label="启用子账户功能"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item
            name="maxSubAccounts"
            label="最大子账户数量"
            rules={[
              { required: true, message: '请输入最大子账户数量' },
              { type: 'number', min: 1, max: 100, message: '数量范围1-100' }
            ]}
          >
            <InputNumber min={1} max={100} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setSubAccountModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUserManagement;
