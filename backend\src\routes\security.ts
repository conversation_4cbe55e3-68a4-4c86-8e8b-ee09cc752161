import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate } from '../middleware/auth';
import { validateId } from '../middleware/validation';
import * as securityController from '../controllers/securityController';

const router = Router();

// 所有路由都需要认证
router.use(authenticate);

// 获取安全设置
router.get('/settings',
  async<PERSON>and<PERSON>(securityController.getSecuritySettings)
);

// 更新安全设置
router.put('/settings',
  asyncHandler(securityController.updateSecuritySettings)
);

// 两步验证相关路由
router.post('/2fa/enable',
  async<PERSON><PERSON><PERSON>(securityController.enableTwoFactor)
);

router.post('/2fa/confirm',
  async<PERSON><PERSON><PERSON>(securityController.confirmTwoFactor)
);

router.post('/2fa/disable',
  async<PERSON><PERSON><PERSON>(securityController.disableTwoFactor)
);

router.post('/2fa/backup-codes',
  async<PERSON>and<PERSON>(securityController.generateNewBackupCodes)
);

// 安全日志
router.get('/logs',
  async<PERSON><PERSON><PERSON>(securityController.getSecurityLogs)
);

// 会话管理
router.get('/sessions',
  asyncHandler(securityController.getActiveSessions)
);

router.delete('/sessions/:sessionId',
  validateId,
  asyncHandler(securityController.terminateSession)
);

router.delete('/sessions',
  asyncHandler(securityController.terminateAllOtherSessions)
);

export default router;
