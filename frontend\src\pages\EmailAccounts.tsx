import { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Switch,
  Select,
  InputNumber,
  message,
  Tag,
  Popconfirm,
  Divider,
  Alert,
  Spin,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  SettingOutlined,
  WifiOutlined,
  DisconnectOutlined,
  WarningOutlined,
  QuestionCircleOutlined,
  ThunderboltOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import * as emailAccountApi from '../services/emailAccountApi';
import type {
  EmailAccount,
  CreateEmailAccountData,
  UpdateEmailAccountData,
  EmailProviderConfig,
  AutoConfigResult,
  ConnectionTestResult
} from '../services/emailAccountApi';

const { Option } = Select;
const { TextArea } = Input;

const EmailAccounts: React.FC = () => {
  const [accounts, setAccounts] = useState<EmailAccount[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingAccount, setEditingAccount] = useState<EmailAccount | null>(null);
  const [testingConnection, setTestingConnection] = useState(false);
  const [providers, setProviders] = useState<EmailProviderConfig[]>([]);
  const [autoConfigLoading, setAutoConfigLoading] = useState(false);
  const [connectionTestResult, setConnectionTestResult] = useState<ConnectionTestResult | null>(null);
  const [form] = Form.useForm();

  // 加载邮箱账户列表
  const loadAccounts = async () => {
    try {
      setLoading(true);
      const data = await emailAccountApi.getEmailAccounts();
      setAccounts(data);
    } catch (error) {
      message.error('加载邮箱账户失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载支持的邮箱提供商
  const loadProviders = async () => {
    try {
      const data = await emailAccountApi.getSupportedProviders();
      setProviders(data);
    } catch (error) {
      console.error('加载邮箱提供商失败:', error);
    }
  };

  useEffect(() => {
    loadAccounts();
    loadProviders();
  }, []);

  // 添加账户
  const handleAdd = () => {
    setEditingAccount(null);
    form.resetFields();
    // 设置默认值
    form.setFieldsValue({
      imapPort: 993,
      imapSecure: true,
      smtpPort: 587,
      smtpSecure: false,
      syncEnabled: true,
      syncInterval: 5,
      maxEmailsPerSync: 50,
    });
    setModalVisible(true);
  };

  // 编辑账户
  const handleEdit = async (account: EmailAccount) => {
    try {
      setLoading(true);
      const accountDetail = await emailAccountApi.getEmailAccountById(account.id);
      setEditingAccount(accountDetail);
      form.setFieldsValue({
        ...accountDetail,
        // 不显示密码
        imapPassword: '',
        smtpPassword: '',
      });
      setModalVisible(true);
    } catch (error) {
      message.error('获取账户详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除账户
  const handleDelete = async (id: number) => {
    try {
      await emailAccountApi.deleteEmailAccount(id);
      message.success('邮箱账户删除成功');
      loadAccounts();
    } catch (error) {
      message.error('删除失败');
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    try {
      const values = await form.validateFields([
        'imapHost', 'imapPort', 'imapSecure', 'imapUsername', 'imapPassword',
        'smtpHost', 'smtpPort', 'smtpSecure', 'smtpUsername', 'smtpPassword'
      ]);

      setTestingConnection(true);
      setConnectionTestResult(null);

      const result = await emailAccountApi.testEmailConnectionDetailed(values);
      setConnectionTestResult(result);

      if (result.success) {
        message.success('连接测试成功');
      } else {
        message.error(`连接测试失败: ${result.error}`);
      }
    } catch (error) {
      if (error instanceof Error) {
        message.error(`连接测试失败: ${error.message}`);
      } else {
        message.error('请先填写完整的连接信息');
      }
    } finally {
      setTestingConnection(false);
    }
  };

  // 自动配置
  const handleAutoConfig = async () => {
    try {
      const email = form.getFieldValue('email');
      if (!email) {
        message.warning('请先输入邮箱地址');
        return;
      }

      setAutoConfigLoading(true);
      const autoConfig = await emailAccountApi.getAutoConfig(email);

      form.setFieldsValue({
        imapHost: autoConfig.imapHost,
        imapPort: autoConfig.imapPort,
        imapSecure: autoConfig.imapSecure,
        imapUsername: autoConfig.imapUsername,
        smtpHost: autoConfig.smtpHost,
        smtpPort: autoConfig.smtpPort,
        smtpSecure: autoConfig.smtpSecure,
        smtpUsername: autoConfig.smtpUsername,
      });

      message.success('自动配置成功');
    } catch (error) {
      message.error('自动配置失败，请手动配置');
    } finally {
      setAutoConfigLoading(false);
    }
  };

  // 更新连接状态
  const handleUpdateConnectionStatus = async (accountId: number) => {
    try {
      setLoading(true);
      const result = await emailAccountApi.updateConnectionStatus(accountId);
      message.success('连接状态更新成功');
      loadAccounts();
    } catch (error) {
      message.error('更新连接状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交表单
  const handleSubmit = async (values: any) => {
    try {
      setLoading(true);
      
      if (editingAccount) {
        // 更新账户
        const updateData: UpdateEmailAccountData = { ...values };
        // 如果密码为空，则不更新密码
        if (!values.imapPassword) delete updateData.imapPassword;
        if (!values.smtpPassword) delete updateData.smtpPassword;
        
        await emailAccountApi.updateEmailAccount(editingAccount.id, updateData);
        message.success('邮箱账户更新成功');
      } else {
        // 创建账户
        const createData: CreateEmailAccountData = values;
        await emailAccountApi.createEmailAccount(createData);
        message.success('邮箱账户创建成功');
      }
      
      setModalVisible(false);
      form.resetFields();
      loadAccounts();
    } catch (error) {
      message.error('操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 邮箱服务商变化处理
  const handleProviderChange = (provider: string) => {
    if (provider !== 'custom') {
      const config = emailAccountApi.EMAIL_PROVIDERS[provider as keyof typeof emailAccountApi.EMAIL_PROVIDERS];
      form.setFieldsValue({
        imapHost: config.imapHost,
        imapPort: config.imapPort,
        imapSecure: config.imapSecure,
        smtpHost: config.smtpHost,
        smtpPort: config.smtpPort,
        smtpSecure: config.smtpSecure,
      });
    }
  };

  // 邮箱地址变化处理
  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const email = e.target.value;
    const provider = emailAccountApi.detectEmailProvider(email);
    
    if (provider !== 'custom') {
      const config = emailAccountApi.EMAIL_PROVIDERS[provider];
      form.setFieldsValue({
        imapHost: config.imapHost,
        imapPort: config.imapPort,
        imapSecure: config.imapSecure,
        smtpHost: config.smtpHost,
        smtpPort: config.smtpPort,
        smtpSecure: config.smtpSecure,
        imapUsername: email,
        smtpUsername: email,
      });
    }
  };

  const columns = [
    {
      title: '账户名称',
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: EmailAccount) => (
        <div>
          <div className="font-medium">{name}</div>
          <div className="text-sm text-gray-500">{record.email}</div>
        </div>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (_: unknown, record: EmailAccount) => (
        <Space direction="vertical" size="small">
          <Space>
            {record.isDefault && <Tag color="blue">默认</Tag>}
            {record.isActive ? (
              <Tag color="green" icon={<CheckCircleOutlined />}>
                活跃
              </Tag>
            ) : (
              <Tag color="red" icon={<ExclamationCircleOutlined />}>
                禁用
              </Tag>
            )}
          </Space>
          <Space>
            {/* 连接状态 */}
            {record.connectionStatus === 'connected' && (
              <Tag color="green" icon={<WifiOutlined />}>
                已连接
              </Tag>
            )}
            {record.connectionStatus === 'disconnected' && (
              <Tag color="orange" icon={<DisconnectOutlined />}>
                未连接
              </Tag>
            )}
            {record.connectionStatus === 'error' && (
              <Tag color="red" icon={<WarningOutlined />}>
                连接错误
              </Tag>
            )}
            {record.connectionStatus === 'unknown' && (
              <Tag color="gray" icon={<QuestionCircleOutlined />}>
                未知
              </Tag>
            )}
            {/* 同步状态 */}
            {record.syncEnabled && (
              <>
                {record.syncStatus === 'syncing' && (
                  <Tag color="blue" icon={<SyncOutlined spin />}>
                    同步中
                  </Tag>
                )}
                {record.syncStatus === 'idle' && (
                  <Tag color="cyan" icon={<SyncOutlined />}>
                    同步
                  </Tag>
                )}
                {record.syncStatus === 'error' && (
                  <Tag color="red" icon={<ExclamationCircleOutlined />}>
                    同步错误
                  </Tag>
                )}
              </>
            )}
          </Space>
        </Space>
      ),
    },
    {
      title: '服务器配置',
      key: 'servers',
      render: (_: unknown, record: EmailAccount) => (
        <div className="text-sm">
          <div>IMAP: {record.imapHost}:{record.imapPort}</div>
          <div>SMTP: {record.smtpHost}:{record.smtpPort}</div>
        </div>
      ),
    },
    {
      title: '最后同步',
      dataIndex: 'lastSyncAt',
      key: 'lastSyncAt',
      render: (lastSyncAt: string) => (
        lastSyncAt ? new Date(lastSyncAt).toLocaleString() : '从未同步'
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_: unknown, record: EmailAccount) => (
        <Space>
          <Button
            type="text"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
            title="编辑账户"
          />
          <Button
            type="text"
            size="small"
            icon={<ReloadOutlined />}
            onClick={() => handleUpdateConnectionStatus(record.id)}
            title="更新连接状态"
          />
          <Popconfirm
            title="确定删除这个邮箱账户吗？"
            description="删除后相关邮件将无法访问"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              danger
              title="删除账户"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="p-6">
      <Card
        title="邮箱账户管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAdd}
          >
            添加邮箱账户
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={accounts}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
        />
      </Card>

      {/* 添加/编辑邮箱账户模态框 */}
      <Modal
        title={editingAccount ? '编辑邮箱账户' : '添加邮箱账户'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Alert
            message="安全提示"
            description="密码将被加密存储。建议使用应用专用密码而非主密码。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            label="账户名称"
            name="name"
            rules={[{ required: true, message: '请输入账户名称' }]}
          >
            <Input placeholder="例如：工作邮箱" />
          </Form.Item>

          <Form.Item
            label="邮箱地址"
            name="email"
            rules={[
              { required: true, message: '请输入邮箱地址' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input.Group compact>
              <Input
                style={{ width: 'calc(100% - 120px)' }}
                placeholder="<EMAIL>"
                onChange={handleEmailChange}
              />
              <Button
                style={{ width: '120px' }}
                icon={<ThunderboltOutlined />}
                onClick={handleAutoConfig}
                loading={autoConfigLoading}
                title="自动配置服务器设置"
              >
                自动配置
              </Button>
            </Input.Group>
          </Form.Item>

          <Form.Item
            label="显示名称"
            name="displayName"
          >
            <Input placeholder="发件人显示名称" />
          </Form.Item>

          <Form.Item label="邮箱服务商">
            <Select
              placeholder="选择邮箱服务商"
              onChange={handleProviderChange}
            >
              {Object.entries(emailAccountApi.EMAIL_PROVIDERS).map(([key, provider]) => (
                <Option key={key} value={key}>
                  {provider.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Divider orientation="left">IMAP 配置（接收邮件）</Divider>
          
          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="IMAP 服务器"
              name="imapHost"
              rules={[{ required: true, message: '请输入IMAP服务器' }]}
            >
              <Input placeholder="imap.blindedby.love" />
            </Form.Item>

            <Form.Item
              label="IMAP 端口"
              name="imapPort"
              rules={[{ required: true, message: '请输入IMAP端口' }]}
            >
              <InputNumber min={1} max={65535} style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="IMAP 用户名"
              name="imapUsername"
              rules={[{ required: true, message: '请输入IMAP用户名' }]}
            >
              <Input placeholder="通常是邮箱地址" />
            </Form.Item>

            <Form.Item
              label="IMAP 密码"
              name="imapPassword"
              rules={editingAccount ? [] : [{ required: true, message: '请输入IMAP密码' }]}
            >
              <Input.Password 
                placeholder={editingAccount ? "留空表示不修改密码" : "邮箱密码或应用专用密码"} 
              />
            </Form.Item>
          </div>

          <Form.Item
            name="imapSecure"
            valuePropName="checked"
          >
            <Switch checkedChildren="SSL/TLS" unCheckedChildren="明文" />
            <span className="ml-2">使用安全连接</span>
          </Form.Item>

          <Divider orientation="left">SMTP 配置（发送邮件）</Divider>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="SMTP 服务器"
              name="smtpHost"
              rules={[{ required: true, message: '请输入SMTP服务器' }]}
            >
              <Input placeholder="smtp.blindedby.love" />
            </Form.Item>

            <Form.Item
              label="SMTP 端口"
              name="smtpPort"
              rules={[{ required: true, message: '请输入SMTP端口' }]}
            >
              <InputNumber min={1} max={65535} style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="SMTP 用户名"
              name="smtpUsername"
              rules={[{ required: true, message: '请输入SMTP用户名' }]}
            >
              <Input placeholder="通常是邮箱地址" />
            </Form.Item>

            <Form.Item
              label="SMTP 密码"
              name="smtpPassword"
              rules={editingAccount ? [] : [{ required: true, message: '请输入SMTP密码' }]}
            >
              <Input.Password 
                placeholder={editingAccount ? "留空表示不修改密码" : "邮箱密码或应用专用密码"} 
              />
            </Form.Item>
          </div>

          <Form.Item
            name="smtpSecure"
            valuePropName="checked"
          >
            <Switch checkedChildren="SSL/TLS" unCheckedChildren="STARTTLS" />
            <span className="ml-2">使用安全连接</span>
          </Form.Item>

          <Divider orientation="left">高级设置</Divider>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              name="syncEnabled"
              valuePropName="checked"
            >
              <Switch />
              <span className="ml-2">启用邮件同步</span>
            </Form.Item>

            <Form.Item
              name="isDefault"
              valuePropName="checked"
            >
              <Switch />
              <span className="ml-2">设为默认账户</span>
            </Form.Item>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <Form.Item
              label="同步间隔（分钟）"
              name="syncInterval"
            >
              <InputNumber min={1} max={60} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              label="每次同步邮件数"
              name="maxEmailsPerSync"
            >
              <InputNumber min={1} max={200} style={{ width: '100%' }} />
            </Form.Item>
          </div>

          <Form.Item
            label="回复邮箱"
            name="replyToEmail"
          >
            <Input placeholder="留空使用发件邮箱" />
          </Form.Item>

          <Form.Item
            label="邮件签名"
            name="signature"
          >
            <TextArea 
              rows={3} 
              placeholder="邮件签名内容"
            />
          </Form.Item>

          {/* 连接测试结果 */}
          {connectionTestResult && (
            <Form.Item>
              <Alert
                message={connectionTestResult.success ? "连接测试成功" : "连接测试失败"}
                description={
                  <div>
                    <div>
                      <strong>IMAP:</strong> {connectionTestResult.imapSuccess ? '✓ 成功' : `✗ 失败 - ${connectionTestResult.imapError}`}
                    </div>
                    <div>
                      <strong>SMTP:</strong> {connectionTestResult.smtpSuccess ? '✓ 成功' : `✗ 失败 - ${connectionTestResult.smtpError}`}
                    </div>
                    <div>
                      <strong>测试耗时:</strong> {connectionTestResult.testDuration}ms
                    </div>
                  </div>
                }
                type={connectionTestResult.success ? "success" : "error"}
                showIcon
                style={{ marginBottom: 16 }}
              />
            </Form.Item>
          )}

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                {editingAccount ? '更新' : '创建'}
              </Button>
              <Button
                onClick={handleTestConnection}
                loading={testingConnection}
                icon={<SettingOutlined />}
              >
                测试连接
              </Button>
              <Button
                onClick={() => {
                  setModalVisible(false);
                  form.resetFields();
                  setConnectionTestResult(null);
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EmailAccounts;
