#!/bin/bash

# 邮箱系统构建脚本
# 用于构建前端和后端应用

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Node.js和npm
check_prerequisites() {
    log_info "检查构建环境..."

    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi

    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi

    # 检查 Node.js 版本
    NODE_VERSION=$(node --version)
    NODE_MAJOR_VERSION=$(echo $NODE_VERSION | cut -d'.' -f1 | sed 's/v//')
    log_info "当前 Node.js 版本: $NODE_VERSION"

    if [ "$NODE_MAJOR_VERSION" -lt 18 ]; then
        log_error "需要 Node.js 18+ 版本，当前版本: $NODE_VERSION"
        log_error "请升级 Node.js 版本"
        exit 1
    fi

    # 检查操作系统
    OS=$(uname -s)
    ARCH=$(uname -m)
    log_info "当前平台: $OS-$ARCH"

    log_success "构建环境检查通过"
}

# 清理构建目录
clean_build() {
    log_info "清理构建目录..."

    if [ -d "frontend/dist" ]; then
        rm -rf frontend/dist
        log_info "清理前端构建目录"
    fi

    if [ -d "backend/dist" ]; then
        rm -rf backend/dist
        log_info "清理后端构建目录"
    fi

    log_success "构建目录清理完成"
}

# 清理跨平台依赖问题
clean_cross_platform_deps() {
    log_info "清理跨平台依赖..."

    # 清理前端平台特定依赖
    if [ -d "frontend/node_modules" ]; then
        log_info "清理前端平台特定依赖..."
        cd frontend

        # 清理 npm 缓存
        npm cache clean --force 2>/dev/null || true

        # 删除可能有问题的平台特定包
        rm -rf node_modules/@tailwindcss 2>/dev/null || true
        rm -rf node_modules/lightningcss* 2>/dev/null || true
        rm -rf node_modules/esbuild* 2>/dev/null || true
        rm -rf node_modules/@esbuild 2>/dev/null || true

        cd ..
        log_info "前端平台特定依赖清理完成"
    fi

    # 清理后端平台特定依赖
    if [ -d "backend/node_modules" ]; then
        log_info "清理后端平台特定依赖..."
        cd backend
        npm cache clean --force 2>/dev/null || true
        cd ..
        log_info "后端平台特定依赖清理完成"
    fi

    log_success "跨平台依赖清理完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装依赖..."

    # 安装前端依赖（跨平台兼容）
    log_info "安装前端依赖..."
    cd frontend

    # 检查是否需要完全重新安装
    if [ ! -d "node_modules" ] || [ ! -f "package-lock.json" ]; then
        log_info "首次安装或缺少依赖文件，执行完整安装..."
        npm install
    else
        # 检查是否存在跨平台问题
        if [ -d "node_modules/lightningcss-win32-x64-msvc" ] && [ "$(uname -s)" = "Linux" ]; then
            log_warning "检测到跨平台依赖问题，重新安装..."
            rm -rf node_modules package-lock.json
            npm install
        else
            log_info "使用缓存安装..."
            npm ci
        fi
    fi

    # 运行平台特定清理脚本（如果存在）
    if [ -f "scripts/clean-platform-deps.js" ]; then
        log_info "运行平台依赖清理..."
        node scripts/clean-platform-deps.js
    fi

    cd ..

    # 安装后端依赖
    log_info "安装后端依赖..."
    cd backend

    if [ ! -d "node_modules" ] || [ ! -f "package-lock.json" ]; then
        npm install
    else
        npm ci
    fi

    cd ..

    log_success "依赖安装完成"
}

# 构建前端
build_frontend() {
    local env=${1:-production}
    log_info "构建前端应用 (环境: $env)..."

    cd frontend

    # 设置 Node.js 内存限制，防止构建时内存不足
    export NODE_OPTIONS="--max-old-space-size=4096"

    # 检查关键依赖是否正确安装
    if [ ! -d "node_modules/@tailwindcss" ]; then
        log_error "@tailwindcss 依赖缺失，请重新安装依赖"
        exit 1
    fi

    if [ ! -d "node_modules/vite" ]; then
        log_error "vite 依赖缺失，请重新安装依赖"
        exit 1
    fi

    # 尝试构建，如果失败则重新安装依赖后再试
    local build_success=false
    local max_retries=2
    local retry_count=0

    while [ $retry_count -lt $max_retries ] && [ "$build_success" = false ]; do
        if [ $retry_count -gt 0 ]; then
            log_warning "构建失败，尝试重新安装依赖... (尝试 $((retry_count + 1))/$max_retries)"
            rm -rf node_modules package-lock.json
            npm install
        fi

        case $env in
            "development")
                if npm run build:dev; then
                    build_success=true
                fi
                ;;
            "staging")
                if npm run build:staging; then
                    build_success=true
                fi
                ;;
            "production")
                if npm run build; then
                    build_success=true
                fi
                ;;
            *)
                log_error "未知的构建环境: $env"
                exit 1
                ;;
        esac

        retry_count=$((retry_count + 1))
    done

    if [ "$build_success" = false ]; then
        log_error "前端构建失败，已尝试 $max_retries 次"
        exit 1
    fi

    # 验证构建结果
    if [ ! -d "dist" ] || [ ! -f "dist/index.html" ]; then
        log_error "构建验证失败：dist 目录或 index.html 不存在"
        exit 1
    fi

    # 显示构建信息
    log_info "构建文件大小: $(du -sh dist/ | cut -f1)"

    cd ..
    log_success "前端构建完成"
}

# 构建后端
build_backend() {
    local env=${1:-production}
    log_info "构建后端应用 (环境: $env)..."

    cd backend

    # 检查环境变量文件
    local env_file=""
    case $env in
        "development")
            env_file=".env"
            ;;
        "staging")
            env_file=".env.staging"
            ;;
        "production")
            env_file=".env.prod"
            ;;
        *)
            log_error "未知环境: $env"
            exit 1
            ;;
    esac

    if [ ! -f "$env_file" ]; then
        log_error "环境配置文件 $env_file 不存在"
        exit 1
    fi

    log_info "使用环境配置文件: $env_file"

    # 生成Prisma客户端
    npm run db:generate

    # 构建TypeScript
    case $env in
        "development")
            NODE_ENV=development npm run build:dev
            ;;
        "staging")
            NODE_ENV=staging npm run build:staging
            ;;
        "production")
            NODE_ENV=production npm run build:prod
            ;;
    esac

    cd ..
    log_success "后端构建完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 前端测试
    log_info "运行前端测试..."
    cd frontend
    npm run test:run
    cd ..
    
    # 后端测试
    log_info "运行后端测试..."
    cd backend
    npm run test
    cd ..
    
    log_success "测试完成"
}

# 代码质量检查
run_linting() {
    log_info "运行代码质量检查..."
    
    # 前端代码检查
    log_info "检查前端代码..."
    cd frontend
    npm run lint
    npm run type-check
    cd ..
    
    # 后端代码检查
    log_info "检查后端代码..."
    cd backend
    npm run lint
    npm run type-check
    cd ..
    
    log_success "代码质量检查完成"
}

# 主函数
main() {
    local env=${1:-production}
    local skip_tests=${2:-false}
    local skip_lint=${3:-false}
    local force_clean=${4:-false}

    log_info "开始构建邮箱系统 (环境: $env)"

    check_prerequisites
    clean_build

    # 如果指定强制清理或检测到跨平台问题，执行深度清理
    if [ "$force_clean" = "true" ] || [ "$(uname -s)" = "Linux" ]; then
        clean_cross_platform_deps
    fi

    install_dependencies

    if [ "$skip_lint" != "true" ]; then
        run_linting
    fi

    if [ "$skip_tests" != "true" ]; then
        run_tests
    fi

    build_backend "$env"
    build_frontend "$env"

    log_success "构建完成！"
    log_info "前端构建文件: frontend/dist/"
    log_info "后端构建文件: backend/dist/"

    # 显示构建摘要
    if [ -d "frontend/dist" ]; then
        log_info "前端构建大小: $(du -sh frontend/dist/ | cut -f1)"
    fi

    if [ -d "backend/dist" ]; then
        log_info "后端构建大小: $(du -sh backend/dist/ | cut -f1)"
    fi
}

# 显示帮助信息
show_help() {
    echo "邮箱系统构建脚本"
    echo ""
    echo "用法: $0 [环境] [选项]"
    echo ""
    echo "环境:"
    echo "  development  开发环境构建"
    echo "  staging      预发布环境构建"
    echo "  production   生产环境构建 (默认)"
    echo ""
    echo "选项:"
    echo "  --skip-tests     跳过测试"
    echo "  --skip-lint      跳过代码检查"
    echo "  --force-clean    强制清理跨平台依赖"
    echo "  --help           显示帮助信息"
    echo ""
    echo "跨平台构建说明:"
    echo "  本脚本自动处理 Windows 开发环境到 Ubuntu 服务器的跨平台部署问题"
    echo "  包括自动检测和清理平台特定的原生依赖包"
    echo ""
    echo "示例:"
    echo "  $0                              # 生产环境构建"
    echo "  $0 development                  # 开发环境构建"
    echo "  $0 production --skip-tests      # 生产环境构建，跳过测试"
    echo "  $0 production --force-clean     # 强制清理跨平台依赖后构建"
}

# 解析命令行参数
ENV="production"
SKIP_TESTS="false"
SKIP_LINT="false"
FORCE_CLEAN="false"

while [[ $# -gt 0 ]]; do
    case $1 in
        development|staging|production)
            ENV="$1"
            shift
            ;;
        --skip-tests)
            SKIP_TESTS="true"
            shift
            ;;
        --skip-lint)
            SKIP_LINT="true"
            shift
            ;;
        --force-clean)
            FORCE_CLEAN="true"
            shift
            ;;
        --help)
            show_help
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main "$ENV" "$SKIP_TESTS" "$SKIP_LINT" "$FORCE_CLEAN"
