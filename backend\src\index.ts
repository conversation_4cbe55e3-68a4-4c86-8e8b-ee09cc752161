import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { createServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';

import { errorHandler, notFoundHandler } from './middleware/errorHandler';
import { rateLimiter } from './middleware/rateLimiter';
import logger from './utils/logger';
import config from './config/env';
// import { schedulerService } from './services/schedulerService';
// 旧的同步服务 - 将被统一同步服务替代
// import { emailSyncManager } from './services/emailSyncManager';
// import realTimeEmailFlowService from './services/realTimeEmailFlowService';
// import { closeAllIMAPConnections } from './services/imapService'; // 已迁移到ImapFlow
import { imapConnectionMonitor } from './services/imapConnectionMonitor';
// 新的统一同步服务 - 使用动态导入避免循环依赖
// import { unifiedEmailSyncService } from './services/unifiedEmailSyncService';
// import { syncServiceMigration } from './services/syncServiceMigration';

// 导入路由
import authRoutes from './routes/auth';
// import userRoutes from './routes/user';
import emailRoutes from './routes/email';
import folderRoutes from './routes/folder';
import contactRoutes from './routes/contact';
import templateRoutes from './routes/template';
import labelRoutes from './routes/label';
import emailRuleRoutes from './routes/emailRule';
import emailAccountRoutes from './routes/emailAccount';
import securityRoutes from './routes/security';
import mailRoutes from './routes/mail';
import emailVerificationRoutes from './routes/emailVerification';
import mailboxRoutes from './routes/mailbox';
import realTimeEmailRoutes from './routes/realTimeEmailRoutes';
import systemUserRoutes from './routes/systemUser';
import systemTemplateRoutes from './routes/systemTemplate';
import trackingRoutes from './routes/tracking';
import './services/imapFlowService';
import emailSyncRoutes from './routes/emailSync';
import emailRuleTemplateRoutes from './routes/emailRuleTemplate';
import adminEmailSyncRoutes from './routes/adminEmailSync';
import adminUserRoutes from './routes/adminUser';
import bounceRoutes from './routes/bounce';
import userManagementRoutes from './routes/userManagement';
import systemMonitoringRoutes from './routes/systemMonitoring';
import uploadRoutes from './routes/upload';
import emailAccountMonitorRoutes from './routes/emailAccountMonitor';
import multiAccountSyncRoutes from './routes/multiAccountSync';
import systemManagementRoutes from './routes/systemManagement';
import dataMigrationRoutes from './routes/dataMigration';
import subAccountRoutes from './routes/subAccount';
import healthRoutes from './routes/health';
import appPasswordRoutes from './routes/appPassword';
import imapConnectionRoutes from './routes/imapConnection';
import testSyncRoutes from './routes/testSync';
import userPreferencesRoutes from './routes/userPreferences';
// import imapMonitorRoutes from './routes/imapMonitor';
// import testRoutes from './routes/testRoutes';
import { initializeRuleTemplates } from './services/emailRuleTemplateService';
import emailAccountMonitorService from './services/emailAccountMonitorService';
import multiAccountSyncManager from './services/multiAccountSyncManager';
import systemManagementService from './services/systemManagementService';
// import trackingRoutes from './routes/tracking';

const app = express();
const server = createServer(app);

// 配置信任代理（生产环境必需）
if (process.env['NODE_ENV'] === 'production') {
  app.set('trust proxy', 1); // 信任第一层代理（Nginx）
}

// 创建Prisma客户端实例
const prisma = new (require('@prisma/client').PrismaClient)();

// 用于跟踪socket和用户邮箱的映射
const socketUserMap = new Map<string, string>();

// CORS配置 - 支持多个域名
const allowedOrigins = config.ALLOWED_ORIGINS;

// Socket.IO配置
const io = new SocketIOServer(server, {
  cors: {
    origin: allowedOrigins,
    methods: ['GET', 'POST'],
    credentials: true,
  },
});

// 全局中间件
app.use(helmet());

app.use(cors({
  origin: (origin, callback) => {
    // 允许没有origin的请求（比如移动应用或Postman）
    if (!origin) return callback(null, true);

    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      console.log('CORS blocked origin:', origin);
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use(morgan('combined', {
  stream: {
    write: (message: string) => logger.info(message.trim()),
  },
}));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 限流中间件
app.use(rateLimiter);

// 健康检查
app.get('/health', (_req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
    memory: process.memoryUsage(),
    pid: process.pid,
  });
});

// API健康检查
app.get('/api/health', (_req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: config.NODE_ENV,
    version: process.env.npm_package_version || '1.0.0',
    memory: process.memoryUsage(),
    pid: process.pid,
  });
});

// API路由
app.use('/api/auth', authRoutes);
// app.use('/api/users', userRoutes);
app.use('/api/emails', emailRoutes);
app.use('/api/folders', folderRoutes);
app.use('/api/contacts', contactRoutes);
app.use('/api/templates', templateRoutes);
app.use('/api/labels', labelRoutes);
app.use('/api/email-rules', emailRuleRoutes);
app.use('/api/email-rule-templates', emailRuleTemplateRoutes);
app.use('/api/email-accounts', emailAccountRoutes);
app.use('/api/security', securityRoutes);
app.use('/api/mail', mailRoutes);
app.use('/api/verification', emailVerificationRoutes);
app.use('/api/mailbox', mailboxRoutes);
app.use('/api/realtime-email', realTimeEmailRoutes);
app.use('/api/unified-sync', require('./routes/unifiedSyncRoutesSimple').default);
app.use('/api/system-users', systemUserRoutes);
app.use('/api/system-templates', systemTemplateRoutes);
app.use('/api/tracking', trackingRoutes);
app.use('/api/emails/sync', emailSyncRoutes);
app.use('/api/admin/email-sync', adminEmailSyncRoutes);
app.use('/api/admin/users', adminUserRoutes);
app.use('/api/bounce', bounceRoutes);
app.use('/api/user-management', userManagementRoutes);
app.use('/api/system-monitoring', systemMonitoringRoutes);
app.use('/api/upload', uploadRoutes);
app.use('/api/email-account-monitor', emailAccountMonitorRoutes);
app.use('/api/multi-account-sync', multiAccountSyncRoutes);
app.use('/api/system-management', systemManagementRoutes);
app.use('/api/data-migration', dataMigrationRoutes);
app.use('/api/sub-accounts', subAccountRoutes);
app.use('/api/app-passwords', appPasswordRoutes);
app.use('/api/imap-connection', imapConnectionRoutes);
// app.use('/api/imap-monitor', imapMonitorRoutes);
app.use('/api/health', healthRoutes);
app.use('/api/test-sync', testSyncRoutes);
app.use('/api/user-preferences', userPreferencesRoutes);

// 测试路由（仅在开发环境中启用）
// if (process.env.NODE_ENV === 'development') {
//   app.use('/api/test', testRoutes);
// }

// 静态文件服务
app.use('/uploads', express.static(config.UPLOAD_PATH));

// 错误处理中间件
app.use(notFoundHandler);
app.use(errorHandler);

// Socket.IO连接处理
io.on('connection', (socket) => {
  logger.info(`🔌 新的WebSocket连接: ${socket.id}`);
  logger.info(`🔌 当前连接数: ${io.engine.clientsCount}`);

  socket.on('join', async (userId: string) => {
    socket.join(`user_${userId}`);
    logger.info(`🏠 用户 ${userId} 加入房间 user_${userId}`);

    // 获取用户邮箱
    try {
      const user = await prisma.user.findUnique({
        where: { id: parseInt(userId) },
        select: { email: true }
      });

      if (user) {
        socketUserMap.set(socket.id, user.email);

        // 通知按需同步服务用户上线
        try {
          const { onDemandEmailSyncService } = await import('./services/onDemandEmailSyncService');
          await onDemandEmailSyncService.onUserOnline(user.email);
        } catch (error) {
          logger.error('通知用户上线失败:', error);
        }

        logger.info(`👤 用户 ${user.email} 已上线`);
      }
    } catch (error) {
      logger.error(`获取用户信息失败:`, error);
    }
  });

  socket.on('disconnect', async () => {
    logger.info(`用户断开连接: ${socket.id}`);

    // 用户下线
    const userEmail = socketUserMap.get(socket.id);
    if (userEmail) {
      // 通知按需同步服务用户下线
      try {
        const { onDemandEmailSyncService } = await import('./services/onDemandEmailSyncService');
        await onDemandEmailSyncService.onUserOffline(userEmail);
      } catch (error) {
        logger.error('通知用户下线失败:', error);
      }

      logger.info(`👋 用户 ${userEmail} 已下线`);
      socketUserMap.delete(socket.id);
    }
  });
});

// 启动服务器 - 修复邮件同步问题
const PORT = config.PORT;
server.listen(PORT, async () => {
  logger.info(`服务器运行在端口 ${PORT}`);
  logger.info(`环境: ${config.NODE_ENV}`);
  logger.info(`前端地址: ${config.FRONTEND_URL}`);

  // 启动定时任务
  // schedulerService.startAll();

  // 初始化邮件规则模板
  try {
    logger.info('初始化邮件规则模板...');
    await initializeRuleTemplates();
  } catch (error) {
    logger.error('初始化邮件规则模板失败:', error);
  }

  // 启动邮件同步服务
  if (config.AUTO_START_EMAIL_SYNC) {
    try {
      if (config.EMAIL_SYNC_MODE === 'ondemand') {
        logger.info('启动按需邮件同步服务（推荐）...');

        // 使用按需同步服务，只为在线用户建立连接
        const { onDemandEmailSyncService } = await import('./services/onDemandEmailSyncService');

        // 设置 Socket.IO 实例
        onDemandEmailSyncService.setSocketIO(io);

        logger.info('✅ 按需邮件同步服务启动成功（等待用户上线）');
      } else {
        logger.info('启动全量邮件同步服务...');

        // 使用全量同步服务，为所有用户建立连接
        const { simpleEmailSyncService } = await import('./services/simpleEmailSyncService');

        // 设置 Socket.IO 实例
        simpleEmailSyncService.setSocketIO(io);

        // 启动全量同步服务
        await simpleEmailSyncService.startAllUserSync();

        logger.info('✅ 全量邮件同步服务启动成功');
      }
    } catch (error) {
      logger.error('启动邮件同步服务失败:', error);
    }
  } else {
    logger.info('邮件同步服务未自动启动，可通过API手动启动');
  }

  // 启动邮箱账户监控服务
  try {
    logger.info('启动邮箱账户监控服务...');
    emailAccountMonitorService.start();
  } catch (error) {
    logger.error('启动邮箱账户监控服务失败:', error);
  }

  // 启动IMAP连接监控服务
  try {
    logger.info('启动IMAP连接监控服务...');
    imapConnectionMonitor.start();
  } catch (error) {
    logger.error('启动IMAP连接监控服务失败:', error);
  }

  // 注释掉多账户同步管理器（已被统一同步服务替代）
  // try {
  //   logger.info('初始化多账户同步管理器...');
  //   await multiAccountSyncManager.initialize();
  // } catch (error) {
  //   logger.error('初始化多账户同步管理器失败:', error);
  // }

  // 记录系统启动
  try {
    // 使用普通日志记录启动信息，而不是异常记录
    logger.info('邮件系统已成功启动', {
      port: PORT,
      timestamp: new Date().toISOString(),
      service: 'email-system'
    });

    // 如果需要记录到数据库，可以创建专门的系统事件记录方法
    // await systemManagementService.recordSystemEvent(...)
  } catch (error) {
    logger.error('记录系统启动信息失败:', error);
  }
});

// 优雅关闭
async function gracefulShutdown() {
  logger.info('开始优雅关闭...');

  try {
    // 停止按需邮件同步服务
    try {
      const { onDemandEmailSyncService } = await import('./services/onDemandEmailSyncService');
      await onDemandEmailSyncService.stopAllUserSync();
    } catch (error) {
      logger.warn('停止按需邮件同步服务失败:', error);
    }

    // 停止其他服务
    emailAccountMonitorService.stop();
    imapConnectionMonitor.stop();
    // closeAllIMAPConnections(); // 已迁移到ImapFlow，连接池会自动管理

    logger.info('所有服务已停止');
  } catch (error) {
    logger.error('关闭服务时发生错误:', error);
  }

  server.close(() => {
    logger.info('服务器已关闭');
    process.exit(0);
  });
}

process.on('SIGTERM', () => {
  logger.info('收到SIGTERM信号');
  gracefulShutdown();
});

process.on('SIGINT', () => {
  logger.info('收到SIGINT信号');
  gracefulShutdown();
});

export { io };
export default app;
