#!/usr/bin/env node

/**
 * 发送测试邮件来验证实时同步
 */

const nodemailer = require('nodemailer');

async function sendTestEmail() {
  try {
    console.log('📧 发送测试邮件...\n');

    // 创建邮件传输器
    const transporter = nodemailer.createTransporter({
      host: 'mail.blindedby.love',
      port: 587,
      secure: false,
      auth: {
        user: '<EMAIL>',
        pass: 'RV5C6Bs11Ujvy8RV' // 使用应用专用密码
      },
      tls: {
        rejectUnauthorized: false
      }
    });

    // 验证连接
    console.log('1. 验证 SMTP 连接...');
    await transporter.verify();
    console.log('✅ SMTP 连接成功');

    // 发送测试邮件
    console.log('\n2. 发送测试邮件...');
    const timestamp = new Date().toLocaleString();
    
    const mailOptions = {
      from: '<EMAIL>',
      to: '<EMAIL>',
      subject: `邮件同步测试 - ${timestamp}`,
      text: `这是一封测试邮件，用于验证统一邮件同步服务的实时功能。\n\n发送时间: ${timestamp}\n\n如果您立即收到这封邮件的通知，说明实时同步正常工作。`,
      html: `
        <h2>📧 邮件同步测试</h2>
        <p>这是一封测试邮件，用于验证统一邮件同步服务的实时功能。</p>
        <p><strong>发送时间:</strong> ${timestamp}</p>
        <p>如果您立即收到这封邮件的通知，说明实时同步正常工作。</p>
        <hr>
        <p><small>此邮件由邮件同步测试脚本自动发送</small></p>
      `
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ 邮件发送成功!');
    console.log('邮件ID:', info.messageId);
    console.log('响应:', info.response);

    console.log('\n📊 现在请观察:');
    console.log('1. 后端服务器日志是否显示收到新邮件');
    console.log('2. 前端是否收到实时通知');
    console.log('3. 数据库中是否立即出现新邮件记录');

    // 等待几秒钟让邮件传递
    console.log('\n⏳ 等待 10 秒让邮件传递...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // 检查数据库中的最新邮件
    console.log('\n3. 检查数据库中的最新邮件...');
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    try {
      const latestEmails = await prisma.email.findMany({
        where: {
          user: { email: '<EMAIL>' }
        },
        orderBy: { createdAt: 'desc' },
        take: 3,
        select: {
          id: true,
          subject: true,
          senderEmail: true,
          createdAt: true,
          receivedAt: true
        }
      });

      if (latestEmails.length > 0) {
        console.log('最新的邮件:');
        latestEmails.forEach((email, index) => {
          const timeDiff = Date.now() - new Date(email.createdAt).getTime();
          const secondsAgo = Math.floor(timeDiff / 1000);
          
          console.log(`  ${index + 1}. ${email.subject}`);
          console.log(`     创建时间: ${email.createdAt} (${secondsAgo} 秒前)`);
          console.log(`     接收时间: ${email.receivedAt}`);
        });

        const testEmail = latestEmails.find(email => 
          email.subject.includes('邮件同步测试')
        );

        if (testEmail) {
          const syncDelay = Date.now() - new Date(testEmail.createdAt).getTime();
          console.log(`\n🎉 找到测试邮件! 同步延迟: ${Math.floor(syncDelay / 1000)} 秒`);
          
          if (syncDelay < 30000) { // 30秒内
            console.log('✅ 实时同步工作正常!');
          } else {
            console.log('⚠️ 同步延迟较高，可能需要优化');
          }
        } else {
          console.log('❌ 未找到测试邮件，同步可能有问题');
        }
      } else {
        console.log('❌ 数据库中没有找到邮件');
      }
    } finally {
      await prisma.$disconnect();
    }

  } catch (error) {
    console.error('❌ 发送测试邮件失败:', error);
  }
}

sendTestEmail();
