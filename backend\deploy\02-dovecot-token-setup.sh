#!/bin/bash

# Dovecot Token认证部署脚本
# 在邮件服务器上执行

set -e

echo "🚀 开始部署Dovecot Token认证..."

# 配置变量
DOVECOT_CONF_DIR="/etc/dovecot"
SCRIPTS_DIR="/usr/local/bin"
API_BASE_URL="http://localhost:3000"
BACKUP_DIR="/root/dovecot-backup-$(date +%Y%m%d-%H%M%S)"

# 1. 备份现有配置
echo "📦 备份现有Dovecot配置..."
mkdir -p "$BACKUP_DIR"
cp -r "$DOVECOT_CONF_DIR" "$BACKUP_DIR/"
echo "✅ 配置已备份到: $BACKUP_DIR"

# 2. 安装依赖
echo "📥 安装必要依赖..."
apt-get update
apt-get install -y curl jq

# 3. 创建Token认证脚本
echo "📝 创建Token认证脚本..."
cat > "$SCRIPTS_DIR/dovecot-token-auth.sh" << 'EOF'
#!/bin/bash

# Dovecot Token认证脚本
# 从环境变量读取认证信息
USERNAME="$USER"
TOKEN="$PASS"
SERVICE="$SERVICE"

# 配置
API_URL="http://localhost:3000/api/auth/verify-mail-token"
TIMEOUT=5

# 日志函数
log_auth() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [$$] $1" >> /var/log/dovecot-token-auth.log
}

# 验证输入
if [[ -z "$USERNAME" || -z "$TOKEN" ]]; then
    log_auth "ERROR: Missing username or token"
    exit 1
fi

# 确定服务类型
PURPOSE="imap"
if [[ "$SERVICE" == "smtp" ]]; then
    PURPOSE="smtp"
fi

log_auth "INFO: Verifying token for $USERNAME ($PURPOSE)"

# 调用API验证Token
response=$(curl -s -X POST "$API_URL" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$USERNAME\",\"token\":\"$TOKEN\",\"purpose\":\"$PURPOSE\"}" \
  --max-time $TIMEOUT 2>/dev/null)

# 检查curl执行结果
if [[ $? -ne 0 ]]; then
    log_auth "ERROR: API call failed for $USERNAME"
    exit 1
fi

# 解析响应
if echo "$response" | jq -e '.valid == true' >/dev/null 2>&1; then
    log_auth "SUCCESS: Token verified for $USERNAME"
    # 输出用户信息给Dovecot
    echo "userdb_uid=vmail"
    echo "userdb_gid=vmail"
    echo "userdb_home=/var/mail/vhosts/$(echo $USERNAME | cut -d@ -f2)/$(echo $USERNAME | cut -d@ -f1)"
    exit 0
else
    log_auth "FAILED: Invalid token for $USERNAME"
    exit 1
fi
EOF

chmod +x "$SCRIPTS_DIR/dovecot-token-auth.sh"
echo "✅ Token认证脚本已创建"

# 4. 创建日志文件
touch /var/log/dovecot-token-auth.log
chown dovecot:dovecot /var/log/dovecot-token-auth.log
chmod 640 /var/log/dovecot-token-auth.log

# 5. 配置Dovecot认证
echo "⚙️  配置Dovecot认证..."

# 备份原始认证配置
cp "$DOVECOT_CONF_DIR/conf.d/10-auth.conf" "$DOVECOT_CONF_DIR/conf.d/10-auth.conf.backup"

# 创建Token认证配置
cat > "$DOVECOT_CONF_DIR/conf.d/10-auth-token.conf" << 'EOF'
# Token认证配置

# 启用Token认证机制
auth_mechanisms = plain login

# Token认证的passdb配置
passdb {
  driver = checkpassword
  args = /usr/local/bin/dovecot-token-auth.sh
  
  # 只对特定服务启用
  # mechanisms = plain login
  
  # 可以设置优先级
  # priority = 1
}

# 保持原有的用户数据库配置
userdb {
  driver = static
  args = uid=vmail gid=vmail home=/var/mail/vhosts/%d/%n
}

# 可选：添加传统密码认证作为备用
# passdb {
#   driver = sql
#   args = /etc/dovecot/dovecot-sql.conf.ext
#   priority = 2
# }
EOF

# 6. 修改主配置文件以包含Token认证
if ! grep -q "10-auth-token.conf" "$DOVECOT_CONF_DIR/dovecot.conf"; then
    echo "!include conf.d/10-auth-token.conf" >> "$DOVECOT_CONF_DIR/dovecot.conf"
fi

# 7. 创建健康检查脚本
cat > "$SCRIPTS_DIR/dovecot-token-health.sh" << 'EOF'
#!/bin/bash

# Dovecot Token认证健康检查

API_URL="http://localhost:3000/api/health"
LOG_FILE="/var/log/dovecot-token-health.log"

# 检查API可用性
if curl -s --max-time 5 "$API_URL" >/dev/null; then
    echo "$(date): API健康检查通过" >> "$LOG_FILE"
    exit 0
else
    echo "$(date): API健康检查失败" >> "$LOG_FILE"
    exit 1
fi
EOF

chmod +x "$SCRIPTS_DIR/dovecot-token-health.sh"

# 8. 设置定时健康检查
(crontab -l 2>/dev/null; echo "*/5 * * * * $SCRIPTS_DIR/dovecot-token-health.sh") | crontab -

# 9. 测试配置
echo "🧪 测试Dovecot配置..."
if doveconf -n >/dev/null 2>&1; then
    echo "✅ Dovecot配置语法正确"
else
    echo "❌ Dovecot配置有错误，请检查"
    exit 1
fi

# 10. 重启Dovecot服务
echo "🔄 重启Dovecot服务..."
systemctl restart dovecot

if systemctl is-active --quiet dovecot; then
    echo "✅ Dovecot服务重启成功"
else
    echo "❌ Dovecot服务重启失败"
    echo "正在恢复备份配置..."
    cp "$BACKUP_DIR/dovecot/dovecot.conf" "$DOVECOT_CONF_DIR/"
    cp "$BACKUP_DIR/dovecot/conf.d/10-auth.conf" "$DOVECOT_CONF_DIR/conf.d/"
    systemctl restart dovecot
    exit 1
fi

echo "🎉 Dovecot Token认证部署完成！"
echo ""
echo "📋 部署信息:"
echo "  - 认证脚本: $SCRIPTS_DIR/dovecot-token-auth.sh"
echo "  - 配置文件: $DOVECOT_CONF_DIR/conf.d/10-auth-token.conf"
echo "  - 日志文件: /var/log/dovecot-token-auth.log"
echo "  - 备份目录: $BACKUP_DIR"
echo ""
echo "🔧 后续步骤:"
echo "  1. 确保后端API服务正在运行"
echo "  2. 测试Token认证: $SCRIPTS_DIR/test-token-auth.sh"
echo "  3. 监控日志: tail -f /var/log/dovecot-token-auth.log"
