import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import {
  recordEmailOpen,
  recordLinkClick,
  getEmailTrackingStats,
  createEmailTracking,
} from '../services/emailTrackingService';
import {
  getEmailReceiptStats,
  sendReadReceipt,
} from '../services/emailReceiptService';
import logger from '../utils/logger';

// 跟踪像素端点
export const trackPixel = async (req: Request, res: Response) => {
  const { trackingId } = req.params;
  
  try {
    // 记录邮件打开事件
    await recordEmailOpen(
      trackingId,
      req.ip,
      req.get('User-Agent'),
      // 这里可以添加地理位置解析
    );

    // 返回1x1透明像素
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );

    res.set({
      'Content-Type': 'image/png',
      'Content-Length': pixel.length,
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    });

    res.send(pixel);
  } catch (error) {
    logger.error('跟踪像素处理失败:', error);
    // 即使出错也要返回像素，避免影响邮件显示
    const pixel = Buffer.from(
      'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
      'base64'
    );
    res.set('Content-Type', 'image/png');
    res.send(pixel);
  }
};

// 跟踪链接点击端点
export const trackClick = async (req: Request, res: Response) => {
  const { trackingId } = req.params;
  const { url } = req.query;

  try {
    if (!url || typeof url !== 'string') {
      throw new AppError('缺少目标URL', 400);
    }

    // 记录链接点击事件
    await recordLinkClick(
      trackingId,
      url,
      req.ip,
      req.get('User-Agent'),
      // 这里可以添加地理位置解析
    );

    // 重定向到原始URL
    res.redirect(decodeURIComponent(url));
  } catch (error) {
    logger.error('跟踪链接处理失败:', error);
    
    // 如果有URL参数，仍然重定向，避免影响用户体验
    if (req.query.url && typeof req.query.url === 'string') {
      res.redirect(decodeURIComponent(req.query.url));
    } else {
      res.status(400).json({
        success: false,
        message: '无效的跟踪链接',
      });
    }
  }
};

// 获取邮件跟踪统计
export const getTrackingStats = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.params;
  const userId = req.user!.id;

  try {
    // 验证邮件是否属于当前用户
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
    });

    if (!email) {
      throw new AppError('邮件不存在', 404);
    }

    // 获取跟踪统计
    const trackingStats = await getEmailTrackingStats(emailId);
    const receiptStats = await getEmailReceiptStats(emailId);

    const response: ApiResponse = {
      success: true,
      message: '获取跟踪统计成功',
      data: {
        tracking: trackingStats,
        receipts: receiptStats,
      },
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(`获取跟踪统计失败: ${(error as Error).message}`, 500);
  }
};

// 创建邮件跟踪
export const createTracking = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.params;
  const { trackOpens, trackClicks, trackReplies, requestReceipt } = req.body;
  const userId = req.user!.id;

  try {
    // 验证邮件是否属于当前用户
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
    });

    if (!email) {
      throw new AppError('邮件不存在', 404);
    }

    // 创建跟踪记录
    const tracking = await createEmailTracking(emailId, userId, {
      trackOpens,
      trackClicks,
      trackReplies,
      requestReceipt,
    });

    const response: ApiResponse = {
      success: true,
      message: '邮件跟踪创建成功',
      data: tracking,
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(`创建邮件跟踪失败: ${(error as Error).message}`, 500);
  }
};

// 发送已读回执
export const sendReceiptRequest = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId } = req.params;
  const { recipientEmail, originalSubject, originalMessageId } = req.body;
  const userId = req.user!.id;

  try {
    // 验证邮件是否属于当前用户
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId,
      },
      include: { user: true },
    });

    if (!email) {
      throw new AppError('邮件不存在', 404);
    }

    // 发送已读回执
    const receipt = await sendReadReceipt(
      emailId,
      recipientEmail,
      email.user.email,
      originalSubject,
      originalMessageId
    );

    const response: ApiResponse = {
      success: true,
      message: '已读回执发送成功',
      data: receipt,
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError(`发送已读回执失败: ${(error as Error).message}`, 500);
  }
};

// 获取用户的跟踪统计概览
export const getTrackingOverview = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;
  const { days = 30 } = req.query;

  try {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - parseInt(days as string));

    // 获取用户的跟踪统计
    const trackingStats = await prisma.emailTracking.findMany({
      where: {
        userId,
        createdAt: { gte: startDate },
      },
      include: {
        email: {
          select: {
            id: true,
            subject: true,
            sentAt: true,
          },
        },
        events: true,
      },
    });

    // 统计数据
    const overview = {
      totalTracked: trackingStats.length,
      totalOpened: trackingStats.filter(t => t.isOpened).length,
      totalClicked: trackingStats.filter(t => 
        t.events.some(e => e.eventType === 'click')
      ).length,
      totalReplied: trackingStats.filter(t => t.isReplied).length,
      totalDelivered: trackingStats.filter(t => t.isDelivered).length,
      openRate: trackingStats.length > 0 
        ? (trackingStats.filter(t => t.isOpened).length / trackingStats.length * 100).toFixed(2)
        : '0',
      clickRate: trackingStats.length > 0
        ? (trackingStats.filter(t => 
            t.events.some(e => e.eventType === 'click')
          ).length / trackingStats.length * 100).toFixed(2)
        : '0',
      replyRate: trackingStats.length > 0
        ? (trackingStats.filter(t => t.isReplied).length / trackingStats.length * 100).toFixed(2)
        : '0',
      recentActivity: trackingStats
        .flatMap(t => t.events)
        .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
        .slice(0, 10),
    };

    const response: ApiResponse = {
      success: true,
      message: '获取跟踪概览成功',
      data: overview,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取跟踪概览失败: ${(error as Error).message}`, 500);
  }
};
