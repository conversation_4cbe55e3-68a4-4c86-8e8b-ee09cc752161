import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate, requireAdmin } from '../middleware/auth';
import * as systemManagementController from '../controllers/systemManagementController';

const router = Router();

// 所有路由都需要认证和管理员权限
router.use(authenticate);
router.use(requireAdmin);

// 获取系统指标
router.get('/metrics',
  asyncHandler(systemManagementController.getSystemMetrics)
);

// 获取系统警报
router.get('/alerts',
  asyncHandler(systemManagementController.getSystemAlerts)
);

// 解决系统警报
router.post('/alerts/:alertId/resolve',
  asyncHandler(systemManagementController.resolveAlert)
);

// 获取系统健康状态
router.get('/health',
  asyncHandler(systemManagementController.getSystemHealth)
);

// 生成系统报告
router.get('/report',
  asyncHandler(systemManagementController.generateSystemReport)
);

// 记录系统异常
router.post('/exceptions',
  asyncHandler(systemManagementController.recordSystemException)
);

// 获取系统日志
router.get('/logs',
  asyncHandler(systemManagementController.getSystemLogs)
);

// 清理系统数据
router.post('/cleanup',
  asyncHandler(systemManagementController.cleanupSystemData)
);

export default router;
