import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Button,
  Avatar,
  Tooltip,
  Spin,
  Typography,
  Space,
  Card,
  Alert,
  Tabs,
  message,
  Dropdown,
  Modal,
  Select,
} from "antd";
import {
  ArrowLeftOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  UserOutlined,
  EyeOutlined,
  FileTextOutlined,
  CodeOutlined,
  RollbackOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined,
  FolderOutlined,
  MailOutlined,
  ExclamationCircleOutlined,
  TagOutlined,
} from "@ant-design/icons";
import { useEmailStore } from "../store/emailStore";
import type { Email } from "../types/email";
import { processEmailContent, extractTextPreview } from "../utils/emailContentProcessor";
import "./EmailDetail.css";

const { Title, Text } = Typography;

const EmailDetail: React.FC = () => {
  const { emailId } = useParams<{ emailId: string }>();
  const navigate = useNavigate();
  const [currentEmail, setCurrentEmail] = useState<Email | null>(null);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'html' | 'text' | 'raw'>('html');
  
  const {
    emails,
    fetchEmails,
    updateEmail,
  } = useEmailStore();

  useEffect(() => {
    if (emailId) {
      // 查找当前邮件
      const email = emails.find(e => e.id === emailId);
      if (email) {
        setCurrentEmail(email);
        setLoading(false);
        
        // 如果邮件未读，标记为已读
        if (!email.isRead) {
          updateEmail(email.id, { isRead: true });
        }
      } else {
        // 如果没有找到邮件，尝试重新获取
        fetchEmails({ folderType: "inbox" }).then(() => {
          const foundEmail = emails.find(e => e.id === emailId);
          if (foundEmail) {
            setCurrentEmail(foundEmail);
            if (!foundEmail.isRead) {
              updateEmail(foundEmail.id, { isRead: true });
            }
          }
          setLoading(false);
        });
      }
    }
  }, [emailId, emails, fetchEmails, updateEmail]);

  // 获取上一封和下一封邮件
  const getCurrentIndex = () => {
    return emails.findIndex(email => email.id === emailId);
  };

  const getPreviousEmail = () => {
    const currentIndex = getCurrentIndex();
    return currentIndex > 0 ? emails[currentIndex - 1] : null;
  };

  const getNextEmail = () => {
    const currentIndex = getCurrentIndex();
    return currentIndex < emails.length - 1 ? emails[currentIndex + 1] : null;
  };

  const handlePrevious = () => {
    const prevEmail = getPreviousEmail();
    if (prevEmail) {
      navigate(`/email/detail/${prevEmail.id}`);
    }
  };

  const handleNext = () => {
    const nextEmail = getNextEmail();
    if (nextEmail) {
      navigate(`/email/detail/${nextEmail.id}`);
    }
  };

  const handleBack = () => {
    navigate(-1);
  };

  // 回复邮件
  const handleReply = () => {
    if (currentEmail) {
      navigate(`/compose?replyTo=${currentEmail.id}`);
    }
  };

  // 回复全部
  const handleReplyAll = () => {
    if (currentEmail) {
      navigate(`/compose?replyTo=${currentEmail.id}&replyAll=true`);
    }
  };

  // 转发邮件
  const handleForward = () => {
    if (currentEmail) {
      navigate(`/compose?forward=${currentEmail.id}`);
    }
  };

  // 删除邮件
  const handleDelete = () => {
    if (!currentEmail) return;

    Modal.confirm({
      title: '删除邮件',
      content: '确定要删除这封邮件吗？',
      icon: <ExclamationCircleOutlined />,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await updateEmail(currentEmail.id, { isDeleted: true });
          message.success('邮件已删除');
          navigate(-1); // 返回上一页
        } catch (error) {
          message.error('删除邮件失败');
        }
      },
    });
  };

  // 切换星标
  const handleToggleStar = async () => {
    if (!currentEmail) return;

    try {
      const newStarred = !currentEmail.isStarred;
      await updateEmail(currentEmail.id, { isStarred: newStarred });
      setCurrentEmail({ ...currentEmail, isStarred: newStarred });
      message.success(newStarred ? '已加星标' : '已取消星标');
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 标记为未读
  const handleMarkAsUnread = async () => {
    if (!currentEmail) return;

    try {
      await updateEmail(currentEmail.id, { isRead: false });
      setCurrentEmail({ ...currentEmail, isRead: false });
      message.success('已标记为未读');
    } catch (error) {
      message.error('操作失败');
    }
  };

  // 移动到文件夹 (暂时简化实现)
  const handleMoveToFolder = () => {
    message.info('移动到文件夹功能开发中...');
  };

  // 举报邮件
  const handleReport = () => {
    message.info('举报功能开发中...');
  };

  // 渲染邮件内容
  const renderEmailContent = () => {
    if (!currentEmail) return null;

    const processedContent = processEmailContent(
      currentEmail.contentHtml,
      currentEmail.contentText
    );

    // 如果没有内容，显示提示
    if (!processedContent.hasContent) {
      return (
        <Card className="email-content-card">
          <Alert
            message="邮件内容为空"
            description="此邮件没有可显示的内容。"
            type="info"
            showIcon
          />
        </Card>
      );
    }

    return (
      <Card className="email-content-card">
        <div className="email-content-header">
          <Space>
            <Button
              type={viewMode === 'html' ? 'primary' : 'default'}
              size="small"
              icon={<EyeOutlined />}
              onClick={() => setViewMode('html')}
            >
              HTML视图
            </Button>
            <Button
              type={viewMode === 'text' ? 'primary' : 'default'}
              size="small"
              icon={<FileTextOutlined />}
              onClick={() => setViewMode('text')}
            >
              文本视图
            </Button>
            <Button
              type={viewMode === 'raw' ? 'primary' : 'default'}
              size="small"
              icon={<CodeOutlined />}
              onClick={() => setViewMode('raw')}
            >
              原始内容
            </Button>
          </Space>
        </div>

        <div className="email-content-body">
          {renderContentByMode(processedContent)}
        </div>
      </Card>
    );
  };

  // 根据查看模式渲染内容
  const renderContentByMode = (processedContent: any) => {
    switch (viewMode) {
      case 'html':
        return (
          <div
            className="email-html-content"
            dangerouslySetInnerHTML={{ __html: processedContent.html }}
          />
        );

      case 'text':
        return (
          <pre className="email-text-content">
            {processedContent.text || '无文本内容'}
          </pre>
        );

      case 'raw':
        return (
          <div className="email-raw-content">
            <Tabs
              size="small"
              items={[
                {
                  key: 'html',
                  label: 'HTML源码',
                  children: (
                    <pre className="raw-content-pre">
                      {currentEmail?.contentHtml || '无HTML内容'}
                    </pre>
                  ),
                },
                {
                  key: 'text',
                  label: '文本源码',
                  children: (
                    <pre className="raw-content-pre">
                      {currentEmail?.contentText || '无文本内容'}
                    </pre>
                  ),
                },
              ]}
            />
          </div>
        );

      default:
        return null;
    }
  };

  if (loading) {
    return (
      <div className="email-detail-loading">
        <Spin size="large" />
      </div>
    );
  }

  if (!currentEmail) {
    return (
      <div className="email-detail-not-found">
        <Title level={3}>邮件未找到</Title>
        <Button onClick={handleBack}>返回</Button>
      </div>
    );
  }

  return (
    <div className="email-detail-page">
      {/* 顶部操作栏 */}
      <div className="email-detail-header">
        <div className="header-left">
          <Tooltip title="返回">
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              className="back-btn"
            />
          </Tooltip>
        </div>

        <div className="header-center">
          <Space size="small">
            {/* 主要操作按钮 */}
            <Tooltip title="回复">
              <Button
                type="primary"
                icon={<RollbackOutlined />}
                onClick={handleReply}
                size="small"
              >
                回复
              </Button>
            </Tooltip>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'reply',
                    label: '回复',
                    icon: <RollbackOutlined />,
                    onClick: handleReply,
                  },
                  {
                    key: 'replyAll',
                    label: '回复全部',
                    icon: <RollbackOutlined />,
                    onClick: handleReplyAll,
                  },
                  {
                    key: 'forward',
                    label: '转发',
                    icon: <ShareAltOutlined />,
                    onClick: handleForward,
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button size="small" icon={<ShareAltOutlined />}>
                更多回复
              </Button>
            </Dropdown>

            <Tooltip title={currentEmail?.isStarred ? "取消星标" : "加星标"}>
              <Button
                type="text"
                icon={currentEmail?.isStarred ? <StarFilled style={{ color: '#faad14' }} /> : <StarOutlined />}
                onClick={handleToggleStar}
                size="small"
              />
            </Tooltip>

            <Tooltip title="标记为未读">
              <Button
                type="text"
                icon={<MailOutlined />}
                onClick={handleMarkAsUnread}
                size="small"
              />
            </Tooltip>

            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                onClick={handleDelete}
                size="small"
              />
            </Tooltip>

            <Dropdown
              menu={{
                items: [
                  {
                    key: 'move',
                    label: '移动到文件夹',
                    icon: <FolderOutlined />,
                    onClick: handleMoveToFolder,
                  },
                  {
                    key: 'label',
                    label: '添加标签',
                    icon: <TagOutlined />,
                    onClick: () => message.info('标签功能开发中...'),
                  },
                  {
                    key: 'report',
                    label: '举报邮件',
                    icon: <ExclamationCircleOutlined />,
                    onClick: handleReport,
                  },
                ],
              }}
              trigger={['click']}
            >
              <Button
                type="text"
                icon={<MoreOutlined />}
                size="small"
              />
            </Dropdown>
          </Space>
        </div>

        <div className="header-right">
          <Space>
            <Tooltip title="上一封">
              <Button
                type="text"
                icon={<ArrowUpOutlined />}
                onClick={handlePrevious}
                disabled={!getPreviousEmail()}
                className="nav-btn"
                size="small"
              />
            </Tooltip>

            <Tooltip title="下一封">
              <Button
                type="text"
                icon={<ArrowDownOutlined />}
                onClick={handleNext}
                disabled={!getNextEmail()}
                className="nav-btn"
                size="small"
              />
            </Tooltip>
          </Space>
        </div>
      </div>

      {/* 邮件内容 */}
      <div className="email-detail-content">
        <div className="email-header">
          <Title level={2} className="email-subject">
            {currentEmail.subject || "(无主题)"}
          </Title>
          
          <div className="email-meta">
            <Avatar
              icon={<UserOutlined />}
              size={40}
              className="sender-avatar"
            />
            <div className="sender-info">
              <div className="sender-name">
                <Text strong>
                  {currentEmail.senderName || currentEmail.senderEmail}
                </Text>
              </div>
              <div className="email-date">
                <Text type="secondary">
                  {currentEmail.receivedAt
                    ? new Date(currentEmail.receivedAt).toLocaleString()
                    : ""}
                </Text>
              </div>
            </div>
          </div>
        </div>
        
        {renderEmailContent()}
      </div>
    </div>
  );
};

export default EmailDetail;
