import logger from '../utils/logger';
import prisma from '../config/database';
import {
  getEmailAccountConfig,
  updateSyncStatus,
  updateConnectionStatus
} from './emailAccountService';
import { applyRulesToEmail } from './emailRuleService';
import systemManagementService from './systemManagementService';
import { subAccountEmailForwardService } from './subAccountEmailForwardService';
import { decrypt } from '../utils/encryption';
import { ImapFlow } from 'imapflow';
import { simpleParser, ParsedMail } from 'mailparser';

interface AccountSyncTask {
  accountId: number;
  userId: number;
  userEmail: string;
  accountEmail: string;
  timer: NodeJS.Timeout;
  intervalMinutes: number;
  isActive: boolean;
}

interface IMAPConnection {
  accountId: number;
  imap: ImapFlow;
  isIdle: boolean;
  lastActivity: Date;
}

class MultiAccountEmailSyncService {
  private syncTasks: Map<number, AccountSyncTask> = new Map();
  private imapConnections: Map<number, IMAPConnection> = new Map();
  private isInitialized = false;

  /**
   * 初始化多账户邮件同步服务
   */
  async initialize() {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('初始化多账户邮件同步服务...');
      
      // 启动所有活跃用户的邮箱账户同步
      await this.startAllAccountSync();
      
      this.isInitialized = true;
      logger.info('多账户邮件同步服务初始化完成');
    } catch (error) {
      logger.error('初始化多账户邮件同步服务失败:', error);
      throw error;
    }
  }

  /**
   * 启动所有活跃邮箱账户的同步
   */
  async startAllAccountSync(): Promise<void> {
    try {
      logger.info('开始启动所有邮箱账户的同步服务...');

      // 获取所有启用同步的邮箱账户
      const accounts = await prisma.emailAccount.findMany({
        where: {
          isActive: true,
          syncEnabled: true,
          connectionStatus: {
            in: ['connected', 'unknown'] // 只同步连接正常或未知状态的账户
          }
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              isActive: true,
              emailVerified: true
            }
          }
        }
      });

      let startedCount = 0;

      for (const account of accounts) {
        if (!account.user.isActive || !account.user.emailVerified) {
          continue;
        }

        try {
          await this.startAccountSync(account.id);
          startedCount++;
        } catch (error) {
          logger.error(`启动账户 ${account.id} (${account.email}) 同步失败:`, error);
          // 更新连接状态为错误
          await updateConnectionStatus(account.id, 'error', (error as Error).message);

          // 记录系统异常
          await systemManagementService.recordSystemException(
            'error',
            '邮箱账户同步启动失败',
            `账户 ${account.email} 同步启动失败: ${(error as Error).message}`,
            'multi_account_sync',
            { accountId: account.id, accountEmail: account.email, error: (error as Error).stack }
          );
        }
      }

      logger.info(`邮箱账户同步服务启动完成，成功启动 ${startedCount} 个账户的同步任务`);
    } catch (error) {
      logger.error('启动邮箱账户同步服务失败:', error);
    }
  }

  /**
   * 启动单个邮箱账户的同步
   */
  async startAccountSync(accountId: number): Promise<void> {
    try {
      // 如果已经存在同步任务，先停止
      if (this.syncTasks.has(accountId)) {
        this.stopAccountSync(accountId);
      }

      // 获取账户信息
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              isActive: true,
              emailVerified: true
            }
          }
        }
      });

      if (!account || !account.user.isActive || !account.user.emailVerified) {
        throw new Error(`账户 ${accountId} 不存在或用户未激活`);
      }

      logger.info(`启动账户 ${account.email} 的邮件同步，间隔: ${account.syncInterval} 分钟`);

      // 更新同步状态
      await updateSyncStatus(accountId, 'idle');

      // 创建同步任务
      const syncEmails = async () => {
        try {
          await this.syncAccountEmails(accountId);
        } catch (error) {
          logger.error(`账户 ${accountId} 邮件同步失败:`, error);
          await updateSyncStatus(accountId, 'error', (error as Error).message);
          await updateConnectionStatus(accountId, 'error', (error as Error).message);
        }
      };

      // 立即执行一次
      await syncEmails();

      // 设置定时任务
      const timer = setInterval(syncEmails, account.syncInterval * 60 * 1000);

      // 保存同步任务引用
      this.syncTasks.set(accountId, {
        accountId,
        userId: account.user.id,
        userEmail: account.user.email,
        accountEmail: account.email,
        timer,
        intervalMinutes: account.syncInterval,
        isActive: true
      });

      logger.info(`账户 ${account.email} 邮件同步已启动`);
    } catch (error) {
      logger.error(`启动账户 ${accountId} 邮件同步失败:`, error);
      throw error;
    }
  }

  /**
   * 停止单个邮箱账户的同步
   */
  stopAccountSync(accountId: number): void {
    const syncTask = this.syncTasks.get(accountId);
    if (syncTask) {
      clearInterval(syncTask.timer);
      syncTask.isActive = false;
      this.syncTasks.delete(accountId);
      logger.info(`账户 ${accountId} 邮件同步已停止`);
    }

    // 关闭IMAP连接
    this.closeIMAPConnection(accountId).catch(error => {
      logger.error(`关闭账户 ${accountId} IMAP连接失败:`, error);
    });
  }

  /**
   * 停止所有邮箱账户的同步
   */
  async stopAllAccountSync(): Promise<void> {
    logger.info('停止所有邮箱账户同步...');
    
    for (const [accountId] of this.syncTasks) {
      this.stopAccountSync(accountId);
    }
    
    // 关闭所有IMAP连接
    const closePromises = Array.from(this.imapConnections.keys()).map(accountId =>
      this.closeIMAPConnection(accountId).catch(error => {
        logger.error(`关闭账户 ${accountId} IMAP连接失败:`, error);
      })
    );
    await Promise.all(closePromises);
    
    logger.info('所有邮箱账户同步已停止');
  }

  /**
   * 重启单个邮箱账户的同步
   */
  async restartAccountSync(accountId: number): Promise<void> {
    this.stopAccountSync(accountId);
    await this.startAccountSync(accountId);
  }

  /**
   * 同步单个邮箱账户的邮件
   */
  private async syncAccountEmails(accountId: number): Promise<void> {
    try {
      await updateSyncStatus(accountId, 'syncing');
      
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        include: {
          user: {
            select: {
              id: true,
              email: true
            }
          }
        }
      });

      if (!account) {
        throw new Error(`账户 ${accountId} 不存在`);
      }

      // 获取账户配置
      const config = await getEmailAccountConfig(accountId);
      
      // 创建IMAP连接
      const imap = await this.getOrCreateIMAPConnection(accountId, config);
      
      // 同步邮件
      const newEmails = await this.fetchNewEmailsFromIMAP(imap, accountId);
      
      if (newEmails.length > 0) {
        logger.info(`账户 ${account.email} 收到 ${newEmails.length} 封新邮件`);
        
        // TODO: 通过WebSocket通知用户 (暂时注释避免循环导入)
        // io.to(`user_${account.user.id}`).emit('newEmails', {
        //   accountId,
        //   accountEmail: account.email,
        //   count: newEmails.length,
        //   emails: newEmails.map(email => ({
        //     id: email.id,
        //     subject: email.subject,
        //     senderEmail: email.senderEmail,
        //     senderName: email.senderName,
        //     receivedAt: email.receivedAt,
        //     isRead: email.isRead
        //   }))
        // });
      }

      // 更新连接状态和同步状态
      await updateConnectionStatus(accountId, 'connected');
      await updateSyncStatus(accountId, 'idle');
      
    } catch (error) {
      logger.error(`同步账户 ${accountId} 邮件失败:`, error);
      await updateSyncStatus(accountId, 'error', (error as Error).message);
      await updateConnectionStatus(accountId, 'error', (error as Error).message);
      throw error;
    }
  }

  /**
   * 获取或创建IMAP连接 - 使用ImapFlow
   */
  private async getOrCreateIMAPConnection(accountId: number, config: any): Promise<ImapFlow> {
    const existingConnection = this.imapConnections.get(accountId);

    if (existingConnection && !existingConnection.imap.closed) {
      // 更新最后活动时间
      existingConnection.lastActivity = new Date();
      return existingConnection.imap;
    }

    // 创建新连接
    const client = new ImapFlow({
      host: config.imapHost,
      port: config.imapPort,
      secure: config.imapSecure,
      auth: {
        user: config.imapUsername,
        pass: config.imapPassword
      },
      tls: {
        rejectUnauthorized: false,
      },
      logger: false
    });

    try {
      await client.connect();
      logger.info(`账户 ${accountId} IMAP连接已建立`);

      // 保存连接
      this.imapConnections.set(accountId, {
        accountId,
        imap: client,
        isIdle: false,
        lastActivity: new Date()
      });

      // 设置事件监听器
      client.on('error', (err: Error) => {
        logger.error(`账户 ${accountId} IMAP连接错误:`, err);
        this.closeIMAPConnection(accountId).catch(error => {
          logger.error(`关闭账户 ${accountId} IMAP连接失败:`, error);
        });
      });

      client.on('close', () => {
        logger.info(`账户 ${accountId} IMAP连接已关闭`);
        this.imapConnections.delete(accountId);
      });

      return client;

    } catch (error) {
      logger.error(`账户 ${accountId} IMAP连接失败:`, error);
      throw error;
    }
  }

  /**
   * 关闭IMAP连接 - 使用ImapFlow
   */
  private async closeIMAPConnection(accountId: number): Promise<void> {
    const connection = this.imapConnections.get(accountId);
    if (connection) {
      try {
        if (!connection.imap.closed) {
          await connection.imap.logout();
        }
      } catch (error) {
        logger.error(`关闭账户 ${accountId} IMAP连接失败:`, error);
      }
      this.imapConnections.delete(accountId);
    }
  }

  /**
   * 从IMAP获取新邮件 - 使用ImapFlow
   */
  private async fetchNewEmailsFromIMAP(imap: ImapFlow, accountId: number): Promise<any[]> {
    try {
      // 打开收件箱
      const mailbox = await imap.mailboxOpen('INBOX');

      // 获取最近的邮件（最多50封）
      const totalMessages = mailbox.exists;
      if (totalMessages === 0) {
        return [];
      }

      const startSeq = Math.max(1, totalMessages - 49); // 获取最近50封
      const endSeq = totalMessages;

      const newEmails: any[] = [];

      // 使用ImapFlow的fetch方法
      for await (const message of imap.fetch(`${startSeq}:${endSeq}`, {
        source: true,
        envelope: true,
        bodyStructure: true,
        internalDate: true,
        size: true,
        flags: true,
        uid: true
      })) {
        try {
          // 解析邮件
          const parsed = await simpleParser(message.source);

          // 检查邮件是否已存在
          const existingEmail = await prisma.email.findFirst({
            where: {
              messageId: parsed.messageId || `${Date.now()}-${message.seq}`,
              accountId: accountId
            }
          });

          if (!existingEmail) {
            // 保存新邮件
            const savedEmail = await this.saveEmailToDatabase(parsed, accountId, message);
            if (savedEmail) {
              newEmails.push(savedEmail);
            }
          }
        } catch (error) {
          logger.error(`处理邮件 ${message.seq} 失败:`, error);
        }
      }

      return newEmails;

    } catch (error) {
      logger.error(`从IMAP获取邮件失败:`, error);
      throw error;
    }
  }

  /**
   * 保存邮件到数据库
   */
  private async saveEmailToDatabase(parsedMail: ParsedMail, accountId: number, attributes: any): Promise<any> {
    try {
      const account = await prisma.emailAccount.findUnique({
        where: { id: accountId },
        include: {
          user: {
            select: {
              id: true,
              email: true
            }
          }
        }
      });

      if (!account) {
        throw new Error(`账户 ${accountId} 不存在`);
      }

      // 获取收件箱文件夹
      const inboxFolder = await prisma.folder.findFirst({
        where: {
          userId: account.user.id,
          type: 'inbox'
        }
      });

      if (!inboxFolder) {
        throw new Error(`用户 ${account.user.id} 的收件箱不存在`);
      }

      const messageId = parsedMail.messageId || `${Date.now()}@${account.email}`;

      // 解析收件人
      const recipients = parsedMail.to ?
        (Array.isArray(parsedMail.to) ? parsedMail.to : [parsedMail.to])
          .map(addr => typeof addr === 'string' ? addr : addr.text)
          .join(', ') : '';

      const ccRecipients = parsedMail.cc ?
        (Array.isArray(parsedMail.cc) ? parsedMail.cc : [parsedMail.cc])
          .map(addr => typeof addr === 'string' ? addr : addr.text)
          .join(', ') : null;

      const bccRecipients = parsedMail.bcc ?
        (Array.isArray(parsedMail.bcc) ? parsedMail.bcc : [parsedMail.bcc])
          .map(addr => typeof addr === 'string' ? addr : addr.text)
          .join(', ') : null;

      // 解析发件人
      const fromAddr = parsedMail.from ?
        (Array.isArray(parsedMail.from) ? parsedMail.from[0] : parsedMail.from) : null;

      const senderEmail = fromAddr ?
        (typeof fromAddr === 'string' ? fromAddr : fromAddr.text) : '';
      const senderName = fromAddr && typeof fromAddr === 'object' ? fromAddr.text : null;

      // 处理附件
      const attachments = parsedMail.attachments ?
        JSON.stringify(parsedMail.attachments.map(att => ({
          filename: att.filename,
          contentType: att.contentType,
          size: att.size
        }))) : null;

      // 创建邮件记录
      const email = await prisma.email.create({
        data: {
          messageId,
          userId: account.user.id,
          accountId: accountId,
          folderId: inboxFolder.id,
          subject: parsedMail.subject || '(无主题)',
          senderEmail,
          senderName,
          recipients,
          ccRecipients,
          bccRecipients,
          contentText: parsedMail.text || '',
          contentHtml: parsedMail.html || '',
          attachments,
          isRead: attributes.flags.includes('\\Seen'),
          isStarred: attributes.flags.includes('\\Flagged'),
          receivedAt: parsedMail.date || new Date(),
          imapUid: attributes.uid,
          imapFlags: JSON.stringify(attributes.flags)
        }
      });

      // 应用邮件规则
      try {
        await applyRulesToEmail(email as any);
      } catch (error) {
        logger.error(`应用邮件规则失败: ${messageId}`, error);
      }

      logger.info(`新邮件已保存: ${messageId} (账户: ${account.email})`);

      // 检查是否为子账户，如果是则转发到主账户
      try {
        await subAccountEmailForwardService.handleSubAccountEmailForward(email);
      } catch (forwardError) {
        logger.error(`子账户邮件转发失败: ${messageId}`, forwardError);
        // 转发失败不影响邮件保存
      }

      return email;

    } catch (error) {
      logger.error('保存邮件到数据库失败:', error);
      throw error;
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus(): Array<{
    accountId: number;
    userEmail: string;
    accountEmail: string;
    intervalMinutes: number;
    isActive: boolean;
  }> {
    return Array.from(this.syncTasks.values()).map(task => ({
      accountId: task.accountId,
      userEmail: task.userEmail,
      accountEmail: task.accountEmail,
      intervalMinutes: task.intervalMinutes,
      isActive: task.isActive
    }));
  }

  /**
   * 检查账户是否正在同步
   */
  isAccountSyncActive(accountId: number): boolean {
    const task = this.syncTasks.get(accountId);
    return task ? task.isActive : false;
  }

  /**
   * 获取活跃同步任务数量
   */
  getActiveSyncCount(): number {
    return Array.from(this.syncTasks.values()).filter(task => task.isActive).length;
  }
}

// 创建单例实例
export const multiAccountEmailSyncService = new MultiAccountEmailSyncService();
export default multiAccountEmailSyncService;
