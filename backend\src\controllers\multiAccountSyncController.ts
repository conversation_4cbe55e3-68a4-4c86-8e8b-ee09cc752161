import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import multiAccountSyncManager from '../services/multiAccountSyncManager';
import prisma from '../config/database';
import logger from '../utils/logger';

// 获取同步状态
export const getSyncStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const status = multiAccountSyncManager.getSyncStatus();

    const response: ApiResponse = {
      success: true,
      message: '获取同步状态成功',
      data: status,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取同步状态失败: ${(error as Error).message}`, 500);
  }
};

// 获取同步统计信息
export const getSyncStatistics = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const statistics = await multiAccountSyncManager.getSyncStatistics();

    const response: ApiResponse = {
      success: true,
      message: '获取同步统计信息成功',
      data: statistics,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取同步统计信息失败: ${(error as Error).message}`, 500);
  }
};

// 启动用户的邮箱同步
export const startUserSync = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    await multiAccountSyncManager.startUserAccountSync(userId);

    const response: ApiResponse = {
      success: true,
      message: '用户邮箱同步已启动',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动用户邮箱同步失败: ${(error as Error).message}`, 500);
  }
};

// 停止用户的邮箱同步
export const stopUserSync = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    await multiAccountSyncManager.stopUserAccountSync(userId);

    const response: ApiResponse = {
      success: true,
      message: '用户邮箱同步已停止',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止用户邮箱同步失败: ${(error as Error).message}`, 500);
  }
};

// 启动单个邮箱账户同步
export const startAccountSync = async (req: AuthenticatedRequest, res: Response) => {
  const { accountId } = req.params;
  const userId = req.user!.id;

  try {
    // 验证账户所有权
    const account = await prisma.emailAccount.findFirst({
      where: {
        id: parseInt(accountId),
        userId,
      },
    });

    if (!account) {
      throw new AppError('邮箱账户不存在', 404);
    }

    await multiAccountSyncManager.startAccountSync(parseInt(accountId));

    const response: ApiResponse = {
      success: true,
      message: '邮箱账户同步已启动',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动邮箱账户同步失败: ${(error as Error).message}`, 500);
  }
};

// 停止单个邮箱账户同步
export const stopAccountSync = async (req: AuthenticatedRequest, res: Response) => {
  const { accountId } = req.params;
  const userId = req.user!.id;

  try {
    // 验证账户所有权
    const account = await prisma.emailAccount.findFirst({
      where: {
        id: parseInt(accountId),
        userId,
      },
    });

    if (!account) {
      throw new AppError('邮箱账户不存在', 404);
    }

    multiAccountSyncManager.stopAccountSync(parseInt(accountId));

    const response: ApiResponse = {
      success: true,
      message: '邮箱账户同步已停止',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止邮箱账户同步失败: ${(error as Error).message}`, 500);
  }
};

// 重启单个邮箱账户同步
export const restartAccountSync = async (req: AuthenticatedRequest, res: Response) => {
  const { accountId } = req.params;
  const userId = req.user!.id;

  try {
    // 验证账户所有权
    const account = await prisma.emailAccount.findFirst({
      where: {
        id: parseInt(accountId),
        userId,
      },
    });

    if (!account) {
      throw new AppError('邮箱账户不存在', 404);
    }

    await multiAccountSyncManager.restartAccountSync(parseInt(accountId));

    const response: ApiResponse = {
      success: true,
      message: '邮箱账户同步已重启',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`重启邮箱账户同步失败: ${(error as Error).message}`, 500);
  }
};

// 管理员功能：启动所有同步
export const startAllSync = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    await multiAccountSyncManager.startAllSync();

    const response: ApiResponse = {
      success: true,
      message: '所有邮箱同步已启动',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动所有邮箱同步失败: ${(error as Error).message}`, 500);
  }
};

// 管理员功能：停止所有同步
export const stopAllSync = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    multiAccountSyncManager.stopAllSync();

    const response: ApiResponse = {
      success: true,
      message: '所有邮箱同步已停止',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止所有邮箱同步失败: ${(error as Error).message}`, 500);
  }
};

// 检查账户同步状态
export const checkAccountSyncStatus = async (req: AuthenticatedRequest, res: Response) => {
  const { accountId } = req.params;
  const userId = req.user!.id;

  try {
    // 验证账户所有权
    const account = await prisma.emailAccount.findFirst({
      where: {
        id: parseInt(accountId),
        userId,
      },
    });

    if (!account) {
      throw new AppError('邮箱账户不存在', 404);
    }

    const isActive = multiAccountSyncManager.isAccountSyncActive(parseInt(accountId));

    const response: ApiResponse = {
      success: true,
      message: '获取账户同步状态成功',
      data: {
        accountId: parseInt(accountId),
        isActive,
        syncEnabled: account.syncEnabled,
        syncInterval: account.syncInterval,
        lastSyncAt: account.lastSyncAt,
        syncStatus: account.syncStatus,
        syncError: account.syncError
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取账户同步状态失败: ${(error as Error).message}`, 500);
  }
};
