#!/bin/bash

# 邮箱系统部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# environment: dev (默认) | prod

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 环境变量检查
check_env() {
    local env_file=".env"
    
    if [ ! -f "$env_file" ]; then
        log_error "环境变量文件 $env_file 不存在"
        log_info "请复制 .env.example 为 .env 并填入正确的配置"
        exit 1
    fi
    
    # 检查必需的环境变量
    local required_vars=("DB_PASSWORD" "JWT_SECRET" "REDIS_PASSWORD")
    
    for var in "${required_vars[@]}"; do
        if ! grep -q "^$var=" "$env_file" || grep -q "^$var=$" "$env_file"; then
            log_error "环境变量 $var 未设置或为空"
            exit 1
        fi
    done
    
    log_success "环境变量检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "/var/lib/email-system/mysql"
        "/var/lib/email-system/redis"
        "/var/lib/email-system/uploads"
        "/var/log/email-system"
        "./docker/ssl"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            sudo mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    # 设置权限
    sudo chown -R $USER:$USER /var/lib/email-system
    sudo chown -R $USER:$USER /var/log/email-system
    
    log_success "目录创建完成"
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml build --no-cache
    else
        docker-compose build --no-cache
    fi
    
    log_success "镜像构建完成"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    # 等待数据库启动
    sleep 10
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml exec backend npx prisma migrate deploy
    else
        docker-compose exec backend npx prisma migrate dev
    fi
    
    log_success "数据库迁移完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 显示状态
show_status() {
    log_info "服务状态:"
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
}

# 主函数
main() {
    local environment=${1:-dev}
    
    log_info "开始部署邮箱系统 (环境: $environment)"
    
    export ENVIRONMENT=$environment
    
    check_dependencies
    check_env
    create_directories
    build_images
    start_services
    migrate_database
    
    if health_check; then
        show_status
        log_success "部署完成!"
        log_info "访问地址: http://localhost (开发环境) 或 https://blindedby.love (生产环境)"
    else
        log_error "部署失败，请检查日志"
        exit 1
    fi
}

# 脚本入口
main "$@"
