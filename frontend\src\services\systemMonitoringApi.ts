import api from '../config/api';
import type { ApiResponse } from '../types/api';

// 系统监控相关类型定义
export interface SystemInfo {
  hostname: string;
  platform: string;
  arch: string;
  nodeVersion: string;
  uptime: number;
  systemUptime: number;
  totalMemory: number;
  freeMemory: number;
  cpuCount: number;
  loadAverage: number[];
}

export interface SystemStatistics {
  totalUsers: number;
  activeUsers: number;
  systemUsers: number;
  totalEmails: number;
  todayEmails: number;
}

export interface SystemOverview {
  systemInfo: SystemInfo;
  statistics: SystemStatistics;
  timestamp: string;
}

export interface ServiceStatus {
  name: string;
  status: 'running' | 'stopped' | 'error';
  message: string;
}

export interface DiskUsage {
  filesystem: string;
  size: string;
  used: string;
  available: string;
  usePercentage: string;
  mountPoint: string;
  error?: string;
}

export interface MailQueue {
  count: number;
  status: 'empty' | 'has_mail';
  message: string;
  error?: string;
}

export interface MailServerStatus {
  services: {
    postfix: ServiceStatus;
    dovecot: ServiceStatus;
  };
  diskUsage: DiskUsage;
  mailQueue: MailQueue;
  timestamp: string;
}

export interface EmailStatsSummary {
  totalEmails: number;
  sentEmails: number;
  receivedEmails: number;
  deletedEmails: number;
  unreadEmails: number;
  starredEmails: number;
}

export interface EmailTimelineData {
  date: string;
  count: number;
}

export interface EmailStatistics {
  period: string;
  summary: EmailStatsSummary;
  timeline: EmailTimelineData[];
  timestamp: string;
}

export interface SystemLog {
  id?: number;
  timestamp: string;
  level: string;
  message: string;
  service?: string;
  action?: string;
  ipAddress?: string;
  userAgent?: string;
  user?: {
    email: string;
    username: string;
  };
  details?: any;
}

export interface SystemLogs {
  logs: SystemLog[];
  type: string;
  level: string;
  limit: number;
  offset: number;
  timestamp: string;
}

/**
 * 获取系统概览信息
 */
export const getSystemOverview = async (): Promise<SystemOverview> => {
  const response = await api.get<ApiResponse<SystemOverview>>('/system-monitoring/overview');
  return response.data.data!;
};

/**
 * 获取邮件服务器状态
 */
export const getMailServerStatus = async (): Promise<MailServerStatus> => {
  const response = await api.get<ApiResponse<MailServerStatus>>('/system-monitoring/mail-server');
  return response.data.data!;
};

/**
 * 获取邮件统计信息
 */
export const getEmailStatistics = async (period: '24h' | '7d' | '30d' = '7d'): Promise<EmailStatistics> => {
  const response = await api.get<ApiResponse<EmailStatistics>>('/system-monitoring/email-stats', {
    params: { period }
  });
  return response.data.data!;
};

/**
 * 获取系统日志
 */
export const getSystemLogs = async (params: {
  type?: 'app' | 'mail' | 'security';
  level?: 'all' | 'error' | 'warn' | 'info';
  limit?: number;
  offset?: number;
} = {}): Promise<SystemLogs> => {
  const response = await api.get<ApiResponse<SystemLogs>>('/system-monitoring/logs', {
    params
  });
  return response.data.data!;
};

/**
 * 格式化内存大小
 */
export const formatBytes = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * 格式化运行时间
 */
export const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  } else {
    return `${minutes}分钟`;
  }
};

/**
 * 获取状态颜色
 */
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'running':
    case 'active':
    case 'success':
      return 'success';
    case 'stopped':
    case 'inactive':
    case 'warning':
      return 'warning';
    case 'error':
    case 'failed':
      return 'error';
    default:
      return 'default';
  }
};

/**
 * 获取日志级别颜色
 */
export const getLogLevelColor = (level: string | undefined | null): string => {
  if (!level) {
    return 'default';
  }

  switch (level.toLowerCase()) {
    case 'error':
      return 'red';
    case 'warn':
    case 'warning':
      return 'orange';
    case 'info':
      return 'blue';
    case 'debug':
      return 'gray';
    default:
      return 'default';
  }
};

/**
 * 获取统计周期选项
 */
export const getPeriodOptions = () => [
  { value: '24h', label: '最近24小时' },
  { value: '7d', label: '最近7天' },
  { value: '30d', label: '最近30天' }
];

/**
 * 获取日志类型选项
 */
export const getLogTypeOptions = () => [
  { value: 'app', label: '应用日志' },
  { value: 'mail', label: '邮件服务日志' },
  { value: 'security', label: '安全日志' }
];

/**
 * 获取日志级别选项
 */
export const getLogLevelOptions = () => [
  { value: 'all', label: '全部级别' },
  { value: 'error', label: '错误' },
  { value: 'warn', label: '警告' },
  { value: 'info', label: '信息' }
];
