import { Response } from 'express';
import { AuthenticatedRequest, AppError, ApiResponse } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';
import { deleteUserMailboxFolders } from '../services/mailboxService';
import { createUserPassword } from '../utils/passwordManager';
import { createUserMailboxFolders } from '../services/mailboxService';

interface SystemUserData {
  email: string;
  username: string;
  displayName: string;
  userType: 'admin' | 'welcome' | 'postManager' | 'custom';
  description?: string;
  password?: string;
}

// 获取系统用户列表
export const getSystemUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const systemUsers = await prisma.user.findMany({
      where: {
        OR: [
          { role: 'admin' },
          { email: { endsWith: '@blindedby.love' } },
        ],
      },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        isActive: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: [
        { role: 'desc' }, // admin 用户优先
        { createdAt: 'asc' },
      ],
    });

    // 为每个用户添加用户类型
    const usersWithType = systemUsers.map(user => {
      let userType: string = 'custom';
      
      if (user.role === 'admin') {
        userType = 'admin';
      } else if (user.email.startsWith('welcome@')) {
        userType = 'welcome';
      } else if (user.email.startsWith('postManager@') || user.email.startsWith('postmaster@')) {
        userType = 'postManager';
      }

      return {
        ...user,
        userType,
        description: getSystemUserDescription(userType),
      };
    });

    const response: ApiResponse = {
      success: true,
      message: '获取系统用户列表成功',
      data: usersWithType,
    };

    res.json(response);
  } catch (error) {
    logger.error('获取系统用户列表失败:', error);
    throw new AppError('获取系统用户列表失败', 500);
  }
};

// 创建系统用户
export const createSystemUser = async (req: AuthenticatedRequest, res: Response) => {
  const { email, username, displayName, userType, description, password }: SystemUserData = req.body;

  try {
    // 检查邮箱是否已存在
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new AppError('邮箱地址已存在', 409);
    }

    // 检查用户名是否已存在
    const existingUsername = await prisma.user.findUnique({
      where: { username },
    });

    if (existingUsername) {
      throw new AppError('用户名已存在', 409);
    }

    // 验证邮箱域名
    if (!email.endsWith('@blindedby.love')) {
      throw new AppError('系统用户邮箱必须使用 @blindedby.love 域名', 400);
    }

    // 获取或创建虚拟域名
    const domain = email.split('@')[1];
    let virtualDomain = await prisma.virtualDomain.findUnique({
      where: { name: domain },
    });

    if (!virtualDomain) {
      virtualDomain = await prisma.virtualDomain.create({
        data: {
          name: domain,
          active: 1,
        },
      });
    }

    // 生成密码
    const passwordResult = await createUserPassword(password || 'system123456');

    // 确定用户角色
    let role = 'user';
    if (userType === 'admin') {
      role = 'admin';
    }

    // 创建用户
    const user = await prisma.user.create({
      data: {
        email,
        username,
        password: passwordResult.webPassword,
        mailPassword: passwordResult.mailPassword || null,
        displayName,
        role,
        isMailActive: !!passwordResult.mailPassword,
        emailVerified: true, // 系统用户默认已验证
        domainId: virtualDomain.id,
      },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        isActive: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // 创建默认邮箱账户配置
    if (passwordResult.mailPassword) {
      const { encrypt } = require('../utils/encryption');

      await prisma.emailAccount.create({
        data: {
          userId: user.id,
          name: '默认邮箱',
          email,
          displayName: user.displayName || user.username,
          imapHost: 'mail.blindedby.love',
          imapPort: 993,
          imapSecure: true,
          imapUsername: email,
          imapPassword: encrypt(passwordResult.mailPassword),
          smtpHost: 'mail.blindedby.love',
          smtpPort: 587,
          smtpSecure: false,
          smtpUsername: email,
          smtpPassword: encrypt(passwordResult.mailPassword),
          authType: 'password',
          isDefault: true,
          isActive: true,
          syncEnabled: true,
          syncInterval: 5,
          autoConfigured: true,
          provider: 'blindedby.love'
        }
      });

      logger.info(`为系统用户 ${email} 创建了默认邮箱账户配置`);
    }

    // 创建用户邮箱文件夹
    try {
      await createUserMailboxFolders(email);
      logger.info(`系统用户邮箱文件夹创建成功: ${email}`);
    } catch (error) {
      logger.warn(`系统用户邮箱文件夹创建失败: ${email}`, error);
      // 不抛出错误，允许用户创建成功
    }

    // 创建默认文件夹
    const folderTypes = ['inbox', 'sent', 'draft', 'trash'];
    for (const type of folderTypes) {
      await prisma.folder.create({
        data: {
          userId: user.id,
          name: getFolderName(type),
          type,
        },
      });
    }

    const response: ApiResponse = {
      success: true,
      message: '系统用户创建成功',
      data: {
        ...user,
        userType,
        description: description || getSystemUserDescription(userType),
      },
    };

    res.status(201).json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('创建系统用户失败:', error);
    throw new AppError('创建系统用户失败', 500);
  }
};

// 更新系统用户
export const updateSystemUser = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { displayName, description }: Partial<SystemUserData> = req.body;

  try {
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) },
    });

    if (!user) {
      throw new AppError('系统用户不存在', 404);
    }

    // 不允许修改admin用户的基本信息
    if (user.role === 'admin' && user.email === '<EMAIL>') {
      throw new AppError('不能修改主管理员账户', 403);
    }

    const updatedUser = await prisma.user.update({
      where: { id: parseInt(id) },
      data: {
        displayName,
      },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        isActive: true,
        role: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const response: ApiResponse = {
      success: true,
      message: '系统用户更新成功',
      data: updatedUser,
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('更新系统用户失败:', error);
    throw new AppError('更新系统用户失败', 500);
  }
};

// 删除系统用户
export const deleteSystemUser = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  try {
    const user = await prisma.user.findUnique({
      where: { id: parseInt(id) },
      include: {
        emails: { select: { id: true } },
        folders: { select: { id: true } },
        contacts: { select: { id: true } },
        contactGroups: { select: { id: true } },
        labels: { select: { id: true } },
        emailAccounts: { select: { id: true } },
        templates: { select: { id: true } },
        rules: { select: { id: true } },
        sessions: { select: { id: true } },
        emailTracking: { select: { id: true } },
        emailReceipts: { select: { id: true } },
        securityLogs: { select: { id: true } },
        suspiciousActivities: { select: { id: true } },
        securitySettings: { select: { id: true } }
      }
    });

    if (!user) {
      throw new AppError('系统用户不存在', 404);
    }

    // 不允许删除admin用户
    if (user.role === 'admin') {
      throw new AppError('不能删除管理员账户', 403);
    }

    logger.info(`开始删除用户 ${user.email} 及其所有关联数据`);

    // 统计要删除的数据
    const deletionStats = {
      emails: user.emails.length,
      folders: user.folders.length,
      contacts: user.contacts.length,
      contactGroups: user.contactGroups.length,
      labels: user.labels.length,
      emailAccounts: user.emailAccounts.length,
      templates: user.templates.length,
      rules: user.rules.length,
      sessions: user.sessions.length,
      emailTracking: user.emailTracking.length,
      emailReceipts: user.emailReceipts.length,
      securityLogs: user.securityLogs.length,
      suspiciousActivities: user.suspiciousActivities.length,
      securitySettings: user.securitySettings ? 1 : 0
    };

    logger.info(`用户 ${user.email} 关联数据统计:`, deletionStats);

    // 删除邮件服务物理文件夹
    try {
      await deleteUserMailboxFolders(user.email);
      logger.info(`用户 ${user.email} 的邮件服务物理文件夹删除成功`);
    } catch (mailboxError) {
      logger.warn(`删除用户 ${user.email} 邮件服务物理文件夹失败:`, mailboxError);
      // 不阻止用户删除，只记录警告
    }

    // 删除用户记录（Prisma的级联删除会自动处理关联数据）
    await prisma.user.delete({
      where: { id: parseInt(id) },
    });

    logger.info(`用户 ${user.email} 及其所有关联数据删除成功`);

    const response: ApiResponse = {
      success: true,
      message: '系统用户删除成功',
      data: {
        deletedUser: {
          id: user.id,
          email: user.email,
          username: user.username
        },
        deletionStats
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('删除系统用户失败:', error);
    throw new AppError('删除系统用户失败', 500);
  }
};

// 辅助函数：获取系统用户描述
function getSystemUserDescription(userType: string): string {
  const descriptions = {
    admin: '系统管理员账户，负责系统配置和用户管理',
    welcome: '新用户注册后发送欢迎邮件的系统账户',
    postManager: '处理邮件投递失败、退信等通知的系统账户',
    custom: '自定义系统用户账户',
  };
  return descriptions[userType as keyof typeof descriptions] || descriptions.custom;
}

// 辅助函数：获取文件夹名称
function getFolderName(type: string): string {
  const folderNames = {
    inbox: '收件箱',
    sent: '发件箱',
    draft: '草稿箱',
    trash: '垃圾箱',
  };
  return folderNames[type as keyof typeof folderNames] || type;
}
