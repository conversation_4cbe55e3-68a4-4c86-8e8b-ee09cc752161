import { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Switch,
  InputNumber,
  Button,
  message,
  Divider,
  Modal,
  Input,
  Alert,
  Space,
  Typography,
  List,
  Tag,
  Popconfirm,
  QRCode,
  Row,
  Col,
} from 'antd';
import {
  SafetyOutlined,
  MobileOutlined,
  HistoryOutlined,
  LockOutlined,
  EyeOutlined,
  DeleteOutlined,
  ReloadOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import * as securityApi from '../services/securityApi';
import type { SecuritySettings, UserSession, SecurityLog, TwoFactorSetup } from '../services/securityApi';
import PasswordChangeModal from '../components/PasswordChangeModal';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

const SecuritySettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<SecuritySettings | null>(null);
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [logs, setLogs] = useState<SecurityLog[]>([]);
  const [loading, setLoading] = useState(false);
  const [twoFactorModalVisible, setTwoFactorModalVisible] = useState(false);
  const [twoFactorSetup, setTwoFactorSetup] = useState<TwoFactorSetup | null>(null);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [passwordChangeModalVisible, setPasswordChangeModalVisible] = useState(false);
  const [backupCodesModalVisible, setBackupCodesModalVisible] = useState(false);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [form] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [twoFactorForm] = Form.useForm();

  // 加载数据
  const loadData = async () => {
    try {
      setLoading(true);
      const [settingsData, sessionsData, logsData] = await Promise.all([
        securityApi.getSecuritySettings(),
        securityApi.getActiveSessions(),
        securityApi.getSecurityLogs({ limit: 10 }),
      ]);
      setSettings(settingsData);
      setSessions(sessionsData);
      setLogs(logsData.logs || []);
      form.setFieldsValue(settingsData);
    } catch (error) {
      message.error('加载安全设置失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  // 更新安全设置
  const handleUpdateSettings = async (values: any) => {
    try {
      setLoading(true);
      const updatedSettings = await securityApi.updateSecuritySettings(values);
      setSettings(updatedSettings);
      message.success('安全设置更新成功');
    } catch (error) {
      message.error('更新失败');
    } finally {
      setLoading(false);
    }
  };

  // 启用两步验证
  const handleEnableTwoFactor = async (values: { password: string }) => {
    try {
      setLoading(true);
      const setup = await securityApi.enableTwoFactor(values.password);
      setTwoFactorSetup(setup);
      setTwoFactorModalVisible(true);
      passwordForm.resetFields();
      setPasswordModalVisible(false);
    } catch (error) {
      message.error('启用两步验证失败');
    } finally {
      setLoading(false);
    }
  };

  // 确认两步验证
  const handleConfirmTwoFactor = async (values: { code: string }) => {
    try {
      setLoading(true);
      await securityApi.confirmTwoFactor(values.code);
      message.success('两步验证启用成功');
      setTwoFactorModalVisible(false);
      twoFactorForm.resetFields();
      loadData();
    } catch (error) {
      message.error('验证码错误');
    } finally {
      setLoading(false);
    }
  };

  // 禁用两步验证
  const handleDisableTwoFactor = async (values: { password: string; code: string }) => {
    try {
      setLoading(true);
      await securityApi.disableTwoFactor(values.password, values.code);
      message.success('两步验证已禁用');
      loadData();
    } catch (error) {
      message.error('禁用失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成新的备用代码
  const handleGenerateBackupCodes = async (values: { password: string }) => {
    try {
      setLoading(true);
      const codes = await securityApi.generateNewBackupCodes(values.password);
      setBackupCodes(codes);
      setBackupCodesModalVisible(true);
      passwordForm.resetFields();
      setPasswordModalVisible(false);
    } catch (error) {
      message.error('生成备用代码失败');
    } finally {
      setLoading(false);
    }
  };

  // 终止会话
  const handleTerminateSession = async (sessionId: string) => {
    try {
      await securityApi.terminateSession(sessionId);
      message.success('会话已终止');
      loadData();
    } catch (error) {
      message.error('终止会话失败');
    }
  };

  // 终止所有其他会话
  const handleTerminateAllSessions = async () => {
    try {
      await securityApi.terminateAllOtherSessions();
      message.success('所有其他会话已终止');
      loadData();
    } catch (error) {
      message.error('操作失败');
    }
  };

  if (!settings) {
    return <div>加载中...</div>;
  }

  return (
    <div className="p-6">
      <Title level={2}>
        <SafetyOutlined className="mr-2" />
        安全设置
      </Title>

      <Row gutter={[16, 16]}>
        {/* 密码管理 */}
        <Col span={24}>
          <Card
            title={
              <span>
                <LockOutlined className="mr-2" />
                密码管理
              </span>
            }
          >
            <div className="mb-4">
              <Text>
                定期修改密码有助于保护您的账户安全。建议使用包含字母、数字和特殊字符的强密码。
              </Text>
            </div>

            <Alert
              message="密码安全提示"
              description={
                <div>
                  <p>• 密码长度至少为6位</p>
                  <p>• 建议包含大小写字母、数字和特殊字符</p>
                  <p>• 不要使用容易猜测的个人信息</p>
                  <p>• 定期更换密码以提高安全性</p>
                </div>
              }
              type="info"
              showIcon
              className="mb-4"
            />

            <Space>
              <Button
                type="primary"
                icon={<LockOutlined />}
                onClick={() => setPasswordChangeModalVisible(true)}
              >
                修改密码
              </Button>
              <Text type="secondary">
                修改后将同时更新Web登录密码和邮件服务器密码
              </Text>
            </Space>
          </Card>
        </Col>

        {/* 基本安全设置 */}
        <Col span={24}>
          <Card title="基本安全设置" loading={loading}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleUpdateSettings}
              initialValues={settings}
            >
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="密码过期天数"
                    name="passwordExpiryDays"
                    help="设置为0表示密码永不过期"
                  >
                    <InputNumber min={0} max={365} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="最大活跃会话数"
                    name="maxActiveSessions"
                  >
                    <InputNumber min={1} max={20} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    label="会话超时时间（小时）"
                    name="sessionTimeout"
                  >
                    <InputNumber min={1} max={168} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="loginNotification"
                valuePropName="checked"
              >
                <Switch />
                <span className="ml-2">登录通知</span>
              </Form.Item>

              <Form.Item
                name="suspiciousActivity"
                valuePropName="checked"
              >
                <Switch />
                <span className="ml-2">可疑活动检测</span>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        {/* 两步验证 */}
        <Col span={24}>
          <Card
            title={
              <span>
                <MobileOutlined className="mr-2" />
                两步验证
              </span>
            }
          >
            <div className="mb-4">
              <Text>
                两步验证为您的账户提供额外的安全保护。启用后，登录时需要输入手机验证器应用生成的验证码。
              </Text>
            </div>

            {settings.twoFactorEnabled ? (
              <div>
                <Alert
                  message="两步验证已启用"
                  description="您的账户已受到两步验证保护"
                  type="success"
                  showIcon
                  className="mb-4"
                />
                <Space>
                  <Button
                    onClick={() => setPasswordModalVisible(true)}
                    icon={<ReloadOutlined />}
                  >
                    重新生成备用代码
                  </Button>
                  <Popconfirm
                    title="确定要禁用两步验证吗？"
                    description="这将降低您账户的安全性"
                    onConfirm={() => setPasswordModalVisible(true)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button danger icon={<WarningOutlined />}>
                      禁用两步验证
                    </Button>
                  </Popconfirm>
                </Space>
              </div>
            ) : (
              <div>
                <Alert
                  message="两步验证未启用"
                  description="建议启用两步验证以提高账户安全性"
                  type="warning"
                  showIcon
                  className="mb-4"
                />
                <Button
                  type="primary"
                  onClick={() => setPasswordModalVisible(true)}
                  icon={<MobileOutlined />}
                >
                  启用两步验证
                </Button>
              </div>
            )}
          </Card>
        </Col>

        {/* 活跃会话 */}
        <Col span={24}>
          <Card
            title={
              <span>
                <EyeOutlined className="mr-2" />
                活跃会话
              </span>
            }
            extra={
              <Popconfirm
                title="确定要终止所有其他会话吗？"
                onConfirm={handleTerminateAllSessions}
                okText="确定"
                cancelText="取消"
              >
                <Button danger size="small">
                  终止所有其他会话
                </Button>
              </Popconfirm>
            }
          >
            <List
              dataSource={sessions}
              renderItem={(session) => {
                const { browser, os } = securityApi.parseUserAgent(session.userAgent);
                return (
                  <List.Item
                    actions={[
                      <Button
                        key="terminate"
                        type="text"
                        danger
                        size="small"
                        icon={<DeleteOutlined />}
                        onClick={() => handleTerminateSession(session.id)}
                      >
                        终止
                      </Button>,
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <div>
                          <Text strong>{browser} on {os}</Text>
                          <Tag color="blue" className="ml-2">当前会话</Tag>
                        </div>
                      }
                      description={
                        <div>
                          <div>IP地址: {securityApi.formatIPAddress(session.ipAddress || '')}</div>
                          <div>创建时间: {new Date(session.createdAt).toLocaleString()}</div>
                          <div>最后使用: {session.lastUsedAt ? new Date(session.lastUsedAt).toLocaleString() : '未知'}</div>
                        </div>
                      }
                    />
                  </List.Item>
                );
              }}
            />
          </Card>
        </Col>

        {/* 安全日志 */}
        <Col span={24}>
          <Card
            title={
              <span>
                <HistoryOutlined className="mr-2" />
                最近安全活动
              </span>
            }
          >
            <List
              dataSource={logs}
              renderItem={(log) => (
                <List.Item>
                  <List.Item.Meta
                    title={
                      <div>
                        <Text>{securityApi.getSecurityActionLabel(log.action)}</Text>
                        <Tag color={log.success ? 'green' : 'red'} className="ml-2">
                          {log.success ? '成功' : '失败'}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div>IP地址: {securityApi.formatIPAddress(log.ipAddress)}</div>
                        <div>时间: {new Date(log.createdAt).toLocaleString()}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 密码确认模态框 */}
      <Modal
        title="密码确认"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        footer={null}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={settings.twoFactorEnabled ? handleGenerateBackupCodes : handleEnableTwoFactor}
        >
          <Form.Item
            label="请输入当前密码"
            name="password"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                确认
              </Button>
              <Button onClick={() => setPasswordModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 两步验证设置模态框 */}
      <Modal
        title="设置两步验证"
        open={twoFactorModalVisible}
        onCancel={() => setTwoFactorModalVisible(false)}
        footer={null}
        width={600}
      >
        {twoFactorSetup && (
          <div>
            <Alert
              message="请使用验证器应用扫描二维码"
              description="推荐使用 Google Authenticator、Microsoft Authenticator 或其他兼容的验证器应用"
              type="info"
              showIcon
              className="mb-4"
            />

            <div className="text-center mb-4">
              <QRCode value={twoFactorSetup.qrCodeUrl} size={200} />
            </div>

            <div className="mb-4">
              <Text strong>手动输入密钥：</Text>
              <div className="bg-gray-100 p-2 rounded mt-2">
                <Text code>{twoFactorSetup.secret}</Text>
              </div>
            </div>

            <div className="mb-4">
              <Text strong>备用代码（请妥善保存）：</Text>
              <div className="bg-gray-100 p-2 rounded mt-2">
                {twoFactorSetup.backupCodes.map((code, index) => (
                  <div key={index}><Text code>{code}</Text></div>
                ))}
              </div>
            </div>

            <Form
              form={twoFactorForm}
              layout="vertical"
              onFinish={handleConfirmTwoFactor}
            >
              <Form.Item
                label="请输入验证器应用中的6位数字验证码"
                name="code"
                rules={[
                  { required: true, message: '请输入验证码' },
                  { pattern: /^\d{6}$/, message: '验证码必须是6位数字' },
                ]}
              >
                <Input placeholder="123456" maxLength={6} />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" loading={loading}>
                    确认启用
                  </Button>
                  <Button onClick={() => setTwoFactorModalVisible(false)}>
                    取消
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 备用代码模态框 */}
      <Modal
        title="新的备用代码"
        open={backupCodesModalVisible}
        onCancel={() => setBackupCodesModalVisible(false)}
        footer={
          <Button type="primary" onClick={() => setBackupCodesModalVisible(false)}>
            我已保存
          </Button>
        }
      >
        <Alert
          message="请妥善保存这些备用代码"
          description="当您无法使用验证器应用时，可以使用这些代码登录。每个代码只能使用一次。"
          type="warning"
          showIcon
          className="mb-4"
        />
        <div className="bg-gray-100 p-4 rounded">
          {backupCodes.map((code, index) => (
            <div key={index} className="mb-2">
              <Text code>{code}</Text>
            </div>
          ))}
        </div>
      </Modal>

      {/* 密码修改模态框 */}
      <PasswordChangeModal
        visible={passwordChangeModalVisible}
        onClose={() => setPasswordChangeModalVisible(false)}
        onSuccess={() => {
          message.success('密码修改成功！请重新登录。');
          // 可以在这里添加自动登出逻辑
        }}
      />
    </div>
  );
};

export default SecuritySettingsPage;
