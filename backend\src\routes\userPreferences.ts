import { Router } from 'express';
import { asyncHandler } from '../utils/asyncHandler';
import { authenticate } from '../middleware/auth';
import * as userPreferencesController from '../controllers/userPreferencesController';

const router = Router();

// 所有路由都需要认证
router.use(authenticate);

/**
 * 获取用户偏好设置
 * GET /api/user-preferences
 */
router.get('/',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userPreferencesController.getUserPreferences)
);

/**
 * 更新用户偏好设置
 * PUT /api/user-preferences
 * Body: 偏好设置对象（部分更新）
 */
router.put('/',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userPreferencesController.updateUserPreferences)
);

/**
 * 重置用户偏好设置为默认值
 * POST /api/user-preferences/reset
 */
router.post('/reset',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userPreferencesController.resetUserPreferences)
);

/**
 * 获取特定偏好设置项
 * GET /api/user-preferences/:key
 */
router.get('/:key',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userPreferencesController.getPreferenceSetting)
);

/**
 * 更新特定偏好设置项
 * PUT /api/user-preferences/:key
 * Body: { value: any }
 */
router.put('/:key',
  // @ts-ignore - Express 5.x 类型兼容性问题
  asyncHandler(userPreferencesController.updatePreferenceSetting)
);

export default router;
