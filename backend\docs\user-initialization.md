# 用户初始化指南

本文档介绍如何在邮箱系统中创建和管理管理员用户。

## 用户角色系统

### 角色类型

系统支持三种用户角色：

- **`admin`** - 管理员：拥有系统的完全访问权限
- **`moderator`** - 版主：拥有部分管理权限
- **`user`** - 普通用户：标准用户权限

### 角色权限

#### 管理员权限 (`admin`)
- 管理所有用户账户
- 访问系统用户管理界面
- 执行域内用户邮件同步
- 查看和管理系统模板
- 访问安全日志和监控
- 管理系统配置

#### 版主权限 (`moderator`)
- 管理指定范围内的用户
- 发送系统通知邮件
- 查看部分系统统计信息

#### 普通用户权限 (`user`)
- 管理个人邮件
- 创建和管理个人联系人
- 使用邮件模板
- 修改个人设置

## 初始化方法

### 方法一：使用种子数据（推荐用于开发环境）

种子数据会创建完整的测试环境，包括多个预设用户：

```bash
# 运行数据库迁移
npm run db:migrate

# 执行种子数据初始化
npm run db:seed
```

**创建的默认用户：**

| 角色 | 邮箱 | 用户名 | 密码 | 说明 |
|------|------|--------|------|------|
| admin | <EMAIL> | admin | admin123456 | 主管理员 |
| admin | <EMAIL> | postmaster | postmaster123456 | 邮件管理员 |
| moderator | <EMAIL> | welcome | welcome123456 | 欢迎邮件系统 |
| user | <EMAIL> | testuser | testuser123456 | 测试普通用户 |

### 方法二：交互式创建管理员（推荐用于生产环境）

使用交互式脚本创建第一个管理员用户：

```bash
npm run db:init-admin
```

脚本会引导您输入：
- 邮箱地址
- 用户名（3-20个字符，仅限字母数字下划线）
- 显示名称
- 密码（至少8位）

### 方法三：通过API创建（需要现有管理员权限）

使用系统用户管理API创建新的管理员：

```bash
POST /api/system-users
Authorization: Bearer <admin-token>
Content-Type: application/json

{
  "email": "<EMAIL>",
  "username": "newadmin",
  "displayName": "新管理员",
  "userType": "admin",
  "password": "securepassword123"
}
```

## 权限验证机制

### 中间件验证

系统使用以下中间件进行权限验证：

```typescript
// 基础认证
app.use('/api/protected', authenticate);

// 管理员权限验证
app.use('/api/admin', authenticate, requireAdmin);
```

### 控制器级别验证

在控制器中进行二次权限验证：

```typescript
export const adminOnlyFunction = async (req: AuthenticatedRequest, res: Response) => {
  // 验证管理员权限
  if (req.user!.role !== 'admin') {
    throw new AppError('权限不足，只有管理员可以执行此操作', 403);
  }
  // ... 业务逻辑
};
```

## 安全建议

### 生产环境安全措施

1. **立即修改默认密码**
   - 种子数据创建的用户使用默认密码
   - 生产环境部署后立即修改所有默认密码

2. **使用强密码策略**
   - 密码长度至少12位
   - 包含大小写字母、数字和特殊字符
   - 定期更换密码

3. **启用两步验证**
   ```bash
   POST /api/security/2fa/enable
   ```

4. **监控管理员活动**
   - 系统会自动记录所有管理员操作
   - 定期检查安全日志

5. **限制管理员数量**
   - 只创建必要的管理员账户
   - 定期审查管理员权限

### 账户管理最佳实践

1. **角色最小权限原则**
   - 根据实际需要分配角色
   - 避免给普通用户过高权限

2. **定期权限审查**
   - 定期检查用户角色分配
   - 及时回收不需要的权限

3. **账户生命周期管理**
   - 员工离职时及时禁用账户
   - 定期清理不活跃账户

## 故障排除

### 常见问题

**Q: 忘记管理员密码怎么办？**
A: 可以通过数据库直接重置密码：
```sql
UPDATE users SET password = '$2b$12$...' WHERE email = '<EMAIL>';
```

**Q: 如何将普通用户提升为管理员？**
A: 通过数据库或API修改用户角色：
```sql
UPDATE users SET role = 'admin' WHERE email = '<EMAIL>';
```

**Q: 系统中没有管理员用户怎么办？**
A: 使用 `npm run db:init-admin` 脚本创建新的管理员用户。

### 调试命令

```bash
# 查看所有管理员用户
npm run db:studio

# 重置数据库（谨慎使用）
npm run db:reset

# 查看用户角色分布
# 在数据库中执行：
SELECT role, COUNT(*) as count FROM users GROUP BY role;
```

## 相关文件

- `backend/prisma/seed.ts` - 种子数据脚本
- `backend/scripts/init-admin.js` - 管理员初始化脚本
- `backend/src/middleware/auth.ts` - 权限验证中间件
- `backend/src/controllers/systemUserController.ts` - 系统用户管理
- `backend/src/types/user.ts` - 用户类型定义
