#!/bin/bash

# 重置用户邮件密码
# 使用方法: ./reset-mail-password.sh [email] [new_password]

EMAIL=${1:-"<EMAIL>"}
NEW_PASSWORD=${2:-"HOUsc@0202"}

echo "🔧 重置用户邮件密码: $EMAIL"
echo "================================"

# 生成SHA512-CRYPT密码哈希
echo "🔐 生成密码哈希..."
if command -v doveadm >/dev/null 2>&1; then
    # 使用doveadm生成密码哈希
    PASSWORD_HASH=$(doveadm pw -s SHA512-CRYPT -p "$NEW_PASSWORD")
    echo "✅ 密码哈希生成成功"
    
    # 更新数据库
    echo "📝 更新数据库..."
    mysql -u mailuser -pHOUsc@0202 -D mailserver -e "
    UPDATE users 
    SET mail_password = '$PASSWORD_HASH'
    WHERE email = '$EMAIL';
    " 2>/dev/null
    
    if [ $? -eq 0 ]; then
        echo "✅ 密码更新成功"
        
        # 验证更新
        echo "🔍 验证更新结果..."
        UPDATED_COUNT=$(mysql -u mailuser -pHOUsc@0202 -D mailserver -se "
        SELECT COUNT(*) FROM users 
        WHERE email = '$EMAIL' AND mail_password = '$PASSWORD_HASH';
        " 2>/dev/null)
        
        if [ "$UPDATED_COUNT" = "1" ]; then
            echo "✅ 密码验证成功"
            echo ""
            echo "🎉 密码重置完成！"
            echo "新密码: $NEW_PASSWORD"
            echo ""
            echo "🧪 现在可以测试IMAP连接:"
            echo "IMAP_PASS='$NEW_PASSWORD' node backend/scripts/test-idle-support.js"
        else
            echo "❌ 密码验证失败"
        fi
    else
        echo "❌ 数据库更新失败"
    fi
    
else
    echo "❌ doveadm命令不可用，无法生成密码哈希"
    echo "请手动运行: doveadm pw -s SHA512-CRYPT -p '$NEW_PASSWORD'"
fi
