import { PrismaClient } from '@prisma/client';
import { createUserPassword } from '../src/utils/passwordManager';

const prisma = new PrismaClient();

// 创建用户密码的辅助函数
async function createPassword(plainPassword: string) {
  const result = await createUserPassword(plainPassword);
  if (result.error) {
    console.warn(`密码生成警告: ${result.error}`);
    // 如果 doveadm 不可用，降级使用 bcrypt
    const bcrypt = require('bcryptjs');
    return {
      webPassword: await bcrypt.hash(plainPassword, 12),
      mailPassword: null
    };
  }
  return result;
}

// 创建默认域名
async function createDefaultDomain() {
  return await prisma.virtualDomain.upsert({
    where: { name: 'blindedby.love' },
    update: {},
    create: {
      name: 'blindedby.love',
      active: 1,
    },
  });
}

// 创建系统用户
async function createSystemUsers(domainId: number) {
  console.log('创建系统用户...');

  const users: any[] = [];

  // 1. 创建主管理员
  const adminPassword = await createPassword('admin123456');
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: adminPassword.webPassword,
      mailPassword: adminPassword.mailPassword,
      displayName: '系统管理员',
      role: 'admin',
      isActive: true,
      emailVerified: true,
      domainId: domainId,
    },
  });
  users.push(adminUser);
  console.log('✓ 创建主管理员:', adminUser.email);

  // 2. 创建邮件管理员
  const postmasterPassword = await createPassword('postmaster123456');
  const postmasterUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'postmaster',
      password: postmasterPassword.webPassword,
      mailPassword: postmasterPassword.mailPassword,
      displayName: '邮件管理员',
      role: 'admin',
      isActive: true,
      emailVerified: true,
      domainId: domainId,
    },
  });
  users.push(postmasterUser);
  console.log('✓ 创建邮件管理员:', postmasterUser.email);

  // 3. 创建欢迎邮件账户
  const welcomePassword = await createPassword('welcome123456');
  const welcomeUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'welcome',
      password: welcomePassword.webPassword,
      mailPassword: welcomePassword.mailPassword,
      displayName: '欢迎邮件系统',
      role: 'moderator',
      isActive: true,
      emailVerified: true,
      domainId: domainId,
    },
  });
  users.push(welcomeUser);
  console.log('✓ 创建欢迎邮件账户:', welcomeUser.email);

  return users;
}

async function main() {
  console.log('🚀 开始数据库种子数据初始化...');

  // 1. 创建默认域名
  console.log('📧 创建默认域名...');
  const domain = await createDefaultDomain();
  console.log('✓ 域名创建完成:', domain.name);

  // 2. 创建系统用户
  const users = await createSystemUsers(domain.id);
  const adminUser = users[0]; // 主管理员用户

  // 3. 为每个用户创建默认文件夹
  console.log('📁 创建默认文件夹...');
  await createDefaultFoldersForUsers(users);

  // 4. 为管理员用户创建默认标签
  console.log('🏷️  创建默认标签...');
  await createDefaultLabels(adminUser.id);

  // 5. 创建默认联系人分组和联系人
  console.log('👥 创建默认联系人...');
  await createDefaultContacts(adminUser.id);

  // 6. 创建默认邮件模板
  console.log('📧 创建默认邮件模板...');
  await createDefaultTemplates(adminUser.id);

  console.log('✅ 数据库种子数据初始化完成！');
  console.log('\n📋 创建的账户信息:');
  console.log('==========================================');
  users.forEach(user => {
    console.log(`${user.role === 'admin' ? '👑' : user.role === 'moderator' ? '🛡️' : '👤'} ${user.displayName}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   用户名: ${user.username}`);
    console.log(`   角色: ${user.role}`);
    console.log(`   密码: ${getDefaultPassword(user.username)}`);
    console.log('------------------------------------------');
  });
  console.log('\n⚠️  请在生产环境中立即修改这些默认密码！');
}

// 创建默认文件夹的辅助函数
async function createDefaultFoldersForUsers(users: any[]) {
  const folders = [
    { name: '收件箱', type: 'inbox' },
    { name: '发件箱', type: 'sent' },
    { name: '草稿箱', type: 'draft' },
    { name: '垃圾箱', type: 'trash' },
  ];

  for (const user of users) {
    for (const folderData of folders) {
      await prisma.folder.upsert({
        where: {
          userId_name: {
            userId: user.id,
            name: folderData.name,
          },
        },
        update: {},
        create: {
          userId: user.id,
          name: folderData.name,
          type: folderData.type,
        },
      });
    }
    console.log(`✓ 为用户 ${user.email} 创建文件夹完成`);
  }
}

// 创建默认标签的辅助函数
async function createDefaultLabels(userId: number) {
  const labels = [
    { name: '重要', color: '#ff4d4f' },
    { name: '工作', color: '#1890ff' },
    { name: '个人', color: '#52c41a' },
    { name: '待办', color: '#faad14' },
    { name: '系统', color: '#722ed1' },
  ];

  for (const labelData of labels) {
    await prisma.label.upsert({
      where: {
        userId_name: {
          userId: userId,
          name: labelData.name,
        },
      },
      update: {},
      create: {
        userId: userId,
        name: labelData.name,
        color: labelData.color,
      },
    });
  }
  console.log('✓ 默认标签创建完成');
}

// 创建默认联系人的辅助函数
async function createDefaultContacts(userId: number) {
  // 创建联系人分组
  const contactGroups = [
    { name: '默认分组' },
    { name: '系统联系人' },
    { name: '重要联系人' },
  ];

  const createdGroups: any[] = [];
  for (const groupData of contactGroups) {
    const group = await prisma.contactGroup.upsert({
      where: {
        userId_name: {
          userId: userId,
          name: groupData.name,
        },
      },
      update: {},
      create: {
        userId: userId,
        name: groupData.name,
      },
    });
    createdGroups.push(group);
  }

  // 创建示例联系人
  const contacts = [
    {
      email: '<EMAIL>',
      name: '技术支持',
      groupId: createdGroups[1].id, // 系统联系人
    },
    {
      email: '<EMAIL>',
      name: '系统通知',
      groupId: createdGroups[1].id, // 系统联系人
    },
  ];

  for (const contactData of contacts) {
    await prisma.contact.upsert({
      where: {
        userId_email: {
          userId: userId,
          email: contactData.email,
        },
      },
      update: {},
      create: {
        userId: userId,
        email: contactData.email,
        name: contactData.name,
        groupId: contactData.groupId,
      },
    });
  }
  console.log('✓ 默认联系人创建完成');
}

// 创建默认邮件模板的辅助函数
async function createDefaultTemplates(userId: number) {
  const templates = [
    {
      name: '欢迎邮件',
      subject: '欢迎使用邮箱系统',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #1890ff;">欢迎使用邮箱系统！</h2>
          <p>亲爱的用户，</p>
          <p>欢迎使用我们的邮箱系统！您的账户已经成功创建。</p>
          <p>如有任何问题，请随时联系我们的技术支持团队。</p>
          <hr style="border: 1px solid #f0f0f0; margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            此邮件由系统自动发送，请勿回复。<br>
            技术支持: <EMAIL>
          </p>
        </div>
      `,
      isPublic: true,
    },
    {
      name: '密码重置',
      subject: '密码重置请求',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #ff4d4f;">密码重置请求</h2>
          <p>您好，</p>
          <p>我们收到了您的密码重置请求。请点击下面的链接重置您的密码：</p>
          <p style="margin: 20px 0;">
            <a href="{{resetLink}}" style="background: #1890ff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;">
              重置密码
            </a>
          </p>
          <p style="color: #666;">如果您没有请求重置密码，请忽略此邮件。</p>
          <p style="color: #666; font-size: 12px;">此链接将在24小时后失效。</p>
        </div>
      `,
      isPublic: true,
    },
    {
      name: '系统通知',
      subject: '系统通知',
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #52c41a;">系统通知</h2>
          <p>{{content}}</p>
          <hr style="border: 1px solid #f0f0f0; margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            此邮件由系统自动发送，请勿回复。
          </p>
        </div>
      `,
      isPublic: true,
    },
  ];

  for (let i = 0; i < templates.length; i++) {
    const templateData = templates[i];
    await prisma.emailTemplate.upsert({
      where: {
        id: i + 1, // 使用简单的ID策略
      },
      update: {},
      create: {
        userId: userId,
        name: templateData.name,
        subject: templateData.subject,
        content: templateData.content,
        isPublic: templateData.isPublic,
      },
    });
  }
  console.log('✓ 默认邮件模板创建完成');
}

// 获取默认密码的辅助函数
function getDefaultPassword(username: string): string {
  const passwordMap: Record<string, string> = {
    'admin': 'HOUsc@0202',
    'postmaster': 'HOUsc@0202',
    'welcome': 'HOUsc@0202',
    'testuser': 'testuser123456',
  };
  return passwordMap[username] || 'HOUsc@0202';
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
