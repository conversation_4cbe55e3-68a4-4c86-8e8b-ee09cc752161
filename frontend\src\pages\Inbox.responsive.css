.mail-root-layout {
  display: flex;
  height: 100vh;
  background: var(--color-gray-1);
}
.mail-nav-area {
  width: 25%;
  min-width: 220px;
  max-width: 320px;
  background: var(--color-gray-1);
  border-right: 1px solid var(--color-gray-2);
  display: flex;
  flex-direction: column;
}
.mail-list-area {
  width: 35%;
  min-width: 320px;
  max-width: 520px;
  background: var(--bg-primary);
  border-right: 1px solid var(--color-gray-2);
  display: flex;
  flex-direction: column;
}
.mail-preview-area {
  width: 40%;
  min-width: 320px;
  background: var(--bg-primary);
  display: flex;
  flex-direction: column;
}

/* 平板：双栏布局（隐藏预览区） */
@media (max-width: 1199px) and (min-width: 768px) {
  .mail-root-layout {
    flex-direction: row;
  }
  .mail-nav-area {
    width: 30%;
    min-width: 180px;
    max-width: 240px;
  }
  .mail-list-area {
    width: 70%;
    min-width: 320px;
    max-width: none;
  }
  .mail-preview-area {
    display: none;
  }
}

/* 手机：单栏布局（汉堡菜单切换） */
@media (max-width: 767px) {
  .mail-root-layout {
    flex-direction: column;
  }
  .mail-nav-area {
    width: 100%;
    min-width: 0;
    max-width: none;
    border-right: none;
    border-bottom: 1px solid var(--color-gray-2);
    display: none; /* 默认隐藏，点击菜单按钮显示 */
    position: absolute;
    z-index: 20;
    background: var(--color-gray-1);
    height: 100vh;
    left: 0;
    top: 0;
  }
  .mail-list-area {
    width: 100%;
    min-width: 0;
    max-width: none;
    border-right: none;
  }
  .mail-preview-area {
    width: 100%;
    min-width: 0;
    max-width: none;
    display: none;
  }
  .nav-menu-btn {
    display: inline-flex !important;
  }
}

/* 桌面下隐藏菜单按钮 */
@media (min-width: 768px) {
  .nav-menu-btn {
    display: none !important;
  }
} 