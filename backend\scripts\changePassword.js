#!/usr/bin/env node

/**
 * 修改用户密码脚本
 * 使用方法：
 * node scripts/changePassword.js --email <EMAIL> --password newpassword
 * node scripts/changePassword.js --id 123 --password newpassword
 * node scripts/changePassword.js --username johndoe --password newpassword
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');

const prisma = new PrismaClient();

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {};
  
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace('--', '');
    const value = args[i + 1];
    options[key] = value;
  }
  
  return options;
}

// 生成密码哈希
async function hashPassword(password) {
  const salt = await bcrypt.genSalt(12);
  return bcrypt.hash(password, salt);
}

// 生成邮件密码（用于IMAP/SMTP）
function generateMailPassword(password, salt = null) {
  if (!salt) {
    salt = crypto.randomBytes(16).toString('hex');
  }
  const hash = crypto.pbkdf2Sync(password, salt, 10000, 64, 'sha512');
  return {
    hash: hash.toString('hex'),
    salt: salt
  };
}

// 查找用户
async function findUser(options) {
  let whereClause = {};
  
  if (options.email) {
    whereClause.email = options.email;
  } else if (options.id) {
    whereClause.id = parseInt(options.id);
  } else if (options.username) {
    whereClause.username = options.username;
  } else {
    throw new Error('必须提供 email、id 或 username 参数');
  }
  
  const user = await prisma.user.findUnique({
    where: whereClause,
    select: {
      id: true,
      email: true,
      username: true,
      displayName: true,
      accountType: true,
      role: true,
      isActive: true
    }
  });
  
  if (!user) {
    throw new Error('用户不存在');
  }
  
  return user;
}

// 更新用户密码
async function updatePassword(userId, newPassword) {
  // 生成Web登录密码哈希
  const webPasswordHash = await hashPassword(newPassword);
  
  // 生成邮件密码哈希
  const { hash: mailPasswordHash, salt } = generateMailPassword(newPassword);
  
  // 更新数据库
  await prisma.user.update({
    where: { id: userId },
    data: {
      password: webPasswordHash,
      mailPassword: mailPasswordHash,
      updatedAt: new Date()
    }
  });
  
  console.log('✅ 密码更新成功');
  console.log(`   Web密码哈希: ${webPasswordHash.substring(0, 20)}...`);
  console.log(`   邮件密码哈希: ${mailPasswordHash.substring(0, 20)}...`);
}

// 显示帮助信息
function showHelp() {
  console.log(`
修改用户密码脚本

使用方法:
  node scripts/changePassword.js --email <EMAIL> --password newpassword
  node scripts/changePassword.js --id 123 --password newpassword  
  node scripts/changePassword.js --username johndoe --password newpassword

参数说明:
  --email     用户邮箱地址
  --id        用户ID
  --username  用户名
  --password  新密码 (必需)
  --help      显示帮助信息

示例:
  node scripts/changePassword.js --email <EMAIL> --password admin123456
  node scripts/changePassword.js --id 1 --password newpassword123
  node scripts/changePassword.js --username admin --password admin123456
`);
}

// 主函数
async function main() {
  try {
    const options = parseArgs();
    
    // 显示帮助
    if (options.help) {
      showHelp();
      return;
    }
    
    // 验证必需参数
    if (!options.password) {
      console.error('❌ 错误: 必须提供 --password 参数');
      showHelp();
      process.exit(1);
    }
    
    if (!options.email && !options.id && !options.username) {
      console.error('❌ 错误: 必须提供 --email、--id 或 --username 参数之一');
      showHelp();
      process.exit(1);
    }
    
    // 密码强度检查
    if (options.password.length < 6) {
      console.error('❌ 错误: 密码长度至少6位');
      process.exit(1);
    }
    
    console.log('🔍 查找用户...');
    const user = await findUser(options);
    
    console.log('📋 用户信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   用户名: ${user.username}`);
    console.log(`   显示名: ${user.displayName || '未设置'}`);
    console.log(`   账户类型: ${user.accountType}`);
    console.log(`   角色: ${user.role}`);
    console.log(`   状态: ${user.isActive ? '激活' : '禁用'}`);
    
    // 确认操作
    console.log('\n⚠️  即将修改用户密码，请确认操作...');
    console.log('按 Ctrl+C 取消，或按 Enter 继续...');
    
    // 等待用户确认
    await new Promise((resolve) => {
      process.stdin.once('data', resolve);
    });
    
    console.log('🔄 更新密码...');
    await updatePassword(user.id, options.password);
    
    console.log('\n✅ 操作完成！');
    console.log(`用户 ${user.email} 的密码已成功更新`);
    
  } catch (error) {
    console.error('❌ 错误:', error.message);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的异常:', error);
  process.exit(1);
});

process.on('SIGINT', () => {
  console.log('\n👋 操作已取消');
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main, findUser, updatePassword };
