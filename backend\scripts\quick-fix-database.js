const mysql = require('mysql2/promise');

async function quickFixDatabase() {
  let connection;
  
  try {
    console.log('🔧 快速修复数据库结构...');

    connection = await mysql.createConnection({
      host: 'mail.blindedby.love',
      user: 'mailuser',
      password: 'HOUsc@0202',
      database: 'mailserver'
    });

    console.log('✅ 数据库连接成功');

    // 检查并添加缺失的字段
    console.log('📝 添加缺失字段...');
    
    try {
      await connection.execute(`
        ALTER TABLE users 
        ADD COLUMN parent_user_id INT NULL,
        ADD COLUMN account_type VARCHAR(191) NOT NULL DEFAULT 'main',
        ADD COLUMN max_sub_accounts INT NOT NULL DEFAULT 5,
        ADD COLUMN sub_account_quota BIGINT NULL,
        ADD COLUMN is_sub_account_enabled BOOLEAN NOT NULL DEFAULT false
      `);
      console.log('✅ 字段添加成功');
    } catch (error) {
      if (error.code === 'ER_DUP_FIELDNAME') {
        console.log('ℹ️  字段已存在，跳过添加');
      } else {
        console.log('⚠️  添加字段时出现错误:', error.message);
      }
    }

    // 创建app_passwords表
    console.log('📝 创建app_passwords表...');
    try {
      await connection.execute(`
        CREATE TABLE IF NOT EXISTS app_passwords (
          id int NOT NULL AUTO_INCREMENT,
          user_id int NOT NULL,
          name varchar(191) NOT NULL,
          password varchar(191) NOT NULL,
          purpose varchar(191) NOT NULL DEFAULT 'imap',
          is_active boolean NOT NULL DEFAULT true,
          last_used_at datetime(3) NULL,
          expires_at datetime(3) NULL,
          created_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
          updated_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
          PRIMARY KEY (id),
          UNIQUE KEY app_passwords_user_id_name_key (user_id, name),
          KEY app_passwords_user_id_fkey (user_id),
          CONSTRAINT app_passwords_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
      `);
      console.log('✅ app_passwords表创建成功');
    } catch (error) {
      console.log('ℹ️  app_passwords表已存在或创建失败:', error.message);
    }

    console.log('🎉 数据库修复完成！');
    console.log('📋 下一步: 重启后端服务');

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

quickFixDatabase().catch(console.error);
