import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Descriptions,
  Badge,
  Space,
  Typography,
  Alert,
  Spin,
  message,
  Row,
  Col,
  Statistic,
  Timeline,
  Tag
} from 'antd';
import {
  ReloadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import {
  getImapConnectionStatus,
  testImapConnection,
  startImapMonitoring,
  stopImapMonitoring,
  type ImapConnectionStatus,
  type ImapTestResult
} from '../api/imapConnection';

const { Title, Text } = Typography;

const ImapConnectionTest: React.FC = () => {
  const [status, setStatus] = useState<ImapConnectionStatus | null>(null);
  const [testResult, setTestResult] = useState<ImapTestResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [testing, setTesting] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // 获取连接状态
  const fetchStatus = async () => {
    try {
      setLoading(true);
      const data = await getImapConnectionStatus();
      setStatus(data);
    } catch (error) {
      message.error('获取连接状态失败');
      console.error('获取连接状态失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 测试IMAP连接
  const testConnection = async () => {
    try {
      setTesting(true);
      const data = await testImapConnection();
      setTestResult(data);
      if (data.testResult.success) {
        message.success('IMAP连接测试成功');
      } else {
        message.error(`IMAP连接测试失败: ${data.testResult.error}`);
      }
    } catch (error) {
      message.error('IMAP连接测试失败');
      console.error('IMAP连接测试失败:', error);
    } finally {
      setTesting(false);
    }
  };

  // 启动监听
  const startMonitoring = async () => {
    try {
      setActionLoading('start');
      await startImapMonitoring();
      message.success('实时邮件监听已启动');
      await fetchStatus(); // 刷新状态
    } catch (error) {
      message.error('启动监听失败');
      console.error('启动监听失败:', error);
    } finally {
      setActionLoading(null);
    }
  };

  // 停止监听
  const stopMonitoring = async () => {
    try {
      setActionLoading('stop');
      await stopImapMonitoring();
      message.success('实时邮件监听已停止');
      await fetchStatus(); // 刷新状态
    } catch (error) {
      message.error('停止监听失败');
      console.error('停止监听失败:', error);
    } finally {
      setActionLoading(null);
    }
  };

  // 获取状态标记
  const getStatusBadge = (status: string, hasConnection: boolean) => {
    if (hasConnection) {
      return <Badge status="success" text="已连接" />;
    } else if (status === 'error') {
      return <Badge status="error" text="连接错误" />;
    } else {
      return <Badge status="default" text="未连接" />;
    }
  };

  // 获取应用专用密码状态
  const getAppPasswordStatus = (appPasswordStatus: string) => {
    switch (appPasswordStatus) {
      case 'available':
        return <Tag color="green">可用</Tag>;
      case 'none':
        return <Tag color="orange">未配置</Tag>;
      case 'error':
        return <Tag color="red">错误</Tag>;
      default:
        return <Tag color="default">未知</Tag>;
    }
  };

  useEffect(() => {
    fetchStatus();
  }, []);

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>IMAP连接测试</Title>
      <Text type="secondary">
        检查当前用户与IMAP服务器的连接状态和实时邮件监听功能
      </Text>

      <Row gutter={[16, 16]} style={{ marginTop: '24px' }}>
        {/* 连接状态概览 */}
        <Col span={24}>
          <Card
            title="连接状态概览"
            extra={
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchStatus}
                loading={loading}
              >
                刷新状态
              </Button>
            }
          >
            {loading ? (
              <Spin size="large" style={{ display: 'block', textAlign: 'center', padding: '40px' }} />
            ) : status ? (
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="在线状态"
                    value={status.realTimeService.isUserOnline ? '在线' : '离线'}
                    prefix={status.realTimeService.isUserOnline ? <WifiOutlined /> : <DisconnectOutlined />}
                    valueStyle={{ color: status.realTimeService.isUserOnline ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="IMAP连接"
                    value={status.realTimeService.hasActiveConnection ? '已连接' : '未连接'}
                    prefix={status.realTimeService.hasActiveConnection ? <CheckCircleOutlined /> : <CloseCircleOutlined />}
                    valueStyle={{ color: status.realTimeService.hasActiveConnection ? '#3f8600' : '#cf1322' }}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="在线用户数"
                    value={status.realTimeService.totalOnlineUsers}
                    prefix={<WifiOutlined />}
                  />
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Statistic
                    title="活跃连接数"
                    value={status.realTimeService.totalActiveConnections}
                    prefix={<CheckCircleOutlined />}
                  />
                </Col>
              </Row>
            ) : (
              <Alert message="无法获取连接状态" type="error" />
            )}
          </Card>
        </Col>

        {/* 用户信息 */}
        {status && (
          <Col xs={24} lg={12}>
            <Card title="用户信息">
              <Descriptions column={1} size="small">
                <Descriptions.Item label="用户ID">{status.user.id}</Descriptions.Item>
                <Descriptions.Item label="邮箱">{status.user.email}</Descriptions.Item>
                <Descriptions.Item label="用户名">{status.user.username}</Descriptions.Item>
                <Descriptions.Item label="应用专用密码">
                  {getAppPasswordStatus(status.appPassword.status)}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        )}

        {/* IMAP配置 */}
        {status && (
          <Col xs={24} lg={12}>
            <Card title="IMAP配置">
              {status.imapConfig.config ? (
                <Descriptions column={1} size="small">
                  <Descriptions.Item label="服务器">{status.imapConfig.config.host}</Descriptions.Item>
                  <Descriptions.Item label="端口">{status.imapConfig.config.port}</Descriptions.Item>
                  <Descriptions.Item label="TLS">{status.imapConfig.config.tls ? '启用' : '禁用'}</Descriptions.Item>
                  <Descriptions.Item label="用户名">{status.imapConfig.config.user}</Descriptions.Item>
                  <Descriptions.Item label="密码长度">{status.imapConfig.config.passwordLength} 字符</Descriptions.Item>
                </Descriptions>
              ) : (
                <Alert message="IMAP配置不可用" type="error" />
              )}
            </Card>
          </Col>
        )}

        {/* 邮箱账户信息 */}
        {status && status.emailAccount && (
          <Col span={24}>
            <Card title="邮箱账户信息">
              <Descriptions column={2} size="small">
                <Descriptions.Item label="账户ID">{status.emailAccount.id}</Descriptions.Item>
                <Descriptions.Item label="IMAP主机">{status.emailAccount.imapHost}</Descriptions.Item>
                <Descriptions.Item label="IMAP端口">{status.emailAccount.imapPort}</Descriptions.Item>
                <Descriptions.Item label="IMAP安全">{status.emailAccount.imapSecure ? 'TLS' : '无'}</Descriptions.Item>
                <Descriptions.Item label="IMAP用户名">{status.emailAccount.imapUsername}</Descriptions.Item>
                <Descriptions.Item label="连接状态">
                  {getStatusBadge(status.emailAccount.connectionStatus, status.realTimeService.hasActiveConnection)}
                </Descriptions.Item>
                <Descriptions.Item label="最后测试">
                  {status.emailAccount.lastConnectionTest 
                    ? new Date(status.emailAccount.lastConnectionTest).toLocaleString()
                    : '从未测试'
                  }
                </Descriptions.Item>
                <Descriptions.Item label="最后错误">
                  {status.emailAccount.lastError ? (
                    <Text type="danger">{status.emailAccount.lastError}</Text>
                  ) : (
                    <Text type="success">无错误</Text>
                  )}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>
        )}

        {/* 操作按钮 */}
        <Col span={24}>
          <Card title="操作">
            <Space size="middle">
              <Button
                type="primary"
                icon={<CheckCircleOutlined />}
                onClick={testConnection}
                loading={testing}
              >
                测试IMAP连接
              </Button>
              <Button
                icon={<PlayCircleOutlined />}
                onClick={startMonitoring}
                loading={actionLoading === 'start'}
                disabled={status?.realTimeService.isUserOnline}
              >
                启动监听
              </Button>
              <Button
                icon={<StopOutlined />}
                onClick={stopMonitoring}
                loading={actionLoading === 'stop'}
                disabled={!status?.realTimeService.isUserOnline}
                danger
              >
                停止监听
              </Button>
            </Space>
          </Card>
        </Col>

        {/* 测试结果 */}
        {testResult && (
          <Col span={24}>
            <Card title="连接测试结果">
              <Timeline>
                <Timeline.Item
                  dot={testResult.testResult.success ? <CheckCircleOutlined style={{ color: '#52c41a' }} /> : <CloseCircleOutlined style={{ color: '#ff4d4f' }} />}
                  color={testResult.testResult.success ? 'green' : 'red'}
                >
                  <div>
                    <Text strong>
                      {testResult.testResult.success ? '连接成功' : '连接失败'}
                    </Text>
                    <br />
                    <Text type="secondary">
                      耗时: {testResult.testResult.duration}ms
                    </Text>
                    {testResult.testResult.error && (
                      <>
                        <br />
                        <Text type="danger">错误: {testResult.testResult.error}</Text>
                      </>
                    )}
                    {testResult.testResult.boxInfo && (
                      <>
                        <br />
                        <Text>
                          邮箱: {testResult.testResult.boxInfo.name}, 
                          总邮件: {testResult.testResult.boxInfo.messages}, 
                          未读: {testResult.testResult.boxInfo.unseen}
                        </Text>
                      </>
                    )}
                  </div>
                </Timeline.Item>
              </Timeline>
            </Card>
          </Col>
        )}
      </Row>
    </div>
  );
};

export default ImapConnectionTest;
