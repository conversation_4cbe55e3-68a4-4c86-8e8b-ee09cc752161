import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { login, register, logout, refreshToken, getCurrentUser, changePassword } from '../../services/authApi';
import type { LoginData, RegisterData } from '../../types';

// Mock the api module
vi.mock('../../config/api', () => ({
  default: {
    post: vi.fn(),
    get: vi.fn(),
    put: vi.fn(),
  },
}));

import api from '../../config/api';

describe('AuthApi', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: vi.fn(),
        setItem: vi.fn(),
        removeItem: vi.fn(),
        clear: vi.fn(),
      },
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const loginData: LoginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockResponse = {
        data: {
          data: {
            user: {
              id: 1,
              email: '<EMAIL>',
              username: 'testuser',
              displayName: 'Test User',
            },
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
          },
        },
      };

      vi.mocked(api.post).mockResolvedValue(mockResponse);

      const result = await login(loginData);

      expect(api.post).toHaveBeenCalledWith('/auth/login', loginData);
      expect(result).toEqual(mockResponse.data.data);
      expect(result.user.email).toBe(loginData.email);
      expect(result.accessToken).toBe('mock-access-token');
      expect(result.refreshToken).toBe('mock-refresh-token');
    });

    it('should throw error on login failure', async () => {
      const loginData: LoginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const mockError = new Error('Login failed');
      vi.mocked(api.post).mockRejectedValue(mockError);

      await expect(login(loginData)).rejects.toThrow('Login failed');
      expect(api.post).toHaveBeenCalledWith('/auth/login', loginData);
    });
  });

  describe('register', () => {
    it('should register successfully with valid data', async () => {
      const registerData: RegisterData = {
        email: '<EMAIL>',
        username: 'newuser',
        password: 'password123',
        displayName: 'New User',
      };

      const mockResponse = {
        data: {
          data: {
            user: {
              id: 2,
              email: '<EMAIL>',
              username: 'newuser',
              displayName: 'New User',
            },
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
          },
        },
      };

      vi.mocked(api.post).mockResolvedValue(mockResponse);

      const result = await register(registerData);

      expect(api.post).toHaveBeenCalledWith('/auth/register', registerData);
      expect(result).toEqual(mockResponse.data.data);
      expect(result.user.email).toBe(registerData.email);
    });

    it('should throw error on registration failure', async () => {
      const registerData: RegisterData = {
        email: '<EMAIL>',
        username: 'existinguser',
        password: 'password123',
      };

      const mockError = new Error('User already exists');
      vi.mocked(api.post).mockRejectedValue(mockError);

      await expect(register(registerData)).rejects.toThrow('User already exists');
      expect(api.post).toHaveBeenCalledWith('/auth/register', registerData);
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      const mockRefreshToken = 'mock-refresh-token';
      vi.mocked(localStorage.getItem).mockReturnValue(mockRefreshToken);
      vi.mocked(api.post).mockResolvedValue({});

      await logout();

      expect(localStorage.getItem).toHaveBeenCalledWith('refreshToken');
      expect(api.post).toHaveBeenCalledWith('/auth/logout', { refreshToken: mockRefreshToken });
    });

    it('should handle logout when no refresh token exists', async () => {
      vi.mocked(localStorage.getItem).mockReturnValue(null);
      vi.mocked(api.post).mockResolvedValue({});

      await logout();

      expect(localStorage.getItem).toHaveBeenCalledWith('refreshToken');
      expect(api.post).toHaveBeenCalledWith('/auth/logout', { refreshToken: null });
    });
  });

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const oldRefreshToken = 'old-refresh-token';
      const mockResponse = {
        data: {
          data: {
            accessToken: 'new-access-token',
            refreshToken: 'new-refresh-token',
          },
        },
      };

      vi.mocked(api.post).mockResolvedValue(mockResponse);

      const result = await refreshToken(oldRefreshToken);

      expect(api.post).toHaveBeenCalledWith('/auth/refresh', {
        refreshToken: oldRefreshToken,
      });
      expect(result).toEqual(mockResponse.data.data);
      expect(result.accessToken).toBe('new-access-token');
      expect(result.refreshToken).toBe('new-refresh-token');
    });

    it('should throw error on refresh failure', async () => {
      const invalidRefreshToken = 'invalid-token';
      const mockError = new Error('Invalid refresh token');
      vi.mocked(api.post).mockRejectedValue(mockError);

      await expect(refreshToken(invalidRefreshToken)).rejects.toThrow('Invalid refresh token');
      expect(api.post).toHaveBeenCalledWith('/auth/refresh', {
        refreshToken: invalidRefreshToken,
      });
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user successfully', async () => {
      const mockResponse = {
        data: {
          data: {
            id: 1,
            email: '<EMAIL>',
            username: 'testuser',
            displayName: 'Test User',
            isActive: true,
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
          },
        },
      };

      vi.mocked(api.get).mockResolvedValue(mockResponse);

      const result = await getCurrentUser();

      expect(api.get).toHaveBeenCalledWith('/auth/me');
      expect(result).toEqual(mockResponse.data.data);
      expect(result.email).toBe('<EMAIL>');
    });

    it('should throw error when user not authenticated', async () => {
      const mockError = new Error('Unauthorized');
      vi.mocked(api.get).mockRejectedValue(mockError);

      await expect(getCurrentUser()).rejects.toThrow('Unauthorized');
      expect(api.get).toHaveBeenCalledWith('/auth/me');
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      const passwordData = {
        currentPassword: 'oldpassword',
        newPassword: 'newpassword123',
      };

      vi.mocked(api.put).mockResolvedValue({});

      await changePassword(passwordData);

      expect(api.put).toHaveBeenCalledWith('/auth/change-password', passwordData);
    });

    it('should throw error on password change failure', async () => {
      const passwordData = {
        currentPassword: 'wrongpassword',
        newPassword: 'newpassword123',
      };

      const mockError = new Error('Current password is incorrect');
      vi.mocked(api.put).mockRejectedValue(mockError);

      await expect(changePassword(passwordData)).rejects.toThrow('Current password is incorrect');
      expect(api.put).toHaveBeenCalledWith('/auth/change-password', passwordData);
    });
  });
});
