#!/usr/bin/env node

/**
 * 数据迁移脚本：从单一admin分发架构迁移到多用户独立IMAP架构
 * 
 * 使用方法:
 * node scripts/migrate-to-multi-account.js [options]
 * 
 * 选项:
 * --dry-run: 试运行模式，不实际修改数据
 * --batch-size: 批处理大小，默认100
 * --skip-existing: 跳过已存在的数据
 * --validate: 验证数据完整性
 */

const { PrismaClient } = require('@prisma/client');
const crypto = require('crypto');
const readline = require('readline');

const prisma = new PrismaClient();

// 解析命令行参数
const args = process.argv.slice(2);
const options = {
  dryRun: args.includes('--dry-run'),
  batchSize: parseInt(args.find(arg => arg.startsWith('--batch-size='))?.split('=')[1]) || 100,
  skipExisting: args.includes('--skip-existing'),
  validate: args.includes('--validate')
};

console.log('🚀 邮件系统架构迁移脚本');
console.log('================================');
console.log(`模式: ${options.dryRun ? '试运行' : '实际执行'}`);
console.log(`批处理大小: ${options.batchSize}`);
console.log(`跳过已存在: ${options.skipExisting}`);
console.log(`验证数据: ${options.validate}`);
console.log('================================\n');

// 生成SHA512-CRYPT格式密码
function generateMailPassword(plainPassword) {
  const salt = crypto.randomBytes(16).toString('hex').substring(0, 16);
  const hash = crypto.createHash('sha512');
  hash.update(plainPassword + salt);
  const hashedPassword = hash.digest('hex');
  return `$6$${salt}$${hashedPassword}`;
}

// 创建默认邮箱账户
async function createDefaultEmailAccount(userId, email, user) {
  const { encrypt } = require('../src/utils/encryption');
  
  return await prisma.emailAccount.create({
    data: {
      userId,
      name: '默认邮箱',
      email,
      displayName: user.displayName || user.username,
      imapHost: 'mail.blindedby.love',
      imapPort: 993,
      imapSecure: true,
      imapUsername: email,
      imapPassword: encrypt(user.mailPassword || user.password),
      smtpHost: 'mail.blindedby.love',
      smtpPort: 587,
      smtpSecure: false,
      smtpUsername: email,
      smtpPassword: encrypt(user.mailPassword || user.password),
      authType: 'password',
      isDefault: true,
      isActive: true,
      syncEnabled: true,
      syncInterval: 5,
      autoConfigured: true,
      provider: 'blindedby.love'
    }
  });
}

// 迁移用户数据
async function migrateUsers() {
  console.log('📧 开始迁移用户数据...');
  
  const users = await prisma.user.findMany({
    where: {
      OR: [
        { mailPassword: null },
        { domainId: null }
      ]
    },
    include: {
      emailAccounts: true
    }
  });

  console.log(`找到 ${users.length} 个用户需要迁移`);

  let migratedCount = 0;
  let errorCount = 0;
  const errors = [];

  for (const user of users) {
    try {
      if (options.dryRun) {
        console.log(`[DRY RUN] 将迁移用户: ${user.email}`);
        migratedCount++;
        continue;
      }

      // 生成邮件服务器密码
      let mailPassword = user.mailPassword;
      if (!mailPassword) {
        mailPassword = generateMailPassword(user.password);
      }

      // 设置默认域名ID
      let domainId = user.domainId;
      if (!domainId) {
        const defaultDomain = await prisma.virtualDomain.findFirst({
          where: { name: 'blindedby.love' }
        });
        domainId = defaultDomain?.id || 1;
      }

      // 更新用户数据
      await prisma.user.update({
        where: { id: user.id },
        data: {
          mailPassword,
          domainId,
          isMailActive: user.isActive,
          emailVerified: user.emailVerified
        }
      });

      // 确保用户有默认邮箱账户
      if (user.emailAccounts.length === 0) {
        await createDefaultEmailAccount(user.id, user.email, { ...user, mailPassword });
      }

      migratedCount++;
      console.log(`✓ 用户 ${user.email} 迁移完成`);

    } catch (error) {
      errorCount++;
      const errorMsg = `迁移用户 ${user.email} 失败: ${error.message}`;
      errors.push(errorMsg);
      console.error(`✗ ${errorMsg}`);
    }
  }

  return { migratedCount, errorCount, errors };
}

// 迁移邮件数据
async function migrateEmails() {
  console.log('📨 开始迁移邮件数据...');
  
  const emails = await prisma.email.findMany({
    where: {
      accountId: null
    },
    include: {
      user: {
        include: {
          emailAccounts: true
        }
      }
    },
    take: options.batchSize
  });

  console.log(`找到 ${emails.length} 封邮件需要迁移`);

  let migratedCount = 0;
  let errorCount = 0;
  const errors = [];

  for (const email of emails) {
    try {
      if (options.dryRun) {
        console.log(`[DRY RUN] 将迁移邮件: ${email.messageId}`);
        migratedCount++;
        continue;
      }

      // 查找用户的默认邮箱账户
      let defaultAccount = email.user.emailAccounts.find(acc => acc.isDefault);
      
      if (!defaultAccount && email.user.emailAccounts.length > 0) {
        defaultAccount = email.user.emailAccounts[0];
      }

      if (!defaultAccount) {
        // 创建默认邮箱账户
        defaultAccount = await createDefaultEmailAccount(email.user.id, email.user.email, email.user);
      }

      // 更新邮件关联的账户
      await prisma.email.update({
        where: { id: email.id },
        data: {
          accountId: defaultAccount.id
        }
      });

      migratedCount++;

    } catch (error) {
      errorCount++;
      const errorMsg = `迁移邮件 ${email.messageId} 失败: ${error.message}`;
      errors.push(errorMsg);
      console.error(`✗ ${errorMsg}`);
    }
  }

  return { migratedCount, errorCount, errors };
}

// 验证数据完整性
async function validateData() {
  console.log('🔍 验证数据完整性...');
  
  const issues = [];
  
  // 检查用户数据
  const usersWithoutMailPassword = await prisma.user.count({
    where: { mailPassword: null }
  });
  if (usersWithoutMailPassword > 0) {
    issues.push(`${usersWithoutMailPassword} 个用户缺少邮件服务器密码`);
  }

  const usersWithoutDomain = await prisma.user.count({
    where: { domainId: null }
  });
  if (usersWithoutDomain > 0) {
    issues.push(`${usersWithoutDomain} 个用户缺少域名关联`);
  }

  // 检查邮件数据
  const emailsWithoutAccount = await prisma.email.count({
    where: { accountId: null }
  });
  if (emailsWithoutAccount > 0) {
    issues.push(`${emailsWithoutAccount} 封邮件缺少账户关联`);
  }

  // 检查用户邮箱账户
  const usersWithoutEmailAccount = await prisma.user.count({
    where: {
      isActive: true,
      emailAccounts: {
        none: {}
      }
    }
  });
  if (usersWithoutEmailAccount > 0) {
    issues.push(`${usersWithoutEmailAccount} 个活跃用户没有邮箱账户`);
  }

  if (issues.length === 0) {
    console.log('✓ 数据完整性验证通过');
  } else {
    console.log('⚠️ 发现数据完整性问题:');
    issues.forEach(issue => console.log(`  - ${issue}`));
  }

  return issues;
}

// 确认执行
async function confirmExecution() {
  if (options.dryRun) {
    return true;
  }

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    rl.question('⚠️  这将修改数据库数据。确定要继续吗？(y/N): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes');
    });
  });
}

// 主函数
async function main() {
  try {
    // 确认执行
    const confirmed = await confirmExecution();
    if (!confirmed) {
      console.log('❌ 迁移已取消');
      return;
    }

    const startTime = Date.now();

    // 验证数据（如果启用）
    if (options.validate) {
      await validateData();
      console.log('');
    }

    // 迁移用户数据
    const userResult = await migrateUsers();
    console.log(`\n📊 用户迁移结果: 成功 ${userResult.migratedCount}, 失败 ${userResult.errorCount}`);

    // 迁移邮件数据
    const emailResult = await migrateEmails();
    console.log(`📊 邮件迁移结果: 成功 ${emailResult.migratedCount}, 失败 ${emailResult.errorCount}`);

    // 最终验证
    if (options.validate) {
      console.log('');
      await validateData();
    }

    const duration = Date.now() - startTime;
    console.log(`\n🎉 迁移完成! 总耗时: ${duration}ms`);

    // 显示错误摘要
    const allErrors = [...userResult.errors, ...emailResult.errors];
    if (allErrors.length > 0) {
      console.log('\n❌ 错误摘要:');
      allErrors.forEach(error => console.log(`  - ${error}`));
    }

  } catch (error) {
    console.error('💥 迁移失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行主函数
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  migrateUsers,
  migrateEmails,
  validateData
};
