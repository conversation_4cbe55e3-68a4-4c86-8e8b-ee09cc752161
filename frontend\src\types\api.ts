// API相关的类型定义

// 基础API响应类型
export interface ApiResponse<T = unknown> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  errors?: ValidationError[];
  meta?: Record<string, unknown>;
}

// 验证错误类型
export interface ValidationError {
  field: string;
  message: string;
  value?: unknown;
}

// 分页参数类型
export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 分页元数据类型
export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[];
  pagination: PaginationMeta;
}

// 搜索参数类型
export interface SearchParams {
  query?: string;
  filters?: Record<string, unknown>;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 搜索结果类型
export interface SearchResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
  facets?: Record<string, SearchFacet[]>;
}

// 搜索分面类型
export interface SearchFacet {
  value: string;
  count: number;
  selected?: boolean;
}

// 批量操作类型
export interface BulkOperation<T = unknown> {
  action: string;
  items: string[] | number[];
  params?: T;
}

// 批量操作结果类型
export interface BulkOperationResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors: Array<{
    item: string | number;
    error: string;
  }>;
  results?: unknown[];
}

// 文件上传相关类型
export interface UploadFile {
  uid: string;
  name: string;
  status: 'uploading' | 'done' | 'error' | 'removed';
  url?: string;
  thumbUrl?: string;
  size?: number;
  type?: string;
  percent?: number;
  originFileObj?: File;
  response?: ApiResponse;
  error?: Error;
}

export interface UploadOptions {
  maxSize?: number; // in bytes
  allowedTypes?: string[];
  multiple?: boolean;
  accept?: string;
  beforeUpload?: (file: File) => boolean | Promise<boolean>;
  onProgress?: (percent: number, file: UploadFile) => void;
  onSuccess?: (response: ApiResponse, file: UploadFile) => void;
  onError?: (error: Error, file: UploadFile) => void;
}

// 导入导出类型
export interface ImportOptions {
  format: 'csv' | 'json' | 'xlsx';
  mapping?: Record<string, string>;
  skipHeader?: boolean;
  delimiter?: string;
  encoding?: string;
  dryRun?: boolean;
}

export interface ImportResult {
  success: boolean;
  totalRows: number;
  processedRows: number;
  skippedRows: number;
  errorRows: number;
  errors: Array<{
    row: number;
    error: string;
    data?: Record<string, unknown>;
  }>;
  warnings: Array<{
    row: number;
    message: string;
  }>;
}

export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx' | 'pdf';
  fields?: string[];
  filters?: Record<string, unknown>;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  includeHeaders?: boolean;
  delimiter?: string;
  encoding?: string;
}

// HTTP错误类型
export interface HttpError extends Error {
  status?: number;
  statusText?: string;
  response?: {
    data?: ApiResponse;
    status: number;
    statusText: string;
    headers: Record<string, string>;
  };
}

// 请求配置类型
export interface RequestConfig {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
  cache?: boolean;
  cacheTTL?: number;
  headers?: Record<string, string>;
  params?: Record<string, unknown>;
  data?: unknown;
}

// API端点类型
export interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';
  url: string;
  config?: RequestConfig;
}

// API客户端配置类型
export interface ApiClientConfig {
  baseURL: string;
  timeout: number;
  headers: Record<string, string>;
  interceptors?: {
    request?: Array<(config: RequestConfig) => RequestConfig | Promise<RequestConfig>>;
    response?: Array<(response: ApiResponse) => ApiResponse | Promise<ApiResponse>>;
    error?: Array<(error: HttpError) => HttpError | Promise<HttpError>>;
  };
}

// WebSocket相关类型
export interface WebSocketConfig {
  url: string;
  protocols?: string[];
  reconnect?: boolean;
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  heartbeat?: boolean;
  heartbeatInterval?: number;
}

export interface WebSocketMessage<T = unknown> {
  type: string;
  data: T;
  timestamp: string;
  id?: string;
}

export interface WebSocketEvent {
  type: 'newEmail' | 'emailRead' | 'emailDeleted' | 'notification' | 'userOnline' | 'userOffline';
  data: WebSocketEventData;
}

export type WebSocketEventData = 
  | NewEmailEventData
  | EmailReadEventData
  | EmailDeletedEventData
  | NotificationEventData
  | UserStatusEventData;

export interface NewEmailEventData {
  id: string;
  subject: string;
  senderEmail: string;
  senderName?: string;
  receivedAt: string;
}

export interface EmailReadEventData {
  emailId: string;
  isRead: boolean;
}

export interface EmailDeletedEventData {
  emailId: string;
  folderId: number;
}

export interface NotificationEventData {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
}

export interface UserStatusEventData {
  userId: number;
  status: 'online' | 'offline' | 'away';
  lastSeen?: string;
}

// 缓存相关类型
export interface CacheEntry<T = unknown> {
  key: string;
  value: T;
  timestamp: number;
  ttl: number;
  tags?: string[];
}

export interface CacheOptions {
  ttl?: number; // Time to live in milliseconds
  tags?: string[];
  serialize?: boolean;
  compress?: boolean;
}

// 健康检查类型
export interface HealthCheck {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  uptime: number;
  version: string;
  environment: string;
  checks: Record<string, HealthCheckResult>;
}

export interface HealthCheckResult {
  status: 'pass' | 'fail' | 'warn';
  message?: string;
  duration?: number;
  data?: Record<string, unknown>;
}
