/**
 * 前端邮件解码工具
 * 用于解码RFC 2047编码的邮件头部信息
 */

/**
 * 解码Base64编码的文本
 */
function decodeBase64(encodedText: string): string {
  try {
    return decodeURIComponent(escape(atob(encodedText)));
  } catch (error) {
    console.warn('Base64解码失败:', error);
    return encodedText;
  }
}

/**
 * 解码Quoted-Printable编码的文本
 */
function decodeQuotedPrintable(encodedText: string): string {
  try {
    return encodedText
      .replace(/=([0-9A-F]{2})/g, (_, hex) => String.fromCharCode(parseInt(hex, 16)))
      .replace(/=\r?\n/g, '');
  } catch (error) {
    console.warn('Quoted-Printable解码失败:', error);
    return encodedText;
  }
}

/**
 * 解码RFC 2047编码的邮件头部信息
 * 处理类似 =?utf-8?B?5reh5b+Y4oCy6K+g6YeK552A5LiA5YiH44Kd?= 的编码
 */
export function decodeEmailHeader(encodedText: string): string {
  if (!encodedText || typeof encodedText !== 'string') {
    return encodedText || '';
  }

  try {
    // RFC 2047编码格式: =?charset?encoding?encoded-text?=
    const rfc2047Pattern = /=\?([^?]+)\?([BbQq])\?([^?]*)\?=/g;
    
    return encodedText.replace(rfc2047Pattern, (match, charset, encoding, encodedPart) => {
      try {
        let decoded = '';
        
        if (encoding.toUpperCase() === 'B') {
          // Base64编码
          decoded = decodeBase64(encodedPart);
        } else if (encoding.toUpperCase() === 'Q') {
          // Quoted-Printable编码
          decoded = decodeQuotedPrintable(encodedPart.replace(/_/g, ' '));
        } else {
          return match; // 不支持的编码类型
        }
        
        return decoded;
      } catch (error) {
        console.warn('RFC 2047解码失败:', { match, charset, encoding, encodedPart, error });
        return match; // 解码失败时返回原始文本
      }
    });
  } catch (error) {
    console.warn('邮件头部解码失败:', { encodedText, error });
    return encodedText; // 解码失败时返回原始文本
  }
}

/**
 * 解码邮件地址（可能包含显示名称）
 * 例如: =?utf-8?B?5reh5b+Y?= <<EMAIL>>
 */
export function decodeEmailAddress(encodedAddress: string): { name?: string; email?: string } {
  if (!encodedAddress || typeof encodedAddress !== 'string') {
    return { email: encodedAddress || '' };
  }

  try {
    // 先解码整个地址字符串
    const decodedAddress = decodeEmailHeader(encodedAddress);
    
    // 匹配邮件地址格式: "显示名称" <<EMAIL>> 或 <EMAIL>
    const emailMatch = decodedAddress.match(/<([^>]+)>/);
    const email = emailMatch ? emailMatch[1] : decodedAddress.trim();

    // 提取显示名称部分
    let name = '';
    if (emailMatch) {
      name = decodedAddress.replace(/<[^>]+>/, '').trim();
      // 移除可能的引号
      name = name.replace(/^["']|["']$/g, '');
    }

    return {
      name: name || undefined,
      email: email
    };
  } catch (error) {
    console.warn('邮件地址解码失败:', { encodedAddress, error });
    return { email: encodedAddress };
  }
}

/**
 * 检查文本是否包含RFC 2047编码
 */
export function isEncodedText(text: string): boolean {
  if (!text || typeof text !== 'string') {
    return false;
  }
  return /=\?[^?]+\?[BbQq]\?[^?]*\?=/.test(text);
}

/**
 * 安全解码邮件文本（如果需要的话）
 */
export function safeDecodeEmailText(text: string): string {
  if (!text || typeof text !== 'string') {
    return text || '';
  }
  
  if (isEncodedText(text)) {
    return decodeEmailHeader(text);
  }
  
  return text;
}
