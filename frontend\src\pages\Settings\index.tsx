import React, { useState, useEffect } from 'react';
import { Tabs, Typography, Card, Form, Input, Button, Switch, Select, Row, Col, message, Spin } from 'antd';
import {
  UserOutlined,
  MailOutlined,
  BellOutlined,
  EditOutlined,
  SettingOutlined,
  SyncOutlined,
  LockOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../store/authStore';
import { updateUserProfile } from '../../services/userApi';

const { Title } = Typography;
const { Option } = Select;

const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [activeTab, setActiveTab] = useState('profile');
  const { user, getCurrentUser, loading } = useAuthStore();
  const [profileForm] = Form.useForm();

  // 加载用户数据
  useEffect(() => {
    if (!user) {
      getCurrentUser();
    }
  }, [user, getCurrentUser]);

  // 当用户数据加载完成后，设置表单初始值
  useEffect(() => {
    if (user) {
      profileForm.setFieldsValue({
        username: user.username,
        displayName: user.displayName || '',
        email: user.email,
        role: user.role,
        emailVerified: user.emailVerified ? '已验证' : '未验证',
        createdAt: user.createdAt ? new Date(user.createdAt).toLocaleDateString() : '',
      });
    }
  }, [user, profileForm]);

  // 根据当前路径设置活跃的tab
  useEffect(() => {
    const path = location.pathname;
    if (path === '/settings') {
      const urlParams = new URLSearchParams(location.search);
      const tabFromUrl = urlParams.get('tab');
      const tabFromStorage = localStorage.getItem('settings-active-tab');

      if (tabFromUrl && ['profile', 'signature', 'email-preferences', 'email-sync', 'notifications', 'password'].includes(tabFromUrl)) {
        setActiveTab(tabFromUrl);
      } else if (tabFromStorage) {
        setActiveTab(tabFromStorage);
      } else {
        setActiveTab('profile');
      }
    }
  }, [location.pathname, location.search]);

  // Tab切换处理
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    localStorage.setItem('settings-active-tab', key);
    const newUrl = `/settings?tab=${key}`;
    navigate(newUrl, { replace: true });
  };

  // 处理个人资料保存
  const handleProfileSave = async (values: any) => {
    try {
      // 只发送可以修改的字段
      const updateData = {
        displayName: values.displayName,
      };

      const updatedUser = await updateUserProfile(updateData);

      // 更新store中的用户信息
      useAuthStore.setState({ user: updatedUser });

      message.success('个人资料更新成功');
    } catch (error: any) {
      console.error('保存个人资料失败:', error);
      message.error(error.response?.data?.message || '保存失败，请重试');
    }
  };

  // 个人资料内容 - 显示已有的用户信息
  const ProfileContent = () => {
    if (loading) {
      return (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>加载用户信息中...</div>
          </div>
        </Card>
      );
    }

    if (!user) {
      return (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <div>无法获取用户信息</div>
            <Button
              type="primary"
              onClick={getCurrentUser}
              style={{ marginTop: '16px' }}
            >
              重新加载
            </Button>
          </div>
        </Card>
      );
    }

    return (
      <Card>
        <Title level={3} style={{ marginBottom: '24px' }}>
          <UserOutlined style={{ marginRight: '8px' }} />
          个人资料
        </Title>

        <Form
          form={profileForm}
          layout="vertical"
          onFinish={handleProfileSave}
        >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="用户名" name="username">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="显示名称"
              name="displayName"
              rules={[
                { max: 50, message: '显示名称不能超过50个字符' }
              ]}
            >
              <Input placeholder="请输入显示名称" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="邮箱地址" name="email">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="邮箱验证状态" name="emailVerified">
              <Input disabled />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="用户角色" name="role">
              <Input disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="注册时间" name="createdAt">
              <Input disabled />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="账户状态">
              <Input value={user.isActive ? '活跃' : '已停用'} disabled />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="最后登录">
              <Input value={user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleString() : '从未登录'} disabled />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item>
          <Button type="primary" htmlType="submit">
            保存设置
          </Button>
        </Form.Item>
      </Form>
    </Card>
    );
  };

  // 邮件签名内容
  const SignatureContent = () => (
    <Card>
      <Title level={3} style={{ marginBottom: '24px' }}>
        <EditOutlined style={{ marginRight: '8px' }} />
        邮件签名
      </Title>
      <Form layout="vertical">
        <Form.Item label="签名名称">
          <Input placeholder="请输入签名名称" />
        </Form.Item>
        <Form.Item label="签名内容">
          <Input.TextArea rows={6} placeholder="请输入签名内容" />
        </Form.Item>
        <Form.Item>
          <Button type="primary">保存签名</Button>
        </Form.Item>
      </Form>
    </Card>
  );

  // 邮件偏好内容
  const EmailPreferencesContent = () => (
    <Card>
      <Title level={3} style={{ marginBottom: '24px' }}>
        <MailOutlined style={{ marginRight: '8px' }} />
        邮件偏好
      </Title>
      <Form layout="vertical">
        <Form.Item label="自动回复">
          <Switch />
        </Form.Item>
        <Form.Item label="邮件格式">
          <Select defaultValue="html">
            <Option value="html">HTML</Option>
            <Option value="text">纯文本</Option>
          </Select>
        </Form.Item>
        <Form.Item>
          <Button type="primary">保存设置</Button>
        </Form.Item>
      </Form>
    </Card>
  );

  // 邮件同步内容
  const EmailSyncContent = () => (
    <Card>
      <Title level={3} style={{ marginBottom: '24px' }}>
        <SyncOutlined style={{ marginRight: '8px' }} />
        邮件同步
      </Title>
      <Form layout="vertical">
        <Form.Item label="自动同步">
          <Switch defaultChecked />
        </Form.Item>
        <Form.Item label="同步频率">
          <Select defaultValue="15">
            <Option value="5">5分钟</Option>
            <Option value="15">15分钟</Option>
            <Option value="30">30分钟</Option>
            <Option value="60">1小时</Option>
          </Select>
        </Form.Item>
        <Form.Item>
          <Button type="primary">保存设置</Button>
        </Form.Item>
      </Form>
    </Card>
  );

  // 通知设置内容
  const NotificationsContent = () => (
    <Card>
      <Title level={3} style={{ marginBottom: '24px' }}>
        <BellOutlined style={{ marginRight: '8px' }} />
        通知设置
      </Title>
      <Form layout="vertical">
        <Form.Item label="邮件通知">
          <Switch defaultChecked />
        </Form.Item>
        <Form.Item label="桌面通知">
          <Switch />
        </Form.Item>
        <Form.Item>
          <Button type="primary">保存设置</Button>
        </Form.Item>
      </Form>
    </Card>
  );

  // 修改密码内容
  const PasswordContent = () => (
    <Card>
      <Title level={3} style={{ marginBottom: '24px' }}>
        <LockOutlined style={{ marginRight: '8px' }} />
        修改密码
      </Title>
      <Form layout="vertical">
        <Form.Item label="当前密码">
          <Input.Password placeholder="请输入当前密码" />
        </Form.Item>
        <Form.Item label="新密码">
          <Input.Password placeholder="请输入新密码" />
        </Form.Item>
        <Form.Item label="确认新密码">
          <Input.Password placeholder="请再次输入新密码" />
        </Form.Item>
        <Form.Item>
          <Button type="primary">修改密码</Button>
        </Form.Item>
      </Form>
    </Card>
  );

  const tabItems = [
    {
      key: 'profile',
      label: (
        <span>
          <UserOutlined />
          个人资料
        </span>
      ),
      children: <ProfileContent />,
    },
    {
      key: 'signature',
      label: (
        <span>
          <EditOutlined />
          邮件签名
        </span>
      ),
      children: <SignatureContent />,
    },
    {
      key: 'email-preferences',
      label: (
        <span>
          <MailOutlined />
          邮件偏好
        </span>
      ),
      children: <EmailPreferencesContent />,
    },
    {
      key: 'email-sync',
      label: (
        <span>
          <SyncOutlined />
          邮件同步
        </span>
      ),
      children: <EmailSyncContent />,
    },
    {
      key: 'notifications',
      label: (
        <span>
          <BellOutlined />
          通知设置
        </span>
      ),
      children: <NotificationsContent />,
    },
    {
      key: 'password',
      label: (
        <span>
          <LockOutlined />
          修改密码
        </span>
      ),
      children: <PasswordContent />,
    },
  ];

  return (
    <div style={{ padding: '24px', height: '100%' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center', gap: '12px' }}>
          <SettingOutlined />
          设置
        </Title>
      </div>
      
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        size="large"
        style={{
          height: 'calc(100vh - 200px)',
        }}
        tabBarStyle={{
          marginBottom: '24px',
          borderBottom: '1px solid #f0f0f0',
          paddingLeft: '0',
        }}
      />
    </div>
  );
};

export default SettingsPage;
