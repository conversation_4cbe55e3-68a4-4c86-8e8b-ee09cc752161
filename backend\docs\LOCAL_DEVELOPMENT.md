# 本地开发环境配置指南

## 概述

本文档描述如何在本地环境中配置和调试邮件Token认证系统，包括前后端服务的启动、Token认证的测试等。

## 开发环境架构

```
本地开发环境:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端开发服务  │    │   后端开发服务  │    │   远程邮件服务器│
│   localhost:5173│◄──►│   localhost:3000│◄──►│mail.blindedby.love│
│                 │    │                 │    │                 │
│ - Vue.js        │    │ - Express.js    │    │ - Dovecot       │
│ - Vite          │    │ - TypeScript    │    │ - Token认证     │
│ - Token管理UI   │    │ - Token服务     │    │ - IMAP/SMTP     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   本地数据库    │
                       │   localhost:3306│
                       │                 │
                       │ - 开发数据      │
                       │ - Token记录     │
                       └─────────────────┘
```

## 环境准备

### 1. 系统要求

- **Node.js**: 16.0+
- **npm**: 8.0+
- **MySQL**: 8.0+ (本地或远程)
- **Git**: 最新版本

### 2. 开发工具推荐

- **IDE**: VS Code / WebStorm
- **数据库工具**: MySQL Workbench / DBeaver
- **API测试**: Postman / Insomnia
- **终端**: Windows Terminal / iTerm2

## 项目设置

### 1. 克隆项目

```bash
# 克隆项目到本地
git clone <repository-url>
cd email-system
```

### 2. 安装依赖

```bash
# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../frontend
npm install
```

### 3. 环境配置

#### 后端环境配置

创建 `backend/.env.development` 文件：

```env
# 数据库配置
DATABASE_URL="mysql://mailuser:HOUsc@<EMAIL>:3306/mailserver"

# JWT配置
JWT_SECRET="your-development-jwt-secret"
JWT_EXPIRES_IN="7d"

# 邮件服务器配置
IMAP_HOST="mail.blindedby.love"
IMAP_PORT=993
IMAP_SECURE=true
SMTP_HOST="mail.blindedby.love"
SMTP_PORT=587
SMTP_SECURE=false

# Token认证配置
MAIL_TOKEN_ENABLED=true
MAIL_TOKEN_EXPIRY=86400
MAIL_TOKEN_CLEANUP_INTERVAL=3600
ENABLE_TOKEN_LOGGING=true

# 开发模式配置
NODE_ENV=development
PORT=3000
LOG_LEVEL=debug

# CORS配置
CORS_ORIGIN="http://localhost:5173"
```

#### 前端环境配置

创建 `frontend/.env.development` 文件：

```env
# API配置
VITE_API_BASE_URL=http://localhost:3000
VITE_API_TIMEOUT=10000

# 开发模式配置
VITE_NODE_ENV=development
VITE_LOG_LEVEL=debug

# 功能开关
VITE_ENABLE_TOKEN_AUTH=true
VITE_ENABLE_DEBUG_PANEL=true
```

## 数据库设置

### 1. 本地数据库（可选）

如果要使用本地数据库进行开发：

```bash
# 创建本地数据库
mysql -u root -p -e "CREATE DATABASE mailserver_dev;"

# 导入schema
mysql -u root -p mailserver_dev < backend/prisma/schema.sql

# 执行Token系统迁移
mysql -u root -p mailserver_dev < backend/deploy/01-database-migration.sql
```

### 2. 远程数据库

使用生产数据库的副本（推荐）：

```bash
# 更新.env.development中的数据库连接
DATABASE_URL="mysql://mailuser:HOUsc@<EMAIL>:3306/mailserver"
```

## 启动开发服务

### 1. 启动后端服务

```bash
cd backend

# 编译TypeScript
npm run build

# 启动开发服务（带热重载）
npm run dev

# 或者启动生产模式
npm start
```

后端服务将在 `http://localhost:3000` 启动

### 2. 启动前端服务

```bash
cd frontend

# 启动开发服务
npm run dev
```

前端服务将在 `http://localhost:5173` 启动

### 3. 验证服务

```bash
# 检查后端API
curl http://localhost:3000/api/health

# 检查前端页面
open http://localhost:5173
```

## Token认证调试

### 1. API测试脚本

创建 `backend/dev-tools/test-token-api.js`：

```javascript
const axios = require('axios');

const API_BASE = 'http://localhost:3000';
const TEST_USER = '<EMAIL>';

async function testTokenAPI() {
  try {
    console.log('🧪 测试Token API...');
    
    // 1. 用户登录获取JWT
    const loginResponse = await axios.post(`${API_BASE}/api/auth/login`, {
      email: TEST_USER,
      password: 'your-password'
    });
    
    const jwtToken = loginResponse.data.token;
    console.log('✅ 登录成功，获得JWT Token');
    
    // 2. 生成邮件Token
    const tokenResponse = await axios.post(
      `${API_BASE}/api/auth/generate-mail-token`,
      { purpose: 'imap' },
      { headers: { Authorization: `Bearer ${jwtToken}` } }
    );
    
    const mailToken = tokenResponse.data.data.token;
    console.log('✅ 邮件Token生成成功');
    console.log(`Token: ${mailToken.substring(0, 20)}...`);
    
    // 3. 验证邮件Token
    const verifyResponse = await axios.post(`${API_BASE}/api/auth/verify-mail-token`, {
      username: TEST_USER,
      token: mailToken,
      purpose: 'imap'
    });
    
    if (verifyResponse.data.valid) {
      console.log('✅ Token验证成功');
    } else {
      console.log('❌ Token验证失败');
    }
    
    console.log('🎉 所有测试通过');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

testTokenAPI();
```

### 2. IMAP连接测试

创建 `backend/dev-tools/test-imap-token.js`：

```javascript
const { createIMAPConnectionWithToken } = require('../dist/services/imapTokenService');

async function testIMAPToken() {
  try {
    console.log('🧪 测试IMAP Token连接...');
    
    const userEmail = '<EMAIL>';
    
    // 创建Token连接
    const imap = await createIMAPConnectionWithToken(userEmail);
    
    console.log('✅ IMAP Token连接成功');
    
    // 测试基本操作
    imap.openBox('INBOX', true, (err, box) => {
      if (err) {
        console.error('❌ 打开收件箱失败:', err);
        return;
      }
      
      console.log(`✅ 收件箱打开成功，共 ${box.messages.total} 封邮件`);
      imap.end();
    });
    
  } catch (error) {
    console.error('❌ IMAP Token连接失败:', error);
  }
}

testIMAPToken();
```

## 前端Token管理界面

### 1. Token管理组件

在前端添加Token管理界面，用于开发调试：

```vue
<!-- frontend/src/components/dev/TokenManager.vue -->
<template>
  <div class="token-manager">
    <h3>Token管理 (开发调试)</h3>
    
    <div class="token-actions">
      <button @click="generateToken('imap')" class="btn-primary">
        生成IMAP Token
      </button>
      <button @click="generateToken('smtp')" class="btn-primary">
        生成SMTP Token
      </button>
      <button @click="revokeAllTokens" class="btn-danger">
        撤销所有Token
      </button>
    </div>
    
    <div v-if="currentToken" class="token-display">
      <h4>当前Token:</h4>
      <textarea v-model="currentToken" readonly rows="3"></textarea>
      <button @click="copyToken" class="btn-secondary">复制</button>
      <button @click="testToken" class="btn-secondary">测试</button>
    </div>
    
    <div class="token-stats">
      <h4>Token统计:</h4>
      <p>总计: {{ tokenStats.totalTokens }}</p>
      <p>活跃: {{ tokenStats.activeTokens }}</p>
      <p>已撤销: {{ tokenStats.revokedTokens }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useAuthStore } from '@/stores/auth';
import api from '@/utils/api';

const authStore = useAuthStore();
const currentToken = ref('');
const tokenStats = ref({
  totalTokens: 0,
  activeTokens: 0,
  revokedTokens: 0
});

const generateToken = async (purpose) => {
  try {
    const response = await api.post('/api/auth/generate-mail-token', {
      purpose
    });
    
    currentToken.value = response.data.data.token;
    await loadTokenStats();
    
  } catch (error) {
    console.error('生成Token失败:', error);
  }
};

const testToken = async () => {
  if (!currentToken.value) return;
  
  try {
    const response = await api.post('/api/auth/verify-mail-token', {
      username: authStore.user.email,
      token: currentToken.value,
      purpose: 'imap'
    });
    
    if (response.data.valid) {
      alert('Token验证成功');
    } else {
      alert('Token验证失败');
    }
    
  } catch (error) {
    alert('Token测试失败: ' + error.message);
  }
};

const loadTokenStats = async () => {
  try {
    const response = await api.get('/api/auth/mail-token-stats');
    tokenStats.value = response.data.data;
  } catch (error) {
    console.error('加载Token统计失败:', error);
  }
};

onMounted(() => {
  loadTokenStats();
});
</script>
```

### 2. 开发调试面板

创建一个开发调试面板，集成各种调试工具：

```vue
<!-- frontend/src/components/dev/DebugPanel.vue -->
<template>
  <div v-if="isDevelopment" class="debug-panel">
    <div class="debug-toggle" @click="togglePanel">
      🛠️ 调试面板
    </div>
    
    <div v-if="panelOpen" class="debug-content">
      <TokenManager />
      <APITester />
      <LogViewer />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import TokenManager from './TokenManager.vue';
import APITester from './APITester.vue';
import LogViewer from './LogViewer.vue';

const panelOpen = ref(false);
const isDevelopment = computed(() => {
  return import.meta.env.VITE_NODE_ENV === 'development';
});

const togglePanel = () => {
  panelOpen.value = !panelOpen.value;
};
</script>

<style scoped>
.debug-panel {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
}

.debug-toggle {
  background: #007bff;
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.debug-content {
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 16px;
  margin-top: 8px;
  width: 400px;
  max-height: 600px;
  overflow-y: auto;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}
</style>
```

## 调试技巧

### 1. 日志调试

```javascript
// 后端日志配置
import logger from '../utils/logger';

// 在Token服务中添加详细日志
logger.debug('生成Token请求', { userEmail, purpose });
logger.info('Token验证成功', { userId, email });
logger.warn('Token验证失败', { error: error.message });
```

### 2. 断点调试

在VS Code中配置调试：

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/dist/server.js",
      "env": {
        "NODE_ENV": "development"
      },
      "console": "integratedTerminal",
      "sourceMaps": true
    }
  ]
}
```

### 3. 网络调试

使用浏览器开发者工具监控API请求：

1. 打开开发者工具 (F12)
2. 切换到Network标签
3. 过滤XHR/Fetch请求
4. 观察Token相关的API调用

## 常见开发问题

### 1. CORS问题

如果遇到跨域问题，检查后端CORS配置：

```javascript
// backend/src/app.ts
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}));
```

### 2. Token过期

开发时可以设置较长的Token有效期：

```env
# .env.development
MAIL_TOKEN_EXPIRY=86400  # 24小时
```

### 3. 数据库连接

确保数据库连接配置正确：

```bash
# 测试数据库连接
mysql -h mail.blindedby.love -u mailuser -p mailserver -e "SELECT 1"
```

## 部署到开发服务器

如果需要在开发服务器上测试：

```bash
# 构建项目
npm run build

# 使用PM2启动
pm2 start ecosystem.config.js --env development

# 查看日志
pm2 logs
```

---

通过以上配置，你就可以在本地环境中完整地开发和调试Token认证系统了。记住在开发过程中经常测试Token的生成、验证和IMAP连接功能。
