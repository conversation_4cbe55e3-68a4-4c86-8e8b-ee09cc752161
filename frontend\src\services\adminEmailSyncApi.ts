import api from '../config/api';
import type { ApiResponse } from '../types';

// 域内用户信息
export interface DomainUser {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  isActive: boolean;
  role: string;
  createdAt: string;
  lastLoginAt?: string;
}

// 用户邮件统计信息
export interface UserEmailStats {
  userEmail: string;
  totalEmails: number;
  inboxCount: number;
  sentCount: number;
  draftCount: number;
  trashCount: number;
  unreadCount: number;
  starredCount: number;
  lastSyncTime?: string;
  physicalFolderStats: {
    totalPhysicalEmails: number;
    inboxPhysical: number;
    sentPhysical: number;
    draftPhysical: number;
    trashPhysical: number;
  };
}

// 全量同步结果
export interface FullSyncResult {
  adminEmail: string;
  targetUserEmail: string;
  deleteResult: {
    deletedCount: number;
    deletedEmailsPreview: Array<{
      id: string;
      subject: string;
      messageId: string;
    }>;
  };
  syncResult: {
    syncedCount: number;
    skippedCount: number;
    errorCount: number;
    syncedEmailsPreview: Array<{
      uid: number;
      messageId: string;
      subject: string;
      status: string;
    }>;
  };
  summary: {
    totalOperations: number;
    success: boolean;
  };
  timestamp: string;
}

// 域内用户列表响应
export interface DomainUsersResponse {
  users: DomainUser[];
  totalUsers: number;
}

/**
 * 获取域内用户列表
 */
export const getDomainUsers = async (): Promise<DomainUser[]> => {
  const response = await api.get<ApiResponse<DomainUsersResponse>>('/admin/email-sync/users');
  return response.data.data!.users;
};

/**
 * 获取指定用户的邮件统计信息
 */
export const getUserEmailStats = async (userEmail: string): Promise<UserEmailStats> => {
  const response = await api.get<ApiResponse<UserEmailStats>>(`/admin/email-sync/users/${encodeURIComponent(userEmail)}/stats`);
  return response.data.data!;
};

/**
 * 管理员为指定用户执行全量邮件同步
 */
export const adminFullEmailSync = async (targetUserEmail: string): Promise<FullSyncResult> => {
  const response = await api.post<ApiResponse<FullSyncResult>>('/admin/email-sync/full-sync', {
    targetUserEmail
  });
  return response.data.data!;
};
