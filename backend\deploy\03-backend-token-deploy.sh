#!/bin/bash

# 后端Token服务部署脚本

set -e

echo "🚀 开始部署后端Token服务..."

# 配置变量
PROJECT_DIR="/var/www/email-system"
BACKEND_DIR="$PROJECT_DIR/backend"
NODE_USER="www-data"
PM2_APP_NAME="email-system-backend"

# 1. 检查环境
echo "🔍 检查部署环境..."

if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo "❌ npm未安装"
    exit 1
fi

if ! command -v pm2 &> /dev/null; then
    echo "❌ PM2未安装，正在安装..."
    npm install -g pm2
fi

echo "✅ 环境检查通过"

# 2. 停止现有服务
echo "⏹️  停止现有服务..."
pm2 stop $PM2_APP_NAME 2>/dev/null || echo "服务未运行"

# 3. 备份现有代码
if [ -d "$BACKEND_DIR" ]; then
    echo "📦 备份现有代码..."
    BACKUP_DIR="/tmp/backend-backup-$(date +%Y%m%d-%H%M%S)"
    cp -r "$BACKEND_DIR" "$BACKUP_DIR"
    echo "✅ 代码已备份到: $BACKUP_DIR"
fi

# 4. 更新代码
echo "📥 更新后端代码..."
cd "$BACKEND_DIR"

# 安装新依赖
npm install

# 编译TypeScript
npm run build

echo "✅ 代码更新完成"

# 5. 数据库迁移
echo "🗄️  执行数据库迁移..."
if [ -f "deploy/01-database-migration.sql" ]; then
    mysql -u root -p mailserver < deploy/01-database-migration.sql
    echo "✅ 数据库迁移完成"
else
    echo "⚠️  数据库迁移文件不存在，请手动执行"
fi

# 6. 更新环境配置
echo "⚙️  更新环境配置..."

# 添加Token相关环境变量
if ! grep -q "MAIL_TOKEN_ENABLED" .env.production; then
    cat >> .env.production << 'EOF'

# 邮件Token认证配置
MAIL_TOKEN_ENABLED=true
MAIL_TOKEN_EXPIRY=86400
MAIL_TOKEN_CLEANUP_INTERVAL=3600
ENABLE_TOKEN_LOGGING=true
EOF
    echo "✅ 环境配置已更新"
fi

# 7. 创建Token测试脚本
cat > "$BACKEND_DIR/test-token-auth.sh" << 'EOF'
#!/bin/bash

# Token认证测试脚本

API_BASE="http://localhost:3000"
TEST_USER="<EMAIL>"

echo "🧪 测试Token认证功能..."

# 1. 生成Token
echo "1️⃣ 生成IMAP Token..."
TOKEN_RESPONSE=$(curl -s -X POST "$API_BASE/api/auth/generate-mail-token" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"purpose":"imap"}')

if echo "$TOKEN_RESPONSE" | jq -e '.success == true' >/dev/null; then
    TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r '.data.token')
    echo "✅ Token生成成功"
    echo "Token: ${TOKEN:0:20}..."
else
    echo "❌ Token生成失败"
    echo "$TOKEN_RESPONSE"
    exit 1
fi

# 2. 验证Token
echo "2️⃣ 验证Token..."
VERIFY_RESPONSE=$(curl -s -X POST "$API_BASE/api/auth/verify-mail-token" \
  -H "Content-Type: application/json" \
  -d "{\"username\":\"$TEST_USER\",\"token\":\"$TOKEN\",\"purpose\":\"imap\"}")

if echo "$VERIFY_RESPONSE" | jq -e '.valid == true' >/dev/null; then
    echo "✅ Token验证成功"
else
    echo "❌ Token验证失败"
    echo "$VERIFY_RESPONSE"
    exit 1
fi

# 3. 测试IMAP连接（模拟）
echo "3️⃣ 测试IMAP连接..."
echo "用户名: $TEST_USER"
echo "Token: ${TOKEN:0:20}..."
echo "✅ 所有测试通过"
EOF

chmod +x "$BACKEND_DIR/test-token-auth.sh"

# 8. 更新PM2配置
cat > "$BACKEND_DIR/ecosystem.config.js" << 'EOF'
module.exports = {
  apps: [{
    name: 'email-system-backend',
    script: './dist/server.js',
    instances: 1,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024',
    
    // Token服务相关配置
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    
    // 健康检查
    health_check_url: 'http://localhost:3000/api/health',
    health_check_grace_period: 3000
  }]
};
EOF

# 9. 创建日志目录
mkdir -p "$BACKEND_DIR/logs"
chown -R $NODE_USER:$NODE_USER "$BACKEND_DIR/logs"

# 10. 启动服务
echo "🚀 启动后端服务..."
pm2 start ecosystem.config.js --env production

# 等待服务启动
sleep 5

# 11. 检查服务状态
if pm2 list | grep -q "$PM2_APP_NAME.*online"; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败"
    pm2 logs $PM2_APP_NAME --lines 20
    exit 1
fi

# 12. 保存PM2配置
pm2 save
pm2 startup

echo "🎉 后端Token服务部署完成！"
echo ""
echo "📋 部署信息:"
echo "  - 服务名称: $PM2_APP_NAME"
echo "  - 项目目录: $BACKEND_DIR"
echo "  - 日志目录: $BACKEND_DIR/logs"
echo "  - 测试脚本: $BACKEND_DIR/test-token-auth.sh"
echo ""
echo "🔧 管理命令:"
echo "  - 查看状态: pm2 status"
echo "  - 查看日志: pm2 logs $PM2_APP_NAME"
echo "  - 重启服务: pm2 restart $PM2_APP_NAME"
echo "  - 测试Token: cd $BACKEND_DIR && ./test-token-auth.sh"
