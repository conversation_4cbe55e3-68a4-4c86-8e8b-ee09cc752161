import { Response } from 'express';
import prisma from '../config/database';
import { io } from '../index';

// 获取标签列表
export const getLabels = async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = '1',
    limit = '50',
    sortBy = 'name',
    sortOrder = 'asc'
  } = req.query as any;

  const pageNum = parseInt(page as string) || 1;
  const limitNum = parseInt(limit as string) || 50;
  const skip = (pageNum - 1) * limitNum;
  const userId = req.user!.id;

  const [labels, total] = await Promise.all([
    prisma.label.findMany({
      where: { userId },
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        _count: {
          select: { emails: true },
        },
      },
    }),
    prisma.label.count({ where: { userId } }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '获取标签列表成功',
    data: {
      data: labels,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取标签详情
export const getLabelById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const label = await prisma.label.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
    include: {
      _count: {
        select: { emails: true },
      },
    },
  });

  if (!label) {
    throw new AppError('标签不存在', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: '获取标签详情成功',
    data: label,
  };

  res.json(response);
};

// 创建标签
export const createLabel = async (req: AuthenticatedRequest, res: Response) => {
  const { name, color } = req.body;
  const userId = req.user!.id;

  // 检查标签名称是否已存在
  const existingLabel = await prisma.label.findFirst({
    where: {
      userId,
      name,
    },
  });

  if (existingLabel) {
    throw new AppError('标签名称已存在', 409);
  }

  const label = await prisma.label.create({
    data: {
      userId,
      name,
      color,
    },
    include: {
      _count: {
        select: { emails: true },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '标签创建成功',
    data: label,
  };

  res.status(201).json(response);
};

// 更新标签
export const updateLabel = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { name, color } = req.body;
  const userId = req.user!.id;

  // 检查标签是否存在
  const existingLabel = await prisma.label.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingLabel) {
    throw new AppError('标签不存在', 404);
  }

  // 如果更新名称，检查是否与其他标签重名
  if (name && name !== existingLabel.name) {
    const duplicateLabel = await prisma.label.findFirst({
      where: {
        userId,
        name,
        id: { not: parseInt(id) },
      },
    });

    if (duplicateLabel) {
      throw new AppError('标签名称已存在', 409);
    }
  }

  const label = await prisma.label.update({
    where: { id: parseInt(id) },
    data: {
      ...(name && { name }),
      ...(color && { color }),
    },
    include: {
      _count: {
        select: { emails: true },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '标签更新成功',
    data: label,
  };

  res.json(response);
};

// 删除标签
export const deleteLabel = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  // 检查标签是否存在
  const existingLabel = await prisma.label.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingLabel) {
    throw new AppError('标签不存在', 404);
  }

  // 删除标签（会自动删除关联的邮件标签记录）
  await prisma.label.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '标签删除成功',
  };

  res.json(response);
};

// 为邮件添加标签
export const addLabelToEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId, labelId } = req.body;
  const userId = req.user!.id;

  // 验证邮件是否属于当前用户
  const email = await prisma.email.findFirst({
    where: {
      id: emailId,
      userId,
      isDeleted: false,
    },
  });

  if (!email) {
    throw new AppError('邮件不存在', 404);
  }

  // 验证标签是否属于当前用户
  const label = await prisma.label.findFirst({
    where: {
      id: labelId,
      userId,
    },
  });

  if (!label) {
    throw new AppError('标签不存在', 404);
  }

  // 检查是否已经添加过该标签
  const existingEmailLabel = await prisma.emailLabel.findFirst({
    where: {
      emailId,
      labelId,
    },
  });

  if (existingEmailLabel) {
    throw new AppError('邮件已经包含该标签', 409);
  }

  // 添加标签
  await prisma.emailLabel.create({
    data: {
      emailId,
      labelId,
    },
  });

  // 通过WebSocket通知邮件标签更新
  io.to(`user_${userId}`).emit('emailLabelAdded', {
    emailId,
    labelId,
    label: {
      id: label.id,
      name: label.name,
      color: label.color,
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '标签添加成功',
  };

  res.json(response);
};

// 从邮件移除标签
export const removeLabelFromEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { emailId, labelId } = req.body;
  const userId = req.user!.id;

  // 验证邮件是否属于当前用户
  const email = await prisma.email.findFirst({
    where: {
      id: emailId,
      userId,
      isDeleted: false,
    },
  });

  if (!email) {
    throw new AppError('邮件不存在', 404);
  }

  // 删除邮件标签关联
  const deletedEmailLabel = await prisma.emailLabel.deleteMany({
    where: {
      emailId,
      labelId,
    },
  });

  if (deletedEmailLabel.count === 0) {
    throw new AppError('邮件标签关联不存在', 404);
  }

  // 通过WebSocket通知邮件标签移除
  io.to(`user_${userId}`).emit('emailLabelRemoved', {
    emailId,
    labelId,
  });

  const response: ApiResponse = {
    success: true,
    message: '标签移除成功',
  };

  res.json(response);
};

// 批量为邮件添加标签
export const batchAddLabelsToEmails = async (req: AuthenticatedRequest, res: Response) => {
  const { emailIds, labelIds } = req.body;
  const userId = req.user!.id;

  // 验证邮件是否都属于当前用户
  const emails = await prisma.email.findMany({
    where: {
      id: { in: emailIds },
      userId,
      isDeleted: false,
    },
  });

  if (emails.length !== emailIds.length) {
    throw new AppError('部分邮件不存在或无权限', 400);
  }

  // 验证标签是否都属于当前用户
  const labels = await prisma.label.findMany({
    where: {
      id: { in: labelIds },
      userId,
    },
  });

  if (labels.length !== labelIds.length) {
    throw new AppError('部分标签不存在', 400);
  }

  // 批量创建邮件标签关联
  for (const emailId of emailIds) {
    for (const labelId of labelIds) {
      try {
        await prisma.emailLabel.create({
          data: { emailId, labelId },
        });
      } catch (error) {
        // 忽略重复记录错误
        if ((error as any)?.code !== 'P2002') {
          throw error;
        }
      }
    }
  }

  // 通过WebSocket通知批量标签更新
  io.to(`user_${userId}`).emit('emailLabelsBatchAdded', {
    emailIds,
    labels: labels.map(label => ({
      id: label.id,
      name: label.name,
      color: label.color,
    })),
  });

  const response: ApiResponse = {
    success: true,
    message: '批量添加标签成功',
  };

  res.json(response);
};
