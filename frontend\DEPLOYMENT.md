# 前端项目跨平台部署指南

本文档提供了从 Windows 开发环境部署到 Ubuntu 服务器的完整解决方案。

## 问题背景

在跨平台部署时，经常遇到以下错误：
```
[vite:css] Failed to load PostCSS config: Loading PostCSS Plugin failed: Failed to load native binding
```

这是由于某些 npm 包（如 `@tailwindcss/postcss`、`lightningcss`、`esbuild` 等）包含平台特定的原生绑定，在不同操作系统间不兼容。

## 解决方案

### 方案一：自动化脚本部署（推荐）

在 Ubuntu 服务器上运行：

```bash
# 进入项目目录
cd /srv/emailSystem/frontend

# 运行部署脚本
./scripts/deploy.sh
```

### 方案二：手动部署

```bash
# 1. 清理环境
rm -rf node_modules package-lock.json dist/
npm cache clean --force

# 2. 重新安装依赖
npm install

# 3. 构建项目
npm run build
```

### 方案三：Docker 部署（最稳定）

```bash
# 构建 Docker 镜像
docker build -f Dockerfile.build -t email-frontend .

# 运行容器
docker run -d -p 80:80 --name email-frontend email-frontend
```

## 配置文件说明

### 1. `.npmrc`
- 配置 npm 行为，确保跨平台兼容性
- 强制重新构建原生模块
- 设置合适的超时时间

### 2. `scripts/clean-platform-deps.js`
- 自动清理不兼容的平台特定包
- 保留当前平台所需的包

### 3. `scripts/deploy.sh`
- 完整的部署流程脚本
- 包含环境检查、依赖安装、构建等步骤

## 开发环境配置

### Windows 开发环境

```bash
# 安装依赖
npm install

# 开发模式
npm run dev

# 构建测试
npm run build:dev
```

### 新增的 npm 脚本

```json
{
  "scripts": {
    "postinstall": "npm run rebuild-native",
    "rebuild-native": "npm rebuild && npm run clean-platform-specific",
    "clean-platform-specific": "node scripts/clean-platform-deps.js",
    "deploy:setup": "npm run clean && npm ci --production=false && npm run build",
    "deploy:ubuntu": "rm -rf node_modules package-lock.json && npm install && npm run build"
  }
}
```

## 常见问题解决

### 1. Node.js 版本问题
确保使用 Node.js 18+ 版本：
```bash
# 检查版本
node --version

# Ubuntu 升级 Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### 2. 权限问题
```bash
# 修复 npm 权限
sudo chown -R $(whoami) ~/.npm
sudo chown -R $(whoami) /usr/local/lib/node_modules
```

### 3. 网络问题
```bash
# 使用国内镜像
npm config set registry https://registry.npmmirror.com/
```

### 4. 内存不足
```bash
# 增加 Node.js 内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

## CI/CD 集成

### GitHub Actions 示例

```yaml
name: Deploy Frontend
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - name: Deploy
        run: |
          cd frontend
          ./scripts/deploy.sh
```

## 监控和维护

### 构建文件检查
```bash
# 检查构建结果
ls -la dist/
du -sh dist/

# 检查关键文件
test -f dist/index.html && echo "✅ index.html 存在" || echo "❌ index.html 缺失"
```

### 性能优化
- 启用 gzip 压缩
- 设置静态资源缓存
- 使用 CDN 加速

## 故障排除

如果部署仍然失败，请按以下步骤排查：

1. 检查 Node.js 版本是否 >= 18
2. 清理所有缓存和依赖
3. 检查网络连接
4. 查看详细错误日志
5. 尝试使用 Docker 部署

## 联系支持

如遇到问题，请提供以下信息：
- 操作系统版本
- Node.js 版本
- 完整错误日志
- 部署步骤
