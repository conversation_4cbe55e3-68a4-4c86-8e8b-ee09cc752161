/**
 * 随机生成工具函数
 */

export interface RandomGenerationConfig {
  usernamePrefix?: string;
  usernameSuffix?: string;
  usernameLength?: number;
  emailDomain?: string;
  includeNumbers?: boolean;
  includeSpecialChars?: boolean;
}

/**
 * 生成随机用户名
 */
export const generateRandomUsername = (config: RandomGenerationConfig = {}): string => {
  const {
    usernamePrefix = 'user',
    usernameSuffix = '',
    usernameLength = 8,
    includeNumbers = true,
    includeSpecialChars = false
  } = config;

  let charset = 'abcdefghijklmnopqrstuvwxyz';
  if (includeNumbers) {
    charset += '**********';
  }
  if (includeSpecialChars) {
    charset += '_';
  }

  let randomPart = '';
  const randomLength = Math.max(3, usernameLength - usernamePrefix.length - usernameSuffix.length);
  
  for (let i = 0; i < randomLength; i++) {
    randomPart += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  return `${usernamePrefix}${randomPart}${usernameSuffix}`;
};

/**
 * 生成随机邮箱地址
 */
export const generateRandomEmail = (domain: string = 'example.com', config: RandomGenerationConfig = {}): string => {
  const {
    usernamePrefix = 'sub',
    includeNumbers = true,
    includeSpecialChars = false
  } = config;

  const username = generateRandomUsername({
    usernamePrefix,
    usernameLength: 10,
    includeNumbers,
    includeSpecialChars
  });

  return `${username}@${domain}`;
};

/**
 * 生成随机显示名称
 */
export const generateRandomDisplayName = (): string => {
  const adjectives = ['Smart', 'Quick', 'Bright', 'Swift', 'Cool', 'Nice', 'Good', 'Fast'];
  const nouns = ['User', 'Member', 'Person', 'Account', 'Client'];
  
  const adjective = adjectives[Math.floor(Math.random() * adjectives.length)];
  const noun = nouns[Math.floor(Math.random() * nouns.length)];
  const number = Math.floor(Math.random() * 1000);
  
  return `${adjective} ${noun} ${number}`;
};

/**
 * 生成随机密码
 */
export const generateRandomPassword = (length: number = 12): string => {
  const lowercase = 'abcdefghijklmnopqrstuvwxyz';
  const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
  const numbers = '**********';
  const symbols = '!@#$%^&*';
  
  const allChars = lowercase + uppercase + numbers + symbols;
  
  let password = '';
  
  // 确保包含每种类型的字符
  password += lowercase[Math.floor(Math.random() * lowercase.length)];
  password += uppercase[Math.floor(Math.random() * uppercase.length)];
  password += numbers[Math.floor(Math.random() * numbers.length)];
  password += symbols[Math.floor(Math.random() * symbols.length)];
  
  // 填充剩余长度
  for (let i = password.length; i < length; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)];
  }
  
  // 打乱字符顺序
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

/**
 * 默认随机生成配置
 */
export const defaultRandomConfig: RandomGenerationConfig = {
  usernamePrefix: 'sub',
  usernameSuffix: '',
  usernameLength: 8,
  includeNumbers: true,
  includeSpecialChars: false
};

/**
 * 验证生成的数据格式
 */
export const validateGeneratedData = (data: {
  email?: string;
  username?: string;
  displayName?: string;
  password?: string;
}): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];

  // 验证邮箱格式
  if (data.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      errors.push('邮箱格式不正确');
    }
  }

  // 验证用户名格式
  if (data.username) {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(data.username)) {
      errors.push('用户名格式不正确（3-20位字母、数字、下划线）');
    }
  }

  // 验证密码强度
  if (data.password) {
    if (data.password.length < 8) {
      errors.push('密码长度至少8位');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};
