generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  id                   Int                   @id @default(autoincrement())
  email                String                @unique(map: "email")
  username             String                @unique(map: "username")
  password             String
  displayName          String?               @map("display_name")
  avatarUrl            String?               @map("avatar_url")
  isActive             Boolean               @default(true) @map("is_active")
  domainId             Int?                  @map("domain_id")
  quota                BigInt                @default(**********)
  mailPassword         String?               @map("mail_password")
  isMailActive         Boolean               @default(true) @map("is_mail_active")
  emailVerified        Boolean               @default(false) @map("email_verified")
  role                 String                @default("user")

  // 子账户相关字段
  parentUserId         Int?                  @map("parent_user_id")
  accountType          String                @default("main") @map("account_type") // main, sub
  maxSubAccounts       Int                   @default(5) @map("max_sub_accounts")
  subAccountQuota      BigInt?               @map("sub_account_quota")
  isSubAccountEnabled  Boolean               @default(false) @map("is_sub_account_enabled")

  createdAt            DateTime?             @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt            DateTime?             @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  syncSettings         String?               @map("sync_settings") @db.Text
  contactGroups        ContactGroup[]
  contacts             Contact[]
  emailAccounts        EmailAccount[]
  emailReceipts        EmailReceipt[]
  rules                EmailRule[]
  templates            EmailTemplate[]
  emailTracking        EmailTracking[]
  emails               Email[]
  // subAccountEmails     Email[]               @relation("EmailParentUser")
  folders              Folder[]
  labels               Label[]
  securityLogs         SecurityLog[]
  suspiciousActivities SuspiciousActivity[]
  securitySettings     UserSecuritySettings?
  sessions             UserSession[]
  appPasswords         AppPassword[]
  domain               VirtualDomain?        @relation(fields: [domainId], references: [id], onUpdate: NoAction, map: "fk_users_domain")

  // 子账户关系
  parentUser           User?                 @relation("UserSubAccounts", fields: [parentUserId], references: [id], onDelete: Cascade)
  subAccounts          User[]                @relation("UserSubAccounts")

  // 子账户相关表
  subAccountPermissions SubAccountPermission[]
  subAccountUsage      SubAccountUsage[]
  parentActivities     SubAccountActivity[] @relation("ParentUserActivities")
  subActivities        SubAccountActivity[] @relation("SubUserActivities")
  sentInvitations      SubAccountInvitation[] @relation("ParentUserInvitations")
  receivedInvitations  SubAccountInvitation[] @relation("SubUserInvitation")

  @@index([domainId], map: "idx_domain_id")
  @@index([email], map: "idx_email")
  @@index([emailVerified], map: "idx_email_verified")
  @@index([isMailActive], map: "idx_is_mail_active")
  @@index([username], map: "idx_username")
  @@index([parentUserId], map: "idx_parent_user_id")
  @@index([accountType], map: "idx_account_type")
  @@index([isSubAccountEnabled], map: "idx_sub_account_enabled")
  @@map("users")
}

model VirtualDomain {
  id        Int            @id @default(autoincrement())
  name      String         @unique(map: "name")
  active    Int            @default(1)
  createdAt DateTime?      @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt DateTime?      @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  users     User[]
  aliases   VirtualAlias[]

  @@index([active], map: "idx_active")
  @@index([name], map: "idx_name")
  @@map("virtual_domains")
}

model VirtualAlias {
  id          Int           @id @default(autoincrement())
  domainId    Int           @map("domain_id")
  source      String
  destination String
  active      Int           @default(1)
  createdAt   DateTime?     @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt   DateTime?     @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  domain      VirtualDomain @relation(fields: [domainId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "virtual_aliases_ibfk_1")

  @@unique([domainId, source], map: "unique_domain_source")
  @@index([domainId, active], map: "idx_domain_active")
  @@index([source], map: "idx_source")
  @@map("virtual_aliases")
}

model Folder {
  id        Int       @id @default(autoincrement())
  userId    Int       @map("user_id")
  name      String
  type      String
  parentId  Int?      @map("parent_id")
  createdAt DateTime? @default(now()) @map("created_at") @db.Timestamp(0)
  emails    Email[]
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "folders_ibfk_1")
  parent    Folder?   @relation("FolderHierarchy", fields: [parentId], references: [id], onUpdate: NoAction, map: "folders_ibfk_2")
  children  Folder[]  @relation("FolderHierarchy")

  @@unique([userId, name], map: "unique_user_name")
  @@index([parentId], map: "idx_parent_id")
  @@index([type], map: "idx_type")
  @@index([userId], map: "idx_user_id")
  @@map("folders")
}

model Email {
  id            String          @id @default(cuid())
  messageId     String          @map("message_id")
  userId        Int             @map("user_id")
  accountId     Int?            @map("account_id")
  folderId      Int             @map("folder_id")
  subject       String?
  senderEmail   String          @map("sender_email")
  senderName    String?         @map("sender_name")
  recipients    String
  ccRecipients  String?         @map("cc_recipients")
  bccRecipients String?         @map("bcc_recipients")
  contentText   String?         @map("content_text") @db.LongText
  contentHtml   String?         @map("content_html") @db.LongText
  attachments   String?
  priority      String          @default("normal")
  isRead        Boolean         @default(false) @map("is_read")
  isStarred     Boolean         @default(false) @map("is_starred")
  isDeleted     Boolean         @default(false) @map("is_deleted")
  receivedAt    DateTime?       @map("received_at") @db.Timestamp(0)
  sentAt        DateTime?       @map("sent_at") @db.Timestamp(0)

  // IMAP相关字段
  imapUid       Int?            @map("imap_uid")
  imapFlags     String?         @map("imap_flags") @db.Text

  // 子账户相关字段 (暂时注释掉)
  // parentUserId  Int?            @map("parent_user_id")
  // isSharedWithParent Boolean    @default(false) @map("is_shared_with_parent")

  createdAt     DateTime?       @default(now()) @map("created_at") @db.Timestamp(0)
  labels        EmailLabel[]
  receipts      EmailReceipt[]
  tracking      EmailTracking[]
  user          User            @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "emails_ibfk_1")
  folder        Folder          @relation(fields: [folderId], references: [id], onUpdate: NoAction, map: "emails_ibfk_2")
  account       EmailAccount?   @relation(fields: [accountId], references: [id], onUpdate: NoAction, map: "fk_emails_account")
  // parentUser    User?           @relation("EmailParentUser", fields: [parentUserId], references: [id], onDelete: SetNull)

  @@unique([messageId, userId], map: "unique_message_user")
  @@unique([messageId, accountId], map: "unique_message_account")
  @@index([folderId], map: "folder_id")
  @@index([accountId], map: "idx_account_id")
  @@index([messageId], map: "idx_message_id")
  @@index([receivedAt], map: "idx_received_at")
  @@index([senderEmail], map: "idx_sender_email")
  @@index([userId, folderId], map: "idx_user_folder")
  @@index([imapUid, accountId], map: "idx_imap_uid_account")
  // @@index([parentUserId], map: "idx_parent_user_id")
  // @@index([isSharedWithParent], map: "idx_shared_with_parent")
  @@map("emails")
}

model Label {
  id        Int          @id @default(autoincrement())
  userId    Int          @map("user_id")
  name      String
  color     String
  createdAt DateTime?    @default(now()) @map("created_at") @db.Timestamp(0)
  emails    EmailLabel[]
  user      User         @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "labels_ibfk_1")

  @@unique([userId, name], map: "unique_user_name")
  @@index([userId], map: "idx_user_id")
  @@map("labels")
}

model EmailLabel {
  emailId String @map("email_id")
  labelId Int    @map("label_id")
  email   Email  @relation(fields: [emailId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "email_labels_ibfk_1")
  label   Label  @relation(fields: [labelId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "email_labels_ibfk_2")

  @@id([emailId, labelId])
  @@index([emailId], map: "idx_email_id")
  @@index([labelId], map: "idx_label_id")
  @@map("email_labels")
}

model Contact {
  id        Int           @id @default(autoincrement())
  userId    Int           @map("user_id")
  email     String
  name      String?
  phone     String?
  company   String?
  notes     String?       @db.Text
  avatarUrl String?       @map("avatar_url")
  groupId   Int?          @map("group_id")
  createdAt DateTime?     @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt DateTime?     @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  user      User          @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "contacts_ibfk_1")
  group     ContactGroup? @relation(fields: [groupId], references: [id], onUpdate: NoAction, map: "fk_contacts_group")

  @@unique([userId, email], map: "unique_user_email")
  @@index([email], map: "idx_email")
  @@index([groupId], map: "idx_group_id")
  @@index([userId], map: "idx_user_id")
  @@map("contacts")
}

model ContactGroup {
  id        Int       @id @default(autoincrement())
  userId    Int       @map("user_id")
  name      String
  createdAt DateTime? @default(now()) @map("created_at") @db.Timestamp(0)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "contact_groups_ibfk_1")
  contacts  Contact[]

  @@unique([userId, name], map: "unique_user_name")
  @@index([userId], map: "idx_user_id")
  @@map("contact_groups")
}

model EmailTemplate {
  id        Int       @id @default(autoincrement())
  userId    Int?      @map("user_id")
  name      String
  subject   String
  content   String    @db.Text
  isPublic  Boolean   @default(false) @map("is_public")
  createdAt DateTime? @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  isActive  Boolean   @default(true) @map("is_active")
  user      User?     @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "email_templates_ibfk_1")

  @@index([name], map: "idx_name")
  @@index([userId], map: "idx_user_id")
  @@map("email_templates")
}

model EmailRule {
  id           Int       @id @default(autoincrement())
  userId       Int       @map("user_id")
  name         String
  conditions   String    @db.Text
  actions      String    @db.Text
  isActive     Boolean   @default(true) @map("is_active")
  priority     Int       @default(0)
  createdAt    DateTime? @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt    DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  isSystemRule Boolean?  @default(false) @map("is_system_rule")
  templateId   String?   @map("template_id") @db.VarChar(50)
  description  String?   @db.Text
  user         User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "email_rules_ibfk_1")

  @@index([priority], map: "idx_priority")
  @@index([userId], map: "idx_user_id")
  @@map("email_rules")
}

model EmailRuleTemplate {
  id              String    @id @db.VarChar(50)
  name            String    @db.VarChar(255)
  description     String?   @db.Text
  conditions      String    @db.Text
  actions         String    @db.Text
  defaultPriority Int?      @default(0)
  isActive        Boolean?  @default(true)
  category        String?   @default("general") @db.VarChar(50)
  createdAt       DateTime? @default(now()) @db.DateTime(0)
  updatedAt       DateTime? @default(now()) @db.DateTime(0)

  @@map("email_rule_templates")
}

model EmailAccount {
  id               Int       @id @default(autoincrement())
  userId           Int       @map("user_id")
  name             String
  email            String
  displayName      String?   @map("display_name")
  imapHost         String    @map("imap_host")
  imapPort         Int       @map("imap_port")
  imapSecure       Boolean   @map("imap_secure")
  imapUsername     String    @map("imap_username")
  imapPassword     String    @map("imap_password") @db.Text
  smtpHost         String    @map("smtp_host")
  smtpPort         Int       @map("smtp_port")
  smtpSecure       Boolean   @map("smtp_secure")
  smtpUsername     String    @map("smtp_username")
  smtpPassword     String    @map("smtp_password") @db.Text

  // OAuth2 支持
  authType         String    @default("password") @map("auth_type") // password, oauth2
  oauthProvider    String?   @map("oauth_provider") // gmail, outlook, yahoo
  oauthAccessToken String?   @map("oauth_access_token") @db.Text
  oauthRefreshToken String?  @map("oauth_refresh_token") @db.Text
  oauthTokenExpiry DateTime? @map("oauth_token_expiry") @db.Timestamp(0)

  // 连接状态监控
  connectionStatus String    @default("unknown") @map("connection_status") // connected, disconnected, error, unknown
  lastConnectionTest DateTime? @map("last_connection_test") @db.Timestamp(0)
  connectionError  String?   @map("connection_error") @db.Text

  // 同步设置和状态
  syncEnabled      Boolean   @default(true) @map("sync_enabled")
  syncInterval     Int       @default(5) @map("sync_interval")
  lastSyncAt       DateTime? @map("last_sync_at") @db.Timestamp(0)
  syncStatus       String    @default("idle") @map("sync_status") // idle, syncing, error
  syncError        String?   @map("sync_error") @db.Text

  // 账户设置
  isActive         Boolean   @default(true) @map("is_active")
  isDefault        Boolean   @default(false) @map("is_default")
  signature        String?   @db.Text
  replyToEmail     String?   @map("reply_to_email")
  maxEmailsPerSync Int       @default(50) @map("max_emails_per_sync")

  // 自动配置支持
  autoConfigured   Boolean   @default(false) @map("auto_configured")
  provider         String?   // gmail, outlook, yahoo, custom

  createdAt        DateTime? @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt        DateTime? @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  user             User      @relation(fields: [userId], references: [id], onDelete: Cascade, onUpdate: NoAction, map: "email_accounts_ibfk_1")
  emails           Email[]

  @@unique([userId, email], map: "unique_user_email")
  @@index([email], map: "idx_email")
  @@index([userId], map: "idx_user_id")
  @@index([userId, isDefault], map: "idx_user_default")
  @@index([connectionStatus], map: "idx_connection_status")
  @@index([syncStatus], map: "idx_sync_status")
  @@map("email_accounts")
}

model UserSecuritySettings {
  id                 Int       @id @default(autoincrement())
  userId             Int       @unique @map("user_id")
  passwordExpiryDays Int?      @map("password_expiry_days")
  lastPasswordChange DateTime? @map("last_password_change")
  twoFactorEnabled   Boolean   @default(false) @map("two_factor_enabled")
  twoFactorSecret    String?   @map("two_factor_secret")
  backup_codes       String?   @db.Text
  loginNotification  Boolean   @default(true) @map("login_notification")
  suspiciousActivity Boolean   @default(true) @map("suspicious_activity")
  maxActiveSessions  Int       @default(5) @map("max_active_sessions")
  sessionTimeout     Int       @default(24) @map("session_timeout")
  ipWhitelist        String?   @map("ip_whitelist") @db.Text
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @default(now()) @updatedAt @map("updated_at")
  user               User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId], map: "user_security_settings_user_id_fkey")
  @@map("user_security_settings")
}

// 应用专用密码表
model AppPassword {
  id          Int      @id @default(autoincrement())
  userId      Int      @map("user_id")
  name        String   @map("name") // 应用专用密码的名称/描述
  password    String   @map("password") // 加密后的应用专用密码
  purpose     String   @default("imap") @map("purpose") // 用途：imap, smtp, api等
  isActive    Boolean  @default(true) @map("is_active")
  lastUsedAt  DateTime? @map("last_used_at")
  expiresAt   DateTime? @map("expires_at") // 可选的过期时间
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, name]) // 同一用户的应用专用密码名称不能重复
  @@map("app_passwords")
}

model SecurityLog {
  id        Int      @id @default(autoincrement())
  userId    Int?     @map("user_id")
  action    String
  ipAddress String   @map("ip_address")
  userAgent String?  @map("user_agent") @db.Text
  location  String?
  success   Boolean  @default(true)
  details   String?  @db.Text
  createdAt DateTime @default(now()) @map("created_at")
  user      User?    @relation(fields: [userId], references: [id])

  @@index([userId, createdAt])
  @@index([action, createdAt])
  @@index([userId], map: "security_logs_user_id_fkey")
  @@map("security_logs")
}

model SuspiciousActivity {
  id          Int                        @id @default(autoincrement())
  userId      Int?                       @map("user_id")
  type        String
  description String                     @db.Text
  ipAddress   String                     @map("ip_address")
  userAgent   String?                    @map("user_agent") @db.Text
  severity    SuspiciousActivitySeverity @default(MEDIUM)
  status      SuspiciousActivityStatus   @default(PENDING)
  details     Json?
  createdAt   DateTime                   @default(now()) @map("created_at")
  updatedAt   DateTime                   @default(now()) @updatedAt @map("updated_at")
  user        User?                      @relation(fields: [userId], references: [id])

  @@index([userId, createdAt])
  @@index([type, createdAt])
  @@index([status])
  @@index([userId], map: "suspicious_activities_user_id_fkey")
  @@map("suspicious_activities")
}

model UserSession {
  id         String    @id @db.VarChar(500)
  userId     Int       @map("user_id")
  ipAddress  String?   @map("ip_address")
  userAgent  String?   @map("user_agent") @db.Text
  createdAt  DateTime  @default(now()) @map("created_at")
  expiresAt  DateTime  @map("expires_at")
  lastUsedAt DateTime? @map("last_used_at")
  user       User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId, expiresAt])
  @@index([userId], map: "user_sessions_user_id_fkey")
  @@map("user_sessions")
}

// 子账户相关模型
// 子账户权限模型
model SubAccountPermission {
  id             Int      @id @default(autoincrement())
  subUserId      Int      @map("sub_user_id")
  permissionType String   @map("permission_type") // email_send, email_receive, email_delete, folder_manage, contact_manage, template_manage, rule_manage
  isAllowed      Boolean  @default(true) @map("is_allowed")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at")

  subUser        User     @relation(fields: [subUserId], references: [id], onDelete: Cascade)

  @@unique([subUserId, permissionType], map: "unique_sub_user_permission")
  @@index([subUserId], map: "idx_sub_user_id")
  @@index([permissionType], map: "idx_permission_type")
  @@map("sub_account_permissions")
}

// 子账户使用量模型
model SubAccountUsage {
  id           Int      @id @default(autoincrement())
  subUserId    Int      @map("sub_user_id")
  usageType    String   @map("usage_type") // storage, emails_sent, emails_received, contacts, folders
  currentUsage BigInt   @default(0) @map("current_usage")
  usageLimit   BigInt?  @map("usage_limit") // NULL表示无限制
  lastUpdated  DateTime @default(now()) @updatedAt @map("last_updated")

  subUser      User     @relation(fields: [subUserId], references: [id], onDelete: Cascade)

  @@unique([subUserId, usageType], map: "unique_sub_user_usage")
  @@index([subUserId], map: "idx_sub_user_id")
  @@index([usageType], map: "idx_usage_type")
  @@map("sub_account_usage")
}

// 子账户活动日志模型
model SubAccountActivity {
  id                  Int      @id @default(autoincrement())
  parentUserId        Int      @map("parent_user_id")
  subUserId           Int      @map("sub_user_id")
  activityType        String   @map("activity_type") // login, logout, email_send, email_receive, permission_change, quota_change
  activityDescription String?  @map("activity_description") @db.Text
  ipAddress           String?  @map("ip_address") @db.VarChar(45)
  userAgent           String?  @map("user_agent") @db.Text
  metadata            String?  @db.Text // JSON
  createdAt           DateTime @default(now()) @map("created_at")

  parentUser          User     @relation("ParentUserActivities", fields: [parentUserId], references: [id], onDelete: Cascade)
  subUser             User     @relation("SubUserActivities", fields: [subUserId], references: [id], onDelete: Cascade)

  @@index([parentUserId], map: "idx_parent_user_id")
  @@index([subUserId], map: "idx_sub_user_id")
  @@index([activityType], map: "idx_activity_type")
  @@index([createdAt], map: "idx_created_at")
  @@map("sub_account_activities")
}

// 子账户邀请模型
model SubAccountInvitation {
  id            Int       @id @default(autoincrement())
  parentUserId  Int       @map("parent_user_id")
  email         String
  invitationToken String  @unique @map("invitation_token")
  permissions   String?   @db.Text // JSON
  quotaLimit    BigInt?   @map("quota_limit")
  expiresAt     DateTime  @map("expires_at")
  acceptedAt    DateTime? @map("accepted_at")
  subUserId     Int?      @unique @map("sub_user_id")
  status        String    @default("pending") // pending, accepted, expired, cancelled
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @default(now()) @updatedAt @map("updated_at")

  parentUser    User      @relation("ParentUserInvitations", fields: [parentUserId], references: [id], onDelete: Cascade)
  subUser       User?     @relation("SubUserInvitation", fields: [subUserId], references: [id], onDelete: SetNull)

  @@index([parentUserId], map: "idx_parent_user_id")
  @@index([invitationToken], map: "idx_invitation_token")
  @@index([email], map: "idx_email")
  @@index([status], map: "idx_status")
  @@index([expiresAt], map: "idx_expires_at")
  @@map("sub_account_invitations")
}

model EmailVerification {
  id        Int                   @id @default(autoincrement())
  email     String
  code      String
  type      EmailVerificationType @default(REGISTRATION)
  expiresAt DateTime              @map("expires_at")
  verified  Boolean               @default(false)
  attempts  Int                   @default(0)
  createdAt DateTime              @default(now()) @map("created_at")
  updatedAt DateTime              @default(now()) @updatedAt @map("updated_at")

  @@index([email, type])
  @@index([code])
  @@index([expiresAt])
  @@map("email_verifications")
}

model EmailTracking {
  id             Int                  @id @default(autoincrement())
  emailId        String               @map("email_id")
  userId         Int                  @map("user_id")
  trackingId     String               @unique @map("tracking_id")
  trackOpens     Boolean              @default(false) @map("track_opens")
  trackClicks    Boolean              @default(false) @map("track_clicks")
  trackReplies   Boolean              @default(false) @map("track_replies")
  requestReceipt Boolean              @default(false) @map("request_receipt")
  isDelivered    Boolean              @default(false) @map("is_delivered")
  deliveredAt    DateTime?            @map("delivered_at") @db.Timestamp(0)
  isOpened       Boolean              @default(false) @map("is_opened")
  firstOpenedAt  DateTime?            @map("first_opened_at") @db.Timestamp(0)
  lastOpenedAt   DateTime?            @map("last_opened_at") @db.Timestamp(0)
  openCount      Int                  @default(0) @map("open_count")
  isReplied      Boolean              @default(false) @map("is_replied")
  repliedAt      DateTime?            @map("replied_at") @db.Timestamp(0)
  createdAt      DateTime             @default(now()) @map("created_at") @db.Timestamp(0)
  updatedAt      DateTime             @default(now()) @updatedAt @map("updated_at") @db.Timestamp(0)
  email          Email                @relation(fields: [emailId], references: [id], onDelete: Cascade)
  user           User                 @relation(fields: [userId], references: [id], onDelete: Cascade)
  events         EmailTrackingEvent[]

  @@index([emailId])
  @@index([userId])
  @@index([trackingId])
  @@map("email_tracking")
}

model EmailTrackingEvent {
  id         Int           @id @default(autoincrement())
  trackingId String        @map("tracking_id")
  eventType  String        @map("event_type")
  ipAddress  String?       @map("ip_address")
  userAgent  String?       @map("user_agent") @db.Text
  location   String?
  clickedUrl String?       @map("clicked_url") @db.Text
  metadata   String?       @db.Text
  createdAt  DateTime      @default(now()) @map("created_at") @db.Timestamp(0)
  tracking   EmailTracking @relation(fields: [trackingId], references: [trackingId], onDelete: Cascade)

  @@index([trackingId])
  @@index([eventType])
  @@index([createdAt])
  @@map("email_tracking_events")
}

model EmailReceipt {
  id             Int                @id @default(autoincrement())
  emailId        String             @map("email_id")
  userId         Int                @map("user_id")
  receiptType    EmailReceiptType   @default(READ) @map("receipt_type")
  status         EmailReceiptStatus @default(PENDING)
  requestedAt    DateTime           @default(now()) @map("requested_at") @db.Timestamp(0)
  receivedAt     DateTime?          @map("received_at") @db.Timestamp(0)
  recipientEmail String             @map("recipient_email")
  metadata       String?            @db.Text
  email          Email              @relation(fields: [emailId], references: [id], onDelete: Cascade)
  user           User               @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([emailId])
  @@index([userId])
  @@index([status])
  @@map("email_receipts")
}

enum SuspiciousActivitySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum SuspiciousActivityStatus {
  PENDING
  REVIEWED
  RESOLVED
  FALSE_POSITIVE
}

enum EmailVerificationType {
  REGISTRATION
  PASSWORD_RESET
  EMAIL_CHANGE
}

enum EmailReceiptType {
  READ
  DELIVERY
}

enum EmailReceiptStatus {
  PENDING
  SENT
  RECEIVED
  FAILED
}
