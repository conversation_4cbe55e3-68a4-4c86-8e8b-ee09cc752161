import api from '../config/api';
import type { ApiResponse } from '../types';

export interface SystemUser {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  isActive: boolean;
  role?: string;
  userType: 'admin' | 'welcome' | 'postManager' | 'custom';
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SystemUserFormData {
  email: string;
  username: string;
  displayName: string;
  userType: 'admin' | 'welcome' | 'postManager' | 'custom';
  description?: string;
  password: string;
}

// 获取系统用户列表
export const getSystemUsers = async (): Promise<SystemUser[]> => {
  const response = await api.get<ApiResponse<SystemUser[]>>('/system-users');
  return response.data.data!;
};

// 创建系统用户
export const createSystemUser = async (data: SystemUserFormData): Promise<SystemUser> => {
  const response = await api.post<ApiResponse<SystemUser>>('/system-users', data);
  return response.data.data!;
};

// 更新系统用户
export const updateSystemUser = async (id: number, data: Partial<SystemUserFormData>): Promise<SystemUser> => {
  const response = await api.put<ApiResponse<SystemUser>>(`/system-users/${id}`, data);
  return response.data.data!;
};

// 删除系统用户
export const deleteSystemUser = async (id: number): Promise<void> => {
  await api.delete(`/system-users/${id}`);
};
