#!/usr/bin/env node

/**
 * 验证 IMAP 认证流程
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifyAuthFlow() {
  console.log('=== 验证 IMAP 认证流程 ===\n');
  
  const testEmail = '<EMAIL>';
  
  try {
    // 1. 模拟 Dovecot 的 password_query
    console.log('1. 执行 Dovecot password_query...');
    const authResult = await prisma.$queryRaw`
      SELECT u.email as user, 
             COALESCE(ap.password, u.mail_password) as password, 
             CASE WHEN ap.password IS NOT NULL THEN 'PLAIN' ELSE 'SHA512-CRYPT' END as scheme,
             ap.password as app_password,
             u.mail_password as user_password
      FROM users u 
      INNER JOIN virtual_domains vd ON (u.domain_id = vd.id OR vd.name = SUBSTRING(u.email, LOCATE('@', u.email) + 1))
      LEFT JOIN app_passwords ap ON (ap.user_id = u.id AND ap.purpose = 'imap' AND ap.is_active = 1 AND (ap.expires_at IS NULL OR ap.expires_at > NOW()))
      WHERE u.email = ${testEmail} AND u.email_verified = 1 AND u.is_mail_active = 1 AND u.is_active = 1 AND vd.active = 1
        AND (u.mail_password IS NOT NULL AND u.mail_password != '' OR ap.password IS NOT NULL)
    `;
    
    if (authResult.length === 0) {
      console.log('❌ 查询没有返回结果');
      return;
    }
    
    const auth = authResult[0];
    console.log('✅ 查询结果:');
    console.log(`   用户: ${auth.user}`);
    console.log(`   认证方案: ${auth.scheme}`);
    console.log(`   使用的密码: ${auth.password}`);
    console.log(`   应用专用密码: ${auth.app_password || '无'}`);
    console.log(`   用户密码: ${auth.user_password ? '有 (哈希)' : '无'}`);
    
    // 2. 分析认证流程
    console.log('\n2. 认证流程分析:');
    if (auth.app_password) {
      console.log('✅ 使用应用专用密码认证');
      console.log(`   - 密码: ${auth.app_password}`);
      console.log(`   - 方案: PLAIN (明文比较)`);
      console.log(`   - 验证: 客户端密码 == "${auth.app_password}"`);
    } else {
      console.log('⚠️ 使用用户密码认证');
      console.log(`   - 密码: ${auth.user_password.substring(0, 20)}...`);
      console.log(`   - 方案: SHA512-CRYPT (哈希比较)`);
      console.log(`   - 验证: SHA512-CRYPT(客户端密码) == 存储的哈希`);
    }
    
    // 3. IMAP 客户端应该使用的密码
    console.log('\n3. IMAP 客户端配置:');
    console.log(`   用户名: ${auth.user}`);
    console.log(`   密码: ${auth.password}`);
    console.log(`   服务器: mail.blindedby.love:993 (TLS)`);
    
    // 4. 验证后端服务是否使用正确的密码
    console.log('\n4. 后端服务验证:');
    
    // 检查后端是否能获取到正确的应用专用密码
    const appPasswordCheck = await prisma.$queryRaw`
      SELECT password, id FROM app_passwords
      WHERE user_id = (SELECT id FROM users WHERE email = ${testEmail})
        AND purpose = 'imap'
        AND is_active = 1
        AND (expires_at IS NULL OR expires_at > NOW())
      ORDER BY created_at DESC
      LIMIT 1
    `;
    
    if (appPasswordCheck.length > 0) {
      const backendPassword = appPasswordCheck[0].password;
      console.log(`✅ 后端获取的应用专用密码: ${backendPassword}`);
      
      if (backendPassword === auth.password) {
        console.log('✅ 后端密码与 Dovecot 查询结果一致');
      } else {
        console.log('❌ 后端密码与 Dovecot 查询结果不一致');
      }
    } else {
      console.log('❌ 后端无法获取应用专用密码');
    }
    
    return auth;
    
  } catch (error) {
    console.error('验证失败:', error);
    return null;
  }
}

async function testIMAPConnection(authInfo) {
  console.log('\n=== 测试 IMAP 连接 ===');
  
  if (!authInfo) {
    console.log('❌ 没有认证信息，跳过连接测试');
    return;
  }
  
  try {
    const Imap = require('imap');
    
    const config = {
      user: authInfo.user,
      password: authInfo.password,
      host: 'mail.blindedby.love',
      port: 993,
      tls: true,
      authTimeout: 10000,
      connTimeout: 10000,
      tlsOptions: {
        rejectUnauthorized: false,
      },
    };
    
    console.log(`尝试连接: ${config.user} @ ${config.host}:${config.port}`);
    console.log(`使用密码: ${config.password}`);
    
    return new Promise((resolve) => {
      const imap = new Imap(config);
      let resolved = false;

      imap.once('ready', () => {
        if (!resolved) {
          resolved = true;
          console.log('✅ IMAP 连接成功！');
          console.log('🎉 认证流程验证完成');
          imap.end();
          resolve(true);
        }
      });

      imap.once('error', (err) => {
        if (!resolved) {
          resolved = true;
          console.log('❌ IMAP 连接失败:', err.message);
          
          // 分析可能的原因
          console.log('\n可能的原因:');
          if (err.message.includes('authentication')) {
            console.log('- 认证失败: 密码不正确或用户不存在');
          } else if (err.message.includes('timeout')) {
            console.log('- 连接超时: 网络问题或服务器未响应');
          } else if (err.message.includes('ECONNREFUSED')) {
            console.log('- 连接被拒绝: Dovecot 服务未运行或端口未开放');
          } else {
            console.log('- 其他错误:', err.message);
          }
          
          resolve(false);
        }
      });

      setTimeout(() => {
        if (!resolved) {
          resolved = true;
          console.log('❌ IMAP 连接超时');
          imap.destroy();
          resolve(false);
        }
      }, 15000);

      imap.connect();
    });
    
  } catch (error) {
    console.error('IMAP 连接测试失败:', error.message);
    return false;
  }
}

async function main() {
  console.log('开始验证 IMAP 认证流程...\n');
  
  try {
    const authInfo = await verifyAuthFlow();
    await testIMAPConnection(authInfo);
    
  } catch (error) {
    console.error('验证过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行验证
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { main };
