import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Avatar,
  Upload,
  message,
  Space,
  Typography,
  Row,
  Col,
  Divider,
  Select
} from 'antd';
import {
  UserOutlined,
  CameraOutlined,
  SaveOutlined,
  EditOutlined
} from '@ant-design/icons';
import type { UploadProps } from 'antd/es/upload/interface';

const { Title, Text } = Typography;

interface UserProfile {
  id: string;
  username: string;
  email: string;
  displayName: string;
  avatar?: string;
  phone?: string;
  department?: string;
  position?: string;
  timezone: string;
  language: string;
}

const ProfilePage: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [avatarUrl, setAvatarUrl] = useState<string>('');

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      // 模拟加载用户资料
      const mockProfile: UserProfile = {
        id: '1',
        username: 'testuser',
        email: '<EMAIL>',
        displayName: '张三',
        avatar: '',
        phone: '+86 138-0000-0000',
        department: '技术部',
        position: '高级软件工程师',
        timezone: 'Asia/Shanghai',
        language: 'zh-CN'
      };
      setProfile(mockProfile);
      setAvatarUrl(mockProfile.avatar || '');
      form.setFieldsValue(mockProfile);
    } catch (error) {
      message.error('加载用户资料失败');
    }
  };

  const handleSave = async (values: any) => {
    setLoading(true);
    try {
      // 模拟保存用户资料
      const updatedProfile = { ...profile, ...values };
      setProfile(updatedProfile);
      message.success('用户资料保存成功');
    } catch (error) {
      message.error('保存用户资料失败');
    } finally {
      setLoading(false);
    }
  };

  const uploadProps: UploadProps = {
    name: 'avatar',
    action: '/api/upload/avatar',
    showUploadList: false,
    beforeUpload: (file) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error('只能上传 JPG/PNG 格式的图片');
        return false;
      }
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB');
        return false;
      }
      return true;
    },
    onChange: (info) => {
      if (info.file.status === 'uploading') {
        setLoading(true);
        return;
      }
      if (info.file.status === 'done') {
        setLoading(false);
        setAvatarUrl(info.file.response?.url || '');
        message.success('头像上传成功');
      } else if (info.file.status === 'error') {
        setLoading(false);
        message.error('头像上传失败');
      }
    }
  };

  return (
    <Card>
      <Title level={3} style={{ marginBottom: '24px' }}>
        <UserOutlined style={{ marginRight: '8px' }} />
        个人资料
      </Title>

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSave}
              initialValues={profile}
            >
              <Row gutter={[24, 24]}>
                {/* 头像部分 */}
                <Col span={24}>
                  <div style={{ textAlign: 'center', marginBottom: '24px' }}>
                    <div style={{ position: 'relative', display: 'inline-block' }}>
                      <Avatar
                        size={120}
                        src={avatarUrl}
                        icon={!avatarUrl && <UserOutlined />}
                        style={{ border: '4px solid #f0f0f0' }}
                      />
                      <Upload {...uploadProps}>
                        <Button
                          type="primary"
                          shape="circle"
                          icon={<CameraOutlined />}
                          size="small"
                          style={{
                            position: 'absolute',
                            bottom: '8px',
                            right: '8px'
                          }}
                        />
                      </Upload>
                    </div>
                    <div style={{ marginTop: '12px' }}>
                      <Text type="secondary">点击相机图标更换头像</Text>
                    </div>
                  </div>
                </Col>

                {/* 基本信息 */}
                <Col span={12}>
                  <Form.Item
                    name="username"
                    label="用户名"
                    rules={[{ required: true, message: '请输入用户名' }]}
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="email"
                    label="邮箱地址"
                    rules={[
                      { required: true, message: '请输入邮箱地址' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input disabled />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="displayName"
                    label="显示名称"
                    rules={[{ required: true, message: '请输入显示名称' }]}
                  >
                    <Input placeholder="请输入显示名称" />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="phone"
                    label="手机号码"
                  >
                    <Input placeholder="请输入手机号码" />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="department"
                    label="部门"
                  >
                    <Input placeholder="请输入部门" />
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="position"
                    label="职位"
                  >
                    <Input placeholder="请输入职位" />
                  </Form.Item>
                </Col>

                <Divider />

                {/* 偏好设置 */}
                <Col span={24}>
                  <Title level={4}>偏好设置</Title>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="timezone"
                    label="时区"
                    rules={[{ required: true, message: '请选择时区' }]}
                  >
                    <Select placeholder="请选择时区">
                      <Select.Option value="Asia/Shanghai">中国标准时间 (UTC+8)</Select.Option>
                      <Select.Option value="America/New_York">美国东部时间 (UTC-5)</Select.Option>
                      <Select.Option value="Europe/London">英国时间 (UTC+0)</Select.Option>
                      <Select.Option value="Asia/Tokyo">日本时间 (UTC+9)</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                <Col span={12}>
                  <Form.Item
                    name="language"
                    label="语言"
                    rules={[{ required: true, message: '请选择语言' }]}
                  >
                    <Select placeholder="请选择语言">
                      <Select.Option value="zh-CN">简体中文</Select.Option>
                      <Select.Option value="zh-TW">繁体中文</Select.Option>
                      <Select.Option value="en-US">English</Select.Option>
                      <Select.Option value="ja-JP">日本語</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>

                {/* 操作按钮 */}
                <Col span={24}>
                  <Divider />
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      icon={<SaveOutlined />}
                      loading={loading}
                      size="large"
                    >
                      保存更改
                    </Button>
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => form.resetFields()}
                      size="large"
                    >
                      重置
                    </Button>
                  </Space>
                </Col>
              </Row>
            </Form>
    </Card>
  );
};

export default ProfilePage;
