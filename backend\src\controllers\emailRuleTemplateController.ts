import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import * as emailRuleTemplateService from '../services/emailRuleTemplateService';

// 获取所有规则模板
export const getAllRuleTemplates = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const templates = await emailRuleTemplateService.getAllRuleTemplates();
    
    const response: ApiResponse = {
      success: true,
      message: '获取规则模板成功',
      data: templates,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取规则模板失败: ${(error as Error).message}`, 500);
  }
};

// 根据分类获取规则模板
export const getRuleTemplatesByCategory = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { category } = req.params;
    
    if (!category) {
      throw new AppError('分类参数不能为空', 400);
    }

    const templates = await emailRuleTemplateService.getRuleTemplatesByCategory(category);
    
    const response: ApiResponse = {
      success: true,
      message: '获取规则模板成功',
      data: templates,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取规则模板失败: ${(error as Error).message}`, 500);
  }
};

// 从模板创建规则
export const createRuleFromTemplate = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { templateId, customizations } = req.body;
    const userId = req.user!.id;

    if (!templateId) {
      throw new AppError('模板ID不能为空', 400);
    }

    await emailRuleTemplateService.createRuleFromTemplate(userId, templateId, customizations);
    
    const response: ApiResponse = {
      success: true,
      message: '从模板创建规则成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`从模板创建规则失败: ${(error as Error).message}`, 500);
  }
};

// 为当前用户创建发件副本规则
export const createSentCopyRule = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;

    await emailRuleTemplateService.createSentCopyRuleForUser(userId);
    
    const response: ApiResponse = {
      success: true,
      message: '发件副本规则创建成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`创建发件副本规则失败: ${(error as Error).message}`, 500);
  }
};

// 管理员：为所有用户创建系统规则
export const createSystemRulesForAllUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 检查管理员权限
    const user = req.user!;
    if (user.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    await emailRuleTemplateService.createSystemRulesForAllUsers();
    
    const response: ApiResponse = {
      success: true,
      message: '为所有用户创建系统规则成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`创建系统规则失败: ${(error as Error).message}`, 500);
  }
};

// 初始化规则模板
export const initializeTemplates = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 检查管理员权限
    const user = req.user!;
    if (user.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    await emailRuleTemplateService.initializeRuleTemplates();
    
    const response: ApiResponse = {
      success: true,
      message: '规则模板初始化成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`初始化规则模板失败: ${(error as Error).message}`, 500);
  }
};
