// 用户相关的类型定义

export type UserRole = 'admin' | 'super_admin' | 'user' | 'moderator';
export type UserStatus = 'active' | 'inactive' | 'suspended' | 'pending';
export type AccountType = 'main' | 'sub';

// 子账户权限类型
export interface SubAccountPermission {
  permissionType: string;
  isAllowed: boolean;
}

// 基础用户类型
export interface User {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  avatarUrl?: string;
  isActive: boolean;
  role: UserRole;
  accountType?: AccountType;
  parentUserId?: number;
  isSubAccountEnabled?: boolean;
  emailVerified: boolean;
  twoFactorEnabled?: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  preferences?: UserPreferences;
  stats?: UserStats;
  subAccountPermissions?: SubAccountPermission[];
}

// 用户认证相关类型
export interface LoginData {
  email: string;
  password: string;
  rememberMe?: boolean;
  twoFactorCode?: string;
}

export interface RegisterData {
  email: string;
  username: string;
  password: string;
  displayName?: string;
  verificationCode?: string;
  inviteCode?: string;
  acceptTerms?: boolean;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  tokenType: string;
}

export interface RefreshTokenData {
  refreshToken: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ResetPasswordData {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface VerifyEmailData {
  token: string;
}

// 用户偏好设置类型
export interface UserPreferences {
  // 基础设置
  language: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  
  // 邮件设置
  emailsPerPage: number;
  autoRefresh: boolean;
  autoRefreshInterval: number; // in seconds
  showPreviewPane: boolean;
  markAsReadOnPreview: boolean;
  confirmBeforeDelete: boolean;
  defaultReplyAction: 'reply' | 'replyAll';
  includeOriginalMessage: boolean;
  quotingStyle: 'top' | 'bottom' | 'inline';
  
  // 界面设置
  theme: ThemeConfig;
  compactMode: boolean;
  showAvatars: boolean;
  sidebarCollapsed: boolean;
  
  // 通知设置
  enableNotifications: boolean;
  notificationSound: boolean;
  desktopNotifications: boolean;
  emailNotifications: boolean;
  
  // 编辑器设置
  signature?: string;
  autoSave: boolean;
  autoSaveInterval: number; // in seconds
  spellCheck: boolean;
  richTextEditor: boolean;
  defaultFontSize: number;
  defaultFontFamily: string;
  
  // 安全设置
  sessionTimeout: number; // in minutes
  requirePasswordForSensitiveActions: boolean;
  logSecurityEvents: boolean;
  
  // 隐私设置
  showOnlineStatus: boolean;
  allowReadReceipts: boolean;
  blockImages: boolean;
  blockExternalContent: boolean;
}

// 主题配置类型
export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeConfig {
  mode: ThemeMode;
  primaryColor: string;
  borderRadius: number;
  fontSize: number;
  fontFamily: string;
  customColors?: Record<string, string>;
}

// 用户统计类型
export interface UserStats {
  totalEmails: number;
  unreadEmails: number;
  sentEmails: number;
  draftEmails: number;
  deletedEmails: number;
  totalContacts: number;
  totalLabels: number;
  totalFolders: number;
  storageUsed: number; // in bytes
  storageQuota: number; // in bytes
  lastLoginAt?: string;
  accountCreatedAt: string;
}

// 用户搜索和过滤类型
export interface UserSearchParams {
  query?: string;
  role?: UserRole;
  status?: UserStatus;
  isActive?: boolean;
  emailVerified?: boolean;
  twoFactorEnabled?: boolean;
  createdFrom?: string;
  createdTo?: string;
  lastLoginFrom?: string;
  lastLoginTo?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// 用户资料更新类型
export interface UserProfileData {
  displayName?: string;
  avatarUrl?: string;
  phone?: string;
  bio?: string;
  website?: string;
  location?: string;
  birthday?: string;
  preferences?: Partial<UserPreferences>;
}

// 用户会话类型
export interface UserSession {
  id: string;
  userId: number;
  deviceInfo: DeviceInfo;
  ipAddress: string;
  location?: string;
  isActive: boolean;
  lastActivityAt: string;
  createdAt: string;
  expiresAt: string;
}

export interface DeviceInfo {
  userAgent: string;
  browser: string;
  os: string;
  device: string;
  isMobile: boolean;
}

// 用户活动日志类型
export interface UserActivity {
  id: string;
  userId: number;
  action: string;
  resource?: string;
  resourceId?: string;
  details?: Record<string, unknown>;
  ipAddress: string;
  userAgent: string;
  success: boolean;
  timestamp: string;
}

// 用户通知类型
export interface UserNotification {
  id: string;
  userId: number;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  data?: Record<string, unknown>;
  isRead: boolean;
  readAt?: string;
  expiresAt?: string;
  createdAt: string;
}

// 用户邀请类型
export interface UserInvite {
  id: string;
  email: string;
  role: UserRole;
  invitedBy: number;
  token: string;
  expiresAt: string;
  acceptedAt?: string;
  createdAt: string;
  inviter?: {
    id: number;
    username: string;
    displayName?: string;
  };
}

// 用户批量操作类型
export interface UserBulkAction {
  action: 'activate' | 'deactivate' | 'delete' | 'changeRole' | 'resetPassword' | 'sendInvite';
  userIds: number[];
  params?: {
    role?: UserRole;
    sendNotification?: boolean;
    message?: string;
  };
}

export interface UserBulkActionResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  errors: Array<{
    userId: number;
    error: string;
  }>;
}

// 用户导入导出类型
export interface UserImportData {
  email: string;
  username: string;
  displayName?: string;
  role?: UserRole;
  isActive?: boolean;
  sendInvite?: boolean;
}

export interface UserExportData extends User {
  totalEmails: number;
  lastLoginAt?: string;
  createdAt: string;
}

// 双因素认证类型
export interface TwoFactorSetupData {
  secret: string;
  qrCode: string;
  backupCodes: string[];
}

export interface TwoFactorVerifyData {
  code: string;
  backupCode?: string;
}

export interface TwoFactorBackupCodes {
  codes: string[];
  generatedAt: string;
}
