/* 邮箱系统设计系统 */

:root {
  /* 主色调 - 清新活力邮箱配色 */
  --primary-color: #FF7F50; /* 珊瑚粉 */
  --primary-hover: #FF6347; /* 番茄红 */
  --primary-active: #FF0000; /* 樱桃红 */
  --primary-light: #FFE4E1; /* 淡珊瑚粉 */
  --primary-lighter: #FFF5F5; /* 极淡粉色 */

  /* 中性色 - 清新对比度 */
  --text-primary: #1a202c; /* 深蓝灰 */
  --text-secondary: #2d3748; /* 中蓝灰 */
  --text-tertiary: #4a5568; /* 浅蓝灰 */
  --text-disabled: #a0aec0; /* 淡蓝灰 */
  --text-inverse: #ffffff;

  /* 背景色 - 天蓝色系层次 */
  --bg-primary: #ffffff;
  --bg-secondary: #E6F0FF; /* 非常淡的天蓝色 */
  --bg-tertiary: #E8F5E9; /* 薄荷绿 */
  --bg-hover: #F0F8FF; /* 爱丽丝蓝 */
  --bg-active: #E0F2FE; /* 淡天蓝 */

  /* 边框色 - 清新边框 */
  --border-color: #e2e8f0;
  --border-light: #f7fafc;
  --border-dark: #cbd5e0;

  /* 功能色 - 与清新配色协调 */
  --success-color: #48bb78; /* 清新绿 */
  --success-light: #f0fff4;
  --warning-color: #ed8936; /* 温暖橙 */
  --warning-light: #fffaf0;
  --error-color: #FF0000; /* 樱桃红 */
  --error-light: #fed7d7;
  --info-color: #4299e1; /* 天蓝 */
  --info-light: #ebf8ff;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-2xl: 24px;

  /* 字体权重 */
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 行高 */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  --spacing-3xl: 64px;

  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-xl: 12px;

  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.2s ease;
  --transition-slow: 0.3s ease;

  /* 布局尺寸 */
  --header-height: 64px;
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 64px;
  --mobile-breakpoint: 768px;
  --tablet-breakpoint: 1024px;

  /* 新视觉规范色彩系统 - 清新活力主题 */
  --color-primary: #FF7F50; /* 珊瑚粉 */
  --color-primary-hover: #FF6347; /* 番茄红 */
  --color-primary-active: #FF0000; /* 樱桃红 */
  --color-star: #FFD700; /* 金色 */
  --color-unread: #1a202c; /* 深蓝灰 */
  --color-danger: #FF0000; /* 樱桃红 */
  --color-success: #48bb78; /* 清新绿 */
  --color-gray-1: #E6F0FF; /* 非常淡的天蓝色 */
  --color-gray-2: #e2e8f0; /* 淡蓝灰 */
  --color-gray-3: #a0aec0; /* 中蓝灰 */
  --color-gray-4: #4a5568; /* 深蓝灰 */
  --color-gray-5: #1a202c; /* 最深蓝灰 */

  /* 新视觉规范字体 */
  --font-title: 20px;
  --font-body: 16px;
  --font-caption: 14px;
  --font-color-secondary: #6B7280;
  --line-height-body: 1.6;
}

/* 深色主题变量 - 清新活力深色版 */
[data-theme="dark"] {
  --text-primary: #ffffff;
  --text-secondary: #e2e8f0;
  --text-tertiary: #a0aec0;
  --text-disabled: #718096;
  --text-inverse: #1a202c;

  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --bg-tertiary: #4a5568;
  --bg-hover: #2c5282;
  --bg-active: #3182ce;

  --border-color: #4a5568;
  --border-light: #2d3748;
  --border-dark: #718096;
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
}

/* 工具类 */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-2 > * + * {
  margin-left: var(--spacing-sm);
}

.space-x-4 > * + * {
  margin-left: var(--spacing-md);
}

.h-full {
  height: 100%;
}

.w-full {
  width: 100%;
}

.min-h-screen {
  min-height: 100vh;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  user-select: none;
}

/* 文本样式 */
.text-xs {
  font-size: var(--font-size-xs);
}

.text-sm {
  font-size: var(--font-size-sm);
}

.text-base {
  font-size: var(--font-size-base);
}

.text-lg {
  font-size: var(--font-size-lg);
}

.text-xl {
  font-size: var(--font-size-xl);
}

.font-medium {
  font-weight: var(--font-weight-medium);
}

.font-semibold {
  font-weight: var(--font-weight-semibold);
}

.font-bold {
  font-weight: var(--font-weight-bold);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-tertiary {
  color: var(--text-tertiary);
}

/* 背景样式 */
.bg-primary {
  background-color: var(--bg-primary);
}

.bg-secondary {
  background-color: var(--bg-secondary);
}

.bg-hover {
  background-color: var(--bg-hover);
}

/* 边框样式 */
.border {
  border: 1px solid var(--border-color);
}

.border-r {
  border-right: 1px solid var(--border-color);
}

.border-b {
  border-bottom: 1px solid var(--border-color);
}

.border-light {
  border-color: var(--border-light);
}

/* 圆角样式 */
.rounded {
  border-radius: var(--radius-md);
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

/* 阴影样式 */
.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

/* 过渡动画 */
.transition {
  transition: all var(--transition-normal);
}

.transition-fast {
  transition: all var(--transition-fast);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .hidden-mobile {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .hidden-desktop {
    display: none !important;
  }
}

/* 布局组件样式 */
.main-layout {
  min-height: 100vh;
  background-color: var(--bg-secondary);
}

.main-header {
  height: var(--header-height);
  background-color: var(--bg-primary);
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  padding: 0 var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.main-sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-tertiary);
  border-right: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.main-sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.main-content {
  flex: 1;
  background-color: var(--bg-primary);
  overflow: auto;
}

/* 侧边栏样式 */
.sidebar-logo {
  height: var(--header-height);
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-lg);
  /* background-color: var(--bg-primary); */
}

.sidebar-logo-icon {
  font-size: var(--font-size-xl);
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}

.sidebar-logo-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.sidebar-menu {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm) 0;
}

/* 头部样式 */
.header-left {
  display: flex;
  align-items: center;
}

.header-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
  margin-left: var(--spacing-md);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  height: 100%;
}

.header-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition-fast);
}

.header-action-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.user-menu {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
}

.user-menu:hover {
  background-color: var(--bg-hover);
}

.user-avatar {
  margin-right: var(--spacing-sm);
}

.user-name {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

/* 菜单项样式 */
.menu-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  margin: 0 var(--spacing-sm);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: var(--transition-normal);
  color: var(--text-secondary);
  text-decoration: none;
}

.menu-item:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.menu-item.active {
  background-color: var(--primary-color);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

.menu-item-icon {
  font-size: var(--font-size-lg);
  margin-right: var(--spacing-md);
  min-width: 20px;
  text-align: center;
}

.menu-item-label {
  flex: 1;
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.menu-item-badge {
  margin-left: auto;
}

/* 侧边栏分组 */
.sidebar-group {
  padding: var(--spacing-md) var(--spacing-sm);
}

.sidebar-group + .sidebar-group {
  border-top: 1px solid rgba(var(--border-light), 0.2);
}

.sidebar-divider {
  margin: 0 var(--spacing-lg);
  border-bottom: 1px solid var(--border-light);
  opacity: 0.2;
}

/* 移动端抽屉样式 */
.mobile-drawer .ant-drawer-header {
  border-bottom: 1px solid var(--border-light);
  padding: var(--spacing-md) var(--spacing-lg);
}

.mobile-drawer .ant-drawer-body {
  padding: 0;
}

/* 通知徽章样式 */
.notification-badge {
  position: relative;
}

.notification-badge .ant-badge-count {
  background-color: var(--error-color);
  border: 2px solid var(--bg-primary);
  box-shadow: var(--shadow-sm);
}

/* 连接状态指示器 */
.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.connection-status.connected {
  color: var(--success-color);
  background-color: var(--success-light);
}

.connection-status.disconnected {
  color: var(--error-color);
  background-color: var(--error-light);
}

.connection-status.connecting {
  color: var(--warning-color);
  background-color: var(--warning-light);
}

/* 加载状态 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 动画效果 */
@keyframes slideInLeft {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in-left {
  animation: slideInLeft var(--transition-normal);
}

.slide-in-right {
  animation: slideInRight var(--transition-normal);
}

.fade-in {
  animation: fadeIn var(--transition-normal);
}

/* 响应式优化 */

/* 平板设备 (768px - 1024px) */
@media (min-width: 768px) and (max-width: 1024px) {
  .main-sidebar {
    width: 200px;
  }

  .main-sidebar.collapsed {
    width: 56px;
  }

  .sidebar-logo-text {
    font-size: var(--font-size-base);
  }

  .header-title {
    font-size: var(--font-size-lg);
  }

  /* 邮件列表优化 */
  .email-item {
    padding: var(--spacing-md);
  }

  .email-item .email-preview {
    display: none;
  }
}

/* 移动设备 (最大768px) */
@media (max-width: 768px) {
  :root {
    --header-height: 56px;
    --sidebar-width: 280px;
  }

  .main-header {
    padding: 0 var(--spacing-md);
    height: var(--header-height);
  }

  .header-title {
    font-size: var(--font-size-base);
  }

  .header-right {
    gap: var(--spacing-xs);
  }

  .header-action-btn {
    width: 32px;
    height: 32px;
    min-width: 32px;
  }

  .user-menu {
    padding: var(--spacing-xs);
  }

  .user-name {
    display: none;
  }

  /* 邮件列表移动端优化 */
  .email-list {
    padding: 0;
  }

  .email-item {
    padding: var(--spacing-md);
    border-radius: 0;
    border-left: none;
    border-right: none;
  }

  .email-item:first-child {
    border-top: none;
  }

  .email-item.selected {
    border-left: 4px solid var(--primary-color);
    background-color: var(--bg-active);
  }

  /* 写邮件页面移动端优化 */
  .compose-container {
    padding: var(--spacing-sm);
  }

  .compose-card {
    margin: 0;
    border-radius: var(--radius-lg);
  }

  .compose-toolbar {
    padding: var(--spacing-md);
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .compose-toolbar .toolbar-left {
    flex: 1;
    min-width: 200px;
  }

  .compose-toolbar .toolbar-right {
    flex-shrink: 0;
  }

  /* 表单字段移动端优化 */
  .ant-form-item-label {
    padding-bottom: var(--spacing-xs);
  }

  .ant-input,
  .ant-select-selector,
  .ant-btn {
    height: 44px;
    font-size: var(--font-size-base);
  }

  .ant-input-lg,
  .ant-select-lg .ant-select-selector,
  .ant-btn-lg {
    height: 48px;
  }

  /* 附件列表移动端优化 */
  .attachment-grid {
    grid-template-columns: 1fr;
  }

  .attachment-item {
    padding: var(--spacing-md);
  }

  /* 收件人标签移动端优化 */
  .recipient-tags {
    gap: var(--spacing-xs);
  }

  .recipient-tag {
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}

/* 小屏幕移动设备 (最大480px) */
@media (max-width: 480px) {
  :root {
    --header-height: 52px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
  }

  .header-title {
    display: none;
  }

  .main-header {
    padding: 0 var(--spacing-sm);
  }

  .header-action-btn {
    width: 28px;
    height: 28px;
    min-width: 28px;
  }

  /* 邮件列表小屏优化 */
  .email-item {
    padding: var(--spacing-sm) var(--spacing-md);
  }

  .email-item .email-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .email-item .email-time {
    font-size: var(--font-size-xs);
  }

  .email-item .email-subject {
    font-size: var(--font-size-sm);
    line-height: var(--line-height-tight);
  }

  .email-item .email-preview {
    display: none;
  }

  /* 写邮件小屏优化 */
  .compose-container {
    padding: var(--spacing-xs);
  }

  .compose-toolbar {
    padding: var(--spacing-sm);
  }

  .compose-toolbar .toolbar-title {
    font-size: var(--font-size-lg);
  }

  .compose-section {
    padding: var(--spacing-md);
  }

  .compose-section-title {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-sm);
  }

  /* 按钮组小屏优化 */
  .button-group {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .button-group .ant-btn {
    width: 100%;
    justify-content: center;
  }

  /* 模态框小屏优化 */
  .ant-modal {
    margin: var(--spacing-sm);
    max-width: calc(100vw - 32px);
  }

  .ant-modal-content {
    border-radius: var(--radius-lg);
  }

  .ant-modal-header {
    padding: var(--spacing-md);
  }

  .ant-modal-body {
    padding: var(--spacing-md);
  }

  .ant-modal-footer {
    padding: var(--spacing-md);
    text-align: center;
  }

  .ant-modal-footer .ant-btn {
    width: 100%;
    margin: var(--spacing-xs) 0;
  }
}

/* 超小屏幕设备 (最大360px) */
@media (max-width: 360px) {
  :root {
    --spacing-sm: 6px;
    --spacing-md: 10px;
    --spacing-lg: 14px;
  }

  .main-header {
    padding: 0 var(--spacing-xs);
  }

  .header-action-btn {
    width: 24px;
    height: 24px;
    min-width: 24px;
  }

  .email-item {
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .compose-container {
    padding: 0;
  }

  .compose-card {
    border-radius: 0;
    border: none;
  }
}

/* 通用按钮组件样式 */
.ui-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: var(--font-body);
  font-weight: 500;
  border-radius: var(--radius-md);
  border: none;
  outline: none;
  padding: 0 20px;
  height: 40px;
  min-width: 80px;
  cursor: pointer;
  transition: background 0.2s, color 0.2s, box-shadow 0.2s;
  background: var(--color-gray-1);
  color: var(--color-gray-5);
  box-shadow: var(--shadow-sm);
  user-select: none;
}
.ui-btn-primary {
  background: var(--color-primary);
  color: #fff;
}
.ui-btn-primary:hover:not(:disabled) {
  background: var(--color-primary-hover);
}
.ui-btn-primary:active:not(:disabled) {
  background: var(--color-primary-active);
}
.ui-btn-danger {
  background: var(--color-danger);
  color: #fff;
}
.ui-btn-danger:hover:not(:disabled) {
  background: #b91c1c;
}
.ui-btn-default {
  background: var(--color-gray-1);
  color: var(--color-gray-5);
  border: 1px solid var(--color-gray-2);
}
.ui-btn-default:hover:not(:disabled) {
  background: var(--color-gray-2);
}
.ui-btn:disabled,
.ui-btn[disabled] {
  background: var(--color-gray-2);
  color: var(--font-color-secondary);
  cursor: not-allowed;
  opacity: 0.7;
}
.ui-btn-block {
  width: 100%;
  display: flex;
}
.ui-btn-icon {
  display: flex;
  align-items: center;
  font-size: 18px;
}
.ui-btn-text {
  display: inline-block;
}
.ui-btn-spinner {
  width: 18px;
  height: 18px;
  border: 2px solid #fff;
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: ui-btn-spin 0.8s linear infinite;
  background: transparent;
}
@keyframes ui-btn-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 用户菜单样式 */
.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: var(--transition-fast);
}

.user-menu:hover {
  background-color: var(--bg-hover);
}

.user-avatar {
  border: 2px solid var(--border-color);
  transition: var(--transition-fast);
}

.user-avatar:hover {
  border-color: var(--primary-color);
  transform: scale(1.05);
}

.user-name {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
}

/* 在线状态指示器 */
.online-indicator .ant-badge-dot {
  background-color: #52c41a !important;
  box-shadow: 0 0 0 2px #ffffff;
  animation: pulse-online 2s infinite;
  width: 8px !important;
  height: 8px !important;
  border-radius: 50% !important;
}

@keyframes pulse-online {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 用户头像样式 */
.user-avatar {
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-menu {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.user-menu:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-menu:hover .user-avatar {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: var(--color-primary);
}

.user-menu:hover .online-indicator .ant-badge-dot {
  animation-duration: 1s;
  transform: scale(1.1);
}

/* 头部右侧区域额外样式 */
.header-right .flex {
  display: flex;
  align-items: center;
  height: 100%;
}

.header-right .space-x-3 > * + * {
  margin-left: 0.75rem;
}

/* 响应式头像调整 */
@media (max-width: 768px) {
  .user-menu {
    padding: 2px;
  }

  .user-avatar {
    width: 28px !important;
    height: 28px !important;
  }

  .online-indicator .ant-badge-dot {
    width: 6px !important;
    height: 6px !important;
  }
}

@media (max-width: 480px) {
  .header-right {
    gap: var(--spacing-sm);
  }

  .user-avatar {
    width: 24px !important;
    height: 24px !important;
  }

  .online-indicator .ant-badge-dot {
    width: 5px !important;
    height: 5px !important;
  }
}
