#!/usr/bin/env node

/**
 * 修复admin用户角色脚本
 * 检查并修复admin用户的角色设置
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAndFixAdminRole() {
  try {
    console.log('🔍 检查admin用户角色设置...\n');

    // 查找所有可能的admin用户
    const adminEmails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    console.log('📋 当前用户列表:');
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        emailVerified: true
      },
      orderBy: { id: 'asc' }
    });

    allUsers.forEach(user => {
      const isSystemUser = adminEmails.includes(user.email);
      console.log(`  ${user.id}: ${user.email} (${user.username}) - 角色: ${user.role} - 状态: ${user.isActive ? '活跃' : '禁用'} ${isSystemUser ? '🔧' : ''}`);
    });

    console.log('\n🔧 检查admin用户...');

    // 查找admin邮箱的用户
    const adminUsers = await prisma.user.findMany({
      where: {
        email: {
          in: adminEmails
        }
      }
    });

    if (adminUsers.length === 0) {
      console.log('❌ 未找到admin用户，需要创建admin用户');
      
      // 创建admin用户
      const adminUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          username: 'admin',
          password: '$2b$12$LQv3c1yqBWVHxkd0LQ4YCOuLQv3c1yqBWVHxkd0LQ4YCOuLQv3c1y', // admin123
          displayName: '系统管理员',
          role: 'admin',
          isActive: true,
          emailVerified: true,
          domainId: 1
        }
      });

      console.log(`✅ 已创建admin用户: ${adminUser.email} (ID: ${adminUser.id})`);
      console.log('🔑 默认密码: admin123 (请登录后立即修改)');
      return;
    }

    // 检查并修复admin用户的角色
    let fixedCount = 0;
    for (const user of adminUsers) {
      console.log(`\n检查用户: ${user.email}`);
      
      let needsUpdate = false;
      const updateData = {};

      // 检查角色
      if (user.role !== 'admin') {
        console.log(`  ⚠️  角色不正确: ${user.role} -> admin`);
        updateData.role = 'admin';
        needsUpdate = true;
      } else {
        console.log(`  ✅ 角色正确: ${user.role}`);
      }

      // 检查激活状态
      if (!user.isActive) {
        console.log(`  ⚠️  账户未激活 -> 激活`);
        updateData.isActive = true;
        needsUpdate = true;
      } else {
        console.log(`  ✅ 账户已激活`);
      }

      // 检查邮箱验证状态
      if (!user.emailVerified) {
        console.log(`  ⚠️  邮箱未验证 -> 验证`);
        updateData.emailVerified = true;
        needsUpdate = true;
      } else {
        console.log(`  ✅ 邮箱已验证`);
      }

      // 更新用户信息
      if (needsUpdate) {
        await prisma.user.update({
          where: { id: user.id },
          data: updateData
        });
        console.log(`  🔧 已修复用户: ${user.email}`);
        fixedCount++;
      } else {
        console.log(`  ✅ 用户配置正确，无需修复`);
      }
    }

    console.log(`\n📊 修复结果:`);
    console.log(`  - 检查了 ${adminUsers.length} 个admin用户`);
    console.log(`  - 修复了 ${fixedCount} 个用户`);

    // 验证修复结果
    console.log('\n🔍 验证修复结果...');
    const verifyAdmins = await prisma.user.findMany({
      where: {
        role: 'admin',
        isActive: true
      },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true,
        emailVerified: true
      }
    });

    console.log('✅ 当前活跃的管理员用户:');
    verifyAdmins.forEach(admin => {
      console.log(`  - ${admin.email} (${admin.username}) - ID: ${admin.id}`);
    });

    if (verifyAdmins.length === 0) {
      console.log('❌ 警告: 系统中没有活跃的管理员用户！');
    } else {
      console.log(`\n🎉 修复完成！系统中有 ${verifyAdmins.length} 个活跃的管理员用户。`);
      console.log('\n📝 使用说明:');
      console.log('1. 使用admin邮箱登录系统');
      console.log('2. 登录后应该能看到"管理员设置"菜单');
      console.log('3. 如果使用默认密码，请立即修改密码');
    }

  } catch (error) {
    console.error('❌ 修复过程中出现错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行修复脚本
if (require.main === module) {
  checkAndFixAdminRole().catch(console.error);
}

module.exports = { checkAndFixAdminRole };
