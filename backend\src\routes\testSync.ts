import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { AuthenticatedRequest } from '../types';
import logger from '../utils/logger';

const router = Router();

/**
 * 手动触发邮件同步检查（测试用）
 */
router.post('/check-emails', authenticate, async (req: AuthenticatedRequest, res) => {
  try {
    const userEmail = req.user?.email;
    if (!userEmail) {
      return res.status(400).json({ error: '用户邮箱不存在' });
    }

    logger.info(`🔧 API: 手动触发用户 ${userEmail} 邮件检查`);

    // 动态导入按需同步服务
    const { onDemandEmailSyncService } = await import('../services/onDemandEmailSyncService');
    
    // 手动触发检查
    await onDemandEmailSyncService.manualCheckNewEmails(userEmail);

    res.json({ 
      success: true, 
      message: `已触发用户 ${userEmail} 的邮件检查` 
    });

  } catch (error) {
    logger.error('手动邮件检查失败:', error);
    res.status(500).json({ 
      error: '邮件检查失败', 
      details: error instanceof Error ? error.message : '未知错误' 
    });
  }
});

/**
 * 获取同步服务状态
 */
router.get('/sync-status', authenticate, async (req: AuthenticatedRequest, res) => {
  try {
    // 动态导入按需同步服务
    const { onDemandEmailSyncService } = await import('../services/onDemandEmailSyncService');
    
    const status = onDemandEmailSyncService.getStatus();
    
    res.json({
      success: true,
      status
    });

  } catch (error) {
    logger.error('获取同步状态失败:', error);
    res.status(500).json({ 
      error: '获取状态失败', 
      details: error instanceof Error ? error.message : '未知错误' 
    });
  }
});

export default router;
