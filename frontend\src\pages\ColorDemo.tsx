import React from 'react';
import { Card, Button, Input, Alert, Tag, Badge, Avatar, Divider } from 'antd';
import { MailOutlined, UserOutlined, SettingOutlined, HeartOutlined } from '@ant-design/icons';

const ColorDemo: React.FC = () => {
  return (
    <div className="p-8 min-h-screen" style={{ background: 'linear-gradient(135deg, #FFF9E6 0%, #FFFDD0 50%, #FFF9E6 100%)' }}>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4" style={{ color: '#8B4513' }}>
            邮箱系统配色方案展示
          </h1>
          <p className="text-lg" style={{ color: '#A0522D' }}>
            温馨、舒适、可靠的邮箱界面设计
          </p>
        </div>

        {/* 配色板展示 */}
        <Card 
          title="配色板" 
          className="mb-8"
          style={{ 
            backgroundColor: 'rgba(255, 253, 208, 0.95)',
            border: '1px solid #D2B48C'
          }}
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div 
                className="w-20 h-20 rounded-lg mx-auto mb-2 shadow-md"
                style={{ backgroundColor: '#FFF9E6' }}
              ></div>
              <p className="text-sm font-medium" style={{ color: '#8B4513' }}>浅米黄色</p>
              <p className="text-xs" style={{ color: '#A0522D' }}>#FFF9E6</p>
            </div>
            <div className="text-center">
              <div 
                className="w-20 h-20 rounded-lg mx-auto mb-2 shadow-md"
                style={{ backgroundColor: '#FFFDD0' }}
              ></div>
              <p className="text-sm font-medium" style={{ color: '#8B4513' }}>奶油色</p>
              <p className="text-xs" style={{ color: '#A0522D' }}>#FFFDD0</p>
            </div>
            <div className="text-center">
              <div 
                className="w-20 h-20 rounded-lg mx-auto mb-2 shadow-md"
                style={{ backgroundColor: '#A0522D' }}
              ></div>
              <p className="text-sm font-medium" style={{ color: '#8B4513' }}>暖棕色</p>
              <p className="text-xs" style={{ color: '#A0522D' }}>#A0522D</p>
            </div>
            <div className="text-center">
              <div 
                className="w-20 h-20 rounded-lg mx-auto mb-2 shadow-md"
                style={{ backgroundColor: '#CD5C5C' }}
              ></div>
              <p className="text-sm font-medium" style={{ color: '#8B4513' }}>砖红色</p>
              <p className="text-xs" style={{ color: '#A0522D' }}>#CD5C5C</p>
            </div>
          </div>
        </Card>

        {/* 组件展示 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* 按钮组件 */}
          <Card 
            title="按钮组件" 
            style={{ 
              backgroundColor: 'rgba(255, 253, 208, 0.95)',
              border: '1px solid #D2B48C'
            }}
          >
            <div className="space-y-4">
              <Button type="primary" size="large" icon={<MailOutlined />}>
                主要按钮
              </Button>
              <br />
              <Button size="large">默认按钮</Button>
              <br />
              <Button type="dashed" size="large">虚线按钮</Button>
              <br />
              <Button type="text" size="large">文本按钮</Button>
            </div>
          </Card>

          {/* 输入框组件 */}
          <Card 
            title="输入框组件" 
            style={{ 
              backgroundColor: 'rgba(255, 253, 208, 0.95)',
              border: '1px solid #D2B48C'
            }}
          >
            <div className="space-y-4">
              <Input 
                placeholder="请输入邮箱地址" 
                prefix={<MailOutlined />}
                size="large"
              />
              <Input.Password 
                placeholder="请输入密码" 
                size="large"
              />
              <Input.TextArea 
                placeholder="请输入邮件内容" 
                rows={3}
              />
            </div>
          </Card>

          {/* 提示组件 */}
          <Card 
            title="提示组件" 
            style={{ 
              backgroundColor: 'rgba(255, 253, 208, 0.95)',
              border: '1px solid #D2B48C'
            }}
          >
            <div className="space-y-4">
              <Alert message="成功提示" type="success" showIcon />
              <Alert message="警告提示" type="warning" showIcon />
              <Alert message="错误提示" type="error" showIcon />
              <Alert message="信息提示" type="info" showIcon />
            </div>
          </Card>

          {/* 标签和徽章 */}
          <Card 
            title="标签和徽章" 
            style={{ 
              backgroundColor: 'rgba(255, 253, 208, 0.95)',
              border: '1px solid #D2B48C'
            }}
          >
            <div className="space-y-4">
              <div>
                <Tag color="default">默认标签</Tag>
                <Tag color="processing">处理中</Tag>
                <Tag color="success">成功</Tag>
                <Tag color="error">错误</Tag>
              </div>
              <div className="flex items-center gap-4">
                <Badge count={5}>
                  <Avatar shape="square" icon={<UserOutlined />} />
                </Badge>
                <Badge dot>
                  <MailOutlined style={{ fontSize: '20px', color: '#A0522D' }} />
                </Badge>
              </div>
            </div>
          </Card>
        </div>

        {/* 文字展示 */}
        <Card 
          title="文字层次" 
          className="mt-8"
          style={{ 
            backgroundColor: 'rgba(255, 253, 208, 0.95)',
            border: '1px solid #D2B48C'
          }}
        >
          <div className="space-y-4">
            <h1 style={{ color: '#8B4513' }}>一级标题 - 深棕色 (#8B4513)</h1>
            <h2 style={{ color: '#A0522D' }}>二级标题 - 暖棕色 (#A0522D)</h2>
            <h3 style={{ color: '#D2691E' }}>三级标题 - 橙棕色 (#D2691E)</h3>
            <p style={{ color: '#A0522D' }}>
              正文内容使用暖棕色，营造温馨舒适的阅读体验。这种配色方案传递出稳重、质朴的感觉，
              非常适合需要建立信任感和亲和力的邮箱系统。
            </p>
            <p style={{ color: '#D2B48C' }}>
              辅助文字使用较浅的棕色调，保持层次感的同时不会过于突出。
            </p>
          </div>
        </Card>

        {/* 特色展示 */}
        <Card 
          title="设计特色" 
          className="mt-8"
          style={{ 
            backgroundColor: 'rgba(255, 253, 208, 0.95)',
            border: '1px solid #D2B48C'
          }}
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center"
                style={{ background: 'linear-gradient(135deg, #A0522D 0%, #CD5C5C 100%)' }}
              >
                <HeartOutlined className="text-2xl text-white" />
              </div>
              <h4 style={{ color: '#8B4513' }}>温馨舒适</h4>
              <p style={{ color: '#A0522D' }}>米黄色背景营造温暖的使用环境</p>
            </div>
            <div className="text-center">
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center"
                style={{ background: 'linear-gradient(135deg, #A0522D 0%, #CD5C5C 100%)' }}
              >
                <SettingOutlined className="text-2xl text-white" />
              </div>
              <h4 style={{ color: '#8B4513' }}>稳重可靠</h4>
              <p style={{ color: '#A0522D' }}>暖棕色调传递专业可信的形象</p>
            </div>
            <div className="text-center">
              <div 
                className="w-16 h-16 rounded-full mx-auto mb-4 flex items-center justify-center"
                style={{ background: 'linear-gradient(135deg, #A0522D 0%, #CD5C5C 100%)' }}
              >
                <MailOutlined className="text-2xl text-white" />
              </div>
              <h4 style={{ color: '#8B4513' }}>亲和友好</h4>
              <p style={{ color: '#A0522D' }}>柔和的色彩搭配增强用户亲和力</p>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default ColorDemo;
