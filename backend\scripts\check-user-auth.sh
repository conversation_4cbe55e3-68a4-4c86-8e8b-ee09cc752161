#!/bin/bash

# 检查用户认证状态
# 使用方法: ./check-user-auth.sh [email]

EMAIL=${1:-"<EMAIL>"}

echo "🔍 检查用户认证状态: $EMAIL"
echo "================================"

# 检查数据库中的用户信息
echo "📋 检查数据库用户信息..."
mysql -u mailuser -pHOUsc@0202 -D mailserver -e "
SELECT 
    email,
    email_verified,
    is_mail_active,
    is_active,
    CASE 
        WHEN mail_password IS NOT NULL THEN 'SHA512-CRYPT密码已设置'
        ELSE '无邮件密码'
    END as password_status,
    created_at
FROM users 
WHERE email = '$EMAIL';
" 2>/dev/null || echo "❌ 无法连接数据库"

# 检查应用密码
echo -e "\n📱 检查应用密码..."
mysql -u mailuser -pHOUsc@0202 -D mailserver -e "
SELECT 
    purpose,
    is_active,
    created_at,
    last_used_at
FROM app_passwords ap
JOIN users u ON ap.user_id = u.id
WHERE u.email = '$EMAIL' AND ap.purpose = 'imap';
" 2>/dev/null || echo "❌ 无法查询应用密码"

# 测试Dovecot认证
echo -e "\n🔐 测试Dovecot认证..."
if command -v doveadm >/dev/null 2>&1; then
    echo "使用doveadm测试认证..."
    # 注意：这需要知道密码，仅用于测试
    echo "请手动运行: doveadm auth test $EMAIL [password]"
else
    echo "❌ doveadm命令不可用"
fi

# 检查Dovecot认证配置
echo -e "\n⚙️ 检查Dovecot认证配置..."
if [ -f /etc/dovecot/dovecot-sql.conf.ext ]; then
    echo "📄 认证查询配置:"
    grep -E "password_query|user_query" /etc/dovecot/dovecot-sql.conf.ext
else
    echo "❌ 找不到dovecot-sql.conf.ext文件"
fi

echo -e "\n💡 建议检查项目:"
echo "1. 确认用户邮件密码已设置"
echo "2. 检查用户账户状态（email_verified, is_mail_active, is_active）"
echo "3. 考虑使用应用密码进行IMAP认证"
echo "4. 检查Dovecot日志: tail -f /var/log/dovecot.log"
