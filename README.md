# 邮件系统项目

这是一个基于 React + Node.js + MySQL 的现代化邮件管理系统，支持完整的邮件收发、管理和自动化处理功能。

## ✨ 功能特性

- 📧 **邮件收发管理** - 完整的邮件收发功能
- 📁 **智能文件夹** - 自动分类和自定义文件夹
- 🔍 **高级搜索** - 全文搜索和筛选功能
- 📎 **附件支持** - 文件上传下载和预览
- 👥 **用户管理** - 多用户支持和权限控制
- 🔐 **安全认证** - JWT 认证和密码加密
- 🤖 **邮件规则** - 自动化邮件处理和分类
- 📊 **实时同步** - 与邮件服务器实时同步

## 🛠 技术栈

### 前端
- **React 18** - 现代化 UI 框架
- **TypeScript** - 类型安全
- **Ant Design** - 企业级 UI 组件库
- **Vite** - 快速构建工具

### 后端
- **Node.js** - 服务端运行时
- **Express** - Web 框架
- **TypeScript** - 类型安全
- **Prisma ORM** - 数据库 ORM
- **Socket.IO** - 实时通信

### 数据库
- **MySQL 8.0** - 关系型数据库

### 邮件服务器
- **Postfix** - SMTP 服务器
- **Dovecot** - IMAP/POP3 服务器
- **应用程序控制** - 邮件规则和自动化处理

### 部署
- **Docker** - 容器化部署
- **Docker Compose** - 多服务编排

## 🚀 快速开始

### 开发环境

1. **克隆项目**
```bash
git clone <repository-url>
cd email-system
```

2. **安装依赖**
```bash
# 安装前端依赖
cd frontend && npm install

# 安装后端依赖
cd ../backend && npm install
```

3. **配置环境变量**
```bash
# 复制环境变量模板
cp backend/.env.example backend/.env

# 编辑配置文件
nano backend/.env
```

4. **启动开发服务**
```bash
# 启动后端 (端口 3001)
cd backend && npm run dev

# 启动前端 (端口 3000)
cd frontend && npm run dev
```

### 生产部署

使用 Docker Compose 一键部署：

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

## 📁 项目结构

```
email-system/
├── frontend/              # 前端 React 应用
│   ├── src/              # 源代码
│   ├── public/           # 静态资源
│   └── dist/             # 构建输出
├── backend/               # 后端 Node.js API
│   ├── src/              # 源代码
│   ├── prisma/           # 数据库模式
│   └── dist/             # 构建输出
├── config/                # 邮件服务器配置
│   ├── postfix/          # Postfix 配置
│   ├── dovecot/          # Dovecot 配置
│   └── mysql/            # 数据库配置
├── docker/                # Docker 配置文件
├── docs/                  # 项目文档
├── scripts/               # 构建和部署脚本
├── database/              # 数据库迁移文件
└── README.md              # 项目说明

scripts/
├── docker-setup.sh           # 🔧 环境初始化
├── docker-start.sh           # 🚀 服务管理
├── docker-monitor.sh         # 📊 服务监控
├── docker-cleanup.sh         # 🗑️ 资源清理
├── deploy.sh                 # 🚀 通用部署
├── deploy-prod.sh            # 🏭 生产部署
├── build.sh                  # 🔨 构建脚本
├── backup.sh                 # 💾 备份脚本
├── update.sh                 # 🔄 更新脚本
├── env-manager.sh            # ⚙️ 环境管理
├── pm2-manage.sh             # 📋 进程管理
├── analyze-bundle.js         # 📈 包分析
├── deploy-client-rules.sh    # 📧 客户端规则
├── setup-email-rule-templates.sh        # 📧 邮件规则
├── setup-email-rule-templates-prisma.sh # 📧 Prisma规则
├── init-mail-domains.js      # 📧 域名初始化
└── init-mail-domains.ts      # 📧 域名初始化(TS)
```

## 📚 文档

- [项目总结](docs/PROJECT_SUMMARY.md) - 项目概述和架构
- [部署指南](docs/deployment.md) - 详细部署说明
- [邮件服务器状态](docs/MAIL_SERVER_STATUS.md) - 服务器配置状态
- [需求文档](docs/需求文档.md) - 功能需求说明
- [开发任务](docs/剩余开发任务清单.md) - 开发进度跟踪
- [项目清理总结](docs/PROJECT_CLEANUP_FINAL.md) - 最新的项目清理记录

## ⚙️ 配置说明

### 邮件服务器配置

项目采用 **应用程序控制** 的邮件处理机制：

- **Postfix BCC** - 自动创建发件副本
- **应用程序规则** - 智能邮件分类和移动
- **Sieve 脚本已禁用** - 避免冲突，由应用程序完全控制

详细配置请参考 [config/CURRENT_CONFIGURATION_STATUS.md](config/CURRENT_CONFIGURATION_STATUS.md)

### 环境变量

主要环境变量配置：

```env
# 数据库配置
DATABASE_URL=mysql://user:password@localhost:3306/mailserver

# 邮件服务器配置
SMTP_HOST=mail.example.com
IMAP_HOST=mail.example.com
SMTP_USER=<EMAIL>
SMTP_PASS=password

# 应用配置
JWT_SECRET=your-secret-key
FRONTEND_URL=http://localhost:3000
```

## 🔧 开发

### 本地开发

```bash
# 启动开发环境
npm run dev

# 运行测试
npm run test

# 构建项目
npm run build
```

### 代码规范

- **ESLint** - 代码质量检查
- **Prettier** - 代码格式化
- **TypeScript** - 类型检查

## 🚀 部署

### Docker 部署

```bash
# 生产环境部署
docker-compose -f docker-compose.prod.yml up -d

# 开发环境部署
docker-compose up -d
```

### 手动部署

详细的手动部署步骤请参考 [部署文档](docs/deployment.md)。

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请查看 [文档](docs/) 或提交 Issue。
