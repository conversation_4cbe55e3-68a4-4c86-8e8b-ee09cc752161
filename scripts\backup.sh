#!/bin/bash

# 邮箱系统备份脚本
# 使用方法: ./scripts/backup.sh [type]
# type: full (默认) | db | files

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
BACKUP_DIR="/var/backups/email-system"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RETENTION_DAYS=30

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建备份目录
create_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        sudo mkdir -p "$BACKUP_DIR"
        sudo chown $USER:$USER "$BACKUP_DIR"
    fi
    
    local today_dir="$BACKUP_DIR/$(date +%Y%m%d)"
    if [ ! -d "$today_dir" ]; then
        mkdir -p "$today_dir"
    fi
    
    echo "$today_dir"
}

# 备份数据库
backup_database() {
    log_info "备份数据库..."
    
    local backup_dir=$1
    local db_backup_file="$backup_dir/database_$TIMESTAMP.sql"
    
    # 从环境变量获取数据库配置
    source .env
    
    docker exec email-system-db-prod mysqldump \
        -u root \
        -p"$DB_ROOT_PASSWORD" \
        --single-transaction \
        --routines \
        --triggers \
        "$DB_NAME" > "$db_backup_file"
    
    # 压缩备份文件
    gzip "$db_backup_file"
    
    log_success "数据库备份完成: ${db_backup_file}.gz"
}

# 备份文件
backup_files() {
    log_info "备份文件..."
    
    local backup_dir=$1
    local files_backup_file="$backup_dir/files_$TIMESTAMP.tar.gz"
    
    # 备份上传文件和日志
    tar -czf "$files_backup_file" \
        -C /var/lib/email-system uploads \
        -C /var/log email-system \
        2>/dev/null || true
    
    log_success "文件备份完成: $files_backup_file"
}

# 备份配置
backup_config() {
    log_info "备份配置文件..."
    
    local backup_dir=$1
    local config_backup_file="$backup_dir/config_$TIMESTAMP.tar.gz"
    
    # 备份配置文件（排除敏感信息）
    tar -czf "$config_backup_file" \
        docker/ \
        scripts/ \
        docker-compose*.yml \
        .env.example \
        2>/dev/null || true
    
    log_success "配置备份完成: $config_backup_file"
}

# 上传到云存储 (可选)
upload_to_cloud() {
    local backup_dir=$1
    
    if [ -n "$BACKUP_S3_BUCKET" ] && [ -n "$AWS_ACCESS_KEY_ID" ]; then
        log_info "上传备份到 S3..."
        
        aws s3 sync "$backup_dir" "s3://$BACKUP_S3_BUCKET/email-system/$(basename $backup_dir)/" \
            --delete \
            --storage-class STANDARD_IA
        
        log_success "备份已上传到 S3"
    else
        log_warning "S3 配置未设置，跳过云备份"
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理 $RETENTION_DAYS 天前的备份..."
    
    find "$BACKUP_DIR" -type d -name "????????" -mtime +$RETENTION_DAYS -exec rm -rf {} \; 2>/dev/null || true
    
    log_success "旧备份清理完成"
}

# 验证备份
verify_backup() {
    local backup_dir=$1
    
    log_info "验证备份文件..."
    
    local files_count=$(find "$backup_dir" -name "*_$TIMESTAMP.*" | wc -l)
    
    if [ $files_count -gt 0 ]; then
        log_success "备份验证通过，共 $files_count 个文件"
        
        # 显示备份文件大小
        du -sh "$backup_dir"/*_$TIMESTAMP.* 2>/dev/null || true
    else
        log_error "备份验证失败"
        return 1
    fi
}

# 发送通知 (可选)
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"邮箱系统备份 - $status: $message\"}" \
            2>/dev/null || true
    fi
}

# 完整备份
full_backup() {
    local backup_dir=$(create_backup_dir)
    
    log_info "开始完整备份..."
    
    backup_database "$backup_dir"
    backup_files "$backup_dir"
    backup_config "$backup_dir"
    
    if verify_backup "$backup_dir"; then
        upload_to_cloud "$backup_dir"
        cleanup_old_backups
        send_notification "成功" "完整备份完成"
        log_success "完整备份完成: $backup_dir"
    else
        send_notification "失败" "备份验证失败"
        return 1
    fi
}

# 仅数据库备份
db_backup() {
    local backup_dir=$(create_backup_dir)
    
    log_info "开始数据库备份..."
    
    backup_database "$backup_dir"
    
    if verify_backup "$backup_dir"; then
        send_notification "成功" "数据库备份完成"
        log_success "数据库备份完成: $backup_dir"
    else
        send_notification "失败" "数据库备份验证失败"
        return 1
    fi
}

# 仅文件备份
files_backup() {
    local backup_dir=$(create_backup_dir)
    
    log_info "开始文件备份..."
    
    backup_files "$backup_dir"
    backup_config "$backup_dir"
    
    if verify_backup "$backup_dir"; then
        send_notification "成功" "文件备份完成"
        log_success "文件备份完成: $backup_dir"
    else
        send_notification "失败" "文件备份验证失败"
        return 1
    fi
}

# 主函数
main() {
    local backup_type=${1:-full}
    
    log_info "开始备份 (类型: $backup_type)"
    
    case $backup_type in
        "full")
            full_backup
            ;;
        "db")
            db_backup
            ;;
        "files")
            files_backup
            ;;
        *)
            log_error "未知的备份类型: $backup_type"
            log_info "支持的类型: full, db, files"
            exit 1
            ;;
    esac
}

# 脚本入口
main "$@"
