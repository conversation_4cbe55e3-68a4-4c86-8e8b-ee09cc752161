#!/bin/bash

# 获取用户的邮件密码信息
# 使用方法: ./get-mail-password.sh [email]

EMAIL=${1:-"<EMAIL>"}

echo "🔍 查询用户邮件认证信息: $EMAIL"
echo "================================"

# 查询用户邮件密码状态
echo "📋 用户账户状态:"
mysql -u mailuser -pHOUsc@0202 -D mailserver -e "
SELECT 
    email,
    email_verified as '邮箱已验证',
    is_mail_active as '邮件功能激活',
    is_active as '账户激活',
    CASE 
        WHEN mail_password IS NOT NULL THEN '已设置'
        ELSE '未设置'
    END as '邮件密码状态',
    created_at as '创建时间'
FROM users 
WHERE email = '$EMAIL';
" 2>/dev/null

# 查询应用密码
echo -e "\n📱 应用密码状态:"
mysql -u mailuser -pHOUsc@0202 -D mailserver -e "
SELECT 
    ap.purpose as '用途',
    ap.is_active as '是否激活',
    ap.password as '应用密码',
    ap.created_at as '创建时间',
    ap.last_used_at as '最后使用'
FROM app_passwords ap
JOIN users u ON ap.user_id = u.id
WHERE u.email = '$EMAIL' AND ap.purpose = 'imap'
ORDER BY ap.created_at DESC;
" 2>/dev/null

echo -e "\n💡 IMAP认证选项:"
echo "1. 使用应用密码（推荐）"
echo "2. 重置邮件密码"
echo "3. 创建新的IMAP应用密码"

echo -e "\n🔧 测试命令示例:"
echo "# 如果有应用密码，使用应用密码:"
echo "IMAP_PASS='应用密码' node backend/scripts/test-idle-support.js"
echo ""
echo "# 或者重置邮件密码后使用新密码:"
echo "IMAP_PASS='新密码' node backend/scripts/test-idle-support.js"
