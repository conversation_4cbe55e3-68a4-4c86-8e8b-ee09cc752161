import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Space,
  message,
  Divider,
  Typography,
  Row,
  Col,
  Select,
  Modal,
  List,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  EyeOutlined
} from '@ant-design/icons';
import RichTextEditor from '../../components/RichTextEditor';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface EmailSignature {
  id: string;
  name: string;
  content: string;
  isDefault: boolean;
  isEnabled: boolean;
  createdAt: string;
  updatedAt: string;
}

const EmailSignaturePage: React.FC = () => {
  const [form] = Form.useForm();
  const [signatures, setSignatures] = useState<EmailSignature[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingSignature, setEditingSignature] = useState<EmailSignature | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewContent, setPreviewContent] = useState('');

  // 模拟数据 - 实际应该从API获取
  useEffect(() => {
    loadSignatures();
  }, []);

  const loadSignatures = async () => {
    setLoading(true);
    try {
      // 模拟API调用
      const mockSignatures: EmailSignature[] = [
        {
          id: '1',
          name: '默认签名',
          content: `
            <div style="font-family: Arial, sans-serif; color: #333;">
              <p>此致<br/>敬礼！</p>
              <hr style="border: none; border-top: 1px solid #ddd; margin: 10px 0;">
              <p><strong>张三</strong><br/>
              高级软件工程师<br/>
              ABC科技有限公司<br/>
              电话：+86 138-0000-0000<br/>
              邮箱：<EMAIL><br/>
              地址：北京市朝阳区xxx路xxx号</p>
            </div>
          `,
          isDefault: true,
          isEnabled: true,
          createdAt: '2024-01-01',
          updatedAt: '2024-01-01'
        },
        {
          id: '2',
          name: '简洁签名',
          content: `
            <div style="font-family: Arial, sans-serif; color: #666;">
              <p>Best regards,<br/>张三</p>
            </div>
          `,
          isDefault: false,
          isEnabled: true,
          createdAt: '2024-01-02',
          updatedAt: '2024-01-02'
        }
      ];
      setSignatures(mockSignatures);
    } catch (error) {
      message.error('加载签名失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSignature = () => {
    setEditingSignature(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  const handleEditSignature = (signature: EmailSignature) => {
    setEditingSignature(signature);
    form.setFieldsValue({
      name: signature.name,
      content: signature.content,
      isEnabled: signature.isEnabled
    });
    setIsModalVisible(true);
  };

  const handleSaveSignature = async (values: any) => {
    setLoading(true);
    try {
      if (editingSignature) {
        // 更新签名
        const updatedSignatures = signatures.map(sig =>
          sig.id === editingSignature.id
            ? { ...sig, ...values, updatedAt: new Date().toISOString() }
            : sig
        );
        setSignatures(updatedSignatures);
        message.success('签名更新成功');
      } else {
        // 创建新签名
        const newSignature: EmailSignature = {
          id: Date.now().toString(),
          name: values.name,
          content: values.content,
          isDefault: signatures.length === 0, // 如果是第一个签名，设为默认
          isEnabled: values.isEnabled ?? true,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        setSignatures([...signatures, newSignature]);
        message.success('签名创建成功');
      }
      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      message.error('保存签名失败');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteSignature = async (id: string) => {
    try {
      const updatedSignatures = signatures.filter(sig => sig.id !== id);
      setSignatures(updatedSignatures);
      message.success('签名删除成功');
    } catch (error) {
      message.error('删除签名失败');
    }
  };

  const handleSetDefault = async (id: string) => {
    try {
      const updatedSignatures = signatures.map(sig => ({
        ...sig,
        isDefault: sig.id === id
      }));
      setSignatures(updatedSignatures);
      message.success('默认签名设置成功');
    } catch (error) {
      message.error('设置默认签名失败');
    }
  };

  const handleToggleEnabled = async (id: string, enabled: boolean) => {
    try {
      const updatedSignatures = signatures.map(sig =>
        sig.id === id ? { ...sig, isEnabled: enabled } : sig
      );
      setSignatures(updatedSignatures);
      message.success(enabled ? '签名已启用' : '签名已禁用');
    } catch (error) {
      message.error('更新签名状态失败');
    }
  };

  const handlePreview = (content: string) => {
    setPreviewContent(content);
    setPreviewVisible(true);
  };

  return (
    <>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
          <Title level={3} style={{ margin: 0 }}>邮件签名管理</Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleCreateSignature}
              >
                创建签名
              </Button>
            </div>

            <List
              loading={loading}
              dataSource={signatures}
              renderItem={(signature) => (
                <List.Item
                  actions={[
                    <Button
                      type="text"
                      icon={<EyeOutlined />}
                      onClick={() => handlePreview(signature.content)}
                    >
                      预览
                    </Button>,
                    <Button
                      type="text"
                      icon={<EditOutlined />}
                      onClick={() => handleEditSignature(signature)}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      title="确定要删除这个签名吗？"
                      onConfirm={() => handleDeleteSignature(signature.id)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="text"
                        danger
                        icon={<DeleteOutlined />}
                        disabled={signature.isDefault}
                      >
                        删除
                      </Button>
                    </Popconfirm>
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space>
                        <span>{signature.name}</span>
                        {signature.isDefault && (
                          <span style={{ color: '#1890ff', fontSize: '12px' }}>默认</span>
                        )}
                      </Space>
                    }
                    description={
                      <Space direction="vertical" size="small">
                        <Text type="secondary">
                          创建时间：{new Date(signature.createdAt).toLocaleString()}
                        </Text>
                        <Space>
                          <span>状态：</span>
                          <Switch
                            size="small"
                            checked={signature.isEnabled}
                            onChange={(checked) => handleToggleEnabled(signature.id, checked)}
                          />
                          <span>{signature.isEnabled ? '已启用' : '已禁用'}</span>
                        </Space>
                        {!signature.isDefault && (
                          <Button
                            type="link"
                            size="small"
                            onClick={() => handleSetDefault(signature.id)}
                          >
                            设为默认
                          </Button>
                        )}
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
        </Card>

        {/* 创建/编辑签名模态框 */}
      <Modal
        title={editingSignature ? '编辑签名' : '创建签名'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={800}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveSignature}
        >
          <Form.Item
            name="name"
            label="签名名称"
            rules={[{ required: true, message: '请输入签名名称' }]}
          >
            <Input placeholder="请输入签名名称" />
          </Form.Item>

          <Form.Item
            name="content"
            label="签名内容"
            rules={[{ required: true, message: '请输入签名内容' }]}
          >
            <RichTextEditor
              placeholder="请输入签名内容..."
              height={300}
            />
          </Form.Item>

          <Form.Item
            name="isEnabled"
            label="启用状态"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button
                type="primary"
                htmlType="submit"
                icon={<SaveOutlined />}
                loading={loading}
              >
                保存
              </Button>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 预览模态框 */}
      <Modal
        title="签名预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        <div
          style={{
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            padding: '16px',
            backgroundColor: '#fafafa'
          }}
          dangerouslySetInnerHTML={{ __html: previewContent }}
        />
      </Modal>
    </>
  );
};

export default EmailSignaturePage;
