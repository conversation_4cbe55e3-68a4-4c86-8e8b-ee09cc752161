-- 增强邮箱账户表，添加OAuth2支持和连接状态监控

-- 添加OAuth2支持字段
ALTER TABLE `email_accounts` ADD COLUMN `auth_type` VARCHAR(191) NOT NULL DEFAULT 'password' COMMENT 'password, oauth2';
ALTER TABLE `email_accounts` ADD COLUMN `oauth_provider` VARCHAR(191) NULL COMMENT 'gmail, outlook, yahoo';
ALTER TABLE `email_accounts` ADD COLUMN `oauth_access_token` TEXT NULL;
ALTER TABLE `email_accounts` ADD COLUMN `oauth_refresh_token` TEXT NULL;
ALTER TABLE `email_accounts` ADD COLUMN `oauth_token_expiry` TIMESTAMP(0) NULL;

-- 添加连接状态监控字段
ALTER TABLE `email_accounts` ADD COLUMN `connection_status` VARCHAR(191) NOT NULL DEFAULT 'unknown' COMMENT 'connected, disconnected, error, unknown';
ALTER TABLE `email_accounts` ADD COLUMN `last_connection_test` TIMESTAMP(0) NULL;
ALTER TABLE `email_accounts` ADD COLUMN `connection_error` TEXT NULL;

-- 添加同步状态字段
ALTER TABLE `email_accounts` ADD COLUMN `sync_status` VARCHAR(191) NOT NULL DEFAULT 'idle' COMMENT 'idle, syncing, error';
ALTER TABLE `email_accounts` ADD COLUMN `sync_error` TEXT NULL;

-- 添加自动配置支持字段
ALTER TABLE `email_accounts` ADD COLUMN `auto_configured` BOOLEAN NOT NULL DEFAULT false;
ALTER TABLE `email_accounts` ADD COLUMN `provider` VARCHAR(191) NULL COMMENT 'gmail, outlook, yahoo, custom';

-- 添加新的索引
CREATE INDEX `idx_user_default` ON `email_accounts` (`user_id`, `is_default`);
CREATE INDEX `idx_connection_status` ON `email_accounts` (`connection_status`);
CREATE INDEX `idx_sync_status` ON `email_accounts` (`sync_status`);
