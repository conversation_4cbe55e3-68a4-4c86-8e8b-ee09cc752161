{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "build:analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "clean": "<PERSON><PERSON><PERSON> dist", "start": "npm run preview", "rebuild-native": "npm rebuild && npm run clean-platform-specific", "clean-platform-specific": "node scripts/clean-platform-deps.js", "deploy:setup": "npm run clean && npm ci --production=false && npm run build", "deploy:ubuntu": "rm -rf node_modules package-lock.json && npm install && npm run build"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@hookform/resolvers": "^5.1.1", "@tanstack/react-query": "^5.80.7", "@types/dompurify": "^3.0.5", "antd": "^5.26.1", "axios": "^1.10.0", "dompurify": "^3.2.6", "lucide-react": "^0.517.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.58.1", "react-router-dom": "^7.6.2", "socket.io-client": "^4.8.1", "tailwindcss": "^4.1.10", "yup": "^1.6.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.4.2", "@testing-library/react": "^16.0.0", "@testing-library/user-event": "^14.5.2", "@types/draft-js": "^0.11.18", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-draft-wysiwyg": "^1.13.8", "@vitejs/plugin-react": "^4.4.1", "@vitest/coverage-v8": "^1.3.1", "@vitest/ui": "^1.3.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^24.0.0", "postcss": "^8.5.6", "rimraf": "^5.0.5", "terser": "^5.27.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-bundle-analyzer": "^0.7.0", "vitest": "^1.3.1"}}