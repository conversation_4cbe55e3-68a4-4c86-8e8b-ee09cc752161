import React from 'react';
import { But<PERSON>, Space, Dropdown, message } from 'antd';
import {
  DeleteOutlined,
  ReplyOutlined,
  ShareAltOutlined,
  CheckOutlined,
  MailOutlined,
  StarOutlined,
  TagOutlined,
  DownOutlined
} from '@ant-design/icons';
import type { Email } from '../types/email';
import './EmailActionButtons.css';

interface EmailActionButtonsProps {
  // 当前选中的邮件（单个操作时使用）
  selectedEmail?: Email | null;
  
  // 批量选中的邮件ID列表
  selectedEmailIds: string[];
  
  // 所有邮件列表（用于批量操作）
  emails: Email[];
  
  // 操作回调函数
  onDelete: (emailIds: string[]) => void;
  onReply: (email: Email) => void;
  onForward: (email: Email) => void;
  onMarkAllRead: () => void;
  onMarkAsRead: (emailIds: string[]) => void;
  onMarkAsUnread: (emailIds: string[]) => void;
  onToggleStar: (emailIds: string[]) => void;
  onAddLabel: (emailIds: string[], label: string) => void;
  
  // 是否为分栏模式
  isSplitMode?: boolean;
}

const EmailActionButtons: React.FC<EmailActionButtonsProps> = ({
  selectedEmail,
  selectedEmailIds,
  emails,
  onDelete,
  onReply,
  onForward,
  onMarkAllRead,
  onMarkAsRead,
  onMarkAsUnread,
  onToggleStar,
  onAddLabel,
  isSplitMode = false
}) => {
  // 判断是否有选中的邮件
  const hasSelection = selectedEmailIds.length > 0;
  const isBatchOperation = selectedEmailIds.length > 1;
  
  // 获取当前操作的邮件
  const getTargetEmails = (): Email[] => {
    if (hasSelection) {
      return emails.filter(email => selectedEmailIds.includes(email.id));
    } else if (selectedEmail) {
      return [selectedEmail];
    }
    return [];
  };

  const targetEmails = getTargetEmails();
  const targetEmailIds = targetEmails.map(email => email.id);

  // 删除操作
  const handleDelete = () => {
    if (targetEmailIds.length === 0) {
      message.warning('请先选择要删除的邮件');
      return;
    }
    onDelete(targetEmailIds);
  };

  // 回复操作（仅支持单个邮件）
  const handleReply = () => {
    if (!selectedEmail && targetEmails.length !== 1) {
      message.warning('回复操作只能对单个邮件进行');
      return;
    }
    const email = selectedEmail || targetEmails[0];
    onReply(email);
  };

  // 转发操作（仅支持单个邮件）
  const handleForward = () => {
    if (!selectedEmail && targetEmails.length !== 1) {
      message.warning('转发操作只能对单个邮件进行');
      return;
    }
    const email = selectedEmail || targetEmails[0];
    onForward(email);
  };

  // 标记为已读
  const handleMarkAsRead = () => {
    if (targetEmailIds.length === 0) {
      message.warning('请先选择要标记的邮件');
      return;
    }
    onMarkAsRead(targetEmailIds);
  };

  // 标记为未读
  const handleMarkAsUnread = () => {
    if (targetEmailIds.length === 0) {
      message.warning('请先选择要标记的邮件');
      return;
    }
    onMarkAsUnread(targetEmailIds);
  };

  // 切换星标
  const handleToggleStar = () => {
    if (targetEmailIds.length === 0) {
      message.warning('请先选择要标记的邮件');
      return;
    }
    onToggleStar(targetEmailIds);
  };

  // 标记为下拉菜单项
  const markAsMenuItems = [
    {
      key: 'read',
      label: '已读',
      icon: <CheckOutlined />,
      onClick: handleMarkAsRead,
    },
    {
      key: 'unread',
      label: '未读',
      icon: <MailOutlined />,
      onClick: handleMarkAsUnread,
    },
    {
      key: 'starred',
      label: '星标',
      icon: <StarOutlined />,
      onClick: handleToggleStar,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'custom-label',
      label: '自定义标签',
      icon: <TagOutlined />,
      onClick: () => {
        // TODO: 实现自定义标签功能
        message.info('自定义标签功能开发中...');
      },
    },
  ];

  return (
    <div className={`email-action-buttons ${isSplitMode ? 'split-mode' : 'list-mode'}`}>
      <Space size="middle">
        {/* 删除按钮 */}
        <Button
          type="text"
          icon={<DeleteOutlined />}
          onClick={handleDelete}
          disabled={targetEmailIds.length === 0}
          danger
        >
          删除
        </Button>

        {/* 回复按钮 - 只在单个邮件时显示 */}
        <Button
          type="text"
          icon={<ReplyOutlined />}
          onClick={handleReply}
          disabled={!selectedEmail && targetEmails.length !== 1}
        >
          回复
        </Button>

        {/* 转发按钮 - 只在单个邮件时显示 */}
        <Button
          type="text"
          icon={<ShareAltOutlined />}
          onClick={handleForward}
          disabled={!selectedEmail && targetEmails.length !== 1}
        >
          转发
        </Button>

        {/* 全部已读按钮 */}
        <Button
          type="text"
          icon={<CheckOutlined />}
          onClick={onMarkAllRead}
        >
          全部已读
        </Button>

        {/* 标记为下拉菜单 */}
        <Dropdown
          menu={{ items: markAsMenuItems }}
          trigger={['click']}
          disabled={targetEmailIds.length === 0}
        >
          <Button type="text">
            标记为 <DownOutlined />
          </Button>
        </Dropdown>
      </Space>
    </div>
  );
};

export default EmailActionButtons;
