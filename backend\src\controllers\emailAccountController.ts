import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import {
  testEmailConnection,
  testEmailConnectionDetailed,
  updateConnectionStatus,
  updateSyncStatus,
  autoConfigureAccount,
  getUserActiveAccounts
} from '../services/emailAccountService';
import { getSupportedProviders, detectEmailProvider } from '../services/emailProviderService';
import multiAccountSyncManager from '../services/multiAccountSyncManager';
import logger from '../utils/logger';

// 获取用户的邮箱账户列表
export const getEmailAccounts = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  const accounts = await prisma.emailAccount.findMany({
    where: { userId },
    select: {
      id: true,
      name: true,
      email: true,
      displayName: true,
      imapHost: true,
      imapPort: true,
      imapSecure: true,
      smtpHost: true,
      smtpPort: true,
      smtpSecure: true,
      syncEnabled: true,
      syncInterval: true,
      lastSyncAt: true,
      isActive: true,
      isDefault: true,
      signature: true,
      replyToEmail: true,
      maxEmailsPerSync: true,
      createdAt: true,
      updatedAt: true,
    },
    orderBy: [
      { isDefault: 'desc' },
      { createdAt: 'asc' },
    ],
  });

  const response: ApiResponse = {
    success: true,
    message: '获取邮箱账户列表成功',
    data: accounts,
  };

  res.json(response);
};

// 获取邮箱账户详情
export const getEmailAccountById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const account = await prisma.emailAccount.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
    select: {
      id: true,
      name: true,
      email: true,
      displayName: true,
      imapHost: true,
      imapPort: true,
      imapSecure: true,
      imapUsername: true,
      smtpHost: true,
      smtpPort: true,
      smtpSecure: true,
      smtpUsername: true,
      syncEnabled: true,
      syncInterval: true,
      lastSyncAt: true,
      isActive: true,
      isDefault: true,
      signature: true,
      replyToEmail: true,
      maxEmailsPerSync: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!account) {
    throw new AppError('邮箱账户不存在', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: '获取邮箱账户详情成功',
    data: account,
  };

  res.json(response);
};

// 创建邮箱账户
export const createEmailAccount = async (req: AuthenticatedRequest, res: Response) => {
  const {
    name,
    email,
    displayName,
    imapHost,
    imapPort,
    imapSecure,
    imapUsername,
    imapPassword,
    smtpHost,
    smtpPort,
    smtpSecure,
    smtpUsername,
    smtpPassword,
    syncEnabled = true,
    syncInterval = 5,
    isDefault = false,
    signature,
    replyToEmail,
    maxEmailsPerSync = 50,
  } = req.body;

  const userId = req.user!.id;

  // 检查邮箱地址是否已存在
  const existingAccount = await prisma.emailAccount.findFirst({
    where: {
      userId,
      email,
    },
  });

  if (existingAccount) {
    throw new AppError('该邮箱账户已存在', 409);
  }

  // 测试邮箱连接
  try {
    await testEmailConnection({
      imapHost,
      imapPort,
      imapSecure,
      imapUsername,
      imapPassword,
      smtpHost,
      smtpPort,
      smtpSecure,
      smtpUsername,
      smtpPassword,
    });
  } catch (error) {
    throw new AppError(`邮箱连接测试失败: ${(error as Error).message}`, 400);
  }

  // 如果设置为默认账户，先取消其他默认账户
  if (isDefault) {
    await prisma.emailAccount.updateMany({
      where: {
        userId,
        isDefault: true,
      },
      data: {
        isDefault: false,
      },
    });
  }

  // 加密密码
  const encryptedImapPassword = encrypt(imapPassword);
  const encryptedSmtpPassword = encrypt(smtpPassword);

  const account = await prisma.emailAccount.create({
    data: {
      userId,
      name,
      email,
      displayName,
      imapHost,
      imapPort,
      imapSecure,
      imapUsername,
      imapPassword: encryptedImapPassword,
      smtpHost,
      smtpPort,
      smtpSecure,
      smtpUsername,
      smtpPassword: encryptedSmtpPassword,
      syncEnabled,
      syncInterval,
      isDefault,
      signature,
      replyToEmail,
      maxEmailsPerSync,
    },
    select: {
      id: true,
      name: true,
      email: true,
      displayName: true,
      imapHost: true,
      imapPort: true,
      imapSecure: true,
      smtpHost: true,
      smtpPort: true,
      smtpSecure: true,
      syncEnabled: true,
      syncInterval: true,
      isActive: true,
      isDefault: true,
      signature: true,
      replyToEmail: true,
      maxEmailsPerSync: true,
      createdAt: true,
    },
  });

  logger.info(`邮箱账户创建成功: ${email} (用户: ${userId})`);

  // 启动邮箱账户同步
  try {
    await multiAccountSyncManager.onAccountCreated(account.id);
  } catch (error) {
    logger.error(`启动新创建账户 ${account.id} 的同步失败:`, error);
  }

  const response: ApiResponse = {
    success: true,
    message: '邮箱账户创建成功',
    data: account,
  };

  res.status(201).json(response);
};

// 更新邮箱账户
export const updateEmailAccount = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;
  const updateData = req.body;

  // 检查账户是否存在
  const existingAccount = await prisma.emailAccount.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingAccount) {
    throw new AppError('邮箱账户不存在', 404);
  }

  // 如果更新密码，需要加密
  if (updateData.imapPassword) {
    updateData.imapPassword = encrypt(updateData.imapPassword);
  }
  if (updateData.smtpPassword) {
    updateData.smtpPassword = encrypt(updateData.smtpPassword);
  }

  // 如果设置为默认账户，先取消其他默认账户
  if (updateData.isDefault) {
    await prisma.emailAccount.updateMany({
      where: {
        userId,
        isDefault: true,
        id: { not: parseInt(id) },
      },
      data: {
        isDefault: false,
      },
    });
  }

  const account = await prisma.emailAccount.update({
    where: { id: parseInt(id) },
    data: updateData,
    select: {
      id: true,
      name: true,
      email: true,
      displayName: true,
      imapHost: true,
      imapPort: true,
      imapSecure: true,
      smtpHost: true,
      smtpPort: true,
      smtpSecure: true,
      syncEnabled: true,
      syncInterval: true,
      isActive: true,
      isDefault: true,
      signature: true,
      replyToEmail: true,
      maxEmailsPerSync: true,
      updatedAt: true,
    },
  });

  // 处理邮箱账户同步
  try {
    await multiAccountSyncManager.onAccountUpdated(parseInt(id));
  } catch (error) {
    logger.error(`处理更新账户 ${id} 的同步失败:`, error);
  }

  const response: ApiResponse = {
    success: true,
    message: '邮箱账户更新成功',
    data: account,
  };

  res.json(response);
};

// 删除邮箱账户
export const deleteEmailAccount = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const account = await prisma.emailAccount.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!account) {
    throw new AppError('邮箱账户不存在', 404);
  }

  // 检查是否有关联的邮件
  const emailCount = await prisma.email.count({
    where: {
      accountId: parseInt(id),
      userId,
    },
  });

  if (emailCount > 0) {
    throw new AppError('该账户下还有邮件，无法删除。请先处理相关邮件。', 400);
  }

  // 停止邮箱账户同步
  multiAccountSyncManager.onAccountDeleted(parseInt(id));

  await prisma.emailAccount.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '邮箱账户删除成功',
  };

  res.json(response);
};

// 测试邮箱连接
export const testConnection = async (req: AuthenticatedRequest, res: Response) => {
  const {
    imapHost,
    imapPort,
    imapSecure,
    imapUsername,
    imapPassword,
    smtpHost,
    smtpPort,
    smtpSecure,
    smtpUsername,
    smtpPassword,
  } = req.body;

  try {
    await testEmailConnection({
      imapHost,
      imapPort,
      imapSecure,
      imapUsername,
      imapPassword,
      smtpHost,
      smtpPort,
      smtpSecure,
      smtpUsername,
      smtpPassword,
    });

    const response: ApiResponse = {
      success: true,
      message: '邮箱连接测试成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`连接测试失败: ${(error as Error).message}`, 400);
  }
};

// 详细连接测试
export const testConnectionDetailed = async (req: AuthenticatedRequest, res: Response) => {
  const {
    imapHost,
    imapPort,
    imapSecure,
    imapUsername,
    imapPassword,
    smtpHost,
    smtpPort,
    smtpSecure,
    smtpUsername,
    smtpPassword,
  } = req.body;

  try {
    const testResult = await testEmailConnectionDetailed({
      imapHost,
      imapPort,
      imapSecure,
      imapUsername,
      imapPassword,
      smtpHost,
      smtpPort,
      smtpSecure,
      smtpUsername,
      smtpPassword,
    });

    const response: ApiResponse = {
      success: testResult.success,
      message: testResult.success ? '邮箱连接测试成功' : '邮箱连接测试失败',
      data: testResult,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`连接测试失败: ${(error as Error).message}`, 400);
  }
};

// 获取支持的邮箱提供商
export const getSupportedEmailProviders = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const providers = getSupportedProviders();

    const response: ApiResponse = {
      success: true,
      message: '获取支持的邮箱提供商成功',
      data: providers,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取邮箱提供商失败: ${(error as Error).message}`, 500);
  }
};

// 自动配置邮箱账户
export const getAutoConfig = async (req: AuthenticatedRequest, res: Response) => {
  const { email } = req.query;

  if (!email || typeof email !== 'string') {
    throw new AppError('请提供邮箱地址', 400);
  }

  try {
    const autoConfig = autoConfigureAccount(email);

    const response: ApiResponse = {
      success: true,
      message: '自动配置获取成功',
      data: autoConfig,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`自动配置失败: ${(error as Error).message}`, 400);
  }
};

// 检测邮箱提供商
export const detectProvider = async (req: AuthenticatedRequest, res: Response) => {
  const { email } = req.query;

  if (!email || typeof email !== 'string') {
    throw new AppError('请提供邮箱地址', 400);
  }

  try {
    const provider = detectEmailProvider(email);

    const response: ApiResponse = {
      success: true,
      message: provider ? '检测到邮箱提供商' : '未检测到支持的邮箱提供商',
      data: provider,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`检测邮箱提供商失败: ${(error as Error).message}`, 500);
  }
};

// 更新账户连接状态
export const updateAccountConnectionStatus = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  try {
    // 验证账户所有权
    const account = await prisma.emailAccount.findFirst({
      where: {
        id: parseInt(id),
        userId,
      },
    });

    if (!account) {
      throw new AppError('邮箱账户不存在', 404);
    }

    // 执行连接测试并更新状态
    const config = {
      imapHost: account.imapHost,
      imapPort: account.imapPort,
      imapSecure: account.imapSecure,
      imapUsername: account.imapUsername,
      imapPassword: account.imapPassword,
      smtpHost: account.smtpHost,
      smtpPort: account.smtpPort,
      smtpSecure: account.smtpSecure,
      smtpUsername: account.smtpUsername,
      smtpPassword: account.smtpPassword,
    };

    const testResult = await testEmailConnectionDetailed(config);

    if (testResult.success) {
      await updateConnectionStatus(account.id, 'connected');
    } else {
      await updateConnectionStatus(account.id, 'error', testResult.error);
    }

    const response: ApiResponse = {
      success: true,
      message: '连接状态更新成功',
      data: {
        connectionStatus: testResult.success ? 'connected' : 'error',
        testResult,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新连接状态失败: ${(error as Error).message}`, 500);
  }
};

// 获取用户活跃账户
export const getUserActiveEmailAccounts = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    const accounts = await getUserActiveAccounts(userId);

    const response: ApiResponse = {
      success: true,
      message: '获取活跃邮箱账户成功',
      data: accounts,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取活跃邮箱账户失败: ${(error as Error).message}`, 500);
  }
};
