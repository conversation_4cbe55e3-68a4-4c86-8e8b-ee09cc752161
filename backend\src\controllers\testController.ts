import { Request, Response } from 'express';
import { parseEmailContent } from '../utils/emailContentParser';
import logger from '../utils/logger';

/**
 * 测试控制器 - 用于测试邮件内容解析功能
 */

/**
 * 测试邮件内容解析
 */
export const testEmailParsing = async (req: Request, res: Response) => {
  try {
    const { emailContent } = req.body;
    
    if (!emailContent) {
      return res.status(400).json({
        success: false,
        message: '请提供邮件内容'
      });
    }
    
    // 解析邮件内容
    const parsedResult = await parseEmailContent(emailContent);
    
    // 返回解析结果
    res.json({
      success: true,
      data: {
        messageId: parsedResult.messageId,
        subject: parsedResult.subject,
        hasContent: parsedResult.hasContent,
        contentType: parsedResult.contentType,
        originalText: parsedResult.text,
        originalHtml: parsedResult.html,
        enhancedText: parsedResult.enhancedText,
        enhancedHtml: parsedResult.enhancedHtml,
        textLength: parsedResult.enhancedText?.length || 0,
        htmlLength: parsedResult.enhancedHtml?.length || 0,
        from: parsedResult.from,
        to: parsedResult.to,
        date: parsedResult.date
      }
    });
    
  } catch (error) {
    logger.error('测试邮件解析失败:', error);
    res.status(500).json({
      success: false,
      message: '邮件解析失败',
      error: error.message
    });
  }
};

/**
 * 获取示例邮件内容用于测试
 */
export const getSampleEmailContent = (req: Request, res: Response) => {
  const sampleEmails = [
    {
      name: 'HTML邮件示例',
      content: `From: <EMAIL>
To: <EMAIL>
Subject: HTML邮件测试
Content-Type: multipart/alternative; boundary="boundary123"

--boundary123
Content-Type: text/plain; charset=utf-8

这是一封HTML邮件的纯文本版本。

包含链接：https://example.com
包含邮箱：<EMAIL>

--boundary123
Content-Type: text/html; charset=utf-8

<html>
<body>
<h1>HTML邮件测试</h1>
<p>这是一封<strong>HTML邮件</strong>。</p>
<p>包含链接：<a href="https://example.com">示例网站</a></p>
<p>包含邮箱：<a href="mailto:<EMAIL>"><EMAIL></a></p>
<img src="https://via.placeholder.com/300x200" alt="示例图片" style="max-width: 100%;">
</body>
</html>

--boundary123--`
    },
    {
      name: '纯文本邮件示例',
      content: `From: <EMAIL>
To: <EMAIL>
Subject: 纯文本邮件测试
Content-Type: text/plain; charset=utf-8

这是一封纯文本邮件。

包含多行内容：
第一行
第二行
第三行

包含链接：https://example.com
包含邮箱：<EMAIL>

谢谢！`
    },
    {
      name: '复杂编码邮件示例',
      content: `From: =?utf-8?B?5Y+R6YCB6ICF?= <<EMAIL>>
To: <EMAIL>
Subject: =?utf-8?B?5oCO5qC35rWL6K+V?=
Content-Type: text/html; charset=utf-8
Content-Transfer-Encoding: quoted-printable

<html>
<body>
<h1>=E6=B5=8B=E8=AF=95=E9=82=AE=E4=BB=B6</h1>
<p>=E8=BF=99=E6=98=AF=E4=B8=80=E5=B0=81=E6=B5=8B=E8=AF=95=E9=82=AE=E4=BB=B6=E3=80=82</p>
<p>=E5=8C=85=E5=90=AB<strong>=E4=B8=AD=E6=96=87=E5=86=85=E5=AE=B9</strong>=E3=80=82</p>
</body>
</html>`
    }
  ];
  
  res.json({
    success: true,
    data: sampleEmails
  });
};

/**
 * 测试邮件内容清理功能
 */
export const testEmailCleaning = async (req: Request, res: Response) => {
  try {
    const { htmlContent } = req.body;
    
    if (!htmlContent) {
      return res.status(400).json({
        success: false,
        message: '请提供HTML内容'
      });
    }
    
    // 这里可以添加HTML清理测试逻辑
    // 目前返回原始内容作为示例
    res.json({
      success: true,
      data: {
        original: htmlContent,
        cleaned: htmlContent, // 实际应用中会进行清理
        message: '内容清理功能正在开发中'
      }
    });
    
  } catch (error) {
    logger.error('测试内容清理失败:', error);
    res.status(500).json({
      success: false,
      message: '内容清理测试失败',
      error: error.message
    });
  }
};
