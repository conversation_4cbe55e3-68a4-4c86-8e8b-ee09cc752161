# 系统监控功能

本文档介绍了完整的系统监控功能，为管理员提供全面的系统状态监控和性能分析。

## 功能概述

系统监控功能提供以下监控能力：

1. **系统概览**：服务器硬件信息、运行状态、用户统计
2. **邮件服务器状态**：Postfix/Dovecot服务状态、磁盘使用、邮件队列
3. **邮件统计分析**：邮件数量趋势、分类统计、时间线图表
4. **系统日志管理**：应用日志、邮件服务日志、安全日志

## API 接口

### 1. 系统概览
```http
GET /api/system-monitoring/overview
Authorization: Bearer <admin-token>
```

**响应示例：**
```json
{
  "success": true,
  "message": "获取系统概览成功",
  "data": {
    "systemInfo": {
      "hostname": "mail-server",
      "platform": "linux",
      "arch": "x64",
      "nodeVersion": "v18.17.0",
      "uptime": 86400,
      "systemUptime": 259200,
      "totalMemory": 8589934592,
      "freeMemory": 2147483648,
      "cpuCount": 4,
      "loadAverage": [0.5, 0.3, 0.2]
    },
    "statistics": {
      "totalUsers": 150,
      "activeUsers": 120,
      "systemUsers": 3,
      "totalEmails": 5000,
      "todayEmails": 50
    }
  }
}
```

### 2. 邮件服务器状态
```http
GET /api/system-monitoring/mail-server
Authorization: Bearer <admin-token>
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "services": {
      "postfix": {
        "name": "postfix",
        "status": "running",
        "message": "服务正常运行"
      },
      "dovecot": {
        "name": "dovecot",
        "status": "running",
        "message": "服务正常运行"
      }
    },
    "diskUsage": {
      "filesystem": "/dev/sda1",
      "size": "100G",
      "used": "25G",
      "available": "75G",
      "usePercentage": "25%",
      "mountPoint": "/var/mail"
    },
    "mailQueue": {
      "count": 0,
      "status": "empty",
      "message": "邮件队列为空"
    }
  }
}
```

### 3. 邮件统计
```http
GET /api/system-monitoring/email-stats?period=7d
Authorization: Bearer <admin-token>
```

**查询参数：**
- `period`: 统计周期 ('24h' | '7d' | '30d')

**响应示例：**
```json
{
  "success": true,
  "data": {
    "period": "7d",
    "summary": {
      "totalEmails": 350,
      "sentEmails": 150,
      "receivedEmails": 200,
      "deletedEmails": 20,
      "unreadEmails": 45,
      "starredEmails": 12
    },
    "timeline": [
      {"date": "2024-01-01", "count": 50},
      {"date": "2024-01-02", "count": 45},
      {"date": "2024-01-03", "count": 60}
    ]
  }
}
```

### 4. 系统日志
```http
GET /api/system-monitoring/logs?type=app&level=error&limit=50
Authorization: Bearer <admin-token>
```

**查询参数：**
- `type`: 日志类型 ('app' | 'mail' | 'security')
- `level`: 日志级别 ('all' | 'error' | 'warn' | 'info')
- `limit`: 返回数量 (默认: 100)
- `offset`: 偏移量 (默认: 0)

## 前端组件

### SystemMonitoring 组件
位置：`frontend/src/components/SystemMonitoring.tsx`

**主要功能：**

1. **实时监控面板**
   - 系统资源使用情况
   - 服务运行状态
   - 关键指标统计

2. **邮件统计图表**
   - 可选择时间周期（24小时/7天/30天）
   - 邮件数量趋势图
   - 分类统计卡片

3. **服务状态监控**
   - Postfix/Dovecot服务状态
   - 磁盘使用情况
   - 邮件队列状态

4. **日志查看器**
   - 多类型日志支持
   - 级别筛选
   - 分页显示

5. **自动刷新**
   - 可开启/关闭自动刷新
   - 30秒刷新间隔
   - 手动刷新按钮

### 集成位置
系统监控已集成到管理员设置页面 (`/admin`) 的"系统监控"标签页。

## 监控指标

### 系统指标
- **CPU使用率**：通过系统负载平均值监控
- **内存使用率**：已用内存/总内存百分比
- **磁盘使用率**：邮件存储目录使用情况
- **系统运行时间**：服务器和应用运行时间

### 邮件指标
- **邮件总量**：系统中所有邮件数量
- **今日邮件**：当天新增邮件数量
- **发送/接收比例**：邮件流向分析
- **未读邮件数**：待处理邮件统计
- **队列状态**：待发送邮件队列

### 用户指标
- **总用户数**：注册用户总数
- **活跃用户数**：已激活用户数量
- **系统用户数**：管理员和协调员数量

## 告警机制

### 自动告警条件
1. **内存使用率 > 80%**：显示警告状态
2. **磁盘使用率 > 90%**：显示危险状态
3. **邮件服务停止**：显示错误状态
4. **邮件队列积压 > 100**：显示警告状态

### 状态指示
- 🟢 **正常**：所有指标在正常范围内
- 🟡 **警告**：部分指标超出正常范围
- 🔴 **错误**：关键服务或指标异常

## 性能优化

### 数据缓存
- 系统信息缓存5分钟
- 邮件统计缓存10分钟
- 服务状态实时检查

### 查询优化
- 使用数据库索引优化统计查询
- 分页加载大量日志数据
- 异步并发获取多项指标

### 前端优化
- 图表数据懒加载
- 自动刷新防抖处理
- 组件状态管理优化

## 安全考虑

### 权限控制
- 仅管理员可访问监控功能
- API接口严格权限验证
- 敏感信息脱敏处理

### 数据保护
- 日志数据定期清理
- 个人信息匿名化
- 访问日志记录

## 故障排除

### 常见问题

1. **服务状态检查失败**
   - 检查SSH连接配置
   - 验证服务器权限
   - 确认服务名称正确

2. **磁盘使用信息获取失败**
   - 检查邮件目录权限
   - 验证df命令可用性
   - 确认挂载点正确

3. **邮件统计数据异常**
   - 检查数据库连接
   - 验证邮件表结构
   - 确认时区设置

4. **图表显示异常**
   - 检查recharts依赖
   - 验证数据格式
   - 确认浏览器兼容性

### 调试方法

1. **后端调试**
   ```bash
   # 运行监控测试
   npm run test:system-monitoring
   
   # 查看应用日志
   tail -f logs/app.log
   
   # 检查API响应
   curl -H "Authorization: Bearer <token>" \
        http://localhost:5000/api/system-monitoring/overview
   ```

2. **前端调试**
   - 打开浏览器开发者工具
   - 检查网络请求状态
   - 查看控制台错误信息

## 扩展功能

### 未来计划
1. **邮件性能分析**：响应时间、吞吐量统计
2. **用户行为分析**：登录频率、使用模式
3. **告警通知**：邮件/短信告警推送
4. **历史数据分析**：长期趋势分析
5. **自动化运维**：自动重启服务、清理日志

### 自定义扩展
- 添加新的监控指标
- 自定义告警规则
- 集成第三方监控工具
- 导出监控报告

## 测试

### 运行测试
```bash
npm run test:system-monitoring
```

### 测试覆盖
- ✅ 系统概览获取
- ✅ 邮件服务器状态检查
- ✅ 邮件统计分析
- ✅ 系统日志查询
- ✅ 权限验证
- ✅ 错误处理

系统监控功能为管理员提供了全面的系统状态可视化，帮助及时发现和解决问题，确保邮件系统的稳定运行。
