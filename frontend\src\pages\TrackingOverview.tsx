import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Timeline,
  Select,
  DatePicker,
  Typography,
  Space,
  Tag,
  List,
  Progress,
} from 'antd';
import {
  BarChartOutlined,
  EyeOutlined,
  MessageOutlined,
  CheckCircleOutlined,
  MailOutlined,
  CiOutlined
} from '@ant-design/icons';
import * as trackingApi from '../services/trackingApi';
import type { TrackingOverview } from '../services/trackingApi';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const TrackingOverviewPage: React.FC = () => {
  const [overview, setOverview] = useState<TrackingOverview | null>(null);
  const [loading, setLoading] = useState(false);
  const [days, setDays] = useState(30);

  // 加载跟踪概览数据
  const loadOverview = async () => {
    try {
      setLoading(true);
      const data = await trackingApi.getTrackingOverview(days);
      setOverview(data);
    } catch (error) {
      console.error('加载跟踪概览失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadOverview();
  }, [days]);

  if (!overview) {
    return <div>加载中...</div>;
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <Title level={2}>
          <BarChartOutlined className="mr-2" />
          邮件跟踪概览
        </Title>
        <Space>
          <Text>时间范围：</Text>
          <Select
            value={days}
            onChange={setDays}
            style={{ width: 120 }}
          >
            <Select.Option value={7}>最近7天</Select.Option>
            <Select.Option value={30}>最近30天</Select.Option>
            <Select.Option value={90}>最近90天</Select.Option>
          </Select>
        </Space>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        <Col span={6}>
          <Card>
            <Statistic
              title="跟踪邮件总数"
              value={overview.totalTracked}
              prefix={<MailOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已打开邮件"
              value={overview.totalOpened}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#52c41a' }}
              suffix={
                <Text type="secondary">
                  ({overview.openRate}%)
                </Text>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="链接点击"
              value={overview.totalClicked}
              prefix={<CiOutlined />}
              valueStyle={{ color: '#faad14' }}
              suffix={
                <Text type="secondary">
                  ({overview.clickRate}%)
                </Text>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="邮件回复"
              value={overview.totalReplied}
              prefix={<EyeOutlined />}
              valueStyle={{ color: '#722ed1' }}
              suffix={
                <Text type="secondary">
                  ({overview.replyRate}%)
                </Text>
              }
            />
          </Card>
        </Col>
      </Row>

      {/* 详细统计 */}
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Card title="参与度统计" loading={loading}>
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <Text>打开率</Text>
                <Text strong>{overview.openRate}%</Text>
              </div>
              <Progress 
                percent={parseFloat(overview.openRate)} 
                strokeColor="#52c41a"
                showInfo={false}
              />
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <Text>点击率</Text>
                <Text strong>{overview.clickRate}%</Text>
              </div>
              <Progress 
                percent={parseFloat(overview.clickRate)} 
                strokeColor="#faad14"
                showInfo={false}
              />
            </div>
            
            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <Text>回复率</Text>
                <Text strong>{overview.replyRate}%</Text>
              </div>
              <Progress 
                percent={parseFloat(overview.replyRate)} 
                strokeColor="#722ed1"
                showInfo={false}
              />
            </div>

            <div className="mt-4 p-3 bg-gray-50 rounded">
              <Text type="secondary">
                在过去{days}天内，您发送了 {overview.totalTracked} 封跟踪邮件，
                其中 {overview.totalOpened} 封被打开，
                {overview.totalClicked} 封有链接点击，
                {overview.totalReplied} 封收到回复。
              </Text>
            </div>
          </Card>
        </Col>

        <Col span={12}>
          <Card title="最近活动" loading={loading}>
            {overview.recentActivity.length > 0 ? (
              <Timeline>
                {overview.recentActivity.slice(0, 8).map((activity, index) => {
                  let icon;
                  let color;
                  
                  switch (activity.eventType) {
                    case 'open':
                      icon = <EyeOutlined />;
                      color = 'blue';
                      break;
                    case 'click':
                      icon = <CiOutlined />;
                      color = 'green';
                      break;
                    case 'reply':
                      icon = <EyeOutlined />;
                      color = 'purple';
                      break;
                    case 'delivery':
                      icon = <CheckCircleOutlined />;
                      color = 'green';
                      break;
                    default:
                      icon = <MailOutlined />;
                      color = 'gray';
                  }

                  return (
                    <Timeline.Item key={index} dot={icon} color={color}>
                      <div>
                        <Text strong>
                          {trackingApi.getEventTypeLabel(activity.eventType)}
                        </Text>
                        <br />
                        <Text type="secondary" className="text-xs">
                          {new Date(activity.createdAt).toLocaleString()}
                        </Text>
                        {activity.clickedUrl && (
                          <div className="mt-1">
                            <Tag color="blue" className="text-xs">
                              {activity.clickedUrl.length > 30 
                                ? activity.clickedUrl.substring(0, 30) + '...'
                                : activity.clickedUrl
                              }
                            </Tag>
                          </div>
                        )}
                      </div>
                    </Timeline.Item>
                  );
                })}
              </Timeline>
            ) : (
              <div className="text-center py-8">
                <MailOutlined style={{ fontSize: '48px', color: '#ccc' }} />
                <div className="mt-2">
                  <Text type="secondary">暂无跟踪活动</Text>
                </div>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 投递状态 */}
      <Row gutter={[16, 16]} className="mt-4">
        <Col span={24}>
          <Card title="投递状态概览">
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="成功投递"
                  value={overview.totalDelivered}
                  prefix={<CheckCircleOutlined />}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="投递率"
                  value={overview.totalTracked > 0 
                    ? ((overview.totalDelivered / overview.totalTracked) * 100).toFixed(1)
                    : '0'
                  }
                  suffix="%"
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={12}>
                <div className="text-right">
                  <Text type="secondary">
                    基于过去{days}天的数据统计
                  </Text>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TrackingOverviewPage;
