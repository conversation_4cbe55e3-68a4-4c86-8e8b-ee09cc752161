import api from '../config/api';
import type { ApiResponse } from '../types/api';

// 用户管理相关类型定义
export interface ManagedUser {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  role: 'user' | 'admin' | 'moderator';
  isActive: boolean;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
  stats: {
    emailCount: number;
    folderCount: number;
    contactCount: number;
    templateCount?: number;
    ruleCount?: number;
  };
}

export interface UserListParams {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface UserListResponse {
  users: ManagedUser[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface UpdateUserPermissionsData {
  role?: 'user' | 'admin' | 'moderator';
  isActive?: boolean;
  emailVerified?: boolean;
  displayName?: string;
  username?: string;
  email?: string;
  newPassword?: string;
}

export interface BatchUpdateData {
  userIds: number[];
  action: 'activate' | 'deactivate' | 'verify_email' | 'unverify_email' | 'set_role' | 'reset_password';
  value?: string; // 当action为set_role时需要
}

/**
 * 获取所有用户列表（管理员功能）
 */
export const getAllUsers = async (params: UserListParams = {}): Promise<UserListResponse> => {
  const response = await api.get<ApiResponse<UserListResponse>>('/user-management/users', {
    params
  });
  return response.data.data!;
};

/**
 * 获取用户详细信息（管理员功能）
 */
export const getUserDetails = async (userId: number): Promise<ManagedUser> => {
  const response = await api.get<ApiResponse<ManagedUser>>(`/user-management/users/${userId}`);
  return response.data.data!;
};

/**
 * 更新用户权限和状态（管理员功能）
 */
export const updateUserPermissions = async (
  userId: number, 
  data: UpdateUserPermissionsData
): Promise<ManagedUser> => {
  const response = await api.put<ApiResponse<ManagedUser>>(
    `/user-management/users/${userId}/permissions`,
    data
  );
  return response.data.data!;
};

/**
 * 批量更新用户状态（管理员功能）
 */
export const batchUpdateUsers = async (data: BatchUpdateData): Promise<{
  affectedCount: number;
  action: string;
  value?: string;
}> => {
  const response = await api.post<ApiResponse<{
    affectedCount: number;
    action: string;
    value?: string;
  }>>('/user-management/users/batch-update', data);
  return response.data.data!;
};

/**
 * 删除用户
 */
export const deleteUser = async (userId: number): Promise<{ deletedUserId: number; deletedUserEmail: string }> => {
  const response = await api.delete<ApiResponse<{ deletedUserId: number; deletedUserEmail: string }>>(`/user-management/users/${userId}`);
  return response.data.data!;
};

/**
 * 获取用户角色选项
 */
export const getUserRoleOptions = () => [
  { value: 'user', label: '普通用户', description: '基础邮件功能' },
  { value: 'moderator', label: '协调员', description: '部分管理功能' },
  { value: 'admin', label: '管理员', description: '完整管理权限' }
];

/**
 * 获取用户状态选项
 */
export const getUserStatusOptions = () => [
  { value: '', label: '全部状态' },
  { value: 'active', label: '已激活' },
  { value: 'inactive', label: '已禁用' }
];

/**
 * 获取批量操作选项
 */
export const getBatchActionOptions = () => [
  { value: 'activate', label: '激活用户' },
  { value: 'deactivate', label: '禁用用户' },
  { value: 'verify_email', label: '验证邮箱' },
  { value: 'unverify_email', label: '取消邮箱验证' },
  { value: 'set_role', label: '设置角色' }
];
