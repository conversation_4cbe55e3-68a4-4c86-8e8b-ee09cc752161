import { body, param, query, validationResult } from 'express-validator';
import { AppError } from '../types';

// 验证结果处理中间件
export const handleValidationErrors = (
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => error.msg).join(', ');
    throw new AppError(`验证失败: ${errorMessages}`, 400);
  }
  next();
};

// 用户注册验证
export const validateRegister = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请提供有效的邮箱地址'),
  body('username')
    .isLength({ min: 3, max: 20 })
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('用户名长度为3-20位，只能包含字母、数字和下划线'),
  body('password')
    .isLength({ min: 6 })
    .withMessage('密码长度至少为6位'),
  body('displayName')
    .optional()
    .isLength({ max: 100 })
    .withMessage('显示名称长度不能超过100位'),
  handleValidationErrors,
];

// 用户登录验证
export const validateLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请提供有效的邮箱地址'),
  body('password')
    .notEmpty()
    .withMessage('请提供密码'),
  handleValidationErrors,
];

// 邮件发送验证
export const validateSendEmail = [
  body('to')
    .isArray({ min: 1 })
    .withMessage('收件人不能为空'),
  body('to.*.email')
    .isEmail()
    .withMessage('收件人邮箱格式不正确'),
  body('subject')
    .notEmpty()
    .isLength({ max: 500 })
    .withMessage('邮件主题不能为空且长度不能超过500字符'),
  body('content')
    .notEmpty()
    .withMessage('邮件内容不能为空'),
  body('cc')
    .optional()
    .isArray()
    .withMessage('抄送人必须是数组'),
  body('cc.*.email')
    .optional()
    .isEmail()
    .withMessage('抄送人邮箱格式不正确'),
  body('bcc')
    .optional()
    .isArray()
    .withMessage('密送人必须是数组'),
  body('bcc.*.email')
    .optional()
    .isEmail()
    .withMessage('密送人邮箱格式不正确'),
  handleValidationErrors,
];

// 文件夹创建验证
export const validateCreateFolder = [
  body('name')
    .notEmpty()
    .isLength({ max: 100 })
    .withMessage('文件夹名称不能为空且长度不能超过100字符'),
  body('type')
    .optional()
    .isIn(['inbox', 'sent', 'draft', 'trash', 'custom'])
    .withMessage('文件夹类型不正确'),
  body('parentId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('父文件夹ID必须是正整数'),
  handleValidationErrors,
];

// 联系人创建验证
export const validateCreateContact = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('请提供有效的邮箱地址'),
  body('name')
    .optional()
    .isLength({ max: 255 })
    .withMessage('姓名长度不能超过255字符'),
  body('phone')
    .optional()
    .isLength({ max: 50 })
    .withMessage('电话长度不能超过50字符'),
  body('company')
    .optional()
    .isLength({ max: 255 })
    .withMessage('公司名称长度不能超过255字符'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('备注长度不能超过1000字符'),
  handleValidationErrors,
];

// 邮件模板验证
export const validateCreateTemplate = [
  body('name')
    .notEmpty()
    .isLength({ max: 255 })
    .withMessage('模板名称不能为空且长度不能超过255字符'),
  body('subject')
    .notEmpty()
    .isLength({ max: 500 })
    .withMessage('模板主题不能为空且长度不能超过500字符'),
  body('content')
    .notEmpty()
    .withMessage('模板内容不能为空'),
  body('isPublic')
    .optional()
    .isBoolean()
    .withMessage('公开状态必须是布尔值'),
  handleValidationErrors,
];

// ID参数验证
export const validateId = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('ID必须是正整数'),
  handleValidationErrors,
];

// 分页参数验证
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('页码必须是正整数'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('每页数量必须是1-100之间的整数'),
  query('sortBy')
    .optional()
    .isString()
    .withMessage('排序字段必须是字符串'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('排序方向必须是asc或desc'),
  handleValidationErrors,
];

// 标签创建验证
export const validateCreateLabel = [
  body('name')
    .notEmpty()
    .isLength({ max: 50 })
    .withMessage('标签名称不能为空且长度不能超过50字符'),
  body('color')
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('颜色必须是有效的十六进制颜色值'),
  handleValidationErrors,
];

// 标签更新验证
export const validateUpdateLabel = [
  body('name')
    .optional()
    .isLength({ max: 50 })
    .withMessage('标签名称长度不能超过50字符'),
  body('color')
    .optional()
    .matches(/^#[0-9A-Fa-f]{6}$/)
    .withMessage('颜色必须是有效的十六进制颜色值'),
  handleValidationErrors,
];

// 邮件标签操作验证
export const validateEmailLabel = [
  body('emailId')
    .notEmpty()
    .isString()
    .withMessage('邮件ID不能为空'),
  body('labelId')
    .isInt({ min: 1 })
    .withMessage('标签ID必须是正整数'),
  handleValidationErrors,
];

// 批量邮件标签操作验证
export const validateBatchEmailLabel = [
  body('emailIds')
    .isArray({ min: 1 })
    .withMessage('邮件ID列表不能为空'),
  body('emailIds.*')
    .isString()
    .withMessage('邮件ID必须是字符串'),
  body('labelIds')
    .isArray({ min: 1 })
    .withMessage('标签ID列表不能为空'),
  body('labelIds.*')
    .isInt({ min: 1 })
    .withMessage('标签ID必须是正整数'),
  handleValidationErrors,
];

// 密码同步验证
export const validatePasswordSync = [
  body('password')
    .isLength({ min: 8 })
    .withMessage('密码长度至少为8位'),
  handleValidationErrors,
];

// 切换邮件账户状态验证
export const validateToggleMailAccount = [
  param('userId')
    .isInt({ min: 1 })
    .withMessage('用户ID必须是正整数'),
  body('enabled')
    .isBoolean()
    .withMessage('启用状态必须是布尔值'),
  handleValidationErrors,
];
