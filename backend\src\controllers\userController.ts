import { Response } from 'express';
import path from 'path';
import fs from 'fs';
import prisma from '../config/database';
import config from '../config/env';
import logger from '../utils/logger';
import { AuthenticatedRequest, ApiResponse, PaginationParams } from '../types';

// 获取用户列表
export const getUsers = async (req: AuthenticatedRequest, res: Response) => {
  const { page = 1, limit = 10, sortBy = 'createdAt', sortOrder = 'desc' }: PaginationParams = req.query as any;

  // 确保 page 和 limit 是数字
  const pageNum = parseInt(String(page)) || 1;
  const limitNum = parseInt(String(limit)) || 10;
  const skip = (pageNum - 1) * limitNum;

  const [users, total] = await Promise.all([
    prisma.user.findMany({
      skip,
      take: limitNum,
      orderBy: { [sortBy]: sortOrder },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        avatarUrl: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    }),
    prisma.user.count(),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '获取用户列表成功',
    data: {
      users,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取用户详情
export const getUserById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;

  const user = await prisma.user.findUnique({
    where: { id: parseInt(id) },
    select: {
      id: true,
      email: true,
      username: true,
      displayName: true,
      avatarUrl: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      message: '用户不存在',
    });
  }

  const response: ApiResponse = {
    success: true,
    message: '获取用户详情成功',
    data: user,
  };

  res.json(response);
};

// 更新用户信息
export const updateUser = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { displayName, avatarUrl } = req.body;

  // 只允许用户更新自己的信息
  if (parseInt(id) !== req.user!.id) {
    return res.status(403).json({
      success: false,
      message: '无权限修改其他用户信息',
    });
  }

  const user = await prisma.user.update({
    where: { id: parseInt(id) },
    data: {
      displayName,
      avatarUrl,
    },
    select: {
      id: true,
      email: true,
      username: true,
      displayName: true,
      avatarUrl: true,
      isActive: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '用户信息更新成功',
    data: user,
  };

  res.json(response);
};

// 上传头像
export const uploadAvatar = async (req: AuthenticatedRequest, res: Response) => {
  if (!req.file) {
    return res.status(400).json({
      success: false,
      message: '请选择要上传的文件',
    });
  }

  const userId = req.user!.id;
  const file = req.file;

  // 检查文件类型
  if (!file.mimetype.startsWith('image/')) {
    return res.status(400).json({
      success: false,
      message: '只能上传图片文件',
    });
  }

  // 检查文件大小 (5MB)
  if (file.size > 5 * 1024 * 1024) {
    return res.status(400).json({
      success: false,
      message: '文件大小不能超过5MB',
    });
  }

  try {
    // 生成文件名
    const fileExtension = file.originalname.split('.').pop() || 'jpg';
    const fileName = `avatar_${userId}_${Date.now()}.${fileExtension}`;
    const filePath = path.join(config.UPLOAD_PATH, 'avatars', fileName);

    // 确保目录存在
    const uploadDir = path.dirname(filePath);
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // 保存文件
    fs.writeFileSync(filePath, file.buffer);

    // 生成访问URL
    const avatarUrl = `/uploads/avatars/${fileName}`;

    // 更新用户头像URL
    await prisma.user.update({
      where: { id: userId },
      data: { avatarUrl },
    });

    const response: ApiResponse = {
      success: true,
      message: '头像上传成功',
      data: { url: avatarUrl },
    };

    res.json(response);
  } catch (error) {
    logger.error('头像上传失败:', error);
    res.status(500).json({
      success: false,
      message: '头像上传失败',
    });
  }
};
