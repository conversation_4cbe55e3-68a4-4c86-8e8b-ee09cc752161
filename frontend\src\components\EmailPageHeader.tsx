import React from 'react';
import {
  <PERSON><PERSON>,
  Space,
  Tooltip,
  Select,
  Checkbox,
  Dropdown
} from 'antd';
import {
  MenuOutlined,
  AppstoreOutlined,
  DownOutlined
} from '@ant-design/icons';
import type { Email } from '../types/email';

interface EmailPageHeaderProps {
  // 页面标题
  title: string;

  // 视图模式
  viewMode: 'list' | 'split';
  onViewModeChange: (mode: 'list' | 'split') => void;

  // 批量选择
  emails: Email[];
  selectedEmailIds: string[];
  currentPage: number;
  pageSize: number;
  onSelectAll: (checked: boolean) => void;
  onClearSelection: () => void;

  // 批量操作
  batchActionsMenuItems?: any[];

  // 筛选器选项
  filterOptions?: Array<{
    value: string;
    label: string;
  }>;
  selectedFilter?: string;
  onFilterChange?: (value: string) => void;
}

const EmailPageHeader: React.FC<EmailPageHeaderProps> = ({
  title,
  viewMode,
  onViewModeChange,
  emails,
  selectedEmailIds,
  currentPage,
  pageSize,
  onSelectAll,
  onClearSelection,
  batchActionsMenuItems = [],
  filterOptions = [
    { value: 'all', label: '全部' },
    { value: 'unread', label: '未读' },
    { value: 'starred', label: '星标' },
    { value: 'attachments', label: '有附件' }
  ],
  selectedFilter = 'all',
  onFilterChange
}) => {
  // 计算当前页的邮件
  const currentPageEmails = emails.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  // 计算全选状态
  const isIndeterminate = selectedEmailIds.length > 0 && 
    selectedEmailIds.length < currentPageEmails.length;
  const isAllSelected = selectedEmailIds.length > 0 && 
    selectedEmailIds.length === currentPageEmails.length;

  return (
    <div className="email-header">
      <div className="header-left">
        <div className="header-title">
          <Checkbox
            indeterminate={isIndeterminate}
            checked={isAllSelected}
            onChange={(e) => onSelectAll(e.target.checked)}
          />
          <h3>{title}</h3>
          {/* 筛选器选项合并到标题后面 */}
          <Select
            value={selectedFilter}
            onChange={onFilterChange}
            style={{ width: 120, marginLeft: 16 }}
            size="small"
          >
            {filterOptions.map(option => (
              <Select.Option key={option.value} value={option.value}>
                {option.label}
              </Select.Option>
            ))}
          </Select>
        </div>
        {/* 批量操作区域 */}
        {selectedEmailIds.length > 0 && (
          <div className="batch-actions">
            <span className="selected-count">
              已选择 {selectedEmailIds.length} 封邮件
            </span>
            {batchActionsMenuItems.length > 0 && (
              <Dropdown
                menu={{ items: batchActionsMenuItems }}
                trigger={["click"]}
              >
                <Button size="small" type="primary">
                  批量操作 <DownOutlined />
                </Button>
              </Dropdown>
            )}
            <Button
              size="small"
              onClick={onClearSelection}
              title="取消选择"
            >
              取消
            </Button>
          </div>
        )}
      </div>

      <div className="header-right">
        <Space>
          <Tooltip
            title={viewMode === "split" ? "切换到列表模式" : "切换到分栏模式"}
          >
            <Button
              type="text"
              icon={
                viewMode === "split" ? <MenuOutlined /> : <AppstoreOutlined />
              }
              onClick={() =>
                onViewModeChange(viewMode === "split" ? "list" : "split")
              }
              size="small"
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );
};

export default EmailPageHeader;
