import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';

/**
 * 用户管理控制器
 * 专门处理管理员对普通用户的管理功能，包括权限设置
 */

/**
 * 获取所有用户列表（管理员功能）
 */
export const getAllUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    const { 
      page = 1, 
      limit = 20, 
      search = '', 
      role = '', 
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const pageNum = parseInt(String(page));
    const limitNum = parseInt(String(limit));
    const skip = (pageNum - 1) * limitNum;

    // 构建查询条件
    const where: any = {};

    // 搜索条件
    if (search) {
      where.OR = [
        { email: { contains: String(search) } },
        { username: { contains: String(search) } },
        { displayName: { contains: String(search) } }
      ];
    }

    // 角色筛选
    if (role) {
      where.role = String(role);
    }

    // 状态筛选
    if (status === 'active') {
      where.isActive = true;
    } else if (status === 'inactive') {
      where.isActive = false;
    }

    // 获取用户列表和总数
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limitNum,
        orderBy: { [String(sortBy)]: String(sortOrder) },
        select: {
          id: true,
          email: true,
          username: true,
          displayName: true,
          role: true,
          isActive: true,
          emailVerified: true,
          createdAt: true,
          updatedAt: true,
          // 统计相关数据
          _count: {
            select: {
              emails: {
                where: { isDeleted: false }
              },
              folders: true,
              contacts: true
            }
          }
        }
      }),
      prisma.user.count({ where })
    ]);

    // 为每个用户添加统计信息
    const usersWithStats = users.map(user => ({
      ...user,
      stats: {
        emailCount: user._count.emails,
        folderCount: user._count.folders,
        contactCount: user._count.contacts
      },
      _count: undefined // 移除原始的_count字段
    }));

    const response: ApiResponse = {
      success: true,
      message: '获取用户列表成功',
      data: {
        users: usersWithStats,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total,
          totalPages: Math.ceil(total / limitNum),
          hasNext: pageNum * limitNum < total,
          hasPrev: pageNum > 1
        }
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取用户列表失败:', error);
    throw new AppError('获取用户列表失败', 500);
  }
};

/**
 * 更新用户权限和状态（管理员功能）
 */
export const updateUserPermissions = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const { role, isActive, emailVerified, displayName, username, email, newPassword } = req.body;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证用户ID
    if (!userId || isNaN(parseInt(userId))) {
      throw new AppError('无效的用户ID', 400);
    }

    const targetUserId = parseInt(userId);

    // 不允许管理员修改自己的权限
    if (targetUserId === req.user!.id) {
      throw new AppError('不能修改自己的权限', 403);
    }

    // 查找目标用户
    const targetUser = await prisma.user.findUnique({
      where: { id: targetUserId },
      select: { id: true, email: true, role: true, isActive: true }
    });

    if (!targetUser) {
      throw new AppError('用户不存在', 404);
    }

    // 构建更新数据
    const updateData: any = {};

    if (role !== undefined) {
      // 验证角色值
      const validRoles = ['user', 'admin', 'moderator'];
      if (!validRoles.includes(role)) {
        throw new AppError('无效的用户角色', 400);
      }
      updateData.role = role;
    }

    if (isActive !== undefined) {
      updateData.isActive = Boolean(isActive);
    }

    if (emailVerified !== undefined) {
      updateData.emailVerified = Boolean(emailVerified);
    }

    if (displayName !== undefined) {
      updateData.displayName = displayName ? String(displayName).trim() : null;
    }

    // 检查用户名和邮箱的唯一性
    if (username !== undefined) {
      const usernameStr = String(username).trim();
      if (usernameStr.length < 3 || usernameStr.length > 30) {
        throw new AppError('用户名长度必须在3-30个字符之间', 400);
      }
      if (!/^[a-zA-Z0-9_-]+$/.test(usernameStr)) {
        throw new AppError('用户名只能包含字母、数字、下划线和连字符', 400);
      }

      // 检查用户名是否已存在（排除当前用户）
      const existingUser = await prisma.user.findFirst({
        where: {
          username: usernameStr,
          id: { not: parseInt(userId) }
        }
      });
      if (existingUser) {
        throw new AppError('用户名已存在', 400);
      }
      updateData.username = usernameStr;
    }

    if (email !== undefined) {
      const emailStr = String(email).trim().toLowerCase();
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(emailStr)) {
        throw new AppError('邮箱格式不正确', 400);
      }

      // 检查邮箱是否已存在（排除当前用户）
      const existingUser = await prisma.user.findFirst({
        where: {
          email: emailStr,
          id: { not: parseInt(userId) }
        }
      });
      if (existingUser) {
        throw new AppError('邮箱已存在', 400);
      }
      updateData.email = emailStr;
    }

    // 处理密码更新
    if (newPassword !== undefined && newPassword.trim() !== '') {
      const passwordStr = String(newPassword).trim();
      if (passwordStr.length < 8 || passwordStr.length > 50) {
        throw new AppError('密码长度必须在8-50个字符之间', 400);
      }

      try {
        const { exec } = require('child_process');
        const util = require('util');
        const execAsync = util.promisify(exec);

        const isLocal = process.env['NODE_ENV'] === 'production' || process.env['MAIL_SERVER_LOCAL'] === 'true';

        // 使用doveadm生成加密密码
        let command: string;
        if (isLocal) {
          command = `doveadm pw -s SHA512-CRYPT -p '${passwordStr}'`;
        } else {
          command = `ssh <EMAIL> "doveadm pw -s SHA512-CRYPT -p '${passwordStr}'"`;
        }

        const { stdout } = await execAsync(command);
        const hashedPassword = stdout.trim();
        updateData.password = hashedPassword;

        logger.info(`管理员 ${req.user.email} 更新了用户 ${targetUser.email} 的密码`);
      } catch (error) {
        logger.error(`密码加密失败: ${error.message}`);
        throw new AppError('密码加密失败，请稍后重试', 500);
      }
    }

    // 更新用户
    const updatedUser = await prisma.user.update({
      where: { id: targetUserId },
      data: updateData,
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true
      }
    });

    // 记录操作日志
    logger.info(`管理员 ${req.user!.email} 更新了用户 ${updatedUser.email} 的权限`, {
      adminId: req.user!.id,
      targetUserId: updatedUser.id,
      changes: updateData
    });

    const response: ApiResponse = {
      success: true,
      message: '用户权限更新成功',
      data: updatedUser
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('更新用户权限失败:', error);
    throw new AppError('更新用户权限失败', 500);
  }
};

/**
 * 批量更新用户状态（管理员功能）
 */
export const batchUpdateUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userIds, action, value } = req.body;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证参数
    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new AppError('请提供有效的用户ID列表', 400);
    }

    const validActions = ['activate', 'deactivate', 'verify_email', 'unverify_email', 'set_role', 'reset_password'];
    if (!validActions.includes(action)) {
      throw new AppError('无效的操作类型', 400);
    }

    // 过滤掉管理员自己的ID
    const targetUserIds = userIds.filter((id: number) => id !== req.user!.id);

    if (targetUserIds.length === 0) {
      throw new AppError('没有可操作的用户', 400);
    }

    // 构建更新数据
    let updateData: any = {};
    switch (action) {
      case 'activate':
        updateData.isActive = true;
        break;
      case 'deactivate':
        updateData.isActive = false;
        break;
      case 'verify_email':
        updateData.emailVerified = true;
        break;
      case 'unverify_email':
        updateData.emailVerified = false;
        break;
      case 'set_role':
        const validRoles = ['user', 'admin', 'moderator'];
        if (!validRoles.includes(value)) {
          throw new AppError('无效的用户角色', 400);
        }
        updateData.role = value;
        break;
      case 'reset_password':
        // 密码重置将在后续处理，这里不设置updateData
        break;
    }

    let result: any;
    let resetPasswordResults: any[] = [];

    if (action === 'reset_password') {
      // 处理密码重置
      const { exec } = require('child_process');
      const util = require('util');
      const execAsync = util.promisify(exec);

      const isLocal = process.env['NODE_ENV'] === 'production' || process.env['MAIL_SERVER_LOCAL'] === 'true';

      // 获取需要重置密码的用户
      const usersToReset = await prisma.user.findMany({
        where: { id: { in: targetUserIds } },
        select: { id: true, email: true, username: true }
      });

      for (const user of usersToReset) {
        try {
          // 生成随机密码
          const newPassword = Math.random().toString(36).slice(-8) + Math.random().toString(36).slice(-8);

          // 使用doveadm生成加密密码
          let command: string;
          if (isLocal) {
            command = `doveadm pw -s SHA512-CRYPT -p '${newPassword}'`;
          } else {
            command = `ssh <EMAIL> "doveadm pw -s SHA512-CRYPT -p '${newPassword}'"`;
          }

          const { stdout } = await execAsync(command);
          const hashedPassword = stdout.trim();

          // 更新数据库中的密码
          await prisma.user.update({
            where: { id: user.id },
            data: { password: hashedPassword }
          });

          resetPasswordResults.push({
            userId: user.id,
            email: user.email,
            newPassword,
            success: true
          });

          logger.info(`密码重置成功: ${user.email}`);
        } catch (error) {
          logger.error(`密码重置失败: ${user.email}`, error);
          resetPasswordResults.push({
            userId: user.id,
            email: user.email,
            success: false,
            error: error.message
          });
        }
      }

      result = { count: resetPasswordResults.filter(r => r.success).length };
    } else {
      // 批量更新用户
      result = await prisma.user.updateMany({
        where: {
          id: { in: targetUserIds }
        },
        data: updateData
      });
    }

    // 记录操作日志
    logger.info(`管理员 ${req.user!.email} 批量更新了 ${result.count} 个用户`, {
      adminId: req.user!.id,
      action,
      value,
      affectedUserIds: targetUserIds
    });

    const response: ApiResponse = {
      success: true,
      message: `成功更新 ${result.count} 个用户`,
      data: {
        affectedCount: result.count,
        action,
        value,
        ...(action === 'reset_password' && { resetPasswordResults })
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('批量更新用户失败:', error);
    throw new AppError('批量更新用户失败', 500);
  }
};

/**
 * 获取用户详细信息（管理员功能）
 */
export const getUserDetails = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    const user = await prisma.user.findUnique({
      where: { id: parseInt(userId) },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        role: true,
        isActive: true,
        emailVerified: true,
        createdAt: true,
        updatedAt: true,
        // 关联数据统计
        _count: {
          select: {
            emails: { where: { isDeleted: false } },
            folders: true,
            contacts: true,
            templates: true,
            rules: true
          }
        }
      }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    const response: ApiResponse = {
      success: true,
      message: '获取用户详情成功',
      data: {
        ...user,
        stats: {
          emailCount: user._count.emails,
          folderCount: user._count.folders,
          contactCount: user._count.contacts,
          templateCount: user._count.templates,
          ruleCount: user._count.rules
        },
        _count: undefined
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取用户详情失败:', error);
    throw new AppError('获取用户详情失败', 500);
  }
};

/**
 * 删除用户
 */
export const deleteUser = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userId } = req.params;
    const adminUser = req.user;

    // 验证管理员权限
    if (adminUser.role !== 'admin') {
      throw new AppError('权限不足', 403);
    }

    // 获取要删除的用户信息
    const targetUser = await prisma.user.findUnique({
      where: { id: parseInt(userId) },
      select: {
        id: true,
        email: true,
        username: true,
        role: true,
        isActive: true
      }
    });

    if (!targetUser) {
      throw new AppError('用户不存在', 404);
    }

    // 防止删除系统用户
    const systemEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    if (systemEmails.includes(targetUser.email)) {
      throw new AppError('系统用户不能删除', 400);
    }

    // 防止管理员删除自己
    if (targetUser.id === adminUser.id) {
      throw new AppError('不能删除自己的账户', 400);
    }

    // 开始事务删除用户及相关数据
    await prisma.$transaction(async (tx) => {
      // 删除用户的邮件
      await tx.email.deleteMany({
        where: { userId: targetUser.id }
      });

      // 删除用户的文件夹
      await tx.folder.deleteMany({
        where: { userId: targetUser.id }
      });

      // 删除用户的联系人
      await tx.contact.deleteMany({
        where: { userId: targetUser.id }
      });

      // 删除用户的邮件规则
      await tx.emailRule.deleteMany({
        where: { userId: targetUser.id }
      });

      // 删除用户的安全日志
      await tx.securityLog.deleteMany({
        where: { userId: targetUser.id }
      });

      // 最后删除用户
      await tx.user.delete({
        where: { id: targetUser.id }
      });
    });

    // 记录删除操作日志
    logger.info(`管理员 ${adminUser.email} 删除了用户 ${targetUser.email}`, {
      adminId: adminUser.id,
      deletedUserId: targetUser.id,
      deletedUserEmail: targetUser.email,
      service: 'email-system',
      timestamp: new Date().toISOString()
    });

    const response: ApiResponse = {
      success: true,
      message: '用户删除成功',
      data: {
        deletedUserId: targetUser.id,
        deletedUserEmail: targetUser.email
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('删除用户失败:', error);
    if (error instanceof AppError) {
      res.status(error.statusCode).json({
        success: false,
        message: error.message
      });
    } else {
      res.status(500).json({
        success: false,
        message: '删除用户失败'
      });
    }
  }
};
