/**
 * 密码策略配置
 * 用于选择和配置密码加密方案
 */

export type PasswordStrategy = 'unified' | 'dual' | 'migration' | 'sync';

export interface PasswordConfig {
  strategy: PasswordStrategy;
  enableMailPasswordSync: boolean;
  enablePasswordMigration: boolean;
  requireMailPasswordForNewUsers: boolean;
}

// 默认配置
export const defaultPasswordConfig: PasswordConfig = {
  strategy: 'sync', // 推荐使用同步策略
  enableMailPasswordSync: true,
  enablePasswordMigration: false,
  requireMailPasswordForNewUsers: true
};

// 从环境变量读取配置
export const getPasswordConfig = (): PasswordConfig => {
  return {
    strategy: (process.env['PASSWORD_STRATEGY'] as PasswordStrategy) || defaultPasswordConfig.strategy,
    enableMailPasswordSync: process.env['ENABLE_MAIL_PASSWORD_SYNC'] === 'true' || defaultPasswordConfig.enableMailPasswordSync,
    enablePasswordMigration: process.env['ENABLE_PASSWORD_MIGRATION'] === 'true' || defaultPasswordConfig.enablePasswordMigration,
    requireMailPasswordForNewUsers: process.env['REQUIRE_MAIL_PASSWORD_FOR_NEW_USERS'] === 'true' || defaultPasswordConfig.requireMailPasswordForNewUsers
  };
};

export const passwordConfig = getPasswordConfig();
