import express from 'express';
import { authenticate } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

/**
 * 简单的状态检查
 */
router.get('/status', authenticate, async (req, res) => {
  try {
    res.json({
      success: true,
      message: '统一同步服务路由正常',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('获取状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取状态失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 测试同步服务
 */
router.post('/test', authenticate, async (req, res) => {
  try {
    // 动态导入避免循环依赖
    const { simpleEmailSyncService } = await import('../services/simpleEmailSyncService');
    const status = simpleEmailSyncService.getStatus();

    res.json({
      success: true,
      message: '简单同步服务测试成功',
      data: status
    });
  } catch (error) {
    logger.error('测试同步服务失败:', error);
    res.status(500).json({
      success: false,
      message: '测试同步服务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

export default router;
