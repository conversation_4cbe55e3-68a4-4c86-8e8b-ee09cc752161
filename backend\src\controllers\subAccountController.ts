import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import subAccountService from '../services/subAccountService';
import prisma from '../config/database';
import logger from '../utils/logger';

// 检查是否可以创建子账户
export const checkCanCreateSubAccount = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const result = await subAccountService.canCreateSubAccount(userId);

    const response: ApiResponse = {
      success: true,
      message: '检查子账户创建权限成功',
      data: result,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`检查子账户创建权限失败: ${(error as Error).message}`, 500);
  }
};

// 启用/禁用子账户功能
export const toggleSubAccountFeature = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { enabled } = req.body;

    if (typeof enabled !== 'boolean') {
      throw new AppError('请提供有效的启用状态', 400);
    }

    // 更新用户的子账户功能状态
    await prisma.user.update({
      where: { id: userId },
      data: { isSubAccountEnabled: enabled }
    });

    const response: ApiResponse = {
      success: true,
      message: `子账户功能已${enabled ? '启用' : '禁用'}`,
      data: { enabled }
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新子账户功能状态失败: ${(error as Error).message}`, 500);
  }
};

// 生成随机子账户信息
export const generateRandomSubAccountData = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const config = req.body || {};

    const randomData = await subAccountService.generateRandomSubAccountData(userId, config);

    res.json({
      success: true,
      data: randomData
    });
  } catch (error: any) {
    throw new AppError(error.message || '生成随机子账户信息失败', 500);
  }
};

// 创建子账户
export const createSubAccount = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { email, username, displayName, permissions, quotaLimit, password } = req.body;

    // 验证必填字段
    if (!email || !username || !permissions) {
      throw new AppError('请提供完整的子账户信息', 400);
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new AppError('邮箱格式不正确', 400);
    }

    // 验证用户名格式
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      throw new AppError('用户名格式不正确（3-20位字母、数字、下划线）', 400);
    }

    const subAccount = await subAccountService.createSubAccount(userId, {
      email,
      username,
      displayName,
      permissions,
      quotaLimit,
      password
    });

    const response: ApiResponse = {
      success: true,
      message: '子账户创建成功',
      data: subAccount,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`创建子账户失败: ${(error as Error).message}`, 500);
  }
};

// 发送子账户邀请
export const sendSubAccountInvitation = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { email, permissions, quotaLimit, expiresInHours } = req.body;

    // 验证必填字段
    if (!email || !permissions) {
      throw new AppError('请提供邮箱和权限信息', 400);
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new AppError('邮箱格式不正确', 400);
    }

    const invitation = await subAccountService.sendSubAccountInvitation(userId, {
      email,
      permissions,
      quotaLimit,
      expiresInHours
    });

    const response: ApiResponse = {
      success: true,
      message: '子账户邀请已发送',
      data: invitation,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`发送子账户邀请失败: ${(error as Error).message}`, 500);
  }
};

// 接受子账户邀请
export const acceptSubAccountInvitation = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { invitationToken } = req.params;
    const { username, displayName, password } = req.body;

    // 验证必填字段
    if (!username || !password) {
      throw new AppError('请提供用户名和密码', 400);
    }

    // 验证用户名格式
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    if (!usernameRegex.test(username)) {
      throw new AppError('用户名格式不正确（3-20位字母、数字、下划线）', 400);
    }

    // 验证密码强度
    if (password.length < 8) {
      throw new AppError('密码长度至少8位', 400);
    }

    const subAccount = await subAccountService.acceptSubAccountInvitation(invitationToken, {
      username,
      displayName,
      password
    });

    const response: ApiResponse = {
      success: true,
      message: '子账户邀请接受成功',
      data: subAccount,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`接受子账户邀请失败: ${(error as Error).message}`, 500);
  }
};

// 获取子账户列表
export const getSubAccounts = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { includeInactive, page, limit } = req.query;

    const options = {
      includeInactive: includeInactive === 'true',
      page: page ? parseInt(String(page)) : 1,
      limit: limit ? parseInt(String(limit)) : 20
    };

    const result = await subAccountService.getSubAccounts(userId, options);

    const response: ApiResponse = {
      success: true,
      message: '获取子账户列表成功',
      data: result,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取子账户列表失败: ${(error as Error).message}`, 500);
  }
};

// 获取子账户详情
export const getSubAccountDetails = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { subAccountId } = req.params;

    const subAccountIdNum = parseInt(subAccountId);
    if (isNaN(subAccountIdNum)) {
      throw new AppError('无效的子账户ID', 400);
    }

    // 验证子账户是否属于当前用户
    const subAccount = await prisma.user.findFirst({
      where: {
        id: subAccountIdNum,
        parentUserId: userId
      },
      include: {
        subAccountPermissions: true,
        subAccountUsage: true,
        emailAccounts: {
          select: {
            id: true,
            email: true,
            connectionStatus: true,
            syncStatus: true,
            lastSyncAt: true
          }
        },
        _count: {
          select: {
            emails: { where: { isDeleted: false } },
            folders: true,
            contacts: true
          }
        }
      }
    });

    if (!subAccount) {
      throw new AppError('子账户不存在或无权限访问', 404);
    }

    const response: ApiResponse = {
      success: true,
      message: '获取子账户详情成功',
      data: {
        ...subAccount,
        password: undefined,
        mailPassword: undefined
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取子账户详情失败: ${(error as Error).message}`, 500);
  }
};

// 更新子账户权限
export const updateSubAccountPermissions = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { subAccountId } = req.params;
    const { permissions } = req.body;

    const subAccountIdNum = parseInt(subAccountId);
    if (isNaN(subAccountIdNum)) {
      throw new AppError('无效的子账户ID', 400);
    }

    if (!permissions) {
      throw new AppError('请提供权限信息', 400);
    }

    // 验证子账户是否属于当前用户
    const subAccount = await prisma.user.findFirst({
      where: {
        id: subAccountIdNum,
        parentUserId: userId
      }
    });

    if (!subAccount) {
      throw new AppError('子账户不存在或无权限访问', 404);
    }

    // 更新权限
    const permissionMappings = [
      { type: 'email_send', allowed: permissions.emailSend },
      { type: 'email_receive', allowed: permissions.emailReceive },
      { type: 'email_delete', allowed: permissions.emailDelete },
      { type: 'folder_manage', allowed: permissions.folderManage },
      { type: 'contact_manage', allowed: permissions.contactManage },
      { type: 'template_manage', allowed: permissions.templateManage },
      { type: 'rule_manage', allowed: permissions.ruleManage }
    ];

    for (const perm of permissionMappings) {
      await prisma.subAccountPermission.upsert({
        where: {
          subUserId_permissionType: {
            subUserId: subAccountIdNum,
            permissionType: perm.type
          }
        },
        update: { isAllowed: perm.allowed },
        create: {
          subUserId: subAccountIdNum,
          permissionType: perm.type,
          isAllowed: perm.allowed
        }
      });
    }

    // 记录活动日志
    await prisma.subAccountActivity.create({
      data: {
        parentUserId: userId,
        subUserId: subAccountIdNum,
        activityType: 'permission_change',
        activityDescription: '权限已更新',
        metadata: JSON.stringify(permissions)
      }
    });

    const response: ApiResponse = {
      success: true,
      message: '子账户权限更新成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新子账户权限失败: ${(error as Error).message}`, 500);
  }
};

// 更新子账户配额
export const updateSubAccountQuota = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { subAccountId } = req.params;
    const { quotaLimit } = req.body;

    const subAccountIdNum = parseInt(subAccountId);
    if (isNaN(subAccountIdNum)) {
      throw new AppError('无效的子账户ID', 400);
    }

    if (quotaLimit !== null && (typeof quotaLimit !== 'number' || quotaLimit < 0)) {
      throw new AppError('配额限制必须是非负数或null', 400);
    }

    // 验证子账户是否属于当前用户
    const subAccount = await prisma.user.findFirst({
      where: {
        id: subAccountIdNum,
        parentUserId: userId
      }
    });

    if (!subAccount) {
      throw new AppError('子账户不存在或无权限访问', 404);
    }

    // 更新存储配额
    await prisma.subAccountUsage.update({
      where: {
        subUserId_usageType: {
          subUserId: subAccountIdNum,
          usageType: 'storage'
        }
      },
      data: {
        usageLimit: quotaLimit
      }
    });

    // 记录活动日志
    await prisma.subAccountActivity.create({
      data: {
        parentUserId: userId,
        subUserId: subAccountIdNum,
        activityType: 'quota_change',
        activityDescription: `配额已更新为 ${quotaLimit ? `${quotaLimit} 字节` : '无限制'}`,
        metadata: JSON.stringify({ quotaLimit })
      }
    });

    const response: ApiResponse = {
      success: true,
      message: '子账户配额更新成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新子账户配额失败: ${(error as Error).message}`, 500);
  }
};

// 停用/启用子账户
export const toggleSubAccountStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { subAccountId } = req.params;
    const { isActive } = req.body;

    const subAccountIdNum = parseInt(subAccountId);
    if (isNaN(subAccountIdNum)) {
      throw new AppError('无效的子账户ID', 400);
    }

    if (typeof isActive !== 'boolean') {
      throw new AppError('请提供有效的状态值', 400);
    }

    // 验证子账户是否属于当前用户
    const subAccount = await prisma.user.findFirst({
      where: {
        id: subAccountIdNum,
        parentUserId: userId
      }
    });

    if (!subAccount) {
      throw new AppError('子账户不存在或无权限访问', 404);
    }

    // 更新账户状态
    await prisma.user.update({
      where: { id: subAccountIdNum },
      data: { isActive }
    });

    // 记录活动日志
    await prisma.subAccountActivity.create({
      data: {
        parentUserId: userId,
        subUserId: subAccountIdNum,
        activityType: isActive ? 'account_activated' : 'account_deactivated',
        activityDescription: `账户已${isActive ? '启用' : '停用'}`
      }
    });

    const response: ApiResponse = {
      success: true,
      message: `子账户已${isActive ? '启用' : '停用'}`,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`更新子账户状态失败: ${(error as Error).message}`, 500);
  }
};

// 删除子账户
export const deleteSubAccount = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { subAccountId } = req.params;

    const subAccountIdNum = parseInt(subAccountId);
    if (isNaN(subAccountIdNum)) {
      throw new AppError('无效的子账户ID', 400);
    }

    // 验证子账户是否属于当前用户
    const subAccount = await prisma.user.findFirst({
      where: {
        id: subAccountIdNum,
        parentUserId: userId
      }
    });

    if (!subAccount) {
      throw new AppError('子账户不存在或无权限访问', 404);
    }

    // 删除子账户（级联删除相关数据）
    await prisma.user.delete({
      where: { id: subAccountIdNum }
    });

    // 记录活动日志
    await prisma.subAccountActivity.create({
      data: {
        parentUserId: userId,
        subUserId: subAccountIdNum,
        activityType: 'account_deleted',
        activityDescription: `子账户 ${subAccount.email} 已删除`
      }
    });

    const response: ApiResponse = {
      success: true,
      message: '子账户删除成功',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`删除子账户失败: ${(error as Error).message}`, 500);
  }
};
