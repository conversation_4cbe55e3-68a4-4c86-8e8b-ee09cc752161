import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import emailAccountMonitorService from '../services/emailAccountMonitorService';
import logger from '../utils/logger';

// 获取监控服务状态
export const getMonitorStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const status = emailAccountMonitorService.getStatus();

    const response: ApiResponse = {
      success: true,
      message: '获取监控状态成功',
      data: status,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取监控状态失败: ${(error as Error).message}`, 500);
  }
};

// 手动触发连接检查
export const triggerConnectionCheck = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 只有管理员可以触发全局检查
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    await emailAccountMonitorService.triggerConnectionCheck();

    const response: ApiResponse = {
      success: true,
      message: '连接检查已触发',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`触发连接检查失败: ${(error as Error).message}`, 500);
  }
};

// 检查用户的邮箱账户
export const checkUserAccounts = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  try {
    await emailAccountMonitorService.checkUserAccounts(userId);

    const response: ApiResponse = {
      success: true,
      message: '用户邮箱账户检查完成',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`检查用户邮箱账户失败: ${(error as Error).message}`, 500);
  }
};

// 获取账户健康状态统计
export const getAccountHealthStats = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const stats = await emailAccountMonitorService.getAccountHealthStats();

    const response: ApiResponse = {
      success: true,
      message: '获取账户健康状态统计成功',
      data: stats,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取账户健康状态统计失败: ${(error as Error).message}`, 500);
  }
};

// 启动监控服务（管理员功能）
export const startMonitorService = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    emailAccountMonitorService.start();

    const response: ApiResponse = {
      success: true,
      message: '邮箱账户监控服务已启动',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`启动监控服务失败: ${(error as Error).message}`, 500);
  }
};

// 停止监控服务（管理员功能）
export const stopMonitorService = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    emailAccountMonitorService.stop();

    const response: ApiResponse = {
      success: true,
      message: '邮箱账户监控服务已停止',
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`停止监控服务失败: ${(error as Error).message}`, 500);
  }
};
