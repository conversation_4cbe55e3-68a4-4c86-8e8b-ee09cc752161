import api from '../config/api';
import type { ApiResponse } from '../types';

export interface MailStatus {
  email: string;
  emailVerified: boolean;
  isMailActive: boolean;
  hasMailPassword: boolean;
  needsPasswordSetup: boolean;
}

export interface MailPasswordSetupData {
  password: string;
}

export interface MailPasswordResetData {
  currentPassword?: string;
  newPassword: string;
}

// 获取邮件状态
export const getMailStatus = async (): Promise<MailStatus> => {
  const response = await api.get<ApiResponse<MailStatus>>('/mail/status');
  return response.data.data!;
};

// 设置邮件密码
export const setupMailPassword = async (data: MailPasswordSetupData): Promise<void> => {
  await api.post('/mail/setup-password', data);
};

// 重置邮件密码
export const resetMailPassword = async (data: MailPasswordResetData): Promise<void> => {
  await api.post('/mail/reset-password', data);
};
