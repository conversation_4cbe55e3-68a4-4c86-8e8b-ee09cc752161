import { Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';

/**
 * 验证用户是否有权限管理子账户
 * 只有主账户或管理员可以管理子账户
 */
export const requireSubAccountManagePermission = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的请求'
      });
    }

    // 查询用户完整信息
    const user = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        role: true,
        accountType: true,
        isActive: true,
        isSubAccountEnabled: true
      }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    if (!user.isActive) {
      return res.status(403).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }

    // 管理员有完全权限
    if (user.role === 'admin' || user.role === 'super_admin') {
      return next();
    }

    // 检查是否为主账户
    if (user.accountType !== 'main') {
      logger.warn(`子账户尝试访问主子账户管理功能: ${user.email}`);
      return res.status(403).json({
        success: false,
        message: '只有主账户可以管理子账户'
      });
    }

    // 检查是否启用了子账户功能
    if (!user.isSubAccountEnabled) {
      return res.status(403).json({
        success: false,
        message: '子账户功能未启用'
      });
    }

    next();
  } catch (error) {
    logger.error('子账户管理权限验证失败:', error);
    res.status(500).json({
      success: false,
      message: '权限验证失败'
    });
  }
};

/**
 * 验证子账户是否有特定功能权限
 * @param permissionType 权限类型
 */
export const requireSubAccountPermission = (permissionType: string) => {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          message: '未认证的请求'
        });
      }

      // 查询用户信息
      const user = await prisma.user.findUnique({
        where: { id: req.user.id },
        select: {
          id: true,
          email: true,
          role: true,
          accountType: true,
          isActive: true
        }
      });

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      if (!user.isActive) {
        return res.status(403).json({
          success: false,
          message: '用户账户已被禁用'
        });
      }

      // 管理员和主账户有完全权限
      if (user.role === 'admin' || user.role === 'super_admin' || user.accountType === 'main') {
        return next();
      }

      // 子账户需要检查具体权限
      if (user.accountType === 'sub') {
        const permission = await prisma.subAccountPermission.findUnique({
          where: {
            subUserId_permissionType: {
              subUserId: user.id,
              permissionType: permissionType
            }
          }
        });

        if (!permission || !permission.isAllowed) {
          logger.warn(`子账户 ${user.email} 尝试访问无权限功能: ${permissionType}`);
          return res.status(403).json({
            success: false,
            message: `无权限执行此操作: ${permissionType}`
          });
        }
      }

      next();
    } catch (error) {
      logger.error('子账户权限验证失败:', error);
      res.status(500).json({
        success: false,
        message: '权限验证失败'
      });
    }
  };
};

/**
 * 验证用户是否可以访问指定子账户的资源
 * 主账户只能访问自己的子账户，管理员可以访问所有
 */
export const requireSubAccountOwnership = async (
  req: AuthenticatedRequest, 
  res: Response, 
  next: NextFunction
) => {
  try {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '未认证的请求'
      });
    }

    const subAccountId = req.params.subAccountId;
    if (!subAccountId) {
      return res.status(400).json({
        success: false,
        message: '缺少子账户ID参数'
      });
    }

    // 查询当前用户信息
    const currentUser = await prisma.user.findUnique({
      where: { id: req.user.id },
      select: {
        id: true,
        email: true,
        role: true,
        accountType: true,
        isActive: true
      }
    });

    if (!currentUser) {
      return res.status(404).json({
        success: false,
        message: '当前用户不存在'
      });
    }

    if (!currentUser.isActive) {
      return res.status(403).json({
        success: false,
        message: '用户账户已被禁用'
      });
    }

    // 管理员可以访问所有子账户
    if (currentUser.role === 'admin' || currentUser.role === 'super_admin') {
      return next();
    }

    // 验证子账户是否属于当前主账户
    const subAccount = await prisma.user.findFirst({
      where: {
        id: parseInt(subAccountId),
        parentUserId: currentUser.id,
        accountType: 'sub'
      }
    });

    if (!subAccount) {
      logger.warn(`用户 ${currentUser.email} 尝试访问不属于自己的子账户: ${subAccountId}`);
      return res.status(403).json({
        success: false,
        message: '无权访问该子账户'
      });
    }

    next();
  } catch (error) {
    logger.error('子账户所有权验证失败:', error);
    res.status(500).json({
      success: false,
      message: '权限验证失败'
    });
  }
};
