# Docker 环境变量配置文件
# 复制此文件为 .env 并填入正确的配置

# ===========================================
# 数据库配置
# ===========================================
DB_ROOT_PASSWORD=your-strong-root-password
DB_NAME=mailserver
DB_USER=emailuser
DB_PASSWORD=your-strong-db-password

# ===========================================
# Redis 配置
# ===========================================
REDIS_PASSWORD=your-strong-redis-password

# ===========================================
# 应用程序配置
# ===========================================
JWT_SECRET=your-super-secret-jwt-key-at-least-32-characters-long
NODE_ENV=production
LOG_LEVEL=info

# ===========================================
# 前端配置
# ===========================================
FRONTEND_URL=http://localhost
# 生产环境使用: https://mail.blindedby.love

# ===========================================
# 邮件服务器配置
# ===========================================
SMTP_HOST=mail.blindedby.love
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

IMAP_HOST=mail.blindedby.love
IMAP_PORT=993
IMAP_USER=<EMAIL>
IMAP_PASS=your-email-password

# ===========================================
# SSL/TLS 配置 (生产环境)
# ===========================================
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# ===========================================
# 监控配置 (可选)
# ===========================================
ENABLE_MONITORING=false
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# ===========================================
# 备份配置 (可选)
# ===========================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30

# ===========================================
# 开发环境特定配置
# ===========================================
# 开发环境下可以启用调试模式
DEBUG=false
VERBOSE_LOGGING=false

# ===========================================
# 安全配置
# ===========================================
# CORS 配置
CORS_ORIGIN=http://localhost,https://mail.blindedby.love
CORS_CREDENTIALS=true

# 速率限制
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session 配置
SESSION_SECRET=your-session-secret-key
SESSION_MAX_AGE=86400000

# ===========================================
# 文件上传配置
# ===========================================
MAX_FILE_SIZE=10485760
UPLOAD_PATH=/app/uploads
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt

# ===========================================
# 邮件处理配置
# ===========================================
EMAIL_SYNC_INTERVAL=300000
MAX_EMAILS_PER_SYNC=100
EMAIL_RETENTION_DAYS=365

# ===========================================
# 性能配置
# ===========================================
# 数据库连接池
DB_CONNECTION_LIMIT=10
DB_CONNECTION_TIMEOUT=60000

# Redis 连接
REDIS_CONNECTION_TIMEOUT=5000
REDIS_RETRY_ATTEMPTS=3

# ===========================================
# 日志配置
# ===========================================
LOG_FILE_PATH=/app/logs/app.log
LOG_MAX_SIZE=10485760
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# ===========================================
# 健康检查配置
# ===========================================
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# ===========================================
# 通知配置 (可选)
# ===========================================
NOTIFICATION_EMAIL=<EMAIL>
SLACK_WEBHOOK_URL=
DISCORD_WEBHOOK_URL=

# ===========================================
# 时区配置
# ===========================================
TZ=Asia/Shanghai
