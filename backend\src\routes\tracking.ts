import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate, optionalAuth } from '../middleware/auth';
import * as trackingController from '../controllers/trackingController';

const router = Router();

// 跟踪像素端点（不需要认证）
router.get('/pixel/:trackingId',
  asyncHandler(trackingController.trackPixel)
);

// 跟踪链接点击端点（不需要认证）
router.get('/click/:trackingId',
  asyncHandler(trackingController.trackClick)
);

// 需要认证的路由
router.use(authenticate);

// 获取邮件跟踪统计
router.get('/stats/:emailId',
  asyncHandler(trackingController.getTrackingStats)
);

// 创建邮件跟踪
router.post('/create/:emailId',
  asyncHandler(trackingController.createTracking)
);

// 发送已读回执请求
router.post('/receipt/:emailId',
  asyncHandler(trackingController.sendReceiptRequest)
);

// 获取跟踪概览
router.get('/overview',
  asyncHandler(trackingController.getTrackingOverview)
);

export default router;
