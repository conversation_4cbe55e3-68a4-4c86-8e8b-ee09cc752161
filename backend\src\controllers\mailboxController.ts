import { Response } from 'express';
import { AuthenticatedRequest, AppError, ApiResponse } from '../types';
import logger from '../utils/logger';
import {
  createUserMailboxFolders,
  checkUserMailboxFolders,
  repairUserMailboxFolders,
  getMailboxStats,
  scanUserMailboxFiles,
  compareMailboxWithDatabase
} from '../services/mailboxService';
import {
  fetchAllEmailsFromIMAP,
  compareIMAPWithDatabase,
  syncIMAPToDatabase as syncIMAPToDatabaseService,
  cleanupDatabaseEmails as cleanupDatabaseEmailsService,
  fullEmailSync as fullEmailSyncService
} from '../services/imapService';

/**
 * 检查用户邮箱文件夹状态
 */
export const checkMailboxFolders = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;
    
    const status = await checkUserMailboxFolders(userEmail);
    
    const response: ApiResponse = {
      success: true,
      message: '邮箱文件夹状态检查完成',
      data: {
        email: userEmail,
        ...status
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('检查邮箱文件夹失败:', error);
    throw new AppError('检查邮箱文件夹失败', 500);
  }
};

/**
 * 创建用户邮箱文件夹
 */
export const createMailboxFolders = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;
    
    await createUserMailboxFolders(userEmail);
    
    const response: ApiResponse = {
      success: true,
      message: '邮箱文件夹创建成功',
      data: {
        email: userEmail
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('创建邮箱文件夹失败:', error);
    throw new AppError('创建邮箱文件夹失败', 500);
  }
};

/**
 * 修复用户邮箱文件夹
 */
export const repairMailboxFolders = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;
    
    await repairUserMailboxFolders(userEmail);
    
    const response: ApiResponse = {
      success: true,
      message: '邮箱文件夹修复成功',
      data: {
        email: userEmail
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('修复邮箱文件夹失败:', error);
    throw new AppError('修复邮箱文件夹失败', 500);
  }
};

/**
 * 获取邮箱统计信息
 */
export const getMailboxStatistics = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;
    
    const stats = await getMailboxStats(userEmail);
    
    const response: ApiResponse = {
      success: true,
      message: '邮箱统计信息获取成功',
      data: {
        email: userEmail,
        ...stats
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('获取邮箱统计信息失败:', error);
    throw new AppError('获取邮箱统计信息失败', 500);
  }
};

/**
 * 扫描用户邮件文件夹
 */
export const scanMailboxFiles = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const scanResult = await scanUserMailboxFiles(userEmail);

    const response: ApiResponse = {
      success: true,
      message: '邮件文件夹扫描完成',
      data: {
        email: userEmail,
        ...scanResult
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('扫描邮件文件夹失败:', error);
    throw new AppError('扫描邮件文件夹失败', 500);
  }
};

/**
 * 对比文件夹邮件与数据库邮件
 */
export const compareMailboxData = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const comparison = await compareMailboxWithDatabase(userEmail);

    const response: ApiResponse = {
      success: true,
      message: '邮件数据对比完成',
      data: {
        email: userEmail,
        ...comparison
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('对比邮件数据失败:', error);
    throw new AppError('对比邮件数据失败', 500);
  }
};

/**
 * 获取IMAP服务器上的所有邮件
 */
export const fetchIMAPEmails = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const imapEmails = await fetchAllEmailsFromIMAP(userEmail);

    const response: ApiResponse = {
      success: true,
      message: 'IMAP邮件获取完成',
      data: {
        email: userEmail,
        count: imapEmails.length,
        emails: imapEmails
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('获取IMAP邮件失败:', error);
    throw new AppError('获取IMAP邮件失败', 500);
  }
};

/**
 * 对比IMAP邮件与数据库邮件
 */
export const compareIMAPData = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const comparison = await compareIMAPWithDatabase(userEmail);

    const response: ApiResponse = {
      success: true,
      message: 'IMAP邮件数据对比完成',
      data: {
        email: userEmail,
        ...comparison
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('对比IMAP邮件数据失败:', error);
    throw new AppError('对比IMAP邮件数据失败', 500);
  }
};

/**
 * 从IMAP同步邮件到数据库
 */
export const syncIMAPToDatabase = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const syncResult = await syncIMAPToDatabaseService(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `IMAP邮件同步完成，成功同步 ${syncResult.syncedCount} 封邮件`,
      data: {
        email: userEmail,
        ...syncResult
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('IMAP邮件同步失败:', error);
    throw new AppError('IMAP邮件同步失败', 500);
  }
};

/**
 * 清理数据库中的无效邮件
 */
export const cleanupDatabaseEmails = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const cleanupResult = await cleanupDatabaseEmailsService(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `数据库邮件清理完成，删除 ${cleanupResult.deletedCount} 封无效邮件`,
      data: {
        email: userEmail,
        ...cleanupResult
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('数据库邮件清理失败:', error);
    throw new AppError('数据库邮件清理失败', 500);
  }
};

/**
 * 完整的邮件同步
 */
export const fullEmailSync = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userEmail = req.user!.email;

    const syncResult = await fullEmailSyncService(userEmail);

    const response: ApiResponse = {
      success: true,
      message: `完整邮件同步完成，同步 ${syncResult.syncResult.syncedCount} 封邮件，清理 ${syncResult.cleanupResult.deletedCount} 封无效邮件`,
      data: {
        email: userEmail,
        ...syncResult
      },
    };

    res.json(response);
  } catch (error) {
    logger.error('完整邮件同步失败:', error);
    throw new AppError('完整邮件同步失败', 500);
  }
};
