#!/usr/bin/env node

/**
 * 系统邮件功能测试脚本
 * 用于测试欢迎邮件和退信通知功能
 */

const { PrismaClient } = require('@prisma/client');

// 尝试导入系统邮件服务
let SystemEmailService;
try {
  SystemEmailService = require('../dist/services/systemEmailService').default;
} catch (error) {
  console.error('无法加载系统邮件服务，请先编译项目：npm run build');
  process.exit(1);
}

const prisma = new PrismaClient();

// 测试发送欢迎邮件
async function testWelcomeEmail() {
  console.log('\n🧪 测试欢迎邮件发送...');
  
  try {
    // 检查welcome用户是否存在
    const welcomeUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!welcomeUser) {
      console.log('❌ Welcome用户不存在，请先运行 npm run db:seed');
      return false;
    }

    console.log('✓ Welcome用户存在');

    // 发送测试欢迎邮件
    await SystemEmailService.sendWelcomeEmail('<EMAIL>', 'testuser');
    console.log('✅ 欢迎邮件发送成功');
    return true;

  } catch (error) {
    console.error('❌ 欢迎邮件发送失败:', error.message);
    return false;
  }
}

// 测试发送退信通知
async function testBounceNotification() {
  console.log('\n🧪 测试退信通知发送...');
  
  try {
    // 检查postmaster用户是否存在
    const postmasterUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!postmasterUser) {
      console.log('❌ Postmaster用户不存在，请先运行 npm run db:seed');
      return false;
    }

    console.log('✓ Postmaster用户存在');

    // 发送测试退信通知
    await SystemEmailService.sendBounceNotification(
      '<EMAIL>',
      '<EMAIL>',
      '测试邮件主题',
      '550 User unknown in virtual mailbox table',
      'test-message-id-123'
    );
    console.log('✅ 退信通知发送成功');
    return true;

  } catch (error) {
    console.error('❌ 退信通知发送失败:', error.message);
    return false;
  }
}

// 检查系统用户状态
async function checkSystemUsers() {
  console.log('\n🔍 检查系统用户状态...');
  
  try {
    const systemUsers = await prisma.user.findMany({
      where: {
        email: {
          in: ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        }
      },
      select: {
        email: true,
        username: true,
        displayName: true,
        role: true,
        isActive: true,
        emailVerified: true
      }
    });

    console.log('系统用户列表:');
    systemUsers.forEach(user => {
      const status = user.isActive ? '✅' : '❌';
      const verified = user.emailVerified ? '✅' : '❌';
      console.log(`  ${status} ${user.email} (${user.role}) - 验证: ${verified}`);
    });

    return systemUsers.length >= 2; // 至少需要welcome和postmaster

  } catch (error) {
    console.error('❌ 检查系统用户失败:', error.message);
    return false;
  }
}

// 检查邮件模板
async function checkEmailTemplates() {
  console.log('\n📧 检查邮件模板...');
  
  try {
    const templates = await prisma.emailTemplate.findMany({
      where: {
        userId: null, // 系统模板
        isActive: true
      },
      select: {
        name: true,
        subject: true,
        isActive: true
      }
    });

    console.log('系统邮件模板:');
    templates.forEach(template => {
      console.log(`  ✅ ${template.name} - ${template.subject}`);
    });

    const hasWelcomeTemplate = templates.some(t => t.name.includes('欢迎'));
    const hasBounceTemplate = templates.some(t => t.name.includes('退信'));

    if (!hasWelcomeTemplate) {
      console.log('⚠️  缺少欢迎邮件模板');
    }
    if (!hasBounceTemplate) {
      console.log('⚠️  缺少退信通知模板');
    }

    return templates.length > 0;

  } catch (error) {
    console.error('❌ 检查邮件模板失败:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 系统邮件功能测试');
  console.log('================================\n');

  try {
    // 检查系统用户
    const usersOk = await checkSystemUsers();
    if (!usersOk) {
      console.log('\n❌ 系统用户检查失败，请先运行 npm run db:seed 创建系统用户');
      return;
    }

    // 检查邮件模板
    const templatesOk = await checkEmailTemplates();
    if (!templatesOk) {
      console.log('\n⚠️  邮件模板检查失败，但不影响基本功能');
    }

    // 测试欢迎邮件
    const welcomeOk = await testWelcomeEmail();

    // 测试退信通知
    const bounceOk = await testBounceNotification();

    // 总结
    console.log('\n📊 测试结果总结:');
    console.log('================================');
    console.log(`系统用户: ${usersOk ? '✅' : '❌'}`);
    console.log(`邮件模板: ${templatesOk ? '✅' : '⚠️'}`);
    console.log(`欢迎邮件: ${welcomeOk ? '✅' : '❌'}`);
    console.log(`退信通知: ${bounceOk ? '✅' : '❌'}`);

    if (welcomeOk && bounceOk) {
      console.log('\n🎉 所有测试通过！系统邮件功能正常工作。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查配置和日志。');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n\n操作已取消。');
  await prisma.$disconnect();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
