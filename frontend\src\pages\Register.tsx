import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Form, Input, Button, Alert, Divider, Checkbox, message } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, SafetyOutlined, RobotOutlined } from '@ant-design/icons';
import { useAuthStore } from '../store/authStore';
import PasswordStrengthBar from '../components/PasswordStrengthBar';

const steps = [
  '真人验证',
  '密码设置',
  '基础信息',
];

const Register: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { register, loading, error, clearError } = useAuthStore();
  const [step, setStep] = useState(0);
  const [emailPrefix, setEmailPrefix] = useState('');
  const [humanVerified, setHumanVerified] = useState(false);
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [agreed, setAgreed] = useState(false);

  // 真人验证相关状态
  const [captchaVerifying, setCaptchaVerifying] = useState(false);
  const [captchaCompleted, setCaptchaCompleted] = useState(false);

  // 密码输入框引用，用于禁用复制粘贴
  const passwordRef = useRef<any>(null);
  const confirmPasswordRef = useRef<any>(null);

  // 禁用密码输入框的复制粘贴
  useEffect(() => {
    const disablePasteAndCopy = (e: ClipboardEvent) => {
      e.preventDefault();
    };

    const passwordInput = passwordRef.current?.input;
    const confirmPasswordInput = confirmPasswordRef.current?.input;

    if (passwordInput) {
      passwordInput.addEventListener('paste', disablePasteAndCopy);
      passwordInput.addEventListener('copy', disablePasteAndCopy);
    }
    if (confirmPasswordInput) {
      confirmPasswordInput.addEventListener('paste', disablePasteAndCopy);
      confirmPasswordInput.addEventListener('copy', disablePasteAndCopy);
    }

    return () => {
      if (passwordInput) {
        passwordInput.removeEventListener('paste', disablePasteAndCopy);
        passwordInput.removeEventListener('copy', disablePasteAndCopy);
      }
      if (confirmPasswordInput) {
        confirmPasswordInput.removeEventListener('paste', disablePasteAndCopy);
        confirmPasswordInput.removeEventListener('copy', disablePasteAndCopy);
      }
    };
  }, [step]);

  // 验证邮箱用户名格式
  const validateEmailPrefix = (prefix: string) => {
    if (!prefix) {
      return '请输入邮箱用户名';
    }
    if (prefix.length < 6) {
      return '用户名长度至少6位';
    }
    if (prefix.length > 12) {
      return '用户名长度不能超过12位';
    }
    if (!/^[a-zA-Z0-9]+$/.test(prefix)) {
      return '用户名只能包含字母和数字';
    }
    return '';
  };

  // 模拟真人验证
  const handleHumanVerification = () => {
    const validationError = validateEmailPrefix(emailPrefix);
    if (validationError) {
      message.error(validationError);
      return;
    }

    setCaptchaVerifying(true);
    // 模拟真人验证过程
    setTimeout(() => {
      setCaptchaCompleted(true);
      setCaptchaVerifying(false);
      setHumanVerified(true);
      message.success('真人验证成功！');
    }, 2000);
  };

  // 进入下一步
  const handleNextStep = () => {
    if (step === 0 && humanVerified) {
      setStep(1);
    } else if (step === 1) {
      setStep(2);
    }
  };

  // 注册提交
  const handleRegister = (values: { username: string; displayName: string }) => {
    const validationError = validateEmailPrefix(emailPrefix);
    if (validationError) {
      message.error(validationError);
      return;
    }

    const fullEmail = `${emailPrefix}@blindedby.love`;

    void (async () => {
      try {
        clearError();
        await register({
          email: fullEmail,
          password,
          username: values.username,
          displayName: values.displayName,
        });



        void navigate('/inbox');
      } catch {
        // 错误已在store处理
      }
    })();
  };



  // 步骤内容
  const renderStep = () => {
    switch (step) {
      case 0:
        return (
          <div>
            <Form layout="vertical">
              <Form.Item
                label={<span style={{ color: '#FF7F50' }} className="font-medium">邮箱地址</span>}
                required
              >
                <Input
                  prefix={<MailOutlined style={{ color: '#FF7F50' }} />}
                  placeholder="请输入用户名（6-12位字母数字）"
                  autoComplete="email"
                  className="h-10"
                  style={{ borderRadius: '6px', borderColor: '#e2e8f0' }}
                  value={emailPrefix}
                  onChange={e => setEmailPrefix(e.target.value)}
                  disabled={false}
                  suffix={<span style={{ color: '#a0aec0' }}>@blindedby.love</span>}
                />
                {emailPrefix && (
                  <div className="text-xs mt-1">
                    <div style={{ color: '#4a5568' }}>
                      完整邮箱：<span style={{ color: '#FF7F50' }}>{emailPrefix}@blindedby.love</span>
                    </div>
                  </div>
                )}
                {emailPrefix && validateEmailPrefix(emailPrefix) && (
                  <div className="text-xs text-red-500 mt-1">
                    {validateEmailPrefix(emailPrefix)}
                  </div>
                )}
              </Form.Item>

              <Form.Item label={<span style={{ color: '#FF7F50' }} className="font-medium">真人验证</span>} required>
                <div className="border rounded-lg p-4" style={{ borderColor: '#e2e8f0', backgroundColor: '#f8fafc' }}>
                  <div className="flex items-center justify-center mb-4">
                    <RobotOutlined style={{ fontSize: '48px', color: captchaCompleted ? '#48bb78' : '#FF7F50' }} />
                  </div>
                  <div className="text-center mb-4">
                    <p className="text-sm text-gray-600 mb-2">
                      {captchaCompleted ? '✓ 真人验证已完成' : '请点击下方按钮完成真人验证'}
                    </p>
                  </div>
                  <Button
                    type="primary"
                    onClick={handleHumanVerification}
                    loading={captchaVerifying}
                    disabled={captchaCompleted}
                    block
                    style={{
                      backgroundColor: captchaCompleted ? '#48bb78' : '#FF7F50',
                      borderColor: captchaCompleted ? '#48bb78' : '#FF7F50',
                    }}
                  >
                    {captchaVerifying ? '验证中...' : captchaCompleted ? '验证完成' : '开始真人验证'}
                  </Button>
                </div>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  onClick={handleNextStep}
                  disabled={!humanVerified}
                  block
                  style={{
                    backgroundColor: '#FF7F50',
                    borderColor: '#FF7F50',
                  }}
                >
                  下一步
                </Button>
              </Form.Item>
            </Form>
          </div>
        );
      case 1:
        return (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleNextStep}
            autoComplete="off"
          >
            <Form.Item
              label={<span style={{ color: '#FF7F50' }} className="font-medium">密码</span>}
              required
            >
              <Input.Password
                ref={passwordRef}
                prefix={<LockOutlined style={{ color: '#a0aec0' }} />}
                placeholder="请输入密码"
                autoComplete="new-password"
                className="h-10"
                style={{ borderRadius: '6px', borderColor: '#e2e8f0' }}
                value={password}
                onChange={e => setPassword(e.target.value)}
                onContextMenu={e => e.preventDefault()} // 禁用右键菜单
              />
            </Form.Item>

            <PasswordStrengthBar password={password} />

            <Form.Item
              label={<span style={{ color: '#FF7F50' }} className="font-medium">确认密码</span>}
              required
            >
              <Input.Password
                ref={confirmPasswordRef}
                prefix={<LockOutlined style={{ color: '#a0aec0' }} />}
                placeholder="请再次输入密码"
                autoComplete="new-password"
                className="h-10"
                style={{ borderRadius: '6px', borderColor: '#e2e8f0' }}
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                onContextMenu={e => e.preventDefault()} // 禁用右键菜单
              />
              {confirmPassword && password !== confirmPassword && (
                <div className="text-xs text-red-500 mt-1">
                  两次输入的密码不一致
                </div>
              )}
            </Form.Item>

            <Form.Item>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={() => setStep(0)}
                  size="large"
                  className="h-12"
                >
                  上一步
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  disabled={!password || password !== confirmPassword || password.length < 6}
                  size="large"
                  className="h-12"
                  style={{
                    backgroundColor: '#FF7F50',
                    borderColor: '#FF7F50',
                  }}
                >
                  下一步
                </Button>
              </div>
            </Form.Item>
          </Form>
        );
      case 2:
        return (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleRegister}
            autoComplete="off"
          >
            <Form.Item
              name="username"
              label={<span style={{ color: '#FF7F50' }} className="font-medium">用户名</span>}
              rules={[
                { required: true, message: '请输入用户名' },
                { min: 3, message: '用户名长度至少为3位' },
                { max: 20, message: '用户名长度不能超过20位' },
                { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },
              ]}
            >
              <Input
                prefix={<UserOutlined style={{ color: '#FF7F50' }} />}
                placeholder="请输入用户名"
                autoComplete="username"
                className="h-10"
                style={{ borderRadius: '6px', borderColor: '#e2e8f0' }}
              />
            </Form.Item>

            <Form.Item
              name="displayName"
              label={<span style={{ color: '#FF7F50' }} className="font-medium">显示名称</span>}
              rules={[
                { required: true, message: '请输入显示名称' },
                { max: 100, message: '显示名称长度不能超过100位' },
              ]}
            >
              <Input
                prefix={<UserOutlined style={{ color: '#a0aec0' }} />}
                placeholder="请输入显示名称"
                className="h-10"
                style={{ borderRadius: '6px', borderColor: '#e2e8f0' }}
              />
            </Form.Item>



            <Form.Item>
              <Checkbox
                checked={agreed}
                onChange={(e) => setAgreed(e.target.checked)}
              >
                我已阅读并同意{' '}
                <a href="#" style={{ color: '#FF7F50' }} className="hover:underline">服务条款</a>{' '}和{' '}
                <a href="#" style={{ color: '#FF7F50' }} className="hover:underline">隐私政策</a>
              </Checkbox>
            </Form.Item>

            <Form.Item>
              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={() => setStep(1)}
                  size="large"
                  className="h-12"
                >
                  上一步
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  disabled={!agreed}
                  size="large"
                  className="h-12"
                  style={{
                    backgroundColor: '#FF7F50',
                    borderColor: '#FF7F50',
                  }}
                >
                  立即注册
                </Button>
              </div>
            </Form.Item>
          </Form>
        );
      default:
        return null;
    }
  };

  return (
    <div
      className="rounded-lg shadow-xl p-8 w-full max-w-md"
      style={{
        backgroundColor: '#ffffff', /* 白色背景 */
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05)'
      }}
    >
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2" style={{ color: '#FF7F50' }}>创建账户</h2>
        <p className="text-sm" style={{ color: '#4a5568' }}>加入我们，开始您的高效邮件管理之旅</p>
      </div>

      {error ? (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          className="mb-6 rounded-lg"
        />
      ) : null}

      {/* 步骤指示 */}
      <div className="flex items-center justify-center gap-4 mb-8">
        {steps.map((s, idx) => (
          <div key={s} className="flex items-center gap-1">
            <div
              style={{
                width: 28,
                height: 28,
                borderRadius: '50%',
                background: idx === step ? '#FF7F50' : '#E5E7EB',
                color: idx === step ? '#fff' : '#6B7280',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontWeight: 600,
                fontSize: 16,
                border: idx < step ? '2px solid #48bb78' : '2px solid #E5E7EB',
                transition: 'all 0.2s',
              }}
            >
              {idx + 1}
            </div>
            <span style={{ color: idx === step ? '#FF7F50' : '#6B7280', fontWeight: idx === step ? 600 : 400, fontSize: 14 }}>{s}</span>
            {idx < steps.length - 1 && <SafetyOutlined style={{ color: idx < step ? '#48bb78' : '#E5E7EB', margin: '0 8px' }} />}
          </div>
        ))}
      </div>
      {renderStep()}
      {/* 登录链接 */}
      <Divider className="my-8" />
      <div className="text-center">
        <span className="text-sm" style={{ color: '#4a5568' }}>已有账户？</span>
        <Link
          to="/login"
          className="font-semibold text-sm hover:underline transition-all duration-200 ml-1"
          style={{ color: '#FF7F50' }}
        >
          立即登录
        </Link>
      </div>


    </div>
  );
};

export default Register;
