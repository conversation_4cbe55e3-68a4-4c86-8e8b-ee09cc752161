import prisma from '../config/database';

// 获取联系人列表
export const getContacts = async (req: AuthenticatedRequest, res: Response) => {
  const {
    page = '1',
    limit = '20',
    sortBy = 'name',
    sortOrder = 'asc',
    groupId,
    search
  } = req.query as any;

  const pageNum = parseInt(page as string) || 1;
  const limitNum = parseInt(limit as string) || 20;
  const skip = (pageNum - 1) * limitNum;
  const userId = req.user!.id;

  const where: any = { userId };

  if (groupId) {
    where.groupId = parseInt(groupId);
  }

  if (search) {
    where.OR = [
      { name: { contains: search } },
      { email: { contains: search } },
      { company: { contains: search } },
    ];
  }

  const [contacts, total] = await Promise.all([
    prisma.contact.findMany({
      where,
      skip,
      take: limitNum,
      orderBy: { [sortBy as string]: sortOrder },
      include: {
        group: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    }),
    prisma.contact.count({ where }),
  ]);

  const response: ApiResponse = {
    success: true,
    message: '获取联系人列表成功',
    data: {
      contacts,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total,
        totalPages: Math.ceil(total / limitNum),
        hasNext: pageNum * limitNum < total,
        hasPrev: pageNum > 1,
      },
    },
  };

  res.json(response);
};

// 获取联系人详情
export const getContactById = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const contact = await prisma.contact.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
    include: {
      group: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  if (!contact) {
    throw new AppError('联系人不存在', 404);
  }

  const response: ApiResponse = {
    success: true,
    message: '获取联系人详情成功',
    data: contact,
  };

  res.json(response);
};

// 创建联系人
export const createContact = async (req: AuthenticatedRequest, res: Response) => {
  const { email, name, phone, company, notes, groupId } = req.body;
  const userId = req.user!.id;

  // 检查邮箱是否已存在
  const existingContact = await prisma.contact.findFirst({
    where: {
      userId,
      email,
    },
  });

  if (existingContact) {
    throw new AppError('该邮箱的联系人已存在', 409);
  }

  // 如果指定了分组，检查分组是否存在
  if (groupId) {
    const group = await prisma.contactGroup.findFirst({
      where: {
        id: groupId,
        userId,
      },
    });

    if (!group) {
      throw new AppError('联系人分组不存在', 404);
    }
  }

  const contact = await prisma.contact.create({
    data: {
      userId,
      email,
      name,
      phone,
      company,
      notes,
      groupId,
    },
    include: {
      group: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人创建成功',
    data: contact,
  };

  res.status(201).json(response);
};

// 更新联系人
export const updateContact = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { email, name, phone, company, notes, groupId } = req.body;
  const userId = req.user!.id;

  // 检查联系人是否存在
  const existingContact = await prisma.contact.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingContact) {
    throw new AppError('联系人不存在', 404);
  }

  // 如果修改了邮箱，检查新邮箱是否已被其他联系人使用
  if (email && email !== existingContact.email) {
    const duplicateContact = await prisma.contact.findFirst({
      where: {
        userId,
        email,
        id: { not: parseInt(id) },
      },
    });

    if (duplicateContact) {
      throw new AppError('该邮箱的联系人已存在', 409);
    }
  }

  // 如果指定了分组，检查分组是否存在
  if (groupId) {
    const group = await prisma.contactGroup.findFirst({
      where: {
        id: groupId,
        userId,
      },
    });

    if (!group) {
      throw new AppError('联系人分组不存在', 404);
    }
  }

  const contact = await prisma.contact.update({
    where: { id: parseInt(id) },
    data: {
      email,
      name,
      phone,
      company,
      notes,
      groupId,
    },
    include: {
      group: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人更新成功',
    data: contact,
  };

  res.json(response);
};

// 删除联系人
export const deleteContact = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const contact = await prisma.contact.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!contact) {
    throw new AppError('联系人不存在', 404);
  }

  await prisma.contact.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人删除成功',
  };

  res.json(response);
};

// 获取联系人分组列表
export const getContactGroups = async (req: AuthenticatedRequest, res: Response) => {
  const userId = req.user!.id;

  const groups = await prisma.contactGroup.findMany({
    where: { userId },
    orderBy: { name: 'asc' },
    include: {
      _count: {
        select: {
          contacts: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '获取联系人分组列表成功',
    data: groups,
  };

  res.json(response);
};

// 创建联系人分组
export const createContactGroup = async (req: AuthenticatedRequest, res: Response) => {
  const { name } = req.body;
  const userId = req.user!.id;

  // 检查分组名称是否已存在
  const existingGroup = await prisma.contactGroup.findFirst({
    where: {
      userId,
      name,
    },
  });

  if (existingGroup) {
    throw new AppError('分组名称已存在', 409);
  }

  const group = await prisma.contactGroup.create({
    data: {
      userId,
      name,
    },
    include: {
      _count: {
        select: {
          contacts: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人分组创建成功',
    data: group,
  };

  res.status(201).json(response);
};

// 更新联系人分组
export const updateContactGroup = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { name } = req.body;
  const userId = req.user!.id;

  // 检查分组是否存在
  const existingGroup = await prisma.contactGroup.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!existingGroup) {
    throw new AppError('联系人分组不存在', 404);
  }

  // 检查新名称是否已被其他分组使用
  if (name !== existingGroup.name) {
    const duplicateGroup = await prisma.contactGroup.findFirst({
      where: {
        userId,
        name,
        id: { not: parseInt(id) },
      },
    });

    if (duplicateGroup) {
      throw new AppError('分组名称已存在', 409);
    }
  }

  const group = await prisma.contactGroup.update({
    where: { id: parseInt(id) },
    data: { name },
    include: {
      _count: {
        select: {
          contacts: true,
        },
      },
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人分组更新成功',
    data: group,
  };

  res.json(response);
};

// 删除联系人分组
export const deleteContactGroup = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const userId = req.user!.id;

  const group = await prisma.contactGroup.findFirst({
    where: {
      id: parseInt(id),
      userId,
    },
  });

  if (!group) {
    throw new AppError('联系人分组不存在', 404);
  }

  // 将该分组下的联系人移到未分组
  await prisma.contact.updateMany({
    where: {
      groupId: parseInt(id),
      userId,
    },
    data: {
      groupId: null,
    },
  });

  await prisma.contactGroup.delete({
    where: { id: parseInt(id) },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人分组删除成功',
  };

  res.json(response);
};

// 批量删除联系人
export const batchDeleteContacts = async (req: AuthenticatedRequest, res: Response) => {
  const { ids } = req.body;
  const userId = req.user!.id;

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new AppError('请提供要删除的联系人ID列表', 400);
  }

  const result = await prisma.contact.deleteMany({
    where: {
      id: { in: ids },
      userId,
    },
  });

  const response: ApiResponse = {
    success: true,
    message: `成功删除 ${result.count} 个联系人`,
    data: { deletedCount: result.count },
  };

  res.json(response);
};

// 搜索联系人建议
export const searchContactSuggestions = async (req: AuthenticatedRequest, res: Response) => {
  const { q } = req.query;
  const userId = req.user!.id;

  if (!q || typeof q !== 'string') {
    throw new AppError('请提供搜索关键词', 400);
  }

  const contacts = await prisma.contact.findMany({
    where: {
      userId,
      OR: [
        { name: { contains: q } },
        { email: { contains: q } },
        { company: { contains: q } },
      ],
    },
    take: 10,
    orderBy: { name: 'asc' },
  });

  const response: ApiResponse = {
    success: true,
    message: '搜索联系人建议成功',
    data: contacts,
  };

  res.json(response);
};

// 从邮件中添加联系人
export const addContactFromEmail = async (req: AuthenticatedRequest, res: Response) => {
  const { email, name, fromEmailId } = req.body;
  const userId = req.user!.id;

  // 检查邮箱是否已存在
  const existingContact = await prisma.contact.findFirst({
    where: {
      userId,
      email,
    },
  });

  if (existingContact) {
    throw new AppError('该邮箱的联系人已存在', 409);
  }

  const contact = await prisma.contact.create({
    data: {
      userId,
      email,
      name,
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '联系人添加成功',
    data: contact,
  };

  res.status(201).json(response);
};

// 导出联系人
export const exportContacts = async (req: AuthenticatedRequest, res: Response) => {
  const { format = 'csv' } = req.query;
  const userId = req.user!.id;

  const contacts = await prisma.contact.findMany({
    where: { userId },
    include: {
      group: {
        select: {
          name: true,
        },
      },
    },
    orderBy: { name: 'asc' },
  });

  if (format === 'csv') {
    // 生成CSV格式
    const csvHeader = 'Name,Email,Phone,Company,Group,Notes\n';
    const csvData = contacts.map(contact => {
      const fields = [
        contact.name || '',
        contact.email,
        contact.phone || '',
        contact.company || '',
        contact.group?.name || '',
        contact.notes || '',
      ];
      return fields.map(field => `"${field.replace(/"/g, '""')}"`).join(',');
    }).join('\n');

    const csvContent = csvHeader + csvData;

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename="contacts.csv"');
    res.send(csvContent);
  } else if (format === 'vcf') {
    // 生成VCF格式
    const vcfData = contacts.map(contact => {
      let vcf = 'BEGIN:VCARD\nVERSION:3.0\n';
      if (contact.name) {
        vcf += `FN:${contact.name}\n`;
      }
      vcf += `EMAIL:${contact.email}\n`;
      if (contact.phone) {
        vcf += `TEL:${contact.phone}\n`;
      }
      if (contact.company) {
        vcf += `ORG:${contact.company}\n`;
      }
      if (contact.notes) {
        vcf += `NOTE:${contact.notes}\n`;
      }
      vcf += 'END:VCARD\n';
      return vcf;
    }).join('\n');

    res.setHeader('Content-Type', 'text/vcard');
    res.setHeader('Content-Disposition', 'attachment; filename="contacts.vcf"');
    res.send(vcfData);
  } else {
    throw new AppError('不支持的导出格式', 400);
  }
};

// 导入联系人
export const importContacts = async (req: AuthenticatedRequest, res: Response) => {
  const { format = 'csv' } = req.body;
  const userId = req.user!.id;

  if (!req.file) {
    throw new AppError('请上传文件', 400);
  }

  const fileContent = req.file.buffer.toString('utf-8');
  let imported = 0;
  let skipped = 0;
  const errors: string[] = [];

  try {
    if (format === 'csv') {
      // 解析CSV文件
      const lines = fileContent.split('\n').filter(line => line.trim());
      const header = lines[0];

      // 简单的CSV解析（实际项目中建议使用专业的CSV解析库）
      for (let i = 1; i < lines.length; i++) {
        try {
          const fields = lines[i].split(',').map(field =>
            field.replace(/^"(.*)"$/, '$1').replace(/""/g, '"')
          );

          if (fields.length >= 2) {
            const [name, email, phone, company, groupName, notes] = fields;

            if (!email || !email.includes('@')) {
              errors.push(`第 ${i + 1} 行：邮箱格式无效`);
              continue;
            }

            // 检查是否已存在
            const existing = await prisma.contact.findFirst({
              where: { userId, email },
            });

            if (existing) {
              skipped++;
              continue;
            }

            // 查找分组
            let groupId: number | null = null;
            if (groupName) {
              const group = await prisma.contactGroup.findFirst({
                where: { userId, name: groupName },
              });
              groupId = group?.id ?? null;
            }

            await prisma.contact.create({
              data: {
                userId,
                email,
                name: name || null,
                phone: phone || null,
                company: company || null,
                notes: notes || null,
                groupId,
              },
            });

            imported++;
          }
        } catch (error) {
          errors.push(`第 ${i + 1} 行：${(error as Error).message}`);
        }
      }
    } else {
      throw new AppError('暂不支持VCF格式导入', 400);
    }

    const response: ApiResponse = {
      success: true,
      message: `导入完成：成功 ${imported} 个，跳过 ${skipped} 个`,
      data: {
        imported,
        skipped,
        errors,
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`导入失败: ${(error as Error).message}`, 500);
  }
};
