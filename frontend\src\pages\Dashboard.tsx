import { useEffect } from 'react';
import { Card, Row, Col, Statistic, List, Avatar, Button } from 'antd';
import {
  MailOutlined,
  SendOutlined,
  InboxOutlined,
  StarOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { useAuthStore } from '../store/authStore';
import { useEmailStore } from '../store/emailStore';

const Dashboard: React.FC = () => {
  const { user } = useAuthStore();
  const { emails, fetchEmails, loading } = useEmailStore();

  useEffect(() => {
    fetchEmails({ limit: 5 });
  }, [fetchEmails]);

  // 模拟统计数据
  const stats = [
    {
      title: '收件箱',
      value: 23,
      icon: <InboxOutlined className="text-blue-600" />,
      color: '#1890ff',
    },
    {
      title: '已发送',
      value: 156,
      icon: <SendOutlined className="text-green-600" />,
      color: '#52c41a',
    },
    {
      title: '星标邮件',
      value: 8,
      icon: <StarOutlined className="text-yellow-600" />,
      color: '#faad14',
    },
    {
      title: '草稿',
      value: 3,
      icon: <MailOutlined className="text-gray-600" />,
      color: '#8c8c8c',
    },
  ];

  return (
    <div className="p-6">
      {/* 邮箱管理面板说明 */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">
          邮箱管理面板
        </h1>
        <p className="text-gray-600">
          这里是您的邮箱管理中心，您可以查看邮件统计和最新邮件。
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="mb-6">
        {stats.map((stat, index) => (
          <Col xs={12} sm={12} md={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.icon}
                valueStyle={{ color: stat.color }}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最新邮件 */}
        <Col xs={24} lg={16}>
          <Card
            title="最新邮件"
            extra={
              <Button type="link" href="/inbox">
                查看全部
              </Button>
            }
          >
            <List
              loading={loading}
              dataSource={emails.slice(0, 5)}
              renderItem={(email) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar icon={<UserOutlined />} />
                    }
                    title={
                      <div className="flex items-center justify-between">
                        <span className={email.isRead ? 'text-gray-600' : 'font-semibold'}>
                          {email.subject || '(无主题)'}
                        </span>
                        <span className="text-xs text-gray-400">
                          {email.receivedAt ? new Date(email.receivedAt).toLocaleDateString() : ''}
                        </span>
                      </div>
                    }
                    description={
                      <div>
                        <div className="text-sm text-gray-600 mb-1">
                          来自: {email.senderName || email.senderEmail}
                        </div>
                        <div className="text-xs text-gray-400 truncate">
                          {email.contentText?.substring(0, 100) || '(无内容)'}
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
              locale={{ emptyText: '暂无邮件' }}
            />
          </Card>
        </Col>

        {/* 快捷操作 */}
        <Col xs={24} lg={8}>
          <Card title="快捷操作">
            <div className="space-y-3">
              <Button
                type="primary"
                icon={<MailOutlined />}
                block
                href="/compose"
              >
                写邮件
              </Button>
              <Button
                icon={<UserOutlined />}
                block
                href="/contacts"
              >
                管理联系人
              </Button>
              <Button
                icon={<StarOutlined />}
                block
                href="/templates"
              >
                邮件模板
              </Button>
            </div>
          </Card>

          {/* 用户信息 */}
          <Card title="账户信息" className="mt-4">
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-gray-600">用户名:</span>
                <span>{user?.username}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">邮箱:</span>
                <span>{user?.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">注册时间:</span>
                <span>
                  {user?.createdAt ? new Date(user.createdAt).toLocaleDateString() : '-'}
                </span>
              </div>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
