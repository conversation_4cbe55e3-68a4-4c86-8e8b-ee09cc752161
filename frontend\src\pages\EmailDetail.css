/* 邮件详情页面样式 */
.email-detail-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.email-detail-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.email-detail-not-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  gap: 16px;
}

/* 顶部操作栏 */
.email-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background-color: white;
  border-bottom: 1px solid #f0f0f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
  margin: 0 24px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* 操作按钮样式 */
.header-center .ant-btn {
  border-radius: 6px;
}

.header-center .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.2);
}

.header-center .ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.3);
}

.header-center .ant-btn-text:hover {
  background-color: #f5f5f5;
}

.header-center .ant-btn-text.ant-btn-dangerous:hover {
  background-color: #fff2f0;
  color: #ff4d4f;
}

/* 邮件内容区域 */
.email-detail-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.email-header {
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.email-subject {
  margin-bottom: 16px !important;
  color: #262626;
}

.email-meta {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sender-avatar {
  flex-shrink: 0;
}

.sender-info {
  flex: 1;
}

.sender-name {
  margin-bottom: 4px;
}

.email-date {
  font-size: 14px;
}

/* 邮件内容卡片 */
.email-content-card {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.email-content-header {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 16px;
}

.email-content-body {
  min-height: 300px;
}

/* HTML内容样式 */
.email-html-content {
  line-height: 1.6;
  color: #262626;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.email-html-content img {
  max-width: 100%;
  height: auto;
}

.email-html-content a {
  color: #1890ff;
  text-decoration: none;
}

.email-html-content a:hover {
  text-decoration: underline;
}

.email-html-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 16px 0;
}

.email-html-content table td,
.email-html-content table th {
  border: 1px solid #d9d9d9;
  padding: 8px;
  text-align: left;
}

.email-html-content table th {
  background-color: #fafafa;
  font-weight: 600;
}

.email-html-content blockquote {
  border-left: 4px solid #d9d9d9;
  margin: 16px 0;
  padding-left: 16px;
  color: #595959;
}

.email-html-content pre {
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 12px;
  overflow-x: auto;
  font-family: 'Courier New', monospace;
}

.email-html-content code {
  background-color: #f5f5f5;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* 文本内容样式 */
.email-text-content {
  line-height: 1.6;
  color: #262626;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #fafafa;
  padding: 16px;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

/* 原始内容样式 */
.email-raw-content {
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px solid #f0f0f0;
}

.raw-content-pre {
  background-color: #f5f5f5;
  border: none;
  border-radius: 0;
  margin: 0;
  padding: 16px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 400px;
  overflow-y: auto;
}

/* 无内容提示 */
.no-content {
  text-align: center;
  color: #8c8c8c;
  font-style: italic;
  padding: 40px;
  background-color: #fafafa;
  border-radius: 4px;
  border: 1px dashed #d9d9d9;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-detail-content {
    padding: 16px;
  }
  
  .email-header {
    padding: 16px;
  }
  
  .email-content-header {
    overflow-x: auto;
  }
  
  .email-content-header .ant-space {
    white-space: nowrap;
  }
}

/* 邮件内容中的文本转HTML样式 */
.email-text-content a {
  color: #1890ff;
  text-decoration: none;
}

.email-text-content a:hover {
  text-decoration: underline;
}

/* 安全提示样式 */
.email-security-warning {
  margin-bottom: 16px;
}

/* 加载状态样式 */
.email-content-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
}
