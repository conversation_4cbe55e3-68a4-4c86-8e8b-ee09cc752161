import { Tabs, Card } from 'antd';
import {
  UserOutlined,
  MailOutlined,
  SettingOutlined,
  ContactsOutlined,
  FileTextOutlined,
  FilterOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import ProfileSettings from '@/components/ProfileSettings'
import EmailAccountSettings from '@/components/EmailAccountSettings';
import EmailRuleManagement from '@/components/EmailRuleManagement';
import EmailTemplateManagement from '@/components/EmailTemplateManagement';
import ContactManagement from '@/components/ContactManagement';
import SubAccountManagement from '@/components/SubAccountManagement';
import { useAuthStore } from '@/store/authStore';

const { TabPane } = Tabs;

const Settings: React.FC = () => {
  const { user } = useAuthStore();

  console.log('Settings页面 - 当前用户:', user);
  console.log('Settings页面 - 用户角色:', user?.role);

  return (
    <div className="p-6">
      <Card>
        <Tabs defaultActiveKey="profile" tabPosition="left" style={{ minHeight: 400 }}>
          <TabPane
            tab={
              <span>
                <UserOutlined />
                个人资料
              </span>
            }
            key="profile"
          >
            <ProfileSettings />
          </TabPane>

          <TabPane
            tab={
              <span>
                <MailOutlined />
                邮箱账户
              </span>
            }
            key="email-accounts"
          >
            <EmailAccountSettings />
          </TabPane>

          <TabPane
            tab={
              <span>
                <TeamOutlined />
                子账户管理
              </span>
            }
            key="sub-accounts"
          >
            <SubAccountManagement />
          </TabPane>

          <TabPane
            tab={
              <span>
                <FilterOutlined />
                邮件规则
              </span>
            }
            key="email-rules"
          >
            <EmailRuleManagement />
          </TabPane>

          <TabPane
            tab={
              <span>
                <FileTextOutlined />
                邮件模板
              </span>
            }
            key="email-templates"
          >
            <EmailTemplateManagement />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ContactsOutlined />
                联系人管理
              </span>
            }
            key="contacts"
          >
            <ContactManagement />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default Settings;
