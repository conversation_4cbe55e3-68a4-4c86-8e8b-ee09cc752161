import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import dataMigrationService from '../services/dataMigrationService';
import compatibilityService from '../services/compatibilityService';
import logger from '../utils/logger';

// 获取数据完整性状态
export const getDataIntegrityStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const validationResult = await dataMigrationService.validateDataIntegrity();

    const response: ApiResponse = {
      success: true,
      message: '数据完整性检查完成',
      data: validationResult,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取数据完整性状态失败: ${(error as Error).message}`, 500);
  }
};

// 检查架构兼容性
export const checkCompatibility = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const compatibilityReport = await compatibilityService.checkArchitectureCompatibility();

    const response: ApiResponse = {
      success: true,
      message: '兼容性检查完成',
      data: compatibilityReport,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`兼容性检查失败: ${(error as Error).message}`, 500);
  }
};

// 修复兼容性问题
export const fixCompatibilityIssues = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const fixResult = await compatibilityService.fixCompatibilityIssues();

    const response: ApiResponse = {
      success: true,
      message: '兼容性问题修复完成',
      data: fixResult,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`修复兼容性问题失败: ${(error as Error).message}`, 500);
  }
};

// 迁移用户数据
export const migrateUserData = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { dryRun, batchSize, skipExisting, validateData } = req.body;

    const options = {
      dryRun: dryRun === true,
      batchSize: batchSize ? parseInt(String(batchSize)) : undefined,
      skipExisting: skipExisting === true,
      validateData: validateData === true
    };

    const migrationResult = await dataMigrationService.migrateUsersToNewArchitecture(options);

    const response: ApiResponse = {
      success: migrationResult.success,
      message: migrationResult.success ? '用户数据迁移完成' : '用户数据迁移部分失败',
      data: migrationResult,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`用户数据迁移失败: ${(error as Error).message}`, 500);
  }
};

// 迁移邮件数据
export const migrateEmailData = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { dryRun, batchSize, skipExisting, validateData } = req.body;

    const options = {
      dryRun: dryRun === true,
      batchSize: batchSize ? parseInt(String(batchSize)) : undefined,
      skipExisting: skipExisting === true,
      validateData: validateData === true
    };

    const migrationResult = await dataMigrationService.migrateEmailsToMultiAccount(options);

    const response: ApiResponse = {
      success: migrationResult.success,
      message: migrationResult.success ? '邮件数据迁移完成' : '邮件数据迁移部分失败',
      data: migrationResult,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`邮件数据迁移失败: ${(error as Error).message}`, 500);
  }
};

// 执行完整迁移
export const performFullMigration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const { dryRun, batchSize, skipExisting, validateData } = req.body;

    const options = {
      dryRun: dryRun === true,
      batchSize: batchSize ? parseInt(String(batchSize)) : undefined,
      skipExisting: skipExisting === true,
      validateData: validateData === true
    };

    logger.info(`开始执行完整数据迁移 (dryRun: ${options.dryRun})`);

    const migrationResult = await dataMigrationService.performFullMigration(options);

    const overallSuccess = migrationResult.userMigration.success && 
                          migrationResult.emailMigration.success &&
                          migrationResult.validationResult.isValid;

    const response: ApiResponse = {
      success: overallSuccess,
      message: overallSuccess ? '完整数据迁移成功' : '数据迁移完成但存在问题',
      data: migrationResult,
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`完整数据迁移失败: ${(error as Error).message}`, 500);
  }
};

// 获取迁移状态
export const getMigrationStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    // 获取数据完整性状态
    const integrityStatus = await dataMigrationService.validateDataIntegrity();
    
    // 获取兼容性状态
    const compatibilityStatus = await compatibilityService.checkArchitectureCompatibility();

    // 计算迁移进度
    const migrationProgress = {
      usersNeedMigration: 0,
      emailsNeedMigration: 0,
      compatibilityIssues: compatibilityStatus.checks.filter(c => c.status !== 'compatible').length,
      overallProgress: 0
    };

    // 计算需要迁移的数据量
    if (!integrityStatus.isValid) {
      const userIssues = integrityStatus.issues.filter(issue => issue.includes('用户'));
      const emailIssues = integrityStatus.issues.filter(issue => issue.includes('邮件'));
      
      migrationProgress.usersNeedMigration = userIssues.length;
      migrationProgress.emailsNeedMigration = emailIssues.length;
    }

    // 计算总体进度
    const totalIssues = migrationProgress.usersNeedMigration + 
                       migrationProgress.emailsNeedMigration + 
                       migrationProgress.compatibilityIssues;
    
    migrationProgress.overallProgress = totalIssues === 0 ? 100 : 
      Math.max(0, 100 - (totalIssues * 10)); // 简化的进度计算

    const response: ApiResponse = {
      success: true,
      message: '获取迁移状态成功',
      data: {
        integrityStatus,
        compatibilityStatus,
        migrationProgress,
        needsMigration: !integrityStatus.isValid || compatibilityStatus.overallStatus !== 'compatible'
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`获取迁移状态失败: ${(error as Error).message}`, 500);
  }
};

// 回滚迁移（预留功能）
export const rollbackMigration = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    // 这里可以实现回滚逻辑
    // 目前返回未实现的响应
    
    const response: ApiResponse = {
      success: false,
      message: '回滚功能暂未实现',
      data: {
        note: '回滚功能需要在实际部署前实现，建议在迁移前做好数据备份'
      },
    };

    res.json(response);
  } catch (error) {
    throw new AppError(`回滚迁移失败: ${(error as Error).message}`, 500);
  }
};

// 导出迁移报告
export const exportMigrationReport = async (req: AuthenticatedRequest, res: Response) => {
  try {
    if (req.user!.role !== 'admin') {
      throw new AppError('需要管理员权限', 403);
    }

    const integrityStatus = await dataMigrationService.validateDataIntegrity();
    const compatibilityStatus = await compatibilityService.checkArchitectureCompatibility();

    const report = {
      timestamp: new Date().toISOString(),
      systemInfo: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      },
      dataIntegrity: integrityStatus,
      compatibility: compatibilityStatus,
      recommendations: [
        ...integrityStatus.recommendations,
        ...compatibilityStatus.checks
          .filter(c => c.recommendation)
          .map(c => c.recommendation!)
      ].filter((rec, index, arr) => arr.indexOf(rec) === index) // 去重
    };

    // 设置下载头
    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', `attachment; filename="migration-report-${new Date().toISOString().split('T')[0]}.json"`);
    
    res.json(report);
  } catch (error) {
    throw new AppError(`导出迁移报告失败: ${(error as Error).message}`, 500);
  }
};
