import { Response } from 'express';
import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';
import { exec } from 'child_process';
import { promisify } from 'util';
import os from 'os';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

/**
 * 系统监控控制器
 * 提供系统状态、性能指标、邮件统计等监控功能
 */

/**
 * 获取系统概览信息
 */
export const getSystemOverview = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 获取系统基本信息
    const systemInfo = {
      hostname: os.hostname(),
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,
      uptime: process.uptime(),
      systemUptime: os.uptime(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpuCount: os.cpus().length,
      loadAverage: os.loadavg()
    };

    // 获取数据库统计
    const [
      userCount,
      emailCount,
      activeUsers,
      todayEmails,
      systemUsers
    ] = await Promise.all([
      prisma.user.count(),
      prisma.email.count({ where: { isDeleted: false } }),
      prisma.user.count({ where: { isActive: true } }),
      prisma.email.count({
        where: {
          receivedAt: {
            gte: new Date(new Date().setHours(0, 0, 0, 0))
          },
          isDeleted: false
        }
      }),
      prisma.user.count({
        where: {
          role: { in: ['admin', 'moderator'] }
        }
      })
    ]);

    const response: ApiResponse = {
      success: true,
      message: '获取系统概览成功',
      data: {
        systemInfo,
        statistics: {
          totalUsers: userCount,
          activeUsers,
          systemUsers,
          totalEmails: emailCount,
          todayEmails
        },
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取系统概览失败:', error);
    throw new AppError('获取系统概览失败', 500);
  }
};

/**
 * 获取邮件服务器状态
 */
export const getMailServerStatus = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    const isLocal = process.env['NODE_ENV'] === 'production' || process.env['MAIL_SERVER_LOCAL'] === 'true';
    
    // 检查邮件服务状态
    const serviceChecks = await Promise.allSettled([
      // 检查Postfix状态
      checkService('postfix', isLocal),
      // 检查Dovecot状态
      checkService('dovecot', isLocal),
      // 检查磁盘使用情况
      checkDiskUsage(isLocal),
      // 检查邮件队列
      checkMailQueue(isLocal)
    ]);

    const [postfixStatus, dovecotStatus, diskUsage, mailQueue] = serviceChecks.map(result => 
      result.status === 'fulfilled' ? result.value : { status: 'error', message: 'Check failed' }
    );

    const response: ApiResponse = {
      success: true,
      message: '获取邮件服务器状态成功',
      data: {
        services: {
          postfix: postfixStatus,
          dovecot: dovecotStatus
        },
        diskUsage,
        mailQueue,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取邮件服务器状态失败:', error);
    throw new AppError('获取邮件服务器状态失败', 500);
  }
};

/**
 * 获取邮件统计信息
 */
export const getEmailStatistics = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    const { period = '7d' } = req.query;
    
    // 计算时间范围
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case '24h':
        startDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    // 获取邮件统计
    const [
      totalEmails,
      sentEmails,
      receivedEmails,
      deletedEmails,
      unreadEmails,
      starredEmails,
      emailsByDay
    ] = await Promise.all([
      // 总邮件数
      prisma.email.count({
        where: {
          receivedAt: { gte: startDate },
          isDeleted: false
        }
      }),
      // 发送邮件数（从sent文件夹统计）
      prisma.email.count({
        where: {
          receivedAt: { gte: startDate },
          folder: { type: 'sent' },
          isDeleted: false
        }
      }),
      // 接收邮件数（从inbox文件夹统计）
      prisma.email.count({
        where: {
          receivedAt: { gte: startDate },
          folder: { type: 'inbox' },
          isDeleted: false
        }
      }),
      // 删除邮件数
      prisma.email.count({
        where: {
          receivedAt: { gte: startDate },
          isDeleted: true
        }
      }),
      // 未读邮件数
      prisma.email.count({
        where: {
          receivedAt: { gte: startDate },
          isRead: false,
          isDeleted: false
        }
      }),
      // 星标邮件数
      prisma.email.count({
        where: {
          receivedAt: { gte: startDate },
          isStarred: true,
          isDeleted: false
        }
      }),
      // 按天统计邮件数量
      getEmailsByDay(startDate, now)
    ]);

    const response: ApiResponse = {
      success: true,
      message: '获取邮件统计成功',
      data: {
        period,
        summary: {
          totalEmails,
          sentEmails,
          receivedEmails,
          deletedEmails,
          unreadEmails,
          starredEmails
        },
        timeline: emailsByDay,
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取邮件统计失败:', error);
    throw new AppError('获取邮件统计失败', 500);
  }
};

/**
 * 获取系统日志
 */
export const getSystemLogs = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    const { 
      type = 'app', 
      level = 'all', 
      limit = 100,
      offset = 0 
    } = req.query;

    // 根据类型获取不同的日志
    let logs: any[] = [];
    
    if (type === 'app') {
      // 获取应用日志（从数据库或日志文件）
      logs = await getApplicationLogs(String(level), Number(limit), Number(offset));
    } else if (type === 'mail') {
      // 获取邮件服务日志
      logs = await getMailServerLogs(Number(limit));
    } else if (type === 'security') {
      // 获取安全日志
      logs = await getSecurityLogs(Number(limit), Number(offset));
    }

    const response: ApiResponse = {
      success: true,
      message: '获取系统日志成功',
      data: {
        logs,
        type,
        level,
        limit: Number(limit),
        offset: Number(offset),
        timestamp: new Date().toISOString()
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取系统日志失败:', error);
    throw new AppError('获取系统日志失败', 500);
  }
};

// 辅助函数

/**
 * 检查服务状态
 */
async function checkService(serviceName: string, isLocal: boolean): Promise<any> {
  try {
    let command: string;
    if (isLocal) {
      command = `systemctl is-active ${serviceName}`;
    } else {
      command = `ssh <EMAIL> "systemctl is-active ${serviceName}"`;
    }
    
    const { stdout } = await execAsync(command);
    const status = stdout.trim();
    
    return {
      name: serviceName,
      status: status === 'active' ? 'running' : 'stopped',
      message: status === 'active' ? '服务正常运行' : '服务已停止'
    };
  } catch (error) {
    return {
      name: serviceName,
      status: 'error',
      message: '无法检查服务状态'
    };
  }
}

/**
 * 检查磁盘使用情况
 */
async function checkDiskUsage(isLocal: boolean): Promise<any> {
  try {
    let command: string;
    if (isLocal) {
      command = `df -h /var/mail | tail -1`;
    } else {
      command = `ssh <EMAIL> "df -h /var/mail | tail -1"`;
    }
    
    const { stdout } = await execAsync(command);
    const parts = stdout.trim().split(/\s+/);
    
    return {
      filesystem: parts[0],
      size: parts[1],
      used: parts[2],
      available: parts[3],
      usePercentage: parts[4],
      mountPoint: parts[5]
    };
  } catch (error) {
    return {
      error: '无法获取磁盘使用情况'
    };
  }
}

/**
 * 检查邮件队列
 */
async function checkMailQueue(isLocal: boolean): Promise<any> {
  try {
    let command: string;
    if (isLocal) {
      command = `mailq | tail -1`;
    } else {
      command = `ssh <EMAIL> "mailq | tail -1"`;
    }
    
    const { stdout } = await execAsync(command);
    const queueInfo = stdout.trim();
    
    // 解析队列信息
    const isEmpty = queueInfo.includes('Mail queue is empty');
    const queueCount = isEmpty ? 0 : parseInt(queueInfo.split(' ')[0]) || 0;
    
    return {
      count: queueCount,
      status: isEmpty ? 'empty' : 'has_mail',
      message: isEmpty ? '邮件队列为空' : `队列中有 ${queueCount} 封邮件`
    };
  } catch (error) {
    return {
      error: '无法获取邮件队列信息'
    };
  }
}

/**
 * 按天统计邮件数量
 */
async function getEmailsByDay(startDate: Date, endDate: Date): Promise<any[]> {
  try {
    const result = await prisma.$queryRaw`
      SELECT 
        DATE(received_at) as date,
        COUNT(*) as count
      FROM emails 
      WHERE received_at >= ${startDate} 
        AND received_at <= ${endDate}
        AND is_deleted = false
      GROUP BY DATE(received_at)
      ORDER BY date ASC
    `;
    
    return Array.isArray(result) ? result.map((row: any) => ({
      date: row.date,
      count: Number(row.count)
    })) : [];
  } catch (error) {
    logger.error('获取按天邮件统计失败:', error);
    return [];
  }
}

/**
 * 获取应用日志
 */
async function getApplicationLogs(level: string, limit: number, offset: number): Promise<any[]> {
  // 这里可以从日志文件或数据库获取应用日志
  // 暂时返回模拟数据
  return [
    {
      id: 1,
      timestamp: new Date().toISOString(),
      level: 'info',
      message: '邮件同步服务启动',
      service: 'email-sync'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 60000).toISOString(),
      level: 'warn',
      message: '用户登录失败次数过多',
      service: 'auth'
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 120000).toISOString(),
      level: 'error',
      message: '数据库连接超时',
      service: 'database'
    }
  ];
}

/**
 * 获取邮件服务日志
 */
async function getMailServerLogs(limit: number): Promise<any[]> {
  // 这里可以从邮件服务器日志文件获取日志
  // 暂时返回模拟数据
  return [
    {
      id: 1,
      timestamp: new Date().toISOString(),
      level: 'info',
      message: 'Mail delivered successfully',
      service: 'postfix'
    },
    {
      id: 2,
      timestamp: new Date(Date.now() - 30000).toISOString(),
      level: 'info',
      message: 'IMAP connection established',
      service: 'dovecot'
    },
    {
      id: 3,
      timestamp: new Date(Date.now() - 90000).toISOString(),
      level: 'warn',
      message: 'Authentication failed for user',
      service: 'dovecot'
    }
  ];
}

/**
 * 获取安全日志
 */
async function getSecurityLogs(limit: number, offset: number): Promise<any[]> {
  try {
    const logs = await prisma.securityLog.findMany({
      take: limit,
      skip: offset,
      orderBy: { createdAt: 'desc' },
      include: {
        user: {
          select: { email: true, username: true }
        }
      }
    });
    
    return logs.map(log => ({
      id: log.id,
      timestamp: log.createdAt,
      level: 'info', // 安全日志默认为info级别
      action: log.action,
      ipAddress: log.ipAddress,
      userAgent: log.userAgent,
      message: log.action || '安全事件',
      service: 'security',
      user: log.user ? {
        email: log.user.email,
        username: log.user.username
      } : null,
      details: log.details
    }));
  } catch (error) {
    logger.error('获取安全日志失败:', error);
    return [];
  }
}
