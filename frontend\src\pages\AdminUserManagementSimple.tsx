import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Space,
  Typography,
  Tag,
  Popconfirm,
  InputNumber,
  Tooltip,
  Badge,
  Row,
  Col
} from 'antd';
import {
  UserOutlined,
  EditOutlined,
  DeleteOutlined,
  KeyOutlined,
  TeamOutlined,
  ReloadOutlined,
  SearchOutlined
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuthStore } from '../store/authStore';
import api from '../config/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface User {
  id: number;
  email: string;
  username: string;
  displayName?: string;
  role: string;
  accountType: string;
  isActive: boolean;
  isSubAccountEnabled?: boolean;
  maxSubAccounts?: number;
  emailVerified: boolean;
  createdAt: string;
  _count: {
    subAccounts: number;
    emails: number;
  };
}

const AdminUserManagementSimple: React.FC = () => {
  const { user: currentUser } = useAuthStore();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [subAccountModalVisible, setSubAccountModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchText, setSearchText] = useState('');

  const [editForm] = Form.useForm();
  const [passwordForm] = Form.useForm();
  const [subAccountForm] = Form.useForm();

  // 加载用户列表
  const loadUsers = async () => {
    try {
      setLoading(true);
      const params = {
        page: 1,
        limit: 50,
        ...(searchText && { search: searchText })
      };

      const response = await api.get('/admin/users', { params });
      setUsers(response.data.data || []);
    } catch (error) {
      message.error('加载用户列表失败');
      console.error('Load users error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadUsers();
  }, [searchText]);

  // 重置密码
  const handleResetPassword = (user: User) => {
    setSelectedUser(user);
    passwordForm.resetFields();
    setPasswordModalVisible(true);
  };

  // 保存密码重置
  const handleSavePassword = async (values: any) => {
    if (!selectedUser) return;

    try {
      await api.post(`/admin/users/${selectedUser.id}/reset-password`, values);
      message.success('密码重置成功');
      setPasswordModalVisible(false);
    } catch (error: any) {
      message.error(error.response?.data?.message || '重置密码失败');
    }
  };

  // 管理子账户功能
  const handleManageSubAccount = (user: User) => {
    setSelectedUser(user);
    subAccountForm.setFieldsValue({
      enabled: user.isSubAccountEnabled || false,
      maxSubAccounts: user.maxSubAccounts || 5,
    });
    setSubAccountModalVisible(true);
  };

  // 保存子账户设置
  const handleSaveSubAccount = async (values: any) => {
    if (!selectedUser) return;

    try {
      await api.put(`/admin/users/${selectedUser.id}/sub-account-feature`, values);
      message.success('子账户设置更新成功');
      setSubAccountModalVisible(false);
      loadUsers();
    } catch (error: any) {
      message.error(error.response?.data?.message || '更新子账户设置失败');
    }
  };

  // 获取角色标签
  const getRoleTag = (role: string) => {
    const roleConfig = {
      'super_admin': { color: 'red', text: '超级管理员' },
      'admin': { color: 'orange', text: '管理员' },
      'user': { color: 'blue', text: '普通用户' },
      'moderator': { color: 'green', text: '版主' },
    };
    const config = roleConfig[role as keyof typeof roleConfig] || { color: 'default', text: role };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns: ColumnsType<User> = [
    {
      title: '用户信息',
      key: 'userInfo',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.email}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            @{record.username}
          </div>
        </div>
      ),
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (role) => getRoleTag(role),
    },
    {
      title: '账户类型',
      dataIndex: 'accountType',
      key: 'accountType',
      render: (accountType) => (
        accountType === 'main' ? 
          <Tag color="blue">主账户</Tag> : 
          <Tag color="green">子账户</Tag>
      ),
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Space direction="vertical" size="small">
          <Badge 
            status={record.isActive ? 'success' : 'error'} 
            text={record.isActive ? '激活' : '禁用'} 
          />
          {record.isSubAccountEnabled && <Badge status="processing" text="子账户功能" />}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="重置密码">
            <Button
              type="text"
              icon={<KeyOutlined />}
              onClick={() => handleResetPassword(record)}
            />
          </Tooltip>
          {record.accountType === 'main' && (
            <Tooltip title="子账户管理">
              <Button
                type="text"
                icon={<TeamOutlined />}
                onClick={() => handleManageSubAccount(record)}
              />
            </Tooltip>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div style={{ 
      padding: '24px',
      height: '100vh',
      overflow: 'auto',
      backgroundColor: '#f5f5f5'
    }}>
      <Card>
        <Row gutter={[16, 16]} align="middle" style={{ marginBottom: '16px' }}>
          <Col flex="auto">
            <Title level={3} style={{ margin: 0 }}>
              <UserOutlined style={{ marginRight: '8px' }} />
              用户管理
            </Title>
          </Col>
          <Col>
            <Space>
              <Input
                placeholder="搜索用户"
                prefix={<SearchOutlined />}
                value={searchText}
                onChange={(e) => setSearchText(e.target.value)}
                style={{ width: '200px' }}
                allowClear
              />
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={loadUsers}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          scroll={{ y: 'calc(100vh - 300px)' }}
        />
      </Card>

      {/* 重置密码模态框 */}
      <Modal
        title="重置用户密码"
        open={passwordModalVisible}
        onCancel={() => setPasswordModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={passwordForm}
          layout="vertical"
          onFinish={handleSavePassword}
        >
          <Form.Item
            name="newPassword"
            label="新密码"
            rules={[
              { required: true, message: '请输入新密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password placeholder="请输入新密码" />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                重置密码
              </Button>
              <Button onClick={() => setPasswordModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 子账户功能管理模态框 */}
      <Modal
        title="子账户功能管理"
        open={subAccountModalVisible}
        onCancel={() => setSubAccountModalVisible(false)}
        footer={null}
        width={400}
      >
        <Form
          form={subAccountForm}
          layout="vertical"
          onFinish={handleSaveSubAccount}
        >
          <Form.Item
            name="enabled"
            label="启用子账户功能"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item
            name="maxSubAccounts"
            label="最大子账户数量"
            rules={[
              { required: true, message: '请输入最大子账户数量' },
              { type: 'number', min: 1, max: 100, message: '数量范围1-100' }
            ]}
          >
            <InputNumber min={1} max={100} style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setSubAccountModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AdminUserManagementSimple;
