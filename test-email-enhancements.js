// 测试邮件增强功能的脚本
const axios = require('axios');

const API_BASE = 'http://localhost:3001/api';

// 测试用户偏好设置API
async function testUserPreferencesAPI() {
  console.log('🧪 测试用户偏好设置API...');
  
  try {
    // 首先需要登录获取token
    console.log('1. 尝试登录...');
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>', // 使用默认管理员账户
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.token;
    console.log('✅ 登录成功');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // 测试获取用户偏好设置
    console.log('2. 获取用户偏好设置...');
    const getResponse = await axios.get(`${API_BASE}/user-preferences`, { headers });
    console.log('✅ 获取偏好设置成功:', getResponse.data.data);
    
    // 测试更新用户偏好设置
    console.log('3. 更新用户偏好设置...');
    const updateData = {
      viewMode: 'list',
      listWidth: 450,
      compactMode: false,
      theme: {
        mode: 'light',
        primaryColor: '#1890ff'
      }
    };
    
    const updateResponse = await axios.put(`${API_BASE}/user-preferences`, updateData, { headers });
    console.log('✅ 更新偏好设置成功:', updateResponse.data.data);
    
    // 测试获取特定设置项
    console.log('4. 获取特定设置项...');
    const getSettingResponse = await axios.get(`${API_BASE}/user-preferences/viewMode`, { headers });
    console.log('✅ 获取特定设置项成功:', getSettingResponse.data.data);
    
    // 测试更新特定设置项
    console.log('5. 更新特定设置项...');
    const updateSettingResponse = await axios.put(`${API_BASE}/user-preferences/viewMode`, 
      { value: 'split' }, 
      { headers }
    );
    console.log('✅ 更新特定设置项成功:', updateSettingResponse.data.data);
    
    console.log('🎉 用户偏好设置API测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.response?.data || error.message);
  }
}

// 测试邮件API的基本功能
async function testEmailAPI() {
  console.log('🧪 测试邮件API基本功能...');
  
  try {
    // 登录
    const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
      email: '<EMAIL>',
      password: 'admin123'
    });
    
    const token = loginResponse.data.data.token;
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // 获取邮件列表
    console.log('1. 获取邮件列表...');
    const emailsResponse = await axios.get(`${API_BASE}/emails?folderType=inbox&limit=5`, { headers });
    console.log('✅ 获取邮件列表成功，邮件数量:', emailsResponse.data.data.length);
    
    if (emailsResponse.data.data.length > 0) {
      const firstEmail = emailsResponse.data.data[0];
      console.log('📧 第一封邮件:', {
        id: firstEmail.id,
        subject: firstEmail.subject,
        isRead: firstEmail.isRead,
        isStarred: firstEmail.isStarred
      });
      
      // 测试更新邮件状态
      console.log('2. 测试更新邮件状态...');
      const updateEmailResponse = await axios.put(
        `${API_BASE}/emails/${firstEmail.id}`,
        { isRead: !firstEmail.isRead },
        { headers }
      );
      console.log('✅ 更新邮件状态成功');
    }
    
    console.log('🎉 邮件API测试完成！');
    
  } catch (error) {
    console.error('❌ 邮件API测试失败:', error.response?.data || error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始测试邮件增强功能...\n');
  
  await testUserPreferencesAPI();
  console.log('\n' + '='.repeat(50) + '\n');
  await testEmailAPI();
  
  console.log('\n✨ 所有测试完成！');
}

// 检查服务器是否运行
async function checkServerStatus() {
  try {
    const response = await axios.get(`${API_BASE}/health`);
    console.log('✅ 服务器运行正常');
    return true;
  } catch (error) {
    console.error('❌ 服务器未运行或无法访问');
    return false;
  }
}

// 主函数
async function main() {
  console.log('🔍 检查服务器状态...');
  const serverRunning = await checkServerStatus();
  
  if (serverRunning) {
    await runAllTests();
  } else {
    console.log('请确保后端服务器正在运行在 http://localhost:3001');
  }
}

// 运行测试
main().catch(console.error);
