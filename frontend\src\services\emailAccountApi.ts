import api from '../config/api';
import type { ApiResponse } from '../types';

export interface EmailAccount {
  id: number;
  name: string;
  email: string;
  displayName?: string;
  imapHost: string;
  imapPort: number;
  imapSecure: boolean;
  imapUsername?: string;
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUsername?: string;

  // OAuth2 支持
  authType: 'password' | 'oauth2';
  oauthProvider?: 'gmail' | 'outlook' | 'yahoo';
  oauthAccessToken?: string;
  oauthRefreshToken?: string;
  oauthTokenExpiry?: string;

  // 连接状态监控
  connectionStatus: 'connected' | 'disconnected' | 'error' | 'unknown';
  lastConnectionTest?: string;
  connectionError?: string;

  // 同步设置和状态
  syncEnabled: boolean;
  syncInterval: number;
  lastSyncAt?: string;
  syncStatus: 'idle' | 'syncing' | 'error';
  syncError?: string;

  // 账户设置
  isActive: boolean;
  isDefault: boolean;
  signature?: string;
  replyToEmail?: string;
  maxEmailsPerSync: number;

  // 自动配置支持
  autoConfigured: boolean;
  provider?: 'gmail' | 'outlook' | 'yahoo' | 'custom';

  createdAt: string;
  updatedAt: string;
}

export interface CreateEmailAccountData {
  name: string;
  email: string;
  displayName?: string;
  imapHost: string;
  imapPort: number;
  imapSecure: boolean;
  imapUsername: string;
  imapPassword: string;
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUsername: string;
  smtpPassword: string;
  syncEnabled?: boolean;
  syncInterval?: number;
  isDefault?: boolean;
  signature?: string;
  replyToEmail?: string;
  maxEmailsPerSync?: number;
}

export interface UpdateEmailAccountData {
  name?: string;
  displayName?: string;
  imapHost?: string;
  imapPort?: number;
  imapSecure?: boolean;
  imapUsername?: string;
  imapPassword?: string;
  smtpHost?: string;
  smtpPort?: number;
  smtpSecure?: boolean;
  smtpUsername?: string;
  smtpPassword?: string;
  syncEnabled?: boolean;
  syncInterval?: number;
  isActive?: boolean;
  isDefault?: boolean;
  signature?: string;
  replyToEmail?: string;
  maxEmailsPerSync?: number;
}

export interface TestConnectionData {
  imapHost: string;
  imapPort: number;
  imapSecure: boolean;
  imapUsername: string;
  imapPassword: string;
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUsername: string;
  smtpPassword: string;
}

// 获取邮箱账户列表
export const getEmailAccounts = async (): Promise<EmailAccount[]> => {
  const response = await api.get<ApiResponse<EmailAccount[]>>('/email-accounts');
  return response.data.data!;
};

// 获取邮箱账户详情
export const getEmailAccountById = async (id: number): Promise<EmailAccount> => {
  const response = await api.get<ApiResponse<EmailAccount>>(`/email-accounts/${id}`);
  return response.data.data!;
};

// 创建邮箱账户
export const createEmailAccount = async (data: CreateEmailAccountData): Promise<EmailAccount> => {
  const response = await api.post<ApiResponse<EmailAccount>>('/email-accounts', data);
  return response.data.data!;
};

// 更新邮箱账户
export const updateEmailAccount = async (id: number, data: UpdateEmailAccountData): Promise<EmailAccount> => {
  const response = await api.put<ApiResponse<EmailAccount>>(`/email-accounts/${id}`, data);
  return response.data.data!;
};

// 删除邮箱账户
export const deleteEmailAccount = async (id: number): Promise<void> => {
  await api.delete(`/email-accounts/${id}`);
};

// 测试邮箱连接
export const testEmailConnection = async (data: TestConnectionData): Promise<void> => {
  await api.post('/email-accounts/test-connection', data);
};

// 详细连接测试
export interface ConnectionTestResult {
  success: boolean;
  imapSuccess: boolean;
  smtpSuccess: boolean;
  error?: string;
  imapError?: string;
  smtpError?: string;
  testDuration: number;
}

export const testEmailConnectionDetailed = async (data: TestConnectionData): Promise<ConnectionTestResult> => {
  const response = await api.post<ApiResponse<ConnectionTestResult>>('/email-accounts/test-connection-detailed', data);
  return response.data.data!;
};

// 邮箱提供商配置
export interface EmailProviderConfig {
  name: string;
  displayName: string;
  domains: string[];
  imapHost: string;
  imapPort: number;
  imapSecure: boolean;
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  authType: 'password' | 'oauth2';
  oauthProvider?: string;
  autoConfigurable: boolean;
  helpUrl?: string;
  setupInstructions?: string;
}

// 获取支持的邮箱提供商
export const getSupportedProviders = async (): Promise<EmailProviderConfig[]> => {
  const response = await api.get<ApiResponse<EmailProviderConfig[]>>('/email-accounts/providers');
  return response.data.data!;
};

// 自动配置
export interface AutoConfigResult {
  imapHost: string;
  imapPort: number;
  imapSecure: boolean;
  imapUsername: string;
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUsername: string;
  authType: string;
  oauthProvider?: string;
  autoConfigured: boolean;
  provider?: string;
  setupInstructions?: string;
}

export const getAutoConfig = async (email: string): Promise<AutoConfigResult> => {
  const response = await api.get<ApiResponse<AutoConfigResult>>(`/email-accounts/auto-config?email=${encodeURIComponent(email)}`);
  return response.data.data!;
};

// 检测邮箱提供商
export const detectProvider = async (email: string): Promise<EmailProviderConfig | null> => {
  const response = await api.get<ApiResponse<EmailProviderConfig | null>>(`/email-accounts/detect-provider?email=${encodeURIComponent(email)}`);
  return response.data.data!;
};

// 更新账户连接状态
export const updateConnectionStatus = async (id: number): Promise<{ connectionStatus: string; testResult: ConnectionTestResult }> => {
  const response = await api.post<ApiResponse<{ connectionStatus: string; testResult: ConnectionTestResult }>>(`/email-accounts/${id}/update-connection-status`);
  return response.data.data!;
};

// 获取用户活跃账户
export const getUserActiveAccounts = async (): Promise<EmailAccount[]> => {
  const response = await api.get<ApiResponse<EmailAccount[]>>('/email-accounts/active');
  return response.data.data!;
};

// 常用邮箱服务商配置
export const EMAIL_PROVIDERS = {
  gmail: {
    name: 'Gmail',
    imapHost: 'imap.gmail.com',
    imapPort: 993,
    imapSecure: true,
    smtpHost: 'smtp.gmail.com',
    smtpPort: 587,
    smtpSecure: false,
  },
  outlook: {
    name: 'Outlook',
    imapHost: 'outlook.office365.com',
    imapPort: 993,
    imapSecure: true,
    smtpHost: 'smtp.office365.com',
    smtpPort: 587,
    smtpSecure: false,
  },
  yahoo: {
    name: 'Yahoo',
    imapHost: 'imap.mail.yahoo.com',
    imapPort: 993,
    imapSecure: true,
    smtpHost: 'smtp.mail.yahoo.com',
    smtpPort: 587,
    smtpSecure: false,
  },
  qq: {
    name: 'QQ邮箱',
    imapHost: 'imap.qq.com',
    imapPort: 993,
    imapSecure: true,
    smtpHost: 'smtp.qq.com',
    smtpPort: 587,
    smtpSecure: false,
  },
  '163': {
    name: '网易163',
    imapHost: 'imap.163.com',
    imapPort: 993,
    imapSecure: true,
    smtpHost: 'smtp.163.com',
    smtpPort: 587,
    smtpSecure: false,
  },
  '126': {
    name: '网易126',
    imapHost: 'imap.126.com',
    imapPort: 993,
    imapSecure: true,
    smtpHost: 'smtp.126.com',
    smtpPort: 587,
    smtpSecure: false,
  },
  custom: {
    name: '自定义',
    imapHost: '',
    imapPort: 993,
    imapSecure: true,
    smtpHost: '',
    smtpPort: 587,
    smtpSecure: false,
  },
};

// 根据邮箱地址自动检测服务商
export const detectEmailProvider = (email: string): keyof typeof EMAIL_PROVIDERS => {
  const domain = email.split('@')[1]?.toLowerCase();
  
  if (domain?.includes('gmail')) return 'gmail';
  if (domain?.includes('outlook') || domain?.includes('hotmail') || domain?.includes('live')) return 'outlook';
  if (domain?.includes('yahoo')) return 'yahoo';
  if (domain?.includes('qq')) return 'qq';
  if (domain?.includes('163')) return '163';
  if (domain?.includes('126')) return '126';
  
  return 'custom';
};
