import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import {
  sendEmailVerificationCode,
  verifyEmailCode,
  getVerificationStatus,
  EmailVerificationType
} from '../services/emailVerificationService';
import { body, validationResult } from 'express-validator';
import logger from '../utils/logger';
import prisma from '../config/database';

/**
 * 发送邮箱验证码
 * POST /api/verification/send-code
 */
export const sendVerificationCode = async (req: Request, res: Response) => {
  try {
    const { email, type = 'REGISTRATION' } = req.body;

    // 验证邮箱格式
    if (!email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      throw new AppError('请输入有效的邮箱地址', 400);
    }

    // 验证类型
    if (!Object.values(EmailVerificationType).includes(type)) {
      throw new AppError('无效的验证类型', 400);
    }

    // 如果是注册验证，检查邮箱是否已存在
    if (type === EmailVerificationType.REGISTRATION) {
      const existingUser = await prisma.user.findUnique({
        where: { email }
      });

      if (existingUser) {
        throw new AppError('该邮箱已被注册', 409);
      }
    }

    // 如果是密码重置，检查邮箱是否存在
    if (type === EmailVerificationType.PASSWORD_RESET) {
      const user = await prisma.user.findUnique({
        where: { email }
      });

      if (!user) {
        throw new AppError('该邮箱未注册', 404);
      }
    }

    await sendEmailVerificationCode(email, type);

    const response: ApiResponse = {
      success: true,
      message: '验证码已发送到您的邮箱',
      data: {
        email,
        type,
        expiresIn: 600 // 10分钟
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('Send verification code failed:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('发送验证码失败', 500);
  }
};

/**
 * 验证邮箱验证码
 * POST /api/verification/verify-code
 */
export const verifyCode = async (req: Request, res: Response) => {
  try {
    const { email, code, type = 'REGISTRATION' } = req.body;

    // 验证输入
    if (!email || !code) {
      throw new AppError('邮箱和验证码不能为空', 400);
    }

    if (!/^[0-9]{6}$/.test(code)) {
      throw new AppError('验证码必须是6位数字', 400);
    }

    // 验证类型
    if (!Object.values(EmailVerificationType).includes(type)) {
      throw new AppError('无效的验证类型', 400);
    }

    const isValid = await verifyEmailCode(email, code, type);

    if (!isValid) {
      throw new AppError('验证失败', 400);
    }

    const response: ApiResponse = {
      success: true,
      message: '验证成功',
      data: {
        email,
        type,
        verified: true
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('Verify code failed:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('验证失败', 500);
  }
};

/**
 * 获取验证码状态
 * GET /api/verification/status
 */
export const getCodeStatus = async (req: Request, res: Response) => {
  try {
    const { email, type = 'REGISTRATION' } = req.query;

    if (!email || typeof email !== 'string') {
      throw new AppError('邮箱参数不能为空', 400);
    }

    // 验证类型
    if (!Object.values(EmailVerificationType).includes(type as EmailVerificationType)) {
      throw new AppError('无效的验证类型', 400);
    }

    const status = await getVerificationStatus(email, type as EmailVerificationType);

    const response: ApiResponse = {
      success: true,
      message: '获取状态成功',
      data: {
        email,
        type,
        ...status
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('Get verification status failed:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('获取状态失败', 500);
  }
};

/**
 * 重新发送验证码（需要认证）
 * POST /api/verification/resend
 */
export const resendVerificationCode = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { type = 'REGISTRATION' } = req.body;

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, emailVerified: true }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    // 如果是注册验证且已验证，不允许重发
    if (type === EmailVerificationType.REGISTRATION && user.emailVerified) {
      throw new AppError('邮箱已验证，无需重复验证', 400);
    }

    await sendEmailVerificationCode(user.email, type);

    const response: ApiResponse = {
      success: true,
      message: '验证码已重新发送',
      data: {
        email: user.email,
        type,
        expiresIn: 600
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('Resend verification code failed:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('重新发送验证码失败', 500);
  }
};

/**
 * 验证当前用户邮箱（需要认证）
 * POST /api/verification/verify-current
 */
export const verifyCurrentUserEmail = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const userId = req.user!.id;
    const { code } = req.body;

    if (!code || !/^[0-9]{6}$/.test(code)) {
      throw new AppError('验证码必须是6位数字', 400);
    }

    // 获取用户信息
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: { email: true, emailVerified: true }
    });

    if (!user) {
      throw new AppError('用户不存在', 404);
    }

    if (user.emailVerified) {
      throw new AppError('邮箱已验证', 400);
    }

    const isValid = await verifyEmailCode(user.email, code, EmailVerificationType.REGISTRATION);

    if (!isValid) {
      throw new AppError('验证失败', 400);
    }

    const response: ApiResponse = {
      success: true,
      message: '邮箱验证成功',
      data: {
        email: user.email,
        verified: true
      }
    };

    res.json(response);
  } catch (error) {
    logger.error('Verify current user email failed:', error);
    if (error instanceof AppError) {
      throw error;
    }
    throw new AppError('验证失败', 500);
  }
};

// 验证中间件
export const validateSendCode = [
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('type')
    .optional()
    .isIn(Object.values(EmailVerificationType))
    .withMessage('无效的验证类型'),
  (req: Request, res: Response, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError(errors.array()[0].msg, 400);
    }
    next();
  }
];

export const validateVerifyCode = [
  body('email')
    .isEmail()
    .withMessage('请输入有效的邮箱地址')
    .normalizeEmail(),
  body('code')
    .matches(/^[0-9]{6}$/)
    .withMessage('验证码必须是6位数字'),
  body('type')
    .optional()
    .isIn(Object.values(EmailVerificationType))
    .withMessage('无效的验证类型'),
  (req: Request, res: Response, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError(errors.array()[0].msg, 400);
    }
    next();
  }
];

export const validateResendCode = [
  body('type')
    .optional()
    .isIn(Object.values(EmailVerificationType))
    .withMessage('无效的验证类型'),
  (req: Request, res: Response, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError(errors.array()[0].msg, 400);
    }
    next();
  }
];

export const validateVerifyCurrentCode = [
  body('code')
    .matches(/^[0-9]{6}$/)
    .withMessage('验证码必须是6位数字'),
  (req: Request, res: Response, next: any) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new AppError(errors.array()[0].msg, 400);
    }
    next();
  }
];
