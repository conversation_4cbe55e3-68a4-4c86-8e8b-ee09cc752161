import { useEffect, useState } from 'react';
import { Layout, Avatar, Checkbox, Button, Empty, Spin, Tag, Pagination, Tooltip, Badge } from 'antd';
import {
  UserOutlined,
  StarOutlined,
  StarFilled,
  DeleteOutlined,
  ReloadOutlined,
  PaperClipOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';
import { useEmailStore } from '../store/emailStore';
import type { Email, EmailSearchParams } from '../types';
import EmailSearch from '../components/EmailSearch';

// 安全解析 JSON 的辅助函数
const safeJsonParse = (jsonString: string | null | undefined, defaultValue: any = []) => {
  if (!jsonString) return defaultValue;
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn('JSON parse error:', error, 'Input:', jsonString);
    return defaultValue;
  }
};

const { Sider, Content } = Layout;

const Sent = () => {
  const {
    emails,
    selectedEmail,
    loading,
    pagination,
    fetchEmails,
    setSelectedEmail,
    updateEmail,
    deleteEmail,
    syncEmails,
    searchEmails,
  } = useEmailStore();

  const [selectedEmailIds, setSelectedEmailIds] = useState<string[]>([]);
  const [hoveredEmailId, setHoveredEmailId] = useState<string | null>(null);
  const [currentSearchParams, setCurrentSearchParams] = useState<EmailSearchParams>({});

  useEffect(() => {
    // 获取发件箱邮件
    fetchEmails({ folderType: 'sent' });
  }, [fetchEmails]);

  const handleEmailClick = async (email: Email) => {
    setSelectedEmail(email);
    
    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      await updateEmail(email.id, { isRead: true });
    }
  };

  const handleStarToggle = async (email: Email, e: React.MouseEvent) => {
    e.stopPropagation();
    await updateEmail(email.id, { isStarred: !email.isStarred });
  };

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmailIds([...selectedEmailIds, emailId]);
    } else {
      setSelectedEmailIds(selectedEmailIds.filter(id => id !== emailId));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedEmailIds(emails.map(email => email.id));
    } else {
      setSelectedEmailIds([]);
    }
  };

  const handleBatchDelete = async () => {
    for (const emailId of selectedEmailIds) {
      await deleteEmail(emailId);
    }
    setSelectedEmailIds([]);
  };

  const handleSearch = async (params: EmailSearchParams) => {
    setCurrentSearchParams(params);
    if (Object.keys(params).length === 0) {
      // 如果没有搜索参数，回到正常的邮件列表
      await fetchEmails({ folderType: 'sent' });
    } else {
      await searchEmails(params);
    }
  };

  const handlePageChange = (page: number, pageSize?: number) => {
    const params = {
      page,
      limit: pageSize || pagination.limit,
      ...currentSearchParams,
    };

    if (Object.keys(currentSearchParams).length === 0) {
      fetchEmails({ ...params, folderType: 'sent' });
    } else {
      searchEmails({ ...currentSearchParams, ...params });
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) {
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays <= 7) {
      return date.toLocaleDateString('zh-CN', { weekday: 'short' });
    } else {
      return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' });
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high':
        return <ExclamationCircleOutlined className="text-red-500" />;
      case 'low':
        return <CheckCircleOutlined className="text-blue-500" />;
      default:
        return null;
    }
  };

  return (
    <Layout className="h-full bg-secondary inbox-layout">
      {/* 邮件列表 */}
      <Sider width={420} className="bg-primary shadow-sm inbox-sidebar">
        {/* 列表头部 */}
        <div className="p-6 border-b border-light">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center">
              <h2 className="text-xl font-semibold text-primary m-0">发件箱</h2>
              <Badge
                count={emails.length}
                className="ml-3"
                showZero={false}
              />
            </div>
            <Tooltip title="刷新邮件" placement="bottom">
              <Button
                type="text"
                icon={<ReloadOutlined spin={loading} />}
                onClick={syncEmails}
                disabled={loading}
                className="header-action-btn"
              />
            </Tooltip>
          </div>

          {/* 搜索组件 */}
          <div className="mb-4">
            <EmailSearch onSearch={handleSearch} loading={loading} />
          </div>

          {/* 批量操作 */}
          {selectedEmailIds.length > 0 && (
            <div className="flex items-center justify-between mb-4 p-3 bg-hover rounded-lg">
              <span className="text-sm text-secondary">
                已选择 {selectedEmailIds.length} 封邮件
              </span>
              <div className="flex space-x-2">
                <Button
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                  danger
                >
                  删除
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* 邮件列表内容 */}
        <div className="flex-1 overflow-hidden">
          {loading && emails.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Spin size="large" />
            </div>
          ) : emails.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <Empty
                description="暂无发送的邮件"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            </div>
          ) : (
            <div className="email-list">
              {/* 全选框 */}
              <div className="flex items-center p-4 border-b border-light">
                <Checkbox
                  checked={selectedEmailIds.length === emails.length && emails.length > 0}
                  indeterminate={selectedEmailIds.length > 0 && selectedEmailIds.length < emails.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                >
                  全选
                </Checkbox>
              </div>

              {emails.map((email) => (
                <div
                  key={email.id}
                  className={`
                    email-item relative cursor-pointer transition-all duration-200
                    ${!email.isRead ? 'unread' : ''}
                    ${selectedEmail?.id === email.id ? 'selected' : ''}
                    ${hoveredEmailId === email.id ? 'hovered' : ''}
                  `}
                  onClick={() => handleEmailClick(email)}
                  onMouseEnter={() => setHoveredEmailId(email.id)}
                  onMouseLeave={() => setHoveredEmailId(null)}
                >
                  {/* 未读指示器 */}
                  {!email.isRead && (
                    <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-primary rounded-r"></div>
                  )}

                  <div className="flex items-start p-4 space-x-3">
                    {/* 选择框 */}
                    <Checkbox
                      checked={selectedEmailIds.includes(email.id)}
                      onChange={(e) => handleSelectEmail(email.id, e.target.checked)}
                      onClick={(e) => e.stopPropagation()}
                      className="mt-1"
                    />

                    {/* 收件人头像 */}
                    <Avatar
                      size={36}
                      icon={<UserOutlined />}
                      className="flex-shrink-0 mt-1"
                      style={{
                        backgroundColor: !email.isRead ? 'var(--primary-color)' : 'var(--text-tertiary)'
                      }}
                    >
                      {(() => {
                        const recipients = safeJsonParse(email.recipients, []);
                        return recipients[0]?.name?.charAt(0)?.toUpperCase() ||
                               recipients[0]?.email?.charAt(0)?.toUpperCase() || 'U';
                      })()}
                    </Avatar>

                    {/* 邮件内容 */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <div className="flex items-center space-x-2 min-w-0 flex-1">
                          <span className={`font-medium truncate ${!email.isRead ? 'text-primary' : 'text-secondary'}`}>
                            发送给: {safeJsonParse(email.recipients, []).map((r: any) => r.name || r.email).join(', ')}
                          </span>
                          {getPriorityIcon(email.priority || 'normal')}
                        </div>
                        <div className="flex items-center space-x-1 flex-shrink-0 ml-2">
                          {email.attachments && safeJsonParse(email.attachments, []).length > 0 && (
                            <PaperClipOutlined className="text-xs text-tertiary" />
                          )}
                          <span className="text-xs text-tertiary">
                            {formatDate(email.sentAt || email.createdAt)}
                          </span>
                        </div>
                      </div>

                      <div className="mb-2">
                        <span className={`text-sm truncate block ${!email.isRead ? 'font-medium text-primary' : 'text-secondary'}`}>
                          {email.subject || '(无主题)'}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <p className="text-xs text-tertiary truncate flex-1 mr-2">
                          {email.contentText?.substring(0, 100) || '(无内容)'}
                        </p>
                        <Button
                          type="text"
                          size="small"
                          icon={email.isStarred ? <StarFilled className="text-yellow-500" /> : <StarOutlined />}
                          onClick={(e) => handleStarToggle(email, e)}
                          className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        />
                      </div>

                      {/* 标签 */}
                      {email.labels && email.labels.length > 0 && (
                        <div className="flex flex-wrap gap-1 mt-2">
                          {email.labels.map((labelItem) => (
                            <Tag
                              key={labelItem.label.id}
                              color={labelItem.label.color}
                              size="small"
                            >
                              {labelItem.label.name}
                            </Tag>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 分页 */}
          {pagination.total > 0 && (
            <div className="p-4 border-t border-light">
              <Pagination
                current={pagination.page}
                pageSize={pagination.limit}
                total={pagination.total}
                showSizeChanger
                showQuickJumper
                showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`}
                onChange={handlePageChange}
                size="small"
              />
            </div>
          )}
        </div>
      </Sider>

      {/* 邮件详情 */}
      <Content className="bg-primary">
        {selectedEmail ? (
          <div className="h-full p-6">
            <div className="bg-primary rounded-lg shadow-sm h-full p-6">
              <div className="border-b border-light pb-4 mb-6">
                <h2 className="text-xl font-semibold text-primary mb-2">
                  {selectedEmail.subject || '(无主题)'}
                </h2>
                <div className="flex items-center justify-between text-sm text-secondary">
                  <div>
                    <span className="font-medium">发送给: </span>
                    {safeJsonParse(selectedEmail.recipients, []).map((r: any) => r.name || r.email).join(', ')}
                  </div>
                  <span>{new Date(selectedEmail.sentAt || selectedEmail.createdAt).toLocaleString()}</span>
                </div>
              </div>
              
              <div 
                className="prose max-w-none text-secondary"
                dangerouslySetInnerHTML={{ 
                  __html: selectedEmail.contentHtml || selectedEmail.contentText?.replace(/\n/g, '<br>') || '' 
                }}
              />
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <Empty
              description="选择一封邮件查看详情"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          </div>
        )}
      </Content>
    </Layout>
  );
};

export default Sent;
