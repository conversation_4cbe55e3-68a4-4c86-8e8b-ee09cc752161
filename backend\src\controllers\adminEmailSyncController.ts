import { AuthenticatedRequest, ApiResponse, AppError } from '../types';
import prisma from '../config/database';
import { adminFullEmailSync } from '../services/imapService';
import logger from '../utils/logger';

/**
 * 管理员获取域内用户列表
 */
export const getDomainUsers = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 验证管理员权限
    const adminUser = req.user!;
    if (adminUser.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 获取所有活跃用户（排除管理员自己）
    const users = await prisma.user.findMany({
      where: {
        isActive: true,
        role: { not: 'admin' }, // 排除其他管理员
        id: { not: adminUser.id } // 排除当前管理员
      },
      select: {
        id: true,
        email: true,
        username: true,
        displayName: true,
        isActive: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        // 统计邮件数量
        _count: {
          select: {
            emails: {
              where: {
                isDeleted: false
              }
            }
          }
        }
      },
      orderBy: [
        { email: 'asc' }
      ]
    });

    // 为每个用户添加邮件统计信息
    const usersWithStats = users.map(user => ({
      id: user.id,
      email: user.email,
      username: user.username,
      displayName: user.displayName,
      isActive: user.isActive,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.updatedAt, // 使用 updatedAt 作为最后登录时间的近似值
      emailCount: user._count.emails
    }));

    const response: ApiResponse = {
      success: true,
      message: '获取域内用户列表成功',
      data: {
        users: usersWithStats,
        totalUsers: usersWithStats.length
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取域内用户列表失败:', error);
    throw new AppError('获取域内用户列表失败', 500);
  }
};

/**
 * 管理员为指定用户执行全量邮件同步
 */
export const adminSyncUserEmails = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { targetUserEmail } = req.body;
    const adminEmail = req.user!.email;

    // 验证管理员权限
    if (req.user!.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证目标用户邮箱参数
    if (!targetUserEmail) {
      throw new AppError('请提供目标用户邮箱地址', 400);
    }

    // 验证目标用户邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(targetUserEmail)) {
      throw new AppError('目标用户邮箱格式无效', 400);
    }

    // 不允许管理员同步自己的邮件（应该使用普通同步接口）
    if (targetUserEmail === adminEmail) {
      throw new AppError('不能使用管理员接口同步自己的邮件，请使用普通同步功能', 400);
    }

    logger.info(`管理员 ${adminEmail} 开始为用户 ${targetUserEmail} 执行全量邮件同步`);

    // 执行管理员全量同步
    const syncResult = await adminFullEmailSync(adminEmail, targetUserEmail);

    const response: ApiResponse = {
      success: true,
      message: `用户 ${targetUserEmail} 的邮件全量同步完成，删除 ${syncResult.deleteResult.deletedCount} 封邮件，重新同步 ${syncResult.syncResult.syncedCount} 封邮件`,
      data: {
        adminEmail,
        targetUserEmail,
        deleteResult: {
          deletedCount: syncResult.deleteResult.deletedCount,
          deletedEmailsPreview: syncResult.deleteResult.deletedEmails.slice(0, 10) // 只返回前10封作为预览
        },
        syncResult: {
          syncedCount: syncResult.syncResult.syncedCount,
          skippedCount: syncResult.syncResult.skippedCount,
          errorCount: syncResult.syncResult.errorCount,
          syncedEmailsPreview: syncResult.syncResult.syncedEmails.slice(0, 10) // 只返回前10封作为预览
        },
        summary: syncResult.summary,
        timestamp: new Date().toISOString()
      }
    };

    logger.info(`管理员 ${adminEmail} 为用户 ${targetUserEmail} 的全量同步操作完成`);
    res.json(response);

  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('管理员邮件同步失败:', error);
    throw new AppError(`管理员邮件同步失败: ${(error as Error).message}`, 500);
  }
};

/**
 * 管理员获取用户邮件统计信息
 */
export const getUserEmailStats = async (req: AuthenticatedRequest, res: Response) => {
  try {
    const { userEmail } = req.params;
    const adminUser = req.user!;

    // 验证管理员权限
    if (adminUser.role !== 'admin') {
      throw new AppError('权限不足，只有管理员可以执行此操作', 403);
    }

    // 验证目标用户
    const targetUser = await prisma.user.findUnique({
      where: { email: userEmail },
      select: { id: true, email: true, username: true, displayName: true }
    });

    if (!targetUser) {
      throw new AppError(`用户 ${userEmail} 不存在`, 404);
    }

    // 获取用户的文件夹信息
    const folders = await prisma.folder.findMany({
      where: { userId: targetUser.id },
      select: { id: true, type: true }
    });

    const folderMap = new Map();
    folders.forEach(folder => {
      folderMap.set(folder.type, folder.id);
    });

    // 获取各文件夹的邮件统计
    const [
      totalEmails,
      inboxCount,
      sentCount,
      draftCount,
      trashCount,
      unreadCount,
      starredCount
    ] = await Promise.all([
      // 总邮件数（不包括已删除）
      prisma.email.count({
        where: { userId: targetUser.id, isDeleted: false }
      }),
      // 收件箱邮件数
      prisma.email.count({
        where: {
          userId: targetUser.id,
          folderId: folderMap.get('inbox'),
          isDeleted: false
        }
      }),
      // 发件箱邮件数
      prisma.email.count({
        where: {
          userId: targetUser.id,
          folderId: folderMap.get('sent'),
          isDeleted: false
        }
      }),
      // 草稿箱邮件数
      prisma.email.count({
        where: {
          userId: targetUser.id,
          folderId: folderMap.get('draft'),
          isDeleted: false
        }
      }),
      // 垃圾箱邮件数（已删除的邮件）
      prisma.email.count({
        where: {
          userId: targetUser.id,
          isDeleted: true
        }
      }),
      // 未读邮件数
      prisma.email.count({
        where: {
          userId: targetUser.id,
          isRead: false,
          isDeleted: false
        }
      }),
      // 星标邮件数
      prisma.email.count({
        where: {
          userId: targetUser.id,
          isStarred: true,
          isDeleted: false
        }
      })
    ]);

    // 获取物理文件夹邮件统计（使用doveadm命令）
    let physicalFolderStats = {
      totalPhysicalEmails: 0,
      inboxPhysical: 0,
      sentPhysical: 0,
      draftPhysical: 0,
      trashPhysical: 0
    };

    try {
      const { exec } = require('child_process');
      const util = require('util');
      const execAsync = util.promisify(exec);

      const isLocal = process.env['NODE_ENV'] === 'production' || process.env['MAIL_SERVER_LOCAL'] === 'true';

      // 获取各文件夹的物理邮件数量
      const folders = ['INBOX', 'Sent', 'Drafts', 'Trash'];
      const physicalCounts = await Promise.all(
        folders.map(async (folder) => {
          try {
            let command: string;
            if (isLocal) {
              command = `doveadm search -u ${userEmail} mailbox ${folder} | wc -l`;
            } else {
              command = `ssh <EMAIL> "doveadm search -u ${userEmail} mailbox ${folder} | wc -l"`;
            }

            const { stdout } = await execAsync(command);
            return parseInt(stdout.trim()) || 0;
          } catch (error) {
            logger.warn(`获取用户 ${userEmail} 的 ${folder} 文件夹物理邮件数失败:`, error);
            return 0;
          }
        })
      );

      physicalFolderStats = {
        totalPhysicalEmails: physicalCounts.reduce((sum, count) => sum + count, 0),
        inboxPhysical: physicalCounts[0],
        sentPhysical: physicalCounts[1],
        draftPhysical: physicalCounts[2],
        trashPhysical: physicalCounts[3]
      };
    } catch (error) {
      logger.warn(`获取用户 ${userEmail} 物理邮件统计失败:`, error);
    }

    const response: ApiResponse = {
      success: true,
      message: '获取用户邮件统计成功',
      data: {
        userEmail: targetUser.email,
        totalEmails,
        inboxCount,
        sentCount,
        draftCount,
        trashCount,
        unreadCount,
        starredCount,
        lastSyncTime: new Date().toISOString(), // 可以后续改为实际的最后同步时间
        physicalFolderStats
      }
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('获取用户邮件统计失败:', error);
    throw new AppError('获取用户邮件统计失败', 500);
  }
};
