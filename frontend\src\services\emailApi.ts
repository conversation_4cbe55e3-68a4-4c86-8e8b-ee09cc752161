import api from '../config/api';
import type { Email, EmailData, Folder, EmailSearchParams, PaginatedResponse, ApiResponse } from '../types';

// 获取邮件列表
export const getEmails = async (params: any = {}): Promise<PaginatedResponse<Email>> => {
  const response = await api.get<ApiResponse<PaginatedResponse<Email>>>('/emails', { params });
  return response.data.data!;
};

// 获取邮件详情
export const getEmailById = async (id: string): Promise<Email> => {
  const response = await api.get<ApiResponse<Email>>(`/emails/${id}`);
  return response.data.data!;
};

// 发送邮件
export const sendEmail = async (data: EmailData): Promise<Email> => {
  const response = await api.post<ApiResponse<Email>>('/emails', data);
  return response.data.data!;
};

// 保存草稿
export const saveDraft = async (data: Partial<EmailData>): Promise<Email> => {
  const response = await api.post<ApiResponse<Email>>('/emails/drafts', data);
  return response.data.data!;
};

// 更新草稿
export const updateDraft = async (id: string, data: Partial<EmailData>): Promise<Email> => {
  const response = await api.put<ApiResponse<Email>>(`/emails/drafts/${id}`, data);
  return response.data.data!;
};

// 更新邮件状态
export const updateEmail = async (id: string, data: Partial<Email>): Promise<void> => {
  await api.put(`/emails/${id}`, data);
};

// 删除邮件
export const deleteEmail = async (id: string): Promise<void> => {
  await api.delete(`/emails/${id}`);
};

// 批量操作邮件
export const batchUpdateEmails = async (
  emailIds: string[],
  action: string,
  value?: any
): Promise<void> => {
  await api.post('/emails/batch', {
    emailIds,
    action,
    value,
  });
};

// 搜索邮件
export const searchEmails = async (params: EmailSearchParams): Promise<PaginatedResponse<Email>> => {
  const response = await api.post<ApiResponse<PaginatedResponse<Email>>>('/emails/search', params);
  return response.data.data!;
};

// 标记为已读
export const markAsRead = async (id: string): Promise<Email> => {
  const response = await api.patch<ApiResponse<Email>>(`/emails/${id}/read`);
  return response.data.data!;
};

// 标记为未读
export const markAsUnread = async (id: string): Promise<Email> => {
  const response = await api.patch<ApiResponse<Email>>(`/emails/${id}/unread`);
  return response.data.data!;
};

// 标记为星标/取消星标
export const toggleStar = async (id: string, isStarred: boolean): Promise<void> => {
  await api.put(`/emails/${id}/star`, { isStarred });
};

// 移动邮件到文件夹
export const moveEmail = async (id: string, folderId: string): Promise<Email> => {
  const response = await api.patch<ApiResponse<Email>>(`/emails/${id}/move`, { folderId });
  return response.data.data!;
};

// 同步邮件
export const syncEmails = async (): Promise<{ count: number; emails: Email[] }> => {
  const response = await api.post<ApiResponse<{ count: number; emails: Email[] }>>('/emails/sync');
  return response.data.data!;
};

// 同步邮件状态到IMAP
export const syncStatusToIMAP = async (): Promise<{
  syncedCount: number;
  errorCount: number;
  syncedEmails: Array<{
    messageId: string;
    subject: string;
    status: 'synced' | 'error';
    error?: string;
  }>;
}> => {
  const response = await api.post<ApiResponse<{
    syncedCount: number;
    errorCount: number;
    syncedEmails: Array<{
      messageId: string;
      subject: string;
      status: 'synced' | 'error';
      error?: string;
    }>;
  }>>('/emails/sync-status');
  return response.data.data!;
};

// 重新分配孤儿邮件
export const reassignOrphanEmails = async (): Promise<{
  totalRecovered: number;
  orphanEmails: {
    reassignedCount: number;
    reassignedEmails: Array<{
      id: string;
      subject: string;
      messageId: string;
      originalUserId: number;
    }>;
  };
  physicalFolderEmails: {
    recoveredCount: number;
    skippedCount: number;
    errorCount: number;
    recoveredEmails: Array<{
      filename: string;
      subject: string;
      messageId: string;
      status: 'recovered' | 'skipped' | 'error';
      error?: string;
    }>;
  };
  userEmail: string;
}> => {
  const response = await api.post<ApiResponse<{
    totalRecovered: number;
    orphanEmails: {
      reassignedCount: number;
      reassignedEmails: Array<{
        id: string;
        subject: string;
        messageId: string;
        originalUserId: number;
      }>;
    };
    physicalFolderEmails: {
      recoveredCount: number;
      skippedCount: number;
      errorCount: number;
      recoveredEmails: Array<{
        filename: string;
        subject: string;
        messageId: string;
        status: 'recovered' | 'skipped' | 'error';
        error?: string;
      }>;
    };
    userEmail: string;
  }>>('/emails/sync/reassign-orphans');
  return response.data.data!;
};

// 获取文件夹列表
export const getFolders = async (): Promise<Folder[]> => {
  const response = await api.get<ApiResponse<Folder[]>>('/folders');
  return response.data.data!;
};

// 创建文件夹
export const createFolder = async (data: {
  name: string;
  type?: string;
  parentId?: number;
}): Promise<Folder> => {
  const response = await api.post<ApiResponse<Folder>>('/folders', data);
  return response.data.data!;
};

// 更新文件夹
export const updateFolder = async (id: number, data: {
  name?: string;
  parentId?: number;
}): Promise<Folder> => {
  const response = await api.put<ApiResponse<Folder>>(`/folders/${id}`, data);
  return response.data.data!;
};

// 删除文件夹
export const deleteFolder = async (id: number): Promise<void> => {
  await api.delete(`/folders/${id}`);
};
