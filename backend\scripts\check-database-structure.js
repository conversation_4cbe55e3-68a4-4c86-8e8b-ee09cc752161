const mysql = require('mysql2/promise');

async function checkDatabaseStructure() {
  let connection;
  
  try {
    console.log('🔍 检查数据库结构...\n');

    // 连接数据库
    connection = await mysql.createConnection({
      host: 'mail.blindedby.love',
      user: 'mailuser',
      password: 'HOUsc@0202',
      database: 'mailserver'
    });

    console.log('✅ 数据库连接成功\n');

    // 检查users表结构
    console.log('📋 检查users表结构:');
    const [columns] = await connection.execute('DESCRIBE users');
    
    console.log('当前字段:');
    columns.forEach(col => {
      console.log(`  - ${col.Field} (${col.Type}) ${col.Null === 'YES' ? 'NULL' : 'NOT NULL'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 检查缺失的字段
    const requiredFields = [
      'parent_user_id',
      'account_type', 
      'max_sub_accounts',
      'sub_account_quota',
      'is_sub_account_enabled'
    ];

    console.log('\n🔍 检查缺失字段:');
    const existingFields = columns.map(col => col.Field);
    const missingFields = requiredFields.filter(field => !existingFields.includes(field));

    if (missingFields.length > 0) {
      console.log('❌ 缺失字段:');
      missingFields.forEach(field => {
        console.log(`  - ${field}`);
      });

      console.log('\n📝 需要添加的字段:');
      const alterStatements = [];

      if (!existingFields.includes('parent_user_id')) {
        alterStatements.push('ADD COLUMN parent_user_id INT NULL');
      }
      if (!existingFields.includes('account_type')) {
        alterStatements.push("ADD COLUMN account_type VARCHAR(191) NOT NULL DEFAULT 'main'");
      }
      if (!existingFields.includes('max_sub_accounts')) {
        alterStatements.push('ADD COLUMN max_sub_accounts INT NOT NULL DEFAULT 5');
      }
      if (!existingFields.includes('sub_account_quota')) {
        alterStatements.push('ADD COLUMN sub_account_quota BIGINT NULL');
      }
      if (!existingFields.includes('is_sub_account_enabled')) {
        alterStatements.push('ADD COLUMN is_sub_account_enabled BOOLEAN NOT NULL DEFAULT false');
      }

      if (alterStatements.length > 0) {
        console.log('\n🔧 执行数据库更新...');
        const alterSQL = `ALTER TABLE users ${alterStatements.join(', ')}`;
        console.log('SQL:', alterSQL);
        
        await connection.execute(alterSQL);
        console.log('✅ 字段添加成功');

        // 添加外键约束
        if (!existingFields.includes('parent_user_id')) {
          try {
            await connection.execute(`
              ALTER TABLE users 
              ADD CONSTRAINT fk_users_parent_user 
              FOREIGN KEY (parent_user_id) REFERENCES users(id) ON DELETE CASCADE
            `);
            console.log('✅ 外键约束添加成功');
          } catch (fkError) {
            console.log('⚠️  外键约束添加失败 (可能已存在):', fkError.message);
          }
        }
      }
    } else {
      console.log('✅ 所有必需字段都存在');
    }

    // 检查app_passwords表
    console.log('\n📋 检查app_passwords表:');
    try {
      const [appPasswordTables] = await connection.execute("SHOW TABLES LIKE 'app_passwords'");
      if (appPasswordTables.length === 0) {
        console.log('❌ app_passwords表不存在，创建中...');
        await connection.execute(`
          CREATE TABLE app_passwords (
            id int NOT NULL AUTO_INCREMENT,
            user_id int NOT NULL,
            name varchar(191) NOT NULL,
            password varchar(191) NOT NULL,
            purpose varchar(191) NOT NULL DEFAULT 'imap',
            is_active boolean NOT NULL DEFAULT true,
            last_used_at datetime(3) NULL,
            expires_at datetime(3) NULL,
            created_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
            updated_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
            PRIMARY KEY (id),
            UNIQUE KEY app_passwords_user_id_name_key (user_id, name),
            KEY app_passwords_user_id_fkey (user_id),
            CONSTRAINT app_passwords_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE
          ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        `);
        console.log('✅ app_passwords表创建成功');
      } else {
        console.log('✅ app_passwords表已存在');
      }
    } catch (error) {
      console.log('❌ 检查app_passwords表失败:', error.message);
    }

    console.log('\n🎉 数据库结构检查完成！');

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkDatabaseStructure().catch(console.error);
