import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import {
  startRealTimeService,
  stopRealTimeService,
  getRealTimeServiceStatus,
  startUserRealTimeSync,
  stopUserRealTimeSync,
  triggerEmailCheck
} from '../controllers/realTimeEmailController';

const router = Router();

// 管理员路由 - 管理整个实时邮件服务
router.post('/start', startRealTimeService);
router.post('/stop', stopRealTimeService);
router.get('/status', getRealTimeServiceStatus);

// 用户路由 - 管理个人实时邮件监听
router.post('/user/start', authenticate, startUserRealTimeSync);
router.post('/user/stop', authenticate, stopUserRealTimeSync);
router.post('/user/check', authenticate, triggerEmailCheck);

export default router;
