# 开发环境配置 - 使用Vite代理解决跨域问题
# 代理配置（推荐）- 通过本地代理访问远程API
# VITE_API_BASE_URL=/api
# VITE_WS_URL=wss://mail.blindedby.love

# 直接访问远程服务器（如果服务器CORS配置正确）
# VITE_API_BASE_URL=https://mail.blindedby.love/api
# VITE_WS_URL=wss://mail.blindedby.love

# 本地开发配置（如果需要连接本地后端）
VITE_API_BASE_URL=http://localhost:3001/api
VITE_WS_URL=ws://localhost:3001

# 应用配置
VITE_APP_NAME=邮箱系统 (开发)
VITE_APP_VERSION=1.0.0
VITE_APP_ENV=development

# 文件上传配置
VITE_MAX_FILE_SIZE=10485760

# 功能开关
VITE_ENABLE_NOTIFICATIONS=true
VITE_ENABLE_DARK_MODE=true

# 调试配置
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_ERROR_REPORTING=false
VITE_DEBUG_MODE=true
