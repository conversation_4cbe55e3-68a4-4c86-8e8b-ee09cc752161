import { Request, Response, NextFunction } from 'express';
import { AppError, ApiResponse } from '../types';
import logger from '../utils/logger';
import config from '../config/env';

// 404错误处理
export const notFoundHandler = (req: Request, res: Response, next: NextFunction) => {
  const error = new AppError(`路由 ${req.originalUrl} 不存在`, 404);
  next(error);
};

// 全局错误处理中间件
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  let statusCode = 500;
  let message = '服务器内部错误';
  let isOperational = false;

  // 如果是自定义错误
  if (error instanceof AppError) {
    statusCode = error.statusCode;
    message = error.message;
    isOperational = error.isOperational;
  }

  // Prisma错误处理
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as unknown as { code: string; meta?: Record<string, unknown> };
    switch (prismaError.code) {
      case 'P2002':
        statusCode = 409;
        message = '数据已存在，请检查唯一字段';
        break;
      case 'P2025':
        statusCode = 404;
        message = '请求的数据不存在';
        break;
      case 'P2003':
        statusCode = 400;
        message = '外键约束错误';
        break;
      default:
        statusCode = 400;
        message = '数据库操作错误';
    }
    isOperational = true;
  }

  // JWT错误处理
  if (error.name === 'JsonWebTokenError') {
    statusCode = 401;
    message = '无效的访问令牌';
    isOperational = true;
  }

  if (error.name === 'TokenExpiredError') {
    statusCode = 401;
    message = '访问令牌已过期';
    isOperational = true;
  }

  // 验证错误处理
  if (error.name === 'ValidationError') {
    statusCode = 400;
    message = '请求数据验证失败';
    isOperational = true;
  }

  // 记录错误日志
  if (!isOperational || statusCode >= 500) {
    logger.error(`${error.message}`, {
      error: error.stack,
      url: req.originalUrl,
      method: req.method,
      ip: req.ip,
      userAgent: req.get('User-Agent') || null,
    });
  }

  // 构造响应
  const response: ApiResponse = {
    success: false,
    message,
  };

  // 开发环境返回详细错误信息
  if (config.NODE_ENV === 'development') {
    response.error = error.stack;
  }

  res.status(statusCode).json(response);
};

// 异步错误包装器
export const asyncHandler = (
  fn: (req: Request, res: Response, next: NextFunction) => Promise<void> | void
) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};
