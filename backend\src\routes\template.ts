import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate } from '../middleware/auth';
import { validateCreateTemplate, validateId, validatePagination } from '../middleware/validation';
import * as templateController from '../controllers/templateController';

const router = Router();

// 所有路由都需要认证
router.use(authenticate);

// 获取邮件模板列表
router.get('/',
  validatePagination,
  asyncHandler(templateController.getTemplates)
);

// 获取邮件模板详情
router.get('/:id',
  validateId,
  asyncHandler(templateController.getTemplateById)
);

// 创建邮件模板
router.post('/',
  validateCreateTemplate,
  asyncHandler(templateController.createTemplate)
);

// 更新邮件模板
router.put('/:id',
  validateId,
  validateCreateTemplate,
  asyncHandler(templateController.updateTemplate)
);

// 删除邮件模板
router.delete('/:id',
  validateId,
  asyncHandler(templateController.deleteTemplate)
);

export default router;
