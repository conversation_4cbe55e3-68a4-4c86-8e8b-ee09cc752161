import { Link, useNavigate } from "react-router-dom";
import { Form, Input, Alert, Button } from "antd";
import { useAuthStore } from "../store/authStore";
import type { LoginData } from "../types";
import { MailOutlined, LockOutlined } from '@ant-design/icons';

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const { login, loading, error, clearError } = useAuthStore();

  const handleSubmit = (values: LoginData) => {
    console.log('🔐 Login页面: 开始登录流程', values);
    void (async () => {
      try {
        clearError();
        console.log('📡 Login页面: 调用 authStore.login');
        await login(values);
        console.log('✅ Login页面: 登录成功，准备跳转到收件箱');
        void navigate("/inbox");
      } catch (error) {
        console.error('❌ Login页面: 登录失败', error);
        // 错误已经在store中处理
      }
    })();
  };

  return (
    <div
      className="rounded-lg shadow-xl p-8 w-full max-w-md"
      style={{
        backgroundColor: '#fffFff', /* 非常淡的天蓝色背景 */
        boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05)'
      }}
    >
      {/* 登录标题 */}
      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold mb-2" style={{ color: '#FF7F50' }}>邮箱客户端</h2>
        <div className="text-sm" style={{ color: '#4a5568' }}>登录您的账户以管理邮件</div>
      </div>

      {error ? (
        <Alert
          message={error}
          type="error"
          showIcon
          closable
          onClose={clearError}
          className="mb-4 rounded-lg"
        />
      ) : null}

      <Form
        form={form}
        onFinish={handleSubmit}
        autoComplete="off"
        layout="vertical"
      >
        {/* 邮箱输入框 */}
        <Form.Item
          label="邮箱地址"
          name="email"
          rules={[
            { required: true, message: "请输入邮箱地址" },
            { type: "email", message: "请输入有效的邮箱地址" },
          ]}
          className="mb-4"
        >
          <Input
            prefix={<MailOutlined style={{ color: '#a0aec0' }} />}
            placeholder="<EMAIL>"
            autoComplete="email"
            className="h-10"
            style={{
              borderRadius: '6px',
              borderColor: '#e2e8f0',
              backgroundColor: '#ffffff',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}
          />
        </Form.Item>

        {/* 密码输入框 */}
        <Form.Item
          label="密码"
          name="password"
          rules={[
            { required: true, message: "请输入密码" },
            { min: 6, message: "密码长度至少为6位" },
          ]}
          className="mb-4"
        >
          <Input.Password
            prefix={<LockOutlined style={{ color: '#a0aec0' }} />}
            placeholder="请输入密码"
            autoComplete="current-password"
            className="h-10"
            style={{
              borderRadius: '6px',
              borderColor: '#e2e8f0',
              backgroundColor: '#ffffff',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
            }}
          />
        </Form.Item>

        <div className="flex justify-end mb-6">
          <Link to="/forgot-password" style={{ color: '#FF7F50' }} className="hover:underline text-sm">忘记密码？</Link>
        </div>

        {/* 登录按钮 */}
        <Form.Item className="mb-6">
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
            style={{
              fontSize: '16px',
              height: 40,
              backgroundColor: '#FF7F50',
              borderColor: '#FF7F50',
              borderRadius: '6px'
            }}
          >
            {loading ? "登录中..." : "登录"}
          </Button>
        </Form.Item>
      </Form>

      {/* 底部链接 */}
      <div className="text-center text-sm">
        <span style={{ color: '#4a5568' }}>还没有账号？</span>
        <Link to="/register" style={{ color: '#FF7F50' }} className="hover:underline ml-1">立即注册</Link>
      </div>
    </div>
  );
};

export default Login;
