#!/bin/bash

# 部署客户端邮件规则的脚本

set -e

echo "=== 部署客户端邮件规则 ==="

# 1. 创建规则模板目录
echo "1. 创建规则模板目录:"
mkdir -p /opt/mail-rules-templates
mkdir -p /opt/mail-rules-templates/thunderbird
mkdir -p /opt/mail-rules-templates/outlook
mkdir -p /opt/mail-rules-templates/apple-mail

# 2. 创建Thunderbird规则模板
echo "2. 创建Thunderbird规则模板:"
cat > /opt/mail-rules-templates/thunderbird/base-template.dat << 'EOF'
version="9"
logging="yes"
name="[SYSTEM] 发件副本自动移动"
enabled="yes"
type="17"
action="Move to folder"
actionValue="mailbox://<EMAIL>/Sent"
condition="AND (header,X-Mail-Submission,contains,yes) AND (header,X-Outgoing-Mail,contains,true) AND (from,contains,<EMAIL>)"
EOF

# 3. 创建Outlook规则模板
echo "3. 创建Outlook规则模板:"
cat > /opt/mail-rules-templates/outlook/base-template.xml << 'EOF'
<?xml version="1.0" encoding="UTF-8"?>
<rules>
  <rule>
    <name>[SYSTEM] 发件副本自动移动</name>
    <enabled>true</enabled>
    <conditions>
      <condition type="header">
        <header>X-Mail-Submission</header>
        <value>yes</value>
      </condition>
      <condition type="from">
        <value><EMAIL></value>
      </condition>
    </conditions>
    <actions>
      <action type="move">
        <folder>Sent Items</folder>
      </action>
      <action type="stop">true</action>
    </actions>
  </rule>
</rules>
EOF

# 4. 获取邮件用户列表
echo "4. 获取邮件用户列表:"
USERS=()
if [ -d "/var/mail/vhosts/blindedby.love" ]; then
    for user_dir in /var/mail/vhosts/blindedby.love/*/; do
        if [ -d "$user_dir" ]; then
            username=$(basename "$user_dir")
            if [[ "$username" != "." && "$username" != ".." ]]; then
                USERS+=("$username")
            fi
        fi
    done
fi

echo "发现用户: ${USERS[*]}"

# 5. 为每个用户生成个性化规则
echo "5. 为每个用户生成个性化规则:"
for user in "${USERS[@]}"; do
    echo "  生成用户 $user 的规则..."
    
    # Thunderbird规则
    sed "s/USER/$user/g" /opt/mail-rules-templates/thunderbird/base-template.dat > \
        "/opt/mail-rules-templates/thunderbird/${user}-rules.dat"
    
    # Outlook规则
    sed "s/USER/$user/g" /opt/mail-rules-templates/outlook/base-template.xml > \
        "/opt/mail-rules-templates/outlook/${user}-rules.xml"
    
    echo "    ✅ $user 的规则已生成"
done

# 6. 创建用户配置指南
echo "6. 创建用户配置指南:"
cat > /opt/mail-rules-templates/USER-GUIDE.md << 'EOF'
# 邮件客户端规则配置指南

## 📧 Thunderbird 配置

### 1. 下载规则文件
从服务器下载你的专用规则文件：
```bash
scp user@server:/opt/mail-rules-templates/thunderbird/YOUR_USERNAME-rules.dat ~/
```

### 2. 导入规则
1. 关闭 Thunderbird
2. 找到配置文件夹：
   - Windows: `%APPDATA%\Thunderbird\Profiles\xxxxxxxx.default\`
   - Linux: `~/.thunderbird/xxxxxxxx.default/`
   - macOS: `~/Library/Thunderbird/Profiles/xxxxxxxx.default/`
3. 将下载的规则文件重命名为 `msgFilterRules.dat`
4. 复制到配置文件夹中
5. 重启 Thunderbird

### 3. 验证规则
1. 打开 **工具** → **消息过滤器**
2. 应该看到 "[SYSTEM] 发件副本自动移动" 规则
3. 确保规则已启用

### 4. 添加个人规则
在系统规则基础上，你可以添加：
- 垃圾邮件过滤
- 重要邮件分类
- 自动转发规则
- 等等...

**注意**: 不要修改或删除 [SYSTEM] 开头的规则！

## 📧 Outlook 配置

### 1. 下载规则文件
```bash
scp user@server:/opt/mail-rules-templates/outlook/YOUR_USERNAME-rules.xml ~/
```

### 2. 导入规则
1. 打开 Outlook
2. **文件** → **管理规则和通知**
3. **选项** → **导入规则**
4. 选择下载的 XML 文件
5. 点击 **确定**

### 3. 验证和自定义
1. 检查 "[SYSTEM] 发件副本自动移动" 规则是否存在
2. 在此基础上添加个人规则
3. 确保系统规则在最高优先级

## 🍎 Apple Mail 配置

### 1. 手动创建规则
由于 Apple Mail 的限制，需要手动创建：

1. **Mail** → **偏好设置** → **规则**
2. **添加规则**
3. 规则名称: `[SYSTEM] 发件副本自动移动`
4. 条件设置:
   - 邮件头部 包含 "X-Mail-Submission: yes"
   - 发件人 包含 "<EMAIL>"
5. 操作: 移动邮件到邮箱 → 已发送
6. ✅ 停止评估规则

## 🔧 故障排除

### 规则不工作？
1. 检查规则是否启用
2. 确认条件设置正确
3. 查看邮件头部信息
4. 检查规则优先级

### 需要帮助？
联系系统管理员或查看详细文档。
EOF

# 7. 设置权限
echo "7. 设置权限:"
chown -R root:mail /opt/mail-rules-templates
chmod -R 644 /opt/mail-rules-templates
chmod 755 /opt/mail-rules-templates
chmod 755 /opt/mail-rules-templates/*/

# 8. 创建Web下载页面（可选）
echo "8. 创建Web下载页面:"
if command -v nginx >/dev/null 2>&1 || command -v apache2 >/dev/null 2>&1; then
    mkdir -p /var/www/html/mail-rules
    cp -r /opt/mail-rules-templates/* /var/www/html/mail-rules/
    
    cat > /var/www/html/mail-rules/index.html << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>邮件客户端规则下载</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>邮件客户端规则下载</h1>
    <p>请选择你的用户名和邮件客户端类型下载对应的规则文件：</p>
    
    <h2>Thunderbird 规则</h2>
    <ul id="thunderbird-rules"></ul>
    
    <h2>Outlook 规则</h2>
    <ul id="outlook-rules"></ul>
    
    <h2>配置指南</h2>
    <p><a href="USER-GUIDE.md">点击查看详细配置指南</a></p>
    
    <script>
        // 动态生成下载链接
        const users = ['qqq', 'aaa', 'admin']; // 这里需要根据实际用户更新
        
        const tbList = document.getElementById('thunderbird-rules');
        const olList = document.getElementById('outlook-rules');
        
        users.forEach(user => {
            // Thunderbird
            const tbLi = document.createElement('li');
            tbLi.innerHTML = `<a href="thunderbird/${user}-rules.dat">${user} - Thunderbird规则</a>`;
            tbList.appendChild(tbLi);
            
            // Outlook
            const olLi = document.createElement('li');
            olLi.innerHTML = `<a href="outlook/${user}-rules.xml">${user} - Outlook规则</a>`;
            olList.appendChild(olLi);
        });
    </script>
</body>
</html>
EOF
    
    echo "  ✅ Web下载页面已创建: http://your-server/mail-rules/"
fi

echo -e "\n=== 部署完成 ==="
echo ""
echo "📁 规则文件位置: /opt/mail-rules-templates/"
echo "📋 用户指南: /opt/mail-rules-templates/USER-GUIDE.md"
echo ""
echo "🔧 下一步操作:"
echo "1. 将规则文件分发给用户"
echo "2. 指导用户按照指南配置客户端"
echo "3. 测试发件副本功能"
echo ""
echo "📧 生成的用户规则:"
for user in "${USERS[@]}"; do
    echo "  - $user: thunderbird/${user}-rules.dat, outlook/${user}-rules.xml"
done
