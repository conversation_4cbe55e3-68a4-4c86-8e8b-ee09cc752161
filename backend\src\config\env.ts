import dotenv from 'dotenv';
import path from 'path';

// 根据环境加载对应的环境变量文件
const NODE_ENV = process.env['NODE_ENV'] || 'development';
const envFile = NODE_ENV === 'production' ? '.env.production' :
               NODE_ENV === 'staging' ? '.env.staging' :
               '.env';

// 尝试加载环境变量文件
const envPath = path.resolve(process.cwd(), envFile);
console.log(`Loading environment from: ${envPath}`);

try {
  dotenv.config({ path: envPath });
  console.log(`✓ Loaded environment file: ${envFile}`);
} catch (error) {
  console.warn(`⚠ Warning: Could not load ${envFile}, trying default .env`);
  // 如果主环境文件不存在，尝试加载默认的 .env 文件
  dotenv.config();
}

interface Config {
  // 应用配置
  NODE_ENV: string;
  PORT: number;
  APP_NAME: string;

  // 数据库配置
  DATABASE_URL: string;
  REDIS_URL: string;

  // JWT配置
  JWT_SECRET: string;
  JWT_EXPIRES_IN: string;
  JWT_REFRESH_EXPIRES_IN: string;

  // 邮件服务配置
  SMTP_HOST: string;
  SMTP_PORT: number;
  SMTP_SECURE: boolean;
  SMTP_USER: string;
  SMTP_PASS: string;

  // IMAP配置
  IMAP_HOST: string;
  IMAP_PORT: number;
  IMAP_SECURE: boolean;
  IMAP_USER: string;
  IMAP_PASS: string;

  // 文件上传配置
  MAX_FILE_SIZE: number;
  UPLOAD_PATH: string;

  // 安全配置
  BCRYPT_ROUNDS: number;
  RATE_LIMIT_WINDOW: number;
  RATE_LIMIT_MAX: number;

  // CORS配置
  FRONTEND_URL: string;
  ALLOWED_ORIGINS: string[];

  // 日志配置
  LOG_LEVEL: string;
  LOG_FILE: string;

  // 邮件域名配置
  MAIL_DOMAIN: string;
  MAIL_SERVER_NAME: string;

  // 邮件同步配置
  AUTO_START_EMAIL_SYNC: boolean;
  EMAIL_SYNC_MODE: 'full' | 'ondemand';
}

const config: Config = {
  NODE_ENV: process.env['NODE_ENV'] || 'development',
  PORT: parseInt(process.env['PORT'] || '3001', 10),
  APP_NAME: process.env['APP_NAME'] || '邮箱系统',

  DATABASE_URL: process.env['DATABASE_URL'] || '',
  REDIS_URL: process.env['REDIS_URL'] || 'redis://localhost:6379',

  JWT_SECRET: process.env['JWT_SECRET'] || 'your-super-secret-jwt-key',
  JWT_EXPIRES_IN: process.env['JWT_EXPIRES_IN'] || '7d',
  JWT_REFRESH_EXPIRES_IN: process.env['JWT_REFRESH_EXPIRES_IN'] || '30d',

  SMTP_HOST: process.env['SMTP_HOST'] || 'mail.blindedby.love',
  SMTP_PORT: parseInt(process.env['SMTP_PORT'] || '587', 10),
  SMTP_SECURE: process.env['SMTP_SECURE'] === 'true',
  SMTP_USER: process.env['SMTP_USER'] || '<EMAIL>',
  SMTP_PASS: process.env['SMTP_PASS'] || 'HOUsc@0202',

  IMAP_HOST: process.env['IMAP_HOST'] || 'mail.blindedby.love',
  IMAP_PORT: parseInt(process.env['IMAP_PORT'] || '993', 10),
  IMAP_SECURE: process.env['IMAP_SECURE'] !== 'false',
  IMAP_USER: process.env['IMAP_USER'] || '<EMAIL>',
  IMAP_PASS: process.env['IMAP_PASS'] || 'HOUsc@0202',

  MAX_FILE_SIZE: parseInt(process.env['MAX_FILE_SIZE'] || '10485760', 10),
  UPLOAD_PATH: process.env['UPLOAD_PATH'] || './uploads',

  BCRYPT_ROUNDS: parseInt(process.env['BCRYPT_ROUNDS'] || '12', 10),
  RATE_LIMIT_WINDOW: parseInt(process.env['RATE_LIMIT_WINDOW'] || '15', 10),
  RATE_LIMIT_MAX: parseInt(process.env['RATE_LIMIT_MAX'] || '100', 10),

  FRONTEND_URL: process.env['FRONTEND_URL'] || 'http://localhost:5173',
  ALLOWED_ORIGINS: process.env['ALLOWED_ORIGINS']
    ? process.env['ALLOWED_ORIGINS'].split(',').map(origin => origin.trim())
    : [
        process.env['FRONTEND_URL'] || 'http://localhost:5173',
        'http://localhost:3000',
        'http://localhost:3002',  // 添加新的前端端口
        'http://localhost:5173',
        'http://127.0.0.1:5173',
        'http://127.0.0.1:3000',
        'http://127.0.0.1:3002',  // 添加新的前端端口
        'https://mail.blindedby.love',  // 生产环境域名
        'https://blindedby.love',       // 主域名
      ],

  LOG_LEVEL: process.env['LOG_LEVEL'] || 'info',
  LOG_FILE: process.env['LOG_FILE'] || './logs/app.log',

  MAIL_DOMAIN: process.env['MAIL_DOMAIN'] || 'blindedby.love',
  MAIL_SERVER_NAME: process.env['MAIL_SERVER_NAME'] || 'mail.blindedby.love',

  // 邮件同步配置
  AUTO_START_EMAIL_SYNC: process.env['AUTO_START_EMAIL_SYNC'] === 'true',
  EMAIL_SYNC_MODE: (process.env['EMAIL_SYNC_MODE'] as 'full' | 'ondemand') || 'ondemand',
};

// 验证必需的环境变量
const requiredEnvVars = ['DATABASE_URL', 'JWT_SECRET'];
for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

export default config;
