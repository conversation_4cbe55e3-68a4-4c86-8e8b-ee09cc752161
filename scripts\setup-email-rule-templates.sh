#!/bin/bash

# 设置邮件规则模板系统

set -e

echo "=== 设置邮件规则模板系统 ==="

# 1. 运行数据库迁移
echo "1. 运行数据库迁移..."
if [ -f "database-migration-system-rules.sql" ]; then
    # 假设使用MySQL
    mysql -u root -p mailserver < database-migration-system-rules.sql
    echo "  ✅ 数据库迁移完成"
else
    echo "  ❌ 找不到数据库迁移文件"
    exit 1
fi

# 2. 重新生成Prisma客户端
echo "2. 重新生成Prisma客户端..."
cd backend
npm run prisma:generate
echo "  ✅ Prisma客户端已更新"

# 3. 安装依赖（如果需要）
echo "3. 检查依赖..."
npm install
echo "  ✅ 依赖检查完成"

# 4. 编译TypeScript
echo "4. 编译TypeScript..."
npm run build
echo "  ✅ 编译完成"

# 5. 重启后端服务
echo "5. 重启后端服务..."
if pm2 list | grep -q "email-backend"; then
    pm2 restart email-backend
    echo "  ✅ 后端服务已重启"
else
    echo "  ⚠️  请手动重启后端服务"
fi

# 6. 编译前端
echo "6. 编译前端..."
cd ../frontend
npm install
npm run build
echo "  ✅ 前端编译完成"

# 7. 初始化规则模板
echo "7. 初始化规则模板..."
echo "请在后端启动后，调用以下API来初始化模板："
echo "POST /api/email-rule-templates/admin/initialize"
echo ""
echo "或者在管理员界面中点击'初始化模板'按钮"

echo -e "\n=== 设置完成 ==="
echo ""
echo "🎯 下一步操作："
echo "1. 确保后端服务正常运行"
echo "2. 登录管理员账户"
echo "3. 访问邮件规则页面"
echo "4. 点击'启用发件副本自动移动'按钮"
echo ""
echo "📋 新增功能："
echo "✅ 规则模板系统"
echo "✅ 发件副本自动移动模板"
echo "✅ 一键创建系统规则"
echo "✅ 自定义规则创建"
echo "✅ 模板分类管理"
echo ""
echo "🔧 API端点："
echo "- GET /api/email-rule-templates - 获取所有模板"
echo "- GET /api/email-rule-templates/category/:category - 按分类获取模板"
echo "- POST /api/email-rule-templates/create-rule - 从模板创建规则"
echo "- POST /api/email-rule-templates/create-sent-copy-rule - 创建发件副本规则"
echo "- POST /api/email-rule-templates/admin/create-system-rules - 为所有用户创建系统规则"
echo "- POST /api/email-rule-templates/admin/initialize - 初始化模板"
