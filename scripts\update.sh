#!/bin/bash

# 邮箱系统更新脚本
# 使用方法: ./scripts/update.sh [environment]
# environment: dev (默认) | prod

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Git状态
check_git_status() {
    log_info "检查Git状态..."
    
    if [ ! -d ".git" ]; then
        log_error "当前目录不是Git仓库"
        exit 1
    fi
    
    # 检查是否有未提交的更改
    if ! git diff-index --quiet HEAD --; then
        log_warning "存在未提交的更改"
        read -p "是否继续更新? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "更新已取消"
            exit 0
        fi
    fi
    
    log_success "Git状态检查完成"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."
    
    local current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"
    
    git fetch origin
    git pull origin "$current_branch"
    
    log_success "代码更新完成"
}

# 备份当前版本
backup_current_version() {
    log_info "备份当前版本..."
    
    # 执行备份脚本
    if [ -f "scripts/backup.sh" ]; then
        ./scripts/backup.sh full
    else
        log_warning "备份脚本不存在，跳过备份"
    fi
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml down
    else
        docker-compose down
    fi
    
    log_success "服务已停止"
}

# 构建新镜像
build_new_images() {
    log_info "构建新镜像..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml build --no-cache
    else
        docker-compose build --no-cache
    fi
    
    log_success "镜像构建完成"
}

# 数据库迁移
migrate_database() {
    log_info "执行数据库迁移..."
    
    # 临时启动数据库服务
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml up -d database redis
    else
        docker-compose up -d database redis
    fi
    
    # 等待数据库启动
    sleep 15
    
    # 执行迁移
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml run --rm backend npx prisma migrate deploy
    else
        docker-compose run --rm backend npx prisma migrate dev
    fi
    
    log_success "数据库迁移完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml up -d
    else
        docker-compose up -d
    fi
    
    log_success "服务启动完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health &> /dev/null; then
            log_success "健康检查通过"
            return 0
        fi
        
        log_info "等待服务启动... ($attempt/$max_attempts)"
        sleep 10
        ((attempt++))
    done
    
    log_error "健康检查失败"
    return 1
}

# 回滚
rollback() {
    log_error "更新失败，开始回滚..."
    
    # 停止当前服务
    stop_services
    
    # 回滚到上一个提交
    git reset --hard HEAD~1
    
    # 重新构建和启动
    build_new_images
    start_services
    
    if health_check; then
        log_success "回滚完成"
    else
        log_error "回滚也失败了，请手动检查"
    fi
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧镜像..."
    
    # 删除未使用的镜像
    docker image prune -f
    
    log_success "镜像清理完成"
}

# 显示更新信息
show_update_info() {
    log_info "更新信息:"
    
    # 显示Git日志
    echo "最近的提交:"
    git log --oneline -5
    
    echo
    
    # 显示服务状态
    if [ "$ENVIRONMENT" = "prod" ]; then
        docker-compose -f docker-compose.prod.yml ps
    else
        docker-compose ps
    fi
}

# 发送通知
send_notification() {
    local status=$1
    local message=$2
    
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"邮箱系统更新 - $status: $message\"}" \
            2>/dev/null || true
    fi
}

# 主函数
main() {
    local environment=${1:-dev}
    
    log_info "开始更新邮箱系统 (环境: $environment)"
    
    export ENVIRONMENT=$environment
    
    check_git_status
    backup_current_version
    pull_latest_code
    stop_services
    build_new_images
    migrate_database
    start_services
    
    if health_check; then
        cleanup_old_images
        show_update_info
        send_notification "成功" "系统更新完成"
        log_success "更新完成!"
    else
        send_notification "失败" "系统更新失败，正在回滚"
        rollback
        exit 1
    fi
}

# 脚本入口
main "$@"
