import { Router } from 'express';
import multer from 'multer';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate } from '../middleware/auth';
import * as userController from '../controllers/userController';

// 配置文件上传
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
});

const router = Router();

// 临时头像上传（注册时使用，不需要认证）
router.post('/avatar/temp',
  upload.single('avatar'),
  asyncHandler(userController.uploadTempAvatar)
);

// 需要认证的路由
router.use(authenticate);

// 上传头像（已登录用户）
router.post('/avatar',
  upload.single('avatar'),
  asyncHandler(userController.uploadAvatar)
);

export default router;
