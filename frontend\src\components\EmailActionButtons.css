/* 邮件操作按钮样式 */
.email-action-buttons {
  padding: 16px;
  border-top: 1px solid #f0f0f0;
  background-color: #fafafa;
}

.email-action-buttons.list-mode {
  margin-top: 16px;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.email-action-buttons.split-mode {
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  background-color: #ffffff;
  padding: 12px 16px;
}

/* 邮件操作按钮容器 */
.email-actions-container {
  margin-top: 16px;
}

/* 详情页面操作按钮 */
.detail-actions {
  margin-top: 12px;
}

/* 按钮样式调整 */
.email-action-buttons .ant-btn {
  height: 32px;
  padding: 4px 12px;
  font-size: 14px;
}

.email-action-buttons .ant-btn:hover {
  background-color: #f5f5f5;
}

.email-action-buttons .ant-btn[disabled] {
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .email-action-buttons {
    padding: 12px;
  }
  
  .email-action-buttons .ant-space {
    flex-wrap: wrap;
  }
  
  .email-action-buttons .ant-btn {
    margin-bottom: 8px;
  }
}
