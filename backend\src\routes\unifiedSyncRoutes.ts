import express from 'express';
import { authenticate } from '../middleware/auth';
import logger from '../utils/logger';

const router = express.Router();

/**
 * 获取统一同步服务状态
 */
router.get('/status', authenticate, async (req, res) => {
  try {
    // 动态导入避免循环依赖
    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');
    const status = unifiedEmailSyncService.getStatus();

    res.json({
      success: true,
      data: {
        ...status,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('获取同步状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取同步状态失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 启动所有用户的同步服务
 */
router.post('/start-all', authenticate, async (req, res) => {
  try {
    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');
    await unifiedEmailSyncService.startAllUserSync();

    res.json({
      success: true,
      message: '所有用户的同步服务已启动'
    });
  } catch (error) {
    logger.error('启动所有同步服务失败:', error);
    res.status(500).json({
      success: false,
      message: '启动同步服务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 停止所有用户的同步服务
 */
router.post('/stop-all', authenticate, async (req, res) => {
  try {
    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');
    await unifiedEmailSyncService.stopAllUserSync();

    res.json({
      success: true,
      message: '所有用户的同步服务已停止'
    });
  } catch (error) {
    logger.error('停止所有同步服务失败:', error);
    res.status(500).json({
      success: false,
      message: '停止同步服务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 启动特定用户的同步服务
 */
router.post('/start-user', authenticate, async (req, res) => {
  try {
    const { userEmail } = req.body;
    
    if (!userEmail) {
      return res.status(400).json({
        success: false,
        message: '缺少用户邮箱参数'
      });
    }

    // 获取用户信息
    const prisma = (await import('../config/database')).default;
    const user = await prisma.user.findUnique({
      where: { email: userEmail },
      select: { id: true }
    });

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');
    await unifiedEmailSyncService.startUserSync(userEmail, user.id);
    
    res.json({
      success: true,
      message: `用户 ${userEmail} 的同步服务已启动`
    });
  } catch (error) {
    logger.error('启动用户同步服务失败:', error);
    res.status(500).json({
      success: false,
      message: '启动用户同步服务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 停止特定用户的同步服务
 */
router.post('/stop-user', authenticate, async (req, res) => {
  try {
    const { userEmail } = req.body;
    
    if (!userEmail) {
      return res.status(400).json({
        success: false,
        message: '缺少用户邮箱参数'
      });
    }

    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');
    await unifiedEmailSyncService.stopUserSync(userEmail);
    
    res.json({
      success: true,
      message: `用户 ${userEmail} 的同步服务已停止`
    });
  } catch (error) {
    logger.error('停止用户同步服务失败:', error);
    res.status(500).json({
      success: false,
      message: '停止用户同步服务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 重启特定用户的同步服务
 */
router.post('/restart-user', authenticate, async (req, res) => {
  try {
    const { userEmail } = req.body;
    
    if (!userEmail) {
      return res.status(400).json({
        success: false,
        message: '缺少用户邮箱参数'
      });
    }

    const { syncServiceMigration } = await import('../services/syncServiceMigration');
    await syncServiceMigration.restartUserSync(userEmail);
    
    res.json({
      success: true,
      message: `用户 ${userEmail} 的同步服务已重启`
    });
  } catch (error) {
    logger.error('重启用户同步服务失败:', error);
    res.status(500).json({
      success: false,
      message: '重启用户同步服务失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 执行同步服务迁移
 */
router.post('/migrate', authenticate, async (req, res) => {
  try {
    const { syncServiceMigration } = await import('../services/syncServiceMigration');
    await syncServiceMigration.migrate();
    
    res.json({
      success: true,
      message: '同步服务迁移完成'
    });
  } catch (error) {
    logger.error('同步服务迁移失败:', error);
    res.status(500).json({
      success: false,
      message: '同步服务迁移失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 健康检查
 */
router.get('/health', authenticate, async (req, res) => {
  try {
    const { syncServiceMigration } = await import('../services/syncServiceMigration');
    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');

    const isHealthy = await syncServiceMigration.healthCheck();

    res.json({
      success: true,
      data: {
        healthy: isHealthy,
        status: unifiedEmailSyncService.getStatus(),
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('健康检查失败:', error);
    res.status(500).json({
      success: false,
      message: '健康检查失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

/**
 * 获取当前用户的同步状态
 */
router.get('/my-status', authenticate, async (req, res) => {
  try {
    const userEmail = req.user?.email;
    
    if (!userEmail) {
      return res.status(400).json({
        success: false,
        message: '无法获取用户信息'
      });
    }

    const { unifiedEmailSyncService } = await import('../services/unifiedEmailSyncService');
    const status = unifiedEmailSyncService.getStatus();
    const userStatus = status.users.find((user: any) => user.userEmail === userEmail);
    
    res.json({
      success: true,
      data: {
        userEmail,
        isActive: userStatus?.isActive || false,
        lastActivity: userStatus?.lastActivity || null,
        retryCount: userStatus?.retryCount || 0,
        imapState: userStatus?.imapState || 'unknown',
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('获取用户同步状态失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户同步状态失败',
      error: error instanceof Error ? error.message : '未知错误'
    });
  }
});

export default router;
