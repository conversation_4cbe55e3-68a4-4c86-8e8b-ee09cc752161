#!/usr/bin/env node

/**
 * 系统监控功能测试脚本
 * 测试系统监控API的各项功能
 */

const { PrismaClient } = require('@prisma/client');

// 尝试导入系统监控控制器
let systemMonitoringController;
try {
  systemMonitoringController = require('../dist/controllers/systemMonitoringController');
} catch (error) {
  console.error('无法加载系统监控控制器，请先编译项目：npm run build');
  process.exit(1);
}

const prisma = new PrismaClient();

// 模拟请求和响应对象
function createMockReqRes(user, query = {}) {
  const req = {
    user,
    query
  };

  let responseData = null;
  let statusCode = 200;
  
  const res = {
    json: (data) => {
      responseData = data;
      return res;
    },
    status: (code) => {
      statusCode = code;
      return res;
    },
    getResponseData: () => responseData,
    getStatusCode: () => statusCode
  };

  return { req, res };
}

// 测试获取系统概览
async function testGetSystemOverview() {
  console.log('\n🧪 测试获取系统概览...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    if (!adminUser) {
      console.log('❌ 没有找到管理员用户');
      return false;
    }

    console.log(`✓ 使用管理员用户: ${adminUser.email}`);

    // 创建模拟请求
    const { req, res } = createMockReqRes(adminUser);

    // 调用API
    await systemMonitoringController.getSystemOverview(req, res);

    // 获取响应数据
    const responseData = res.getResponseData();
    
    if (!responseData || !responseData.success) {
      console.log('❌ API调用失败');
      return false;
    }

    console.log('✅ 获取系统概览成功');
    console.log(`📊 系统信息:`);
    console.log(`  - 主机名: ${responseData.data.systemInfo.hostname}`);
    console.log(`  - 平台: ${responseData.data.systemInfo.platform}`);
    console.log(`  - Node版本: ${responseData.data.systemInfo.nodeVersion}`);
    console.log(`  - CPU核心: ${responseData.data.systemInfo.cpuCount}`);
    console.log(`📈 统计信息:`);
    console.log(`  - 总用户数: ${responseData.data.statistics.totalUsers}`);
    console.log(`  - 活跃用户: ${responseData.data.statistics.activeUsers}`);
    console.log(`  - 总邮件数: ${responseData.data.statistics.totalEmails}`);
    console.log(`  - 今日邮件: ${responseData.data.statistics.todayEmails}`);

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试获取邮件服务器状态
async function testGetMailServerStatus() {
  console.log('\n🧪 测试获取邮件服务器状态...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    console.log(`✓ 使用管理员用户: ${adminUser.email}`);

    // 创建模拟请求
    const { req, res } = createMockReqRes(adminUser);

    // 调用API
    await systemMonitoringController.getMailServerStatus(req, res);

    // 获取响应数据
    const responseData = res.getResponseData();
    
    if (!responseData || !responseData.success) {
      console.log('❌ API调用失败');
      return false;
    }

    console.log('✅ 获取邮件服务器状态成功');
    console.log(`📧 服务状态:`);
    console.log(`  - Postfix: ${responseData.data.services.postfix.status} - ${responseData.data.services.postfix.message}`);
    console.log(`  - Dovecot: ${responseData.data.services.dovecot.status} - ${responseData.data.services.dovecot.message}`);
    
    if (responseData.data.diskUsage.error) {
      console.log(`💾 磁盘使用: ${responseData.data.diskUsage.error}`);
    } else {
      console.log(`💾 磁盘使用: ${responseData.data.diskUsage.used}/${responseData.data.diskUsage.size} (${responseData.data.diskUsage.usePercentage})`);
    }
    
    if (responseData.data.mailQueue.error) {
      console.log(`📮 邮件队列: ${responseData.data.mailQueue.error}`);
    } else {
      console.log(`📮 邮件队列: ${responseData.data.mailQueue.message}`);
    }

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试获取邮件统计
async function testGetEmailStatistics() {
  console.log('\n🧪 测试获取邮件统计...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    console.log(`✓ 使用管理员用户: ${adminUser.email}`);

    // 测试不同的统计周期
    const periods = ['24h', '7d', '30d'];
    
    for (const period of periods) {
      console.log(`\n  📊 测试 ${period} 统计...`);
      
      // 创建模拟请求
      const { req, res } = createMockReqRes(adminUser, { period });

      // 调用API
      await systemMonitoringController.getEmailStatistics(req, res);

      // 获取响应数据
      const responseData = res.getResponseData();
      
      if (!responseData || !responseData.success) {
        console.log(`  ❌ ${period} 统计获取失败`);
        continue;
      }

      console.log(`  ✅ ${period} 统计获取成功`);
      console.log(`    - 总邮件: ${responseData.data.summary.totalEmails}`);
      console.log(`    - 发送: ${responseData.data.summary.sentEmails}`);
      console.log(`    - 接收: ${responseData.data.summary.receivedEmails}`);
      console.log(`    - 未读: ${responseData.data.summary.unreadEmails}`);
      console.log(`    - 时间线数据点: ${responseData.data.timeline.length}`);
    }

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试获取系统日志
async function testGetSystemLogs() {
  console.log('\n🧪 测试获取系统日志...');
  
  try {
    // 获取管理员用户
    const adminUser = await prisma.user.findFirst({
      where: { role: 'admin' },
      select: { id: true, email: true, role: true }
    });

    console.log(`✓ 使用管理员用户: ${adminUser.email}`);

    // 测试不同类型的日志
    const logTypes = ['app', 'mail', 'security'];
    
    for (const type of logTypes) {
      console.log(`\n  📝 测试 ${type} 日志...`);
      
      // 创建模拟请求
      const { req, res } = createMockReqRes(adminUser, { 
        type, 
        level: 'all', 
        limit: 10 
      });

      // 调用API
      await systemMonitoringController.getSystemLogs(req, res);

      // 获取响应数据
      const responseData = res.getResponseData();
      
      if (!responseData || !responseData.success) {
        console.log(`  ❌ ${type} 日志获取失败`);
        continue;
      }

      console.log(`  ✅ ${type} 日志获取成功`);
      console.log(`    - 日志条数: ${responseData.data.logs.length}`);
      console.log(`    - 日志类型: ${responseData.data.type}`);
      console.log(`    - 日志级别: ${responseData.data.level}`);
    }

    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 测试权限检查
async function testPermissionCheck() {
  console.log('\n🧪 测试权限检查...');
  
  try {
    // 获取普通用户
    const normalUser = await prisma.user.findFirst({
      where: { role: 'user' },
      select: { id: true, email: true, role: true }
    });

    if (!normalUser) {
      console.log('❌ 没有找到普通用户');
      return false;
    }

    console.log(`✓ 使用普通用户: ${normalUser.email}`);

    // 创建模拟请求 - 普通用户尝试获取系统概览
    const { req, res } = createMockReqRes(normalUser);

    try {
      await systemMonitoringController.getSystemOverview(req, res);
      console.log('❌ 权限检查失败：普通用户不应该能访问系统监控功能');
      return false;
    } catch (error) {
      if (error.message.includes('权限不足')) {
        console.log('✅ 权限检查正常：普通用户被正确拒绝');
        return true;
      } else {
        console.log('❌ 权限检查异常:', error.message);
        return false;
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    return false;
  }
}

// 检查数据库连接和基础数据
async function checkDatabaseConnection() {
  console.log('\n🔍 检查数据库连接和基础数据...');
  
  try {
    // 检查数据库连接
    await prisma.$connect();
    console.log('✓ 数据库连接正常');

    // 检查基础数据
    const [userCount, emailCount] = await Promise.all([
      prisma.user.count(),
      prisma.email.count()
    ]);

    console.log(`✓ 数据库中有 ${userCount} 个用户`);
    console.log(`✓ 数据库中有 ${emailCount} 封邮件`);

    return userCount > 0;

  } catch (error) {
    console.error('❌ 数据库检查失败:', error.message);
    return false;
  }
}

// 主函数
async function main() {
  console.log('🚀 系统监控功能测试');
  console.log('================================\n');

  try {
    // 检查数据库连接
    const dbOk = await checkDatabaseConnection();
    if (!dbOk) {
      console.log('\n❌ 数据库检查失败');
      return;
    }

    // 测试获取系统概览
    const overviewOk = await testGetSystemOverview();

    // 测试获取邮件服务器状态
    const mailServerOk = await testGetMailServerStatus();

    // 测试获取邮件统计
    const emailStatsOk = await testGetEmailStatistics();

    // 测试获取系统日志
    const systemLogsOk = await testGetSystemLogs();

    // 测试权限检查
    const permissionCheckOk = await testPermissionCheck();

    // 总结
    console.log('\n📊 测试结果总结:');
    console.log('================================');
    console.log(`数据库连接: ${dbOk ? '✅' : '❌'}`);
    console.log(`系统概览: ${overviewOk ? '✅' : '❌'}`);
    console.log(`邮件服务器状态: ${mailServerOk ? '✅' : '❌'}`);
    console.log(`邮件统计: ${emailStatsOk ? '✅' : '❌'}`);
    console.log(`系统日志: ${systemLogsOk ? '✅' : '❌'}`);
    console.log(`权限检查: ${permissionCheckOk ? '✅' : '❌'}`);

    if (overviewOk && mailServerOk && emailStatsOk && systemLogsOk && permissionCheckOk) {
      console.log('\n🎉 所有测试通过！系统监控功能正常工作。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查配置和日志。');
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 处理程序退出
process.on('SIGINT', async () => {
  console.log('\n\n操作已取消。');
  await prisma.$disconnect();
  process.exit(0);
});

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
