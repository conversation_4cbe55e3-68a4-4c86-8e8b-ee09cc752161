import { Router } from 'express';
import multer from 'multer';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate } from '../middleware/auth';
import { validatePagination } from '../middleware/validation';
import * as userController from '../controllers/userController';

// 配置文件上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB
  },
});

const router = Router();

// 所有路由都需要认证
router.use(authenticate);

// 获取用户列表
router.get('/',
  validatePagination,
  asyncHandler(userController.getUsers)
);

// 获取用户详情
router.get('/:id',
  asyncHandler(userController.getUserById)
);

// 更新用户信息
router.put('/:id',
  asyncHandler(userController.updateUser)
);

// 上传头像
router.post('/avatar',
  upload.single('avatar'),
  async<PERSON><PERSON><PERSON>(userController.uploadAvatar)
);

export default router;
