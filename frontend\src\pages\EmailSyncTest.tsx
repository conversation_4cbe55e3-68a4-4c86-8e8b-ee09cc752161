import React, { useState } from 'react';
import { Button, Input, Card, Space, Typography, message, Divider } from 'antd';
import api from '../config/api';

const { Title, Text, Paragraph } = Typography;

interface Email {
  id: string;
  messageId: string;
  subject: string;
  isRead: boolean;
  senderEmail: string;
}

const EmailSyncTest: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [emailId, setEmailId] = useState('');
  const [emails, setEmails] = useState<Email[]>([]);
  const [result, setResult] = useState<any>(null);
  const [manualMessageId, setManualMessageId] = useState('');

  const showResult = (data: any, type: 'success' | 'error' = 'success') => {
    setResult({ data, type });
  };

  // 测试IMAP权限
  const testPermissions = async () => {
    setLoading(true);
    try {
      const response = await api.post('/emails/test-permissions');
      showResult(response.data, response.data.success ? 'success' : 'error');
      if (response.data.success) {
        message.success('IMAP权限测试成功');
      } else {
        message.error('IMAP权限测试失败');
      }
    } catch (error: any) {
      console.error('权限测试失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error('权限测试失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取邮件ID列表（使用现有的邮件API）
  const getEmailIds = async () => {
    setLoading(true);
    try {
      // 使用现有的邮件列表API
      const response = await api.get('/emails?folderType=inbox&page=1&pageSize=10');
      if (response.data.success) {
        const emailData = response.data.data.emails || [];
        const formattedEmails = emailData.map((email: any) => ({
          id: email.id,
          messageId: email.messageId,
          subject: email.subject || '无主题',
          isRead: email.isRead,
          senderEmail: email.senderEmail || '未知发件人'
        }));
        setEmails(formattedEmails);
        message.success(`获取到 ${formattedEmails.length} 封邮件`);
      } else {
        message.error('获取邮件列表失败');
      }
    } catch (error: any) {
      console.error('获取邮件列表失败:', error);
      message.error(`获取邮件列表失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 测试邮件同步（使用现有的邮件更新API）
  const testSimpleSync = async (testEmailId?: string) => {
    const targetEmailId = testEmailId || emailId;
    if (!targetEmailId) {
      message.error('请输入邮件ID');
      return;
    }

    setLoading(true);
    try {
      // 先获取邮件当前状态
      const currentEmail = emails.find(e => e.id === targetEmailId);
      const currentIsRead = currentEmail?.isRead || false;

      message.info(`开始测试邮件同步: ${currentIsRead ? '标记为未读' : '标记为已读'}`);

      // 使用现有的邮件更新API来触发IMAP同步
      const response = await api.put(`/emails/${targetEmailId}`, {
        isRead: !currentIsRead // 切换已读状态
      });

      if (response.data.success) {
        showResult({
          message: '邮件状态更新成功，IMAP同步已在后台进行',
          emailId: targetEmailId,
          previousState: currentIsRead ? '已读' : '未读',
          newState: !currentIsRead ? '已读' : '未读',
          note: '请查看后端日志了解IMAP同步详情'
        }, 'success');
        message.success('邮件状态更新成功，请查看后端日志');

        // 刷新邮件列表
        setTimeout(() => {
          getEmailIds();
        }, 1000);
      } else {
        showResult(response.data, 'error');
        message.error('邮件状态更新失败');
      }
    } catch (error: any) {
      console.error('邮件同步测试失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`邮件同步测试失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 重置所有邮件为未读
  const resetToUnread = async () => {
    setLoading(true);
    try {
      const response = await api.post('/emails/reset-to-unread');
      if (response.data.success) {
        showResult(response.data, 'success');
        message.success(response.data.message);
        // 刷新邮件列表
        getEmailIds();
      } else {
        showResult(response.data, 'error');
        message.error('重置失败');
      }
    } catch (error: any) {
      console.error('重置失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error('重置失败');
    } finally {
      setLoading(false);
    }
  };

  // 检查MessageId格式（使用现有邮件数据）
  const checkMessageIds = () => {
    if (emails.length === 0) {
      message.warning('请先获取邮件列表');
      return;
    }

    const messageIdInfo = emails.map(email => ({
      id: email.id,
      messageId: email.messageId,
      messageIdLength: email.messageId?.length || 0,
      subject: email.subject?.substring(0, 50) + '...',
      isRead: email.isRead,
      senderEmail: email.senderEmail,
    }));

    showResult({
      message: `检查了 ${emails.length} 封邮件的MessageId`,
      data: messageIdInfo,
      summary: {
        totalEmails: emails.length,
        readEmails: emails.filter(e => e.isRead).length,
        unreadEmails: emails.filter(e => !e.isRead).length,
        averageMessageIdLength: Math.round(
          emails.reduce((sum, e) => sum + (e.messageId?.length || 0), 0) / emails.length
        ),
      }
    }, 'success');

    message.success('MessageId检查完成，请查看结果');
  };

  // 检查邮件文件系统（简化版本）
  const checkEmailFiles = () => {
    if (emails.length === 0) {
      message.warning('请先获取邮件列表');
      return;
    }

    // 显示当前邮件状态统计
    const readEmails = emails.filter(e => e.isRead);
    const unreadEmails = emails.filter(e => !e.isRead);

    showResult({
      message: '邮件状态统计（基于数据库）',
      data: {
        totalEmails: emails.length,
        readEmails: readEmails.length,
        unreadEmails: unreadEmails.length,
        readEmailsList: readEmails.map(e => ({
          id: e.id,
          subject: e.subject?.substring(0, 30) + '...',
          messageId: e.messageId
        })),
        unreadEmailsList: unreadEmails.map(e => ({
          id: e.id,
          subject: e.subject?.substring(0, 30) + '...',
          messageId: e.messageId
        })),
        note: '这是数据库中的状态。如果IMAP同步正常工作，服务器上的邮件文件应该有对应的:2,S标志。'
      }
    }, 'success');

    message.success('邮件状态统计完成');
  };

  // 强制IMAP同步
  const forceImapSync = async (testEmailId?: string) => {
    const targetEmailId = testEmailId || emailId;
    if (!targetEmailId) {
      message.error('请输入邮件ID');
      return;
    }

    setLoading(true);
    try {
      message.info('开始强制IMAP同步，请查看后端日志');

      const response = await api.post('/emails/debug/force-sync', {
        emailId: targetEmailId
      });

      if (response.data.success) {
        showResult({
          message: '强制IMAP同步已启动',
          emailId: targetEmailId,
          data: response.data.data,
          note: '请查看后端日志了解详细的同步过程'
        }, 'success');
        message.success('强制IMAP同步已启动，请查看后端日志');

        // 延迟刷新邮件列表和文件系统检查
        setTimeout(() => {
          getEmailIds();
          checkEmailFiles();
        }, 2000);
      } else {
        showResult(response.data, 'error');
        message.error('强制IMAP同步失败');
      }
    } catch (error: any) {
      console.error('强制IMAP同步失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`强制IMAP同步失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 直接IMAP测试
  const directImapTest = async () => {
    setLoading(true);
    try {
      message.info('开始直接IMAP测试，请查看后端日志');

      const response = await api.post('/emails/debug/direct-imap');

      if (response.data.success) {
        showResult({
          message: '直接IMAP测试已启动',
          note: '这将直接测试IMAP连接、邮箱打开、邮件搜索和标志操作，请查看后端日志了解详细过程'
        }, 'success');
        message.success('直接IMAP测试已启动，请查看后端日志');
      } else {
        showResult(response.data, 'error');
        message.error('直接IMAP测试失败');
      }
    } catch (error: any) {
      console.error('直接IMAP测试失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`直接IMAP测试失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 简化IMAP测试
  const simpleImapTest = async () => {
    setLoading(true);
    try {
      message.info('开始简化IMAP测试，请查看后端日志');

      const response = await api.post('/emails/debug/simple-imap');

      if (response.data.success) {
        showResult({
          message: '简化IMAP测试已启动',
          note: '这将测试IMAP连接状态、服务器能力和邮箱列表，不会尝试打开邮箱，请查看后端日志了解详细过程'
        }, 'success');
        message.success('简化IMAP测试已启动，请查看后端日志');
      } else {
        showResult(response.data, 'error');
        message.error('简化IMAP测试失败');
      }
    } catch (error: any) {
      console.error('简化IMAP测试失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`简化IMAP测试失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 文件系统标志测试
  const fileSystemFlagTest = async () => {
    setLoading(true);
    try {
      message.info('开始文件系统标志测试，请查看后端日志');

      const response = await api.post('/emails/debug/filesystem-flag');

      if (response.data.success) {
        showResult({
          message: '文件系统标志测试已启动',
          note: '这将直接操作邮件文件，绕过IMAP协议来设置已读标志，请查看后端日志了解详细过程'
        }, 'success');
        message.success('文件系统标志测试已启动，请查看后端日志');
      } else {
        showResult(response.data, 'error');
        message.error('文件系统标志测试失败');
      }
    } catch (error: any) {
      console.error('文件系统标志测试失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`文件系统标志测试失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 替代IMAP方法测试
  const alternativeImapTest = async () => {
    setLoading(true);
    try {
      message.info('开始替代IMAP方法测试，请查看后端日志');

      const response = await api.post('/emails/debug/alternative-imap');

      if (response.data.success) {
        showResult({
          message: '替代IMAP方法测试已启动',
          note: '这将尝试多种不同的IMAP标志操作方法，包括messageFlagsAdd、messageFlagsSet、store命令等，请查看后端日志了解哪种方法有效'
        }, 'success');
        message.success('替代IMAP方法测试已启动，请查看后端日志');
      } else {
        showResult(response.data, 'error');
        message.error('替代IMAP方法测试失败');
      }
    } catch (error: any) {
      console.error('替代IMAP方法测试失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`替代IMAP方法测试失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 手动IMAP同步
  const manualImapSync = async (action: 'read' | 'unread') => {
    if (!manualMessageId.trim()) {
      message.error('请输入邮件MessageId');
      return;
    }

    setLoading(true);
    try {
      message.info(`开始手动IMAP同步: ${action}，请查看后端日志`);

      const response = await api.post('/emails/debug/manual-sync', {
        messageId: manualMessageId.trim(),
        action
      });

      if (response.data.success) {
        showResult({
          message: `手动IMAP同步已启动: ${action}`,
          messageId: manualMessageId.trim(),
          note: '请查看后端日志了解详细过程'
        }, 'success');
        message.success(`手动IMAP同步已启动: ${action}`);
      } else {
        showResult(response.data, 'error');
        message.error('手动IMAP同步失败');
      }
    } catch (error: any) {
      console.error('手动IMAP同步失败:', error);
      showResult(error.response?.data || error.message, 'error');
      message.error(`手动IMAP同步失败: ${error.response?.data?.message || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      padding: '24px',
      maxWidth: '1200px',
      margin: '0 auto',
      minHeight: '100vh',
      height: 'auto',
      overflow: 'visible'
    }}>
      <Title level={2}>邮件同步调试工具</Title>

      {/* 快速测试 */}
      <Card title="🚀 快速测试" size="small" style={{ marginBottom: '24px', backgroundColor: '#e6f7ff' }}>
        <Space wrap>
          <Button
            onClick={simpleImapTest}
            loading={loading}
            type="primary"
            size="large"
          >
            简化IMAP测试
          </Button>
          <Button
            onClick={directImapTest}
            loading={loading}
            size="large"
          >
            完整IMAP测试
          </Button>
          <Button
            onClick={checkEmailFiles}
            loading={loading}
            size="large"
          >
            检查文件系统
          </Button>
          <Button
            onClick={fileSystemFlagTest}
            loading={loading}
            size="large"
            style={{ backgroundColor: '#52c41a', borderColor: '#52c41a', color: 'white' }}
          >
            文件系统标志测试
          </Button>
          <Button
            onClick={alternativeImapTest}
            loading={loading}
            size="large"
            style={{ backgroundColor: '#722ed1', borderColor: '#722ed1', color: 'white' }}
          >
            替代IMAP方法
          </Button>
        </Space>

        <div style={{ marginTop: '16px' }}>
          <Text strong>手动IMAP同步测试：</Text>
          <div style={{ marginTop: '8px' }}>
            <Input.Group compact>
              <Input
                style={{ width: '300px' }}
                placeholder="输入邮件MessageId"
                value={manualMessageId}
                onChange={(e) => setManualMessageId(e.target.value)}
              />
              <Button
                onClick={() => manualImapSync('read')}
                loading={loading}
                type="primary"
              >
                标记已读
              </Button>
              <Button
                onClick={() => manualImapSync('unread')}
                loading={loading}
              >
                标记未读
              </Button>
            </Input.Group>
          </div>
          <div>
            <Text type="secondary">点击"直接IMAP测试"开始调试IMAP标志操作</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              手动检查命令: <Text code>ls -la /var/mail/vhosts/blindedby.love/qwe123/cur/ | grep ":2,S"</Text>
            </Text>
          </div>
        </div>
      </Card>

      <Space direction="vertical" size="large" style={{ width: '100%', display: 'flex' }}>
        {/* 1. 测试IMAP权限 */}
        <Card title="1. 测试IMAP权限" size="small">
          <Space>
            <Button 
              type="primary" 
              onClick={testPermissions}
              loading={loading}
            >
              测试IMAP权限
            </Button>
            <Text type="secondary">测试IMAP连接和邮件标志修改权限</Text>
          </Space>
        </Card>

        {/* 2. 获取邮件ID列表 */}
        <Card title="2. 获取邮件ID列表" size="small">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button 
              onClick={getEmailIds}
              loading={loading}
            >
              获取邮件ID
            </Button>
            
            {emails.length > 0 && (
              <div>
                <Text strong>邮件列表 ({emails.length} 封):</Text>
                <div style={{ maxHeight: '200px', overflowY: 'auto', marginTop: '8px' }}>
                  {emails.map((email) => (
                    <Card 
                      key={email.id} 
                      size="small" 
                      style={{ marginBottom: '8px' }}
                      extra={
                        <Space>
                          <Button
                            size="small"
                            type="link"
                            onClick={() => testSimpleSync(email.id)}
                          >
                            测试同步
                          </Button>
                          <Button
                            size="small"
                            type="link"
                            onClick={() => forceImapSync(email.id)}
                            style={{ color: '#ff4d4f' }}
                          >
                            强制同步
                          </Button>
                        </Space>
                      }
                    >
                      <Text strong>ID:</Text> {email.id}<br/>
                      <Text strong>主题:</Text> {email.subject}<br/>
                      <Text strong>发件人:</Text> {email.senderEmail}<br/>
                      <Text strong>状态:</Text> 
                      <Text type={email.isRead ? 'success' : 'warning'}>
                        {email.isRead ? '已读' : '未读'}
                      </Text><br/>
                      <Text strong>MessageId:</Text> 
                      <Text code style={{ fontSize: '12px' }}>{email.messageId}</Text>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </Space>
        </Card>

        {/* 3. 测试邮件同步 */}
        <Card title="3. 测试邮件同步" size="small">
          <Space>
            <Input
              placeholder="输入邮件ID"
              value={emailId}
              onChange={(e) => setEmailId(e.target.value)}
              style={{ width: '300px' }}
            />
            <Button
              type="primary"
              onClick={() => testSimpleSync()}
              loading={loading}
            >
              切换已读状态
            </Button>
          </Space>
          <Paragraph type="secondary" style={{ marginTop: '8px' }}>
            输入邮件ID进行测试，或使用上方邮件列表中的"测试同步"按钮。
            此操作会切换邮件的已读/未读状态，并触发IMAP同步。
          </Paragraph>
        </Card>

        {/* 4. 调试工具 */}
        <Card title="4. 调试工具" size="small" style={{ marginBottom: '24px' }}>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
              <Button
                onClick={checkMessageIds}
                loading={loading}
              >
                检查MessageId格式
              </Button>
              <Button
                onClick={checkEmailFiles}
                loading={loading}
              >
                检查文件系统
              </Button>
              <Button
                onClick={directImapTest}
                loading={loading}
                type="primary"
              >
                直接IMAP测试
              </Button>
              <Button
                danger
                onClick={resetToUnread}
                loading={loading}
              >
                重置为未读
              </Button>
            </div>
            <Paragraph type="secondary" style={{ margin: 0 }}>
              检查数据库中MessageId的格式、文件系统中的邮件标志状态，重置所有邮件为未读状态
            </Paragraph>
          </div>
        </Card>

        {/* 结果显示 */}
        {result && (
          <Card
            title="测试结果"
            size="small"
            style={{
              borderColor: result.type === 'success' ? '#52c41a' : '#ff4d4f',
              backgroundColor: result.type === 'success' ? '#f6ffed' : '#fff2f0',
              marginBottom: '24px'
            }}
          >
            <pre style={{
              whiteSpace: 'pre-wrap',
              wordBreak: 'break-word',
              fontSize: '12px',
              maxHeight: '400px',
              overflowY: 'auto',
              margin: 0
            }}>
              {JSON.stringify(result.data, null, 2)}
            </pre>
          </Card>
        )}

        <Divider />
        
        <Card title="使用说明" size="small">
          <Paragraph>
            <Text strong>调试步骤：</Text>
          </Paragraph>
          <ol>
            <li><Text strong>测试IMAP权限：</Text>确认IMAP连接和权限是否正常</li>
            <li><Text strong>获取邮件列表：</Text>查看当前用户的邮件ID和状态</li>
            <li><Text strong>测试邮件同步：</Text>选择一封邮件，切换其已读状态来触发IMAP同步</li>
            <li><Text strong>查看后端日志：</Text>观察详细的同步过程和错误信息</li>
            <li><Text strong>检查文件系统：</Text>验证邮件文件是否添加了 :2,S 标志
              <br />
              <Text code style={{ fontSize: '11px' }}>
                手动检查: ls -la /var/mail/vhosts/blindedby.love/qwe123/cur/ | grep ":2,S"
              </Text>
            </li>
            <li><Text strong>重置测试：</Text>使用重置功能将所有邮件标记为未读，重新开始测试</li>
          </ol>
          <Paragraph>
            <Text type="secondary">
              所有操作都会在后端日志中显示详细信息，请同时观察后端控制台输出。
            </Text>
          </Paragraph>
        </Card>
      </Space>
    </div>
  );
};

export default EmailSyncTest;
