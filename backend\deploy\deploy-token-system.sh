#!/bin/bash

# 邮件Token系统完整部署脚本
# 在服务器上执行，完成整个Token认证系统的部署

set -e

echo "🚀 开始部署邮件Token认证系统..."
echo "=================================================="

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
LOG_FILE="/tmp/token-deploy-$(date +%Y%m%d-%H%M%S).log"

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') $1" | tee -a "$LOG_FILE"
}

# 错误处理
handle_error() {
    log "❌ 部署失败，错误发生在第 $1 行"
    log "📋 查看完整日志: $LOG_FILE"
    exit 1
}

trap 'handle_error $LINENO' ERR

log "📋 部署日志: $LOG_FILE"

# 1. 环境检查
log "🔍 步骤1: 环境检查"

# 检查是否为root用户
if [[ $EUID -ne 0 ]]; then
   log "❌ 请使用root用户执行此脚本"
   exit 1
fi

# 检查必要的命令
REQUIRED_COMMANDS=("mysql" "node" "npm" "systemctl" "curl")
for cmd in "${REQUIRED_COMMANDS[@]}"; do
    if ! command -v "$cmd" &> /dev/null; then
        log "❌ 缺少必要命令: $cmd"
        exit 1
    fi
done

log "✅ 环境检查通过"

# 2. 数据库迁移
log "🗄️  步骤2: 数据库迁移"

if [ -f "$SCRIPT_DIR/01-database-migration.sql" ]; then
    log "执行数据库迁移..."
    mysql -u root -p mailserver < "$SCRIPT_DIR/01-database-migration.sql" >> "$LOG_FILE" 2>&1
    log "✅ 数据库迁移完成"
else
    log "❌ 数据库迁移文件不存在"
    exit 1
fi

# 3. 后端服务部署
log "🔧 步骤3: 后端服务部署"

if [ -f "$SCRIPT_DIR/03-backend-token-deploy.sh" ]; then
    log "执行后端部署..."
    bash "$SCRIPT_DIR/03-backend-token-deploy.sh" >> "$LOG_FILE" 2>&1
    log "✅ 后端服务部署完成"
else
    log "❌ 后端部署脚本不存在"
    exit 1
fi

# 4. Dovecot配置
log "📧 步骤4: Dovecot配置"

if [ -f "$SCRIPT_DIR/02-dovecot-token-setup.sh" ]; then
    log "配置Dovecot Token认证..."
    bash "$SCRIPT_DIR/02-dovecot-token-setup.sh" >> "$LOG_FILE" 2>&1
    log "✅ Dovecot配置完成"
else
    log "❌ Dovecot配置脚本不存在"
    exit 1
fi

# 5. 服务验证
log "🧪 步骤5: 服务验证"

# 等待服务启动
sleep 10

# 检查后端服务
if curl -s http://localhost:3000/api/health >/dev/null; then
    log "✅ 后端API服务正常"
else
    log "❌ 后端API服务异常"
    exit 1
fi

# 检查Dovecot服务
if systemctl is-active --quiet dovecot; then
    log "✅ Dovecot服务正常"
else
    log "❌ Dovecot服务异常"
    exit 1
fi

# 6. 创建管理脚本
log "🛠️  步骤6: 创建管理脚本"

cat > /usr/local/bin/mail-token-admin << 'EOF'
#!/bin/bash

# 邮件Token系统管理脚本

case "$1" in
    status)
        echo "=== 服务状态 ==="
        echo "后端服务:"
        pm2 list | grep email-system-backend || echo "未运行"
        echo ""
        echo "Dovecot服务:"
        systemctl status dovecot --no-pager -l
        ;;
    logs)
        echo "=== 服务日志 ==="
        echo "后端日志:"
        pm2 logs email-system-backend --lines 20
        echo ""
        echo "Dovecot Token日志:"
        tail -20 /var/log/dovecot-token-auth.log
        ;;
    restart)
        echo "=== 重启服务 ==="
        pm2 restart email-system-backend
        systemctl restart dovecot
        echo "服务重启完成"
        ;;
    test)
        echo "=== 测试Token认证 ==="
        cd /var/www/email-system/backend
        ./test-token-auth.sh
        ;;
    cleanup)
        echo "=== 清理过期Token ==="
        mysql -u root -p -e "CALL mailserver.CleanupExpiredMailTokens();"
        ;;
    *)
        echo "邮件Token系统管理工具"
        echo ""
        echo "用法: $0 {status|logs|restart|test|cleanup}"
        echo ""
        echo "命令说明:"
        echo "  status  - 查看服务状态"
        echo "  logs    - 查看服务日志"
        echo "  restart - 重启所有服务"
        echo "  test    - 测试Token认证"
        echo "  cleanup - 清理过期Token"
        exit 1
        ;;
esac
EOF

chmod +x /usr/local/bin/mail-token-admin
log "✅ 管理脚本已创建: /usr/local/bin/mail-token-admin"

# 7. 创建监控脚本
cat > /etc/cron.d/mail-token-monitor << 'EOF'
# 邮件Token系统监控

# 每5分钟检查服务状态
*/5 * * * * root /usr/local/bin/dovecot-token-health.sh

# 每小时清理过期Token
0 * * * * root mysql -u root -e "CALL mailserver.CleanupExpiredMailTokens();" >/dev/null 2>&1

# 每天备份Token使用日志
0 2 * * * root mysqldump -u root mailserver mail_token_usage_logs > /var/backups/mail-token-logs-$(date +\%Y\%m\%d).sql
EOF

log "✅ 监控任务已配置"

# 8. 生成部署报告
log "📋 步骤7: 生成部署报告"

REPORT_FILE="/root/token-deploy-report-$(date +%Y%m%d-%H%M%S).txt"

cat > "$REPORT_FILE" << EOF
邮件Token认证系统部署报告
========================================

部署时间: $(date)
部署版本: Token Auth v1.0
部署状态: 成功

组件状态:
- 数据库: ✅ 已迁移
- 后端API: ✅ 已部署
- Dovecot: ✅ 已配置
- 监控: ✅ 已启用

服务信息:
- 后端服务: http://localhost:3000
- Token API: http://localhost:3000/api/auth/verify-mail-token
- 管理工具: /usr/local/bin/mail-token-admin

重要文件:
- 配置文件: /etc/dovecot/conf.d/10-auth-token.conf
- 认证脚本: /usr/local/bin/dovecot-token-auth.sh
- 日志文件: /var/log/dovecot-token-auth.log
- 部署日志: $LOG_FILE

管理命令:
- 查看状态: mail-token-admin status
- 查看日志: mail-token-admin logs
- 重启服务: mail-token-admin restart
- 测试认证: mail-token-admin test
- 清理Token: mail-token-admin cleanup

下一步:
1. 测试Token认证: mail-token-admin test
2. 配置前端Token管理界面
3. 监控系统运行状态

注意事项:
- Token默认有效期: 24小时
- 自动清理间隔: 1小时
- 备份策略: 每日备份日志
EOF

log "✅ 部署报告已生成: $REPORT_FILE"

# 9. 最终测试
log "🎯 步骤8: 最终测试"

log "执行系统测试..."
if /usr/local/bin/mail-token-admin test >> "$LOG_FILE" 2>&1; then
    log "✅ 系统测试通过"
else
    log "⚠️  系统测试有警告，请检查日志"
fi

echo ""
echo "🎉 邮件Token认证系统部署完成！"
echo "=================================================="
echo ""
echo "📋 部署摘要:"
echo "  ✅ 数据库迁移完成"
echo "  ✅ 后端服务已启动"
echo "  ✅ Dovecot配置完成"
echo "  ✅ 监控任务已配置"
echo ""
echo "📁 重要文件:"
echo "  - 部署报告: $REPORT_FILE"
echo "  - 部署日志: $LOG_FILE"
echo "  - 管理工具: /usr/local/bin/mail-token-admin"
echo ""
echo "🔧 快速开始:"
echo "  1. 查看状态: mail-token-admin status"
echo "  2. 测试认证: mail-token-admin test"
echo "  3. 查看日志: mail-token-admin logs"
echo ""
echo "📖 详细信息请查看部署报告: $REPORT_FILE"
