#!/usr/bin/env node

/**
 * 检查最近的邮件同步情况
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkRecentEmails() {
  try {
    console.log('=== 检查最近的邮件同步情况 ===\n');

    // 1. 检查最近10分钟的邮件
    const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000);
    
    const recentEmails = await prisma.email.findMany({
      where: {
        createdAt: {
          gte: tenMinutesAgo
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 10,
      select: {
        id: true,
        subject: true,
        senderEmail: true,
        senderName: true,
        receivedAt: true,
        createdAt: true,
        isRead: true,
        user: { select: { email: true } },
        folder: { select: { name: true, type: true } }
      }
    });

    console.log(`📧 最近10分钟内的邮件 (${recentEmails.length} 封):`);
    
    if (recentEmails.length === 0) {
      console.log('   没有找到最近的邮件');
    } else {
      recentEmails.forEach((email, index) => {
        const timeDiff = Date.now() - new Date(email.createdAt).getTime();
        const minutesAgo = Math.floor(timeDiff / (1000 * 60));
        const secondsAgo = Math.floor((timeDiff % (1000 * 60)) / 1000);
        
        console.log(`\n   ${index + 1}. ${email.subject || '(无主题)'}`);
        console.log(`      用户: ${email.user.email}`);
        console.log(`      发件人: ${email.senderEmail} (${email.senderName || ''})`);
        console.log(`      文件夹: ${email.folder.name} (${email.folder.type})`);
        console.log(`      接收时间: ${email.receivedAt}`);
        console.log(`      入库时间: ${email.createdAt} (${minutesAgo}分${secondsAgo}秒前)`);
        console.log(`      状态: ${email.isRead ? '已读' : '未读'}`);
      });
    }

    // 2. 检查所有邮件的总数
    const totalEmails = await prisma.email.count({
      where: { isDeleted: false }
    });
    
    console.log(`\n📊 数据库中总邮件数: ${totalEmails}`);

    // 3. 检查各用户的邮件数量
    const emailsByUser = await prisma.email.groupBy({
      by: ['userId'],
      where: { isDeleted: false },
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } }
    });

    console.log('\n👥 各用户邮件数量:');
    for (const userStat of emailsByUser) {
      const user = await prisma.user.findUnique({
        where: { id: userStat.userId },
        select: { email: true }
      });
      console.log(`   ${user?.email || `用户ID:${userStat.userId}`}: ${userStat._count.id} 封`);
    }

    // 4. 检查最新邮件的详细信息
    const latestEmail = await prisma.email.findFirst({
      where: { isDeleted: false },
      orderBy: { createdAt: 'desc' },
      select: {
        id: true,
        subject: true,
        senderEmail: true,
        senderName: true,
        receivedAt: true,
        createdAt: true,
        isRead: true,
        content: true,
        user: { select: { email: true } },
        folder: { select: { name: true, type: true } }
      }
    });

    if (latestEmail) {
      console.log('\n📮 最新邮件详情:');
      console.log(`   ID: ${latestEmail.id}`);
      console.log(`   主题: ${latestEmail.subject || '(无主题)'}`);
      console.log(`   发件人: ${latestEmail.senderEmail} (${latestEmail.senderName || ''})`);
      console.log(`   用户: ${latestEmail.user.email}`);
      console.log(`   文件夹: ${latestEmail.folder.name} (${latestEmail.folder.type})`);
      console.log(`   接收时间: ${latestEmail.receivedAt}`);
      console.log(`   入库时间: ${latestEmail.createdAt}`);
      console.log(`   状态: ${latestEmail.isRead ? '已读' : '未读'}`);
      console.log(`   内容长度: ${latestEmail.content?.length || 0} 字符`);
    }

    // 5. 检查是否有重复邮件
    const duplicateCheck = await prisma.$queryRaw`
      SELECT messageId, COUNT(*) as count 
      FROM emails 
      WHERE isDeleted = 0 AND messageId IS NOT NULL
      GROUP BY messageId 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 5
    `;

    if (duplicateCheck.length > 0) {
      console.log('\n⚠️ 发现重复邮件:');
      duplicateCheck.forEach(dup => {
        console.log(`   MessageID: ${dup.messageId} (${dup.count} 次)`);
      });
    } else {
      console.log('\n✅ 没有发现重复邮件');
    }

    // 6. 检查同步时间分布
    const syncTimeDistribution = await prisma.$queryRaw`
      SELECT 
        DATE_FORMAT(createdAt, '%Y-%m-%d %H:%i') as time_slot,
        COUNT(*) as email_count
      FROM emails 
      WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        AND isDeleted = 0
      GROUP BY time_slot
      ORDER BY time_slot DESC
      LIMIT 10
    `;

    if (syncTimeDistribution.length > 0) {
      console.log('\n⏰ 最近1小时邮件同步时间分布:');
      syncTimeDistribution.forEach(slot => {
        console.log(`   ${slot.time_slot}: ${slot.email_count} 封邮件`);
      });
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkRecentEmails();
