import { useEffect } from "react";
import {
  List,
  Avatar,
  Button,
  Tooltip,
  Empty,
  Spin,
  Typography,
} from "antd";
import {
  ReloadOutlined,
  UserOutlined,
  MailOutlined,
  MailFilled,
} from "@ant-design/icons";
import { useEmailStore } from "../store/emailStore";
import type { Email } from "../types/email";

const { Text } = Typography;

const EmailTrash: React.FC = () => {
  const {
    emails,
    selectedEmail,
    loading,
    fetchEmails,
    setSelectedEmail,
    updateEmail,
    syncEmails,
  } = useEmailStore();

  useEffect(() => {
    // 获取垃圾箱邮件
    fetchEmails({ folderType: "trash" });
  }, [fetchEmails]);

  // 处理邮件点击
  const handleEmailClick = async (email: Email) => {
    setSelectedEmail(email);
    
    // 如果邮件未读，标记为已读
    if (!email.isRead) {
      await updateEmail(email.id, { isRead: true });
    }
  };

  return (
    <div className="email-two-column">
      {/* 邮件列表 */}
      <div className="email-list-panel">
        <div className="list-header">
          <div className="list-title">
            <h3>垃圾箱</h3>
          </div>
          <Tooltip title="刷新">
            <Button
              type="text"
              icon={<ReloadOutlined spin={loading} />}
              onClick={syncEmails}
              disabled={loading}
            />
          </Tooltip>
        </div>

        <div className="email-list">
          <Spin spinning={loading}>
            {emails.length === 0 ? (
              <Empty description="垃圾箱为空" />
            ) : (
              <List
                dataSource={emails}
                renderItem={(email) => (
                  <List.Item
                    className={`email-item ${
                      selectedEmail?.id === email.id ? "selected" : ""
                    } ${!email.isRead ? "unread" : ""}`}
                    onClick={() => handleEmailClick(email)}
                  >
                    <List.Item.Meta
                      avatar={
                        <div className="email-avatar-container">
                          <Avatar
                            icon={<UserOutlined />}
                            size={40}
                          />
                          {/* 邮件状态图标 */}
                          <div className="email-status-icon">
                            {email.isRead ? (
                              <MailOutlined className="read-icon" />
                            ) : (
                              <MailFilled className="unread-icon" />
                            )}
                          </div>
                        </div>
                      }
                      title={
                        <div className="email-title">
                          <Text strong={!email.isRead}>
                            {email.senderName || email.senderEmail}
                          </Text>
                          <Text type="secondary" className="email-time">
                            {email.deletedAt
                              ? new Date(email.deletedAt).toLocaleDateString()
                              : ""}
                          </Text>
                        </div>
                      }
                      description={
                        <div className="email-preview">
                          <div className="email-subject">
                            {email.subject || "(无主题)"}
                          </div>
                          <div className="email-content">
                            {email.contentText?.substring(0, 100) || "(无内容)"}
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            )}
          </Spin>
        </div>
      </div>

      {/* 邮件详情 */}
      <div className="email-detail-panel">
        {selectedEmail ? (
          <div className="email-detail">
            <div className="detail-header">
              <h2>{selectedEmail.subject || "(无主题)"}</h2>
              <div className="email-meta">
                <Avatar
                  icon={<UserOutlined />}
                  size={32}
                />
                <div className="sender-info">
                  <div className="sender-name">
                    {selectedEmail.senderName || selectedEmail.senderEmail}
                  </div>
                  <div className="email-date">
                    {selectedEmail.deletedAt
                      ? new Date(selectedEmail.deletedAt).toLocaleString()
                      : ""}
                  </div>
                </div>
              </div>
            </div>
            
            <div className="detail-content">
              <div
                dangerouslySetInnerHTML={{
                  __html: selectedEmail.contentHtml || selectedEmail.contentText || "",
                }}
              />
            </div>
          </div>
        ) : (
          <div className="no-email-selected">
            <Empty description="请选择一封邮件查看详情" />
          </div>
        )}
      </div>
    </div>
  );
};

export default EmailTrash;
