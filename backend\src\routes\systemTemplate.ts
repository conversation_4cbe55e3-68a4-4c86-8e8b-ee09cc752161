import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate, requireAdmin } from '../middleware/auth';
import * as systemTemplateController from '../controllers/systemTemplateController';

const router = Router();

// 所有路由都需要认证和管理员权限
router.use(authenticate);
router.use(requireAdmin);

// 获取系统模板列表
router.get('/',
  asyncHandler(systemTemplateController.getSystemTemplates)
);

// 更新系统模板
router.put('/:id',
  asyncHandler(systemTemplateController.updateSystemTemplate)
);

export default router;
