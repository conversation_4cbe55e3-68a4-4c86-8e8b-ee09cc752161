import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useAuthStore } from '../../store/authStore';
import type { LoginData, RegisterData } from '../../types';

// Mock the authApi module
vi.mock('../../services/authApi', () => ({
  login: vi.fn(),
  register: vi.fn(),
  logout: vi.fn(),
  getCurrentUser: vi.fn(),
}));

import * as authApi from '../../services/authApi';

describe('AuthStore', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset store state
    useAuthStore.setState({
      user: null,
      isAuthenticated: false,
      loading: false,
      error: null,
    });
    
    // Clear localStorage
    localStorage.clear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('login', () => {
    it('should login successfully', async () => {
      const loginData: LoginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockAuthResponse = {
        user: {
          id: 1,
          email: '<EMAIL>',
          username: 'testuser',
          displayName: 'Test User',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
      };

      vi.mocked(authApi.login).mockResolvedValue(mockAuthResponse);

      const store = useAuthStore.getState();
      await store.login(loginData);

      const newState = useAuthStore.getState();

      expect(authApi.login).toHaveBeenCalledWith(loginData);
      expect(localStorage.setItem).toHaveBeenCalledWith('accessToken', 'mock-access-token');
      expect(localStorage.setItem).toHaveBeenCalledWith('refreshToken', 'mock-refresh-token');
      expect(newState.user).toEqual(mockAuthResponse.user);
      expect(newState.isAuthenticated).toBe(true);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBeNull();
    });

    it('should handle login failure', async () => {
      const loginData: LoginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      const mockError = {
        response: {
          data: {
            message: '邮箱或密码错误',
          },
        },
      };

      vi.mocked(authApi.login).mockRejectedValue(mockError);

      const store = useAuthStore.getState();
      
      await expect(store.login(loginData)).rejects.toEqual(mockError);

      const newState = useAuthStore.getState();

      expect(authApi.login).toHaveBeenCalledWith(loginData);
      expect(newState.user).toBeNull();
      expect(newState.isAuthenticated).toBe(false);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBe('邮箱或密码错误');
    });

    it('should handle login failure with generic error', async () => {
      const loginData: LoginData = {
        email: '<EMAIL>',
        password: 'password123',
      };

      const mockError = new Error('Network error');
      vi.mocked(authApi.login).mockRejectedValue(mockError);

      const store = useAuthStore.getState();
      
      await expect(store.login(loginData)).rejects.toEqual(mockError);

      const newState = useAuthStore.getState();

      expect(newState.error).toBe('Network error');
    });
  });

  describe('register', () => {
    it('should register successfully', async () => {
      const registerData: RegisterData = {
        email: '<EMAIL>',
        username: 'newuser',
        password: 'password123',
        displayName: 'New User',
      };

      const mockAuthResponse = {
        user: {
          id: 2,
          email: '<EMAIL>',
          username: 'newuser',
          displayName: 'New User',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        accessToken: 'mock-access-token',
        refreshToken: 'mock-refresh-token',
      };

      vi.mocked(authApi.register).mockResolvedValue(mockAuthResponse);

      const store = useAuthStore.getState();
      await store.register(registerData);

      const newState = useAuthStore.getState();

      expect(authApi.register).toHaveBeenCalledWith(registerData);
      expect(localStorage.setItem).toHaveBeenCalledWith('accessToken', 'mock-access-token');
      expect(localStorage.setItem).toHaveBeenCalledWith('refreshToken', 'mock-refresh-token');
      expect(newState.user).toEqual(mockAuthResponse.user);
      expect(newState.isAuthenticated).toBe(true);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBeNull();
    });

    it('should handle registration failure', async () => {
      const registerData: RegisterData = {
        email: '<EMAIL>',
        username: 'existinguser',
        password: 'password123',
      };

      const mockError = {
        response: {
          data: {
            message: '邮箱或用户名已存在',
          },
        },
      };

      vi.mocked(authApi.register).mockRejectedValue(mockError);

      const store = useAuthStore.getState();
      
      await expect(store.register(registerData)).rejects.toEqual(mockError);

      const newState = useAuthStore.getState();

      expect(authApi.register).toHaveBeenCalledWith(registerData);
      expect(newState.user).toBeNull();
      expect(newState.isAuthenticated).toBe(false);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBe('邮箱或用户名已存在');
    });
  });

  describe('logout', () => {
    it('should logout successfully', async () => {
      // Set initial authenticated state
      useAuthStore.setState({
        user: {
          id: 1,
          email: '<EMAIL>',
          username: 'testuser',
          displayName: 'Test User',
          isActive: true,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: '2024-01-01T00:00:00Z',
        },
        isAuthenticated: true,
      });

      vi.mocked(authApi.logout).mockResolvedValue();

      const store = useAuthStore.getState();
      store.logout();

      const newState = useAuthStore.getState();

      expect(authApi.logout).toHaveBeenCalled();
      expect(localStorage.removeItem).toHaveBeenCalledWith('accessToken');
      expect(localStorage.removeItem).toHaveBeenCalledWith('refreshToken');
      expect(newState.user).toBeNull();
      expect(newState.isAuthenticated).toBe(false);
      expect(newState.error).toBeNull();
    });
  });

  describe('getCurrentUser', () => {
    it('should get current user successfully', async () => {
      const mockUser = {
        id: 1,
        email: '<EMAIL>',
        username: 'testuser',
        role: 'user' as const,
        isActive: true,
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-01T00:00:00Z',
      };

      // 设置token以便getCurrentUser能够执行
      vi.mocked(localStorage.getItem).mockReturnValue('mock-token');

      vi.mocked(authApi.getCurrentUser).mockResolvedValue(mockUser);

      const store = useAuthStore.getState();
      await store.getCurrentUser();

      const newState = useAuthStore.getState();

      expect(authApi.getCurrentUser).toHaveBeenCalled();
      expect(newState.user).toEqual(mockUser);
      expect(newState.isAuthenticated).toBe(true);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBeNull();
    });

    it('should handle getCurrentUser failure', async () => {
      const mockError = {
        response: {
          data: {
            message: 'Unauthorized',
          },
        },
      };

      // 设置token以便getCurrentUser能够执行
      vi.mocked(localStorage.getItem).mockReturnValue('mock-token');
      vi.mocked(authApi.getCurrentUser).mockRejectedValue(mockError);

      const store = useAuthStore.getState();
      await store.getCurrentUser();

      const newState = useAuthStore.getState();

      expect(authApi.getCurrentUser).toHaveBeenCalled();
      expect(newState.user).toBeNull();
      expect(newState.isAuthenticated).toBe(false);
      expect(newState.loading).toBe(false);
      expect(newState.error).toBe('Unauthorized');
    });
  });

  describe('utility methods', () => {
    it('should clear error', () => {
      // Set initial error state
      useAuthStore.setState({
        error: 'Some error message',
      });

      const store = useAuthStore.getState();
      store.clearError();

      const newState = useAuthStore.getState();
      expect(newState.error).toBeNull();
    });

    it('should set loading state', () => {
      const store = useAuthStore.getState();
      
      store.setLoading(true);
      expect(useAuthStore.getState().loading).toBe(true);

      store.setLoading(false);
      expect(useAuthStore.getState().loading).toBe(false);
    });
  });
});
