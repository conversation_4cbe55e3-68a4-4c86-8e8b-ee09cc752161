-- 修复数据库结构，添加缺失的字段

-- 1. 添加子账户相关字段到users表
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS parent_user_id INT NULL,
ADD COLUMN IF NOT EXISTS account_type VARCHAR(191) NOT NULL DEFAULT 'main',
ADD COLUMN IF NOT EXISTS max_sub_accounts INT NOT NULL DEFAULT 5,
ADD COLUMN IF NOT EXISTS sub_account_quota BIGINT NULL,
ADD COLUMN IF NOT EXISTS is_sub_account_enabled BOOLEAN NOT NULL DEFAULT false;

-- 2. 添加外键约束（如果不存在）
-- 注意：MySQL不支持IF NOT EXISTS for foreign keys，所以我们需要先检查

-- 3. 创建app_passwords表（如果不存在）
CREATE TABLE IF NOT EXISTS app_passwords (
  id int NOT NULL AUTO_INCREMENT,
  user_id int NOT NULL,
  name varchar(191) NOT NULL,
  password varchar(191) NOT NULL,
  purpose varchar(191) NOT NULL DEFAULT 'imap',
  is_active boolean NOT NULL DEFAULT true,
  last_used_at datetime(3) NULL,
  expires_at datetime(3) NULL,
  created_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
  updated_at datetime(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3),
  PRIMARY KEY (id),
  UNIQUE KEY app_passwords_user_id_name_key (user_id, name),
  KEY app_passwords_user_id_fkey (user_id),
  CONSTRAINT app_passwords_user_id_fkey FOREIGN KEY (user_id) REFERENCES users (id) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 4. 添加索引
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_parent_user_id (parent_user_id);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_account_type (account_type);
ALTER TABLE users ADD INDEX IF NOT EXISTS idx_sub_account_enabled (is_sub_account_enabled);
