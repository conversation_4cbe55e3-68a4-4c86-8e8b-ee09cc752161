import { Response } from 'express';
import { AuthenticatedRequest, AppError, ApiResponse } from '../types';
import prisma from '../config/database';
import logger from '../utils/logger';

interface SystemTemplate {
  id: string;
  name: string;
  type: 'welcome' | 'notification' | 'bounce' | 'custom';
  subject: string;
  content: string;
  htmlContent?: string;
  description?: string;
  isActive: boolean;
}

interface SystemTemplateData {
  name: string;
  type: 'welcome' | 'notification' | 'bounce' | 'custom';
  subject: string;
  content: string;
  htmlContent?: string;
  description?: string;
  isActive?: boolean;
}

// 获取系统模板列表
export const getSystemTemplates = async (req: AuthenticatedRequest, res: Response) => {
  try {
    // 从数据库获取系统模板
    const templates = await prisma.emailTemplate.findMany({
      where: {
        userId: null, // 系统模板的userId为null
      },
      select: {
        id: true,
        name: true,
        subject: true,
        content: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // 如果没有系统模板，创建默认模板
    if (templates.length === 0) {
      await createDefaultSystemTemplates();
      // 重新获取模板
      const newTemplates = await prisma.emailTemplate.findMany({
        where: {
          userId: null,
        },
        select: {
          id: true,
          name: true,
          subject: true,
          content: true,
          isActive: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy: {
          createdAt: 'asc',
        },
      });

      const response: ApiResponse = {
        success: true,
        message: '获取系统模板列表成功',
        data: newTemplates.map(template => ({
          ...template,
          type: getTemplateType(template.name),
          htmlContent: template.content,
          description: getTemplateDescription(template.name),
        })),
      };

      return res.json(response);
    }

    const response: ApiResponse = {
      success: true,
      message: '获取系统模板列表成功',
      data: templates.map(template => ({
        ...template,
        type: getTemplateType(template.name),
        htmlContent: template.content,
        description: getTemplateDescription(template.name),
      })),
    };

    res.json(response);
  } catch (error) {
    logger.error('获取系统模板列表失败:', error);
    throw new AppError('获取系统模板列表失败', 500);
  }
};

// 更新系统模板
export const updateSystemTemplate = async (req: AuthenticatedRequest, res: Response) => {
  const { id } = req.params;
  const { name, subject, content, htmlContent, description, isActive }: SystemTemplateData = req.body;

  try {
    const template = await prisma.emailTemplate.findFirst({
      where: {
        id: parseInt(id),
        userId: null, // 确保是系统模板
      },
    });

    if (!template) {
      throw new AppError('系统模板不存在', 404);
    }

    const updatedTemplate = await prisma.emailTemplate.update({
      where: { id: parseInt(id) },
      data: {
        name,
        subject,
        content: htmlContent || content,
        isActive: isActive !== undefined ? isActive : template.isActive,
      },
      select: {
        id: true,
        name: true,
        subject: true,
        content: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    const response: ApiResponse = {
      success: true,
      message: '系统模板更新成功',
      data: {
        ...updatedTemplate,
        type: getTemplateType(updatedTemplate.name),
        htmlContent: updatedTemplate.content,
        description: description || getTemplateDescription(updatedTemplate.name),
      },
    };

    res.json(response);
  } catch (error) {
    if (error instanceof AppError) {
      throw error;
    }
    logger.error('更新系统模板失败:', error);
    throw new AppError('更新系统模板失败', 500);
  }
};

// 创建默认系统模板
async function createDefaultSystemTemplates() {
  const defaultTemplates = [
    {
      name: '欢迎邮件模板',
      subject: '欢迎使用邮箱系统 - {{username}}',
      content: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">欢迎使用邮箱系统</h2>
          <p>亲爱的 {{username}}，</p>
          <p>欢迎加入我们的邮箱系统！您的账户已成功创建。</p>
          <p>现在您可以：</p>
          <ul>
            <li>发送和接收邮件</li>
            <li>管理联系人</li>
            <li>创建邮件模板</li>
            <li>设置邮件规则</li>
          </ul>
          <div style="text-align: center; margin: 30px 0;">
            <a href="https://mail.blindedby.love"
               style="background-color: #28a745; color: white; padding: 12px 30px;
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              开始使用
            </a>
          </div>
          <p>如果您有任何问题，请随时联系我们的支持团队。</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px; text-align: center;">
            邮箱系统团队
          </p>
        </div>
      `,
      isActive: true,
    },
    {
      name: '退信通知模板',
      subject: '邮件投递失败通知 - {{originalSubject}}',
      content: `
        <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #d32f2f; text-align: center;">邮件投递失败通知</h2>
          <p>您好，</p>
          <p>您发送的邮件投递失败，详细信息如下：</p>
          <div style="background-color: #f5f5f5; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>收件人：</strong> {{originalRecipientEmail}}</p>
            <p><strong>主题：</strong> {{originalSubject}}</p>
            <p><strong>失败原因：</strong> {{bounceReason}}</p>
            <p><strong>邮件ID：</strong> {{originalMessageId}}</p>
          </div>
          <p>请检查收件人邮箱地址是否正确，或稍后重试。</p>
          <p>如果问题持续存在，请联系系统管理员。</p>
          <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
          <p style="color: #666; font-size: 12px; text-align: center;">
            邮件系统 - 自动通知
          </p>
        </div>
      `,
      isActive: true,
    },
    {
      name: '系统通知模板',
      subject: '系统通知',
      content: `
        <h2>系统通知</h2>
        <p>尊敬的用户，</p>
        <p>这是一条来自邮箱系统的重要通知。</p>
        <p>通知内容：{{content}}</p>
        <p>如有疑问，请联系系统管理员。</p>
        <p>邮箱系统</p>
      `,
      isActive: true,
    },
  ];

  for (const template of defaultTemplates) {
    await prisma.emailTemplate.create({
      data: {
        ...template,
        userId: null, // 系统模板
      },
    });
  }

  logger.info('默认系统模板创建完成');
}

// 辅助函数：根据模板名称获取类型
function getTemplateType(name: string): string {
  if (name.includes('欢迎')) return 'welcome';
  if (name.includes('退信') || name.includes('投递失败')) return 'bounce';
  if (name.includes('通知')) return 'notification';
  return 'custom';
}

// 辅助函数：根据模板名称获取描述
function getTemplateDescription(name: string): string {
  const descriptions: { [key: string]: string } = {
    '欢迎邮件模板': '新用户注册后自动发送的欢迎邮件',
    '退信通知模板': '邮件投递失败时发送给发件人的通知',
    '系统通知模板': '系统维护、更新等重要通知邮件',
  };
  return descriptions[name] || '自定义系统邮件模板';
}

// 获取特定类型的模板（供其他服务使用）
export const getTemplateByType = async (type: string): Promise<SystemTemplate | null> => {
  try {
    const template = await prisma.emailTemplate.findFirst({
      where: {
        userId: null,
        isActive: true,
        name: {
          contains: getTemplateNameKeyword(type),
        },
      },
    });

    if (!template) {
      return null;
    }

    return {
      id: template.id.toString(),
      name: template.name,
      type: type as any,
      subject: template.subject,
      content: template.content,
      htmlContent: template.content,
      description: getTemplateDescription(template.name),
      isActive: template.isActive,
    };
  } catch (error) {
    logger.error(`获取${type}模板失败:`, error);
    return null;
  }
};

// 辅助函数：根据类型获取模板名称关键词
function getTemplateNameKeyword(type: string): string {
  const keywords: { [key: string]: string } = {
    welcome: '欢迎',
    bounce: '退信',
    notification: '通知',
  };
  return keywords[type] || type;
}
