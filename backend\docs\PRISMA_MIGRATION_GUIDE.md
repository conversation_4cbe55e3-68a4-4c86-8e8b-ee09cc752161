# Prisma 数据库迁移完整指南

本文档提供了完整的Prisma数据库迁移步骤，用于解决同步接口的Prisma报错问题。

## 问题诊断

当前遇到的主要问题：
1. **Prisma Schema 不一致**：子账户相关的模型和关系被注释掉了
2. **数据库连接问题**：`Server has closed the connection`
3. **API路径重复**：前端API调用路径错误

## 完整迁移步骤

### 步骤1: 备份数据库

```bash
# 备份当前数据库（重要！）
mysqldump -u mailadmin -p mailserver > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 步骤2: 检查环境配置

```bash
# 检查环境变量
cd backend
cat .env | grep DATABASE_URL

# 确保数据库连接字符串正确
# DATABASE_URL="mysql://mailadmin:HOUsc@<EMAIL>:3306/mailserver"
```

### 步骤3: 修复Prisma Schema

确保 `backend/prisma/schema.prisma` 中的子账户相关模型已启用：

```prisma
model User {
  // ... 其他字段
  
  // 子账户相关字段（确保未被注释）
  accountType       String?   @default("main")
  parentUserId      Int?
  isSubAccountEnabled Boolean? @default(false)
  maxSubAccounts    Int?      @default(5)
  
  // 子账户关系（确保未被注释）
  parentUser           User?                 @relation("UserSubAccounts", fields: [parentUserId], references: [id], onDelete: Cascade)
  subAccounts          User[]                @relation("UserSubAccounts")
  
  // 子账户相关表（确保未被注释）
  subAccountPermissions SubAccountPermission[]
  subAccountUsage      SubAccountUsage[]
  parentActivities     SubAccountActivity[] @relation("ParentUserActivities")
  subActivities        SubAccountActivity[] @relation("SubUserActivities")
  sentInvitations      SubAccountInvitation[] @relation("ParentUserInvitations")
  receivedInvitations  SubAccountInvitation[] @relation("SubUserInvitation")
  
  // 索引（确保未被注释）
  @@index([parentUserId], map: "idx_parent_user_id")
  @@index([accountType], map: "idx_account_type")
  @@index([isSubAccountEnabled], map: "idx_sub_account_enabled")
}

// 确保以下模型未被注释
model SubAccountPermission { ... }
model SubAccountUsage { ... }
model SubAccountActivity { ... }
model SubAccountInvitation { ... }
```

### 步骤4: 生成Prisma客户端

```bash
cd backend

# 生成新的Prisma客户端
npm run db:generate
# 或者
npx prisma generate
```

### 步骤5: 推送数据库更改

```bash
# 方法1: 使用 db push（推荐用于开发环境）
npx prisma db push --accept-data-loss

# 方法2: 使用迁移（推荐用于生产环境）
npx prisma migrate dev --name add-sub-account-features

# 如果遇到冲突，可以重置迁移
npx prisma migrate reset --force
```

### 步骤6: 验证数据库结构

```bash
# 检查数据库连接
npx prisma db pull

# 查看数据库结构
npx prisma studio
```

### 步骤7: 重新构建项目

```bash
# 清理构建缓存
npm run clean

# 重新构建
npm run build

# 检查TypeScript错误
npm run type-check
```

### 步骤8: 重启服务

```bash
# 重启后端服务
npm run dev

# 或者在生产环境
npm run start:prod
```

## 自动化迁移脚本

使用提供的自动化脚本：

```bash
# 运行完整迁移脚本
node scripts/migrate-database.js

# 或者使用npm脚本（如果已配置）
npm run db:migrate:full
```

## 验证步骤

### 1. 检查Prisma客户端

```javascript
// 测试代码
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function testConnection() {
  try {
    // 测试基本连接
    const userCount = await prisma.user.count();
    console.log('用户数量:', userCount);
    
    // 测试子账户相关表
    const permissionCount = await prisma.subAccountPermission.count();
    console.log('权限记录数量:', permissionCount);
    
    console.log('✅ 数据库连接正常');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testConnection();
```

### 2. 测试API端点

```bash
# 测试用户管理API
curl -X GET "http://localhost:3001/api/admin/users?page=1&limit=10" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 测试健康检查
curl -X GET "http://localhost:3001/api/health"
```

### 3. 检查前端API调用

确保前端使用正确的API客户端：

```typescript
// ✅ 正确的方式
import api from '../config/api';
const response = await api.get('/admin/users');

// ❌ 错误的方式（会导致路径重复）
const response = await fetch('/api/admin/users');
```

## 常见问题解决

### 问题1: "Server has closed the connection"

**原因**: 数据库连接超时或连接池耗尽

**解决方案**:
```bash
# 重启数据库服务
sudo systemctl restart mysql

# 检查连接池配置
# 在 schema.prisma 中添加：
datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
  relationMode = "prisma"
}
```

### 问题2: "Invalid `prisma.xxx.create()` invocation"

**原因**: Schema与数据库结构不匹配

**解决方案**:
```bash
# 强制同步schema到数据库
npx prisma db push --force-reset

# 或者重新生成迁移
npx prisma migrate reset --force
npx prisma migrate dev
```

### 问题3: TypeScript类型错误

**原因**: Prisma客户端类型未更新

**解决方案**:
```bash
# 重新生成类型
npx prisma generate

# 重启TypeScript服务
# 在VSCode中: Ctrl+Shift+P -> "TypeScript: Restart TS Server"
```

### 问题4: 前端API路径重复

**原因**: 环境变量配置问题

**解决方案**:
```bash
# 检查前端环境变量
cat frontend/.env.local

# 确保配置正确
VITE_API_BASE_URL=http://localhost:3001/api
```

## 生产环境部署

### 1. 迁移脚本

```bash
# 生产环境迁移
npx prisma migrate deploy

# 生成生产客户端
npx prisma generate
```

### 2. 健康检查

```bash
# 添加健康检查端点
curl -f http://localhost:3001/api/health || exit 1
```

### 3. 回滚计划

```bash
# 如果迁移失败，使用备份恢复
mysql -u mailadmin -p mailserver < backup_YYYYMMDD_HHMMSS.sql
```

## 监控和维护

### 1. 日志监控

```bash
# 监控错误日志
tail -f backend/logs/error.log | grep -i prisma

# 监控数据库连接
tail -f backend/logs/app.log | grep -i "database"
```

### 2. 性能监控

```bash
# 检查数据库连接数
SHOW PROCESSLIST;

# 检查慢查询
SHOW VARIABLES LIKE 'slow_query_log';
```

### 3. 定期维护

```bash
# 每周运行一次
npx prisma db pull  # 检查schema一致性
npx prisma generate # 更新客户端
npm run type-check  # 检查类型错误
```

## 总结

完成以上步骤后，Prisma相关的同步接口问题应该得到解决。关键点：

1. ✅ 确保Schema中子账户模型未被注释
2. ✅ 生成最新的Prisma客户端
3. ✅ 推送数据库更改
4. ✅ 修复前端API路径问题
5. ✅ 重启所有服务
6. ✅ 验证功能正常

如果问题仍然存在，请检查具体的错误日志并按照上述故障排除步骤进行处理。
