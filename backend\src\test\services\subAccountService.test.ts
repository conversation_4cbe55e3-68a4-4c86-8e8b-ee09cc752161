import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { subAccountService } from '../../services/subAccountService';
import prisma from '../../config/database';

describe('SubAccountService Random Generation', () => {
  let testUserId: number;
  let testDomainId: number;

  beforeEach(async () => {
    // 创建测试域名
    const domain = await prisma.virtualDomain.create({
      data: {
        name: 'test.com',
        active: 1
      }
    });
    testDomainId = domain.id;

    // 创建测试用户
    const user = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'testuser',
        password: 'hashedpassword',
        mailPassword: 'mailpassword',
        domainId: testDomainId,
        isSubAccountEnabled: true,
        maxSubAccounts: 5
      }
    });
    testUserId = user.id;
  });

  afterEach(async () => {
    // 清理测试数据
    await prisma.user.deleteMany({
      where: {
        OR: [
          { id: testUserId },
          { parentUserId: testUserId }
        ]
      }
    });
    await prisma.virtualDomain.delete({
      where: { id: testDomainId }
    });
  });

  describe('generateRandomUsername', () => {
    it('should generate username with default config', () => {
      const username = subAccountService.generateRandomUsername();
      expect(username).toMatch(/^user[a-z0-9]+$/);
      expect(username.length).toBeGreaterThanOrEqual(3);
      expect(username.length).toBeLessThanOrEqual(20);
    });

    it('should generate username with custom prefix', () => {
      const username = subAccountService.generateRandomUsername({
        usernamePrefix: 'test',
        usernameLength: 10
      });
      expect(username).toMatch(/^test[a-z0-9]+$/);
      expect(username.length).toBe(10);
    });

    it('should generate username without numbers when disabled', () => {
      const username = subAccountService.generateRandomUsername({
        usernamePrefix: 'test',
        includeNumbers: false,
        usernameLength: 8
      });
      expect(username).toMatch(/^test[a-z]+$/);
      expect(username).not.toMatch(/[0-9]/);
    });

    it('should generate username with special characters when enabled', () => {
      const username = subAccountService.generateRandomUsername({
        usernamePrefix: 'test',
        includeSpecialChars: true,
        usernameLength: 10
      });
      expect(username).toMatch(/^test[a-z0-9_]+$/);
    });
  });

  describe('generateRandomEmail', () => {
    it('should generate email with domain from parent user', async () => {
      const email = await subAccountService.generateRandomEmail(testUserId);
      expect(email).toMatch(/^sub[a-z0-9]+@test\.com$/);
    });

    it('should generate email with custom domain config', async () => {
      const email = await subAccountService.generateRandomEmail(testUserId, {
        emailDomain: 'custom.com',
        usernamePrefix: 'custom'
      });
      expect(email).toMatch(/^custom[a-z0-9]+@custom\.com$/);
    });
  });

  describe('generateRandomDisplayName', () => {
    it('should generate display name with adjective and noun', () => {
      const displayName = subAccountService.generateRandomDisplayName();
      expect(displayName).toMatch(/^[A-Z][a-z]+ [A-Z][a-z]+ \d+$/);
    });
  });

  describe('generateRandomSubAccountData', () => {
    it('should generate complete sub account data', async () => {
      const data = await subAccountService.generateRandomSubAccountData(testUserId);
      
      expect(data.email).toMatch(/^sub[a-z0-9]+@test\.com$/);
      expect(data.username).toMatch(/^user[a-z0-9]+$/);
      expect(data.displayName).toMatch(/^[A-Z][a-z]+ [A-Z][a-z]+ \d+$/);
      expect(data.password).toBeDefined();
      expect(data.password.length).toBeGreaterThanOrEqual(12);
    });

    it('should generate unique data when called multiple times', async () => {
      const data1 = await subAccountService.generateRandomSubAccountData(testUserId);
      const data2 = await subAccountService.generateRandomSubAccountData(testUserId);
      
      expect(data1.email).not.toBe(data2.email);
      expect(data1.username).not.toBe(data2.username);
      expect(data1.password).not.toBe(data2.password);
    });

    it('should handle conflicts by regenerating', async () => {
      // 首先创建一个用户来制造冲突
      const firstData = await subAccountService.generateRandomSubAccountData(testUserId);
      await prisma.user.create({
        data: {
          email: firstData.email,
          username: firstData.username,
          password: 'hashedpassword',
          mailPassword: 'mailpassword',
          domainId: testDomainId,
          parentUserId: testUserId,
          accountType: 'sub'
        }
      });

      // 再次生成应该避免冲突
      const secondData = await subAccountService.generateRandomSubAccountData(testUserId);
      expect(secondData.email).not.toBe(firstData.email);
      expect(secondData.username).not.toBe(firstData.username);
    });
  });
});
