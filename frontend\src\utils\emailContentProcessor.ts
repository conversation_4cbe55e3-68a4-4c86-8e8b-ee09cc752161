import DOMPurify from 'dompurify';

/**
 * 邮件内容处理工具
 * 用于安全地处理和显示邮件内容
 */

export interface ProcessedEmailContent {
  html: string;
  text: string;
  hasContent: boolean;
  isHtml: boolean;
}

/**
 * 处理邮件内容，确保安全显示
 */
export function processEmailContent(
  contentHtml?: string | null,
  contentText?: string | null
): ProcessedEmailContent {
  // 清理和验证HTML内容
  const cleanHtml = contentHtml ? sanitizeHtml(contentHtml) : '';
  
  // 清理和验证文本内容
  const cleanText = contentText ? sanitizeText(contentText) : '';
  
  // 判断是否有有效内容
  const hasHtmlContent = cleanHtml.trim().length > 0;
  const hasTextContent = cleanText.trim().length > 0;
  const hasContent = hasHtmlContent || hasTextContent;
  
  // 如果有HTML内容，优先使用HTML
  if (hasHtmlContent) {
    return {
      html: cleanHtml,
      text: cleanText,
      hasContent: true,
      isHtml: true
    };
  }
  
  // 如果只有文本内容，转换为HTML格式
  if (hasTextContent) {
    const htmlFromText = convertTextToHtml(cleanText);
    return {
      html: htmlFromText,
      text: cleanText,
      hasContent: true,
      isHtml: false
    };
  }
  
  // 没有内容
  return {
    html: '<div class="no-content">此邮件没有内容</div>',
    text: '',
    hasContent: false,
    isHtml: false
  };
}

/**
 * 安全清理HTML内容
 */
function sanitizeHtml(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }
  
  try {
    // 使用DOMPurify清理HTML，移除危险标签和属性
    const cleanHtml = DOMPurify.sanitize(html, {
      ALLOWED_TAGS: [
        'p', 'br', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
        'strong', 'b', 'em', 'i', 'u', 'strike', 'del', 'ins',
        'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
        'a', 'img', 'table', 'thead', 'tbody', 'tr', 'td', 'th',
        'hr', 'small', 'sub', 'sup', 'mark'
      ],
      ALLOWED_ATTR: [
        'href', 'src', 'alt', 'title', 'width', 'height',
        'style', 'class', 'id', 'target', 'rel'
      ],
      ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp|data):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i,
      ADD_ATTR: ['target'],
      ADD_DATA_URI_TAGS: ['img'],
      FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
      FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover']
    });
    
    return cleanHtml;
  } catch (error) {
    console.error('HTML清理失败:', error);
    return convertTextToHtml(html.replace(/<[^>]*>/g, '')); // 移除所有HTML标签作为备选
  }
}

/**
 * 清理文本内容
 */
function sanitizeText(text: string): string {
  if (!text || typeof text !== 'string') {
    return '';
  }
  
  // 移除控制字符，但保留换行符和制表符
  return text.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
}

/**
 * 将纯文本转换为HTML格式
 */
function convertTextToHtml(text: string): string {
  if (!text) {
    return '';
  }
  
  // 转义HTML特殊字符
  const escaped = text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
  
  // 转换换行符为<br>标签
  const withBreaks = escaped.replace(/\r?\n/g, '<br>');
  
  // 转换URL为链接
  const withLinks = withBreaks.replace(
    /(https?:\/\/[^\s<>"]+)/gi,
    '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>'
  );
  
  // 转换邮箱地址为链接
  const withEmailLinks = withLinks.replace(
    /([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/gi,
    '<a href="mailto:$1">$1</a>'
  );
  
  return `<div class="email-text-content">${withEmailLinks}</div>`;
}

/**
 * 提取邮件内容的纯文本预览
 */
export function extractTextPreview(
  contentHtml?: string | null,
  contentText?: string | null,
  maxLength: number = 100
): string {
  // 优先使用文本内容
  if (contentText && contentText.trim()) {
    return contentText.trim().substring(0, maxLength);
  }
  
  // 如果没有文本内容，从HTML中提取
  if (contentHtml && contentHtml.trim()) {
    // 移除HTML标签
    const textFromHtml = contentHtml
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
    
    return textFromHtml.substring(0, maxLength);
  }
  
  return '(无内容)';
}

/**
 * 检查内容是否为HTML格式
 */
export function isHtmlContent(content: string): boolean {
  if (!content) return false;
  
  // 检查是否包含HTML标签
  const htmlTagRegex = /<[^>]+>/;
  return htmlTagRegex.test(content);
}

/**
 * 格式化邮件内容用于回复
 */
export function formatContentForReply(
  originalContent: string,
  isHtml: boolean = true
): string {
  if (!originalContent) {
    return '';
  }
  
  if (isHtml) {
    return `
      <div style="border-left: 3px solid #ccc; padding-left: 10px; margin-left: 10px; color: #666;">
        ${originalContent}
      </div>
    `;
  } else {
    // 为纯文本添加引用标记
    const lines = originalContent.split('\n');
    const quotedLines = lines.map(line => `> ${line}`);
    return quotedLines.join('\n');
  }
}
