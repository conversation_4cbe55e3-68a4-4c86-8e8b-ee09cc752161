import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  List,
  Avatar,
  Button,
  Tooltip,
  Empty,
  Spin,
  Typography,
  Pagination,
  Input,
  Select,
  Space,
  Dropdown,
  Menu,
  Checkbox,
  message,
} from "antd";
import {
  ReloadOutlined,
  UserOutlined,
  MailOutlined,
  MailFilled,
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
  DeleteOutlined,
  StarOutlined,
  CheckOutlined,
  DownOutlined,
  AppstoreOutlined,
  MenuOutlined,
} from "@ant-design/icons";
import { useEmailStore } from "../store/emailStore";
import {
  useViewSettingsStore,
  calculateCompactMode,
} from "../store/viewSettingsStore";
import type { Email } from "../types/email";
import EmailInboxLogic from "../components/EmailInboxLogic";
import EmailPageHeader from "../components/EmailPageHeader";
import api from "../config/api";

const { Text } = Typography;

const EmailInbox: React.FC = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize] = useState(20);
  const [searchText, setSearchText] = useState("");
  const [searchType, setSearchType] = useState("all");
  const [showAdvancedSearch, setShowAdvancedSearch] = useState(false);
  const [filters, setFilters] = useState({
    isRead: "all", // all, read, unread
    hasAttachment: "all", // all, yes, no
    dateRange: "all", // all, today, week, month
    isStarred: "all", // all, starred, unstarred
  });
  const [selectedEmailIds, setSelectedEmailIds] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  // 使用全局视图设置
  const {
    viewMode,
    setViewMode,
    listWidth,
    setListWidth,
    isCompactMode,
    setIsCompactMode,
  } = useViewSettingsStore();

  const {
    emails,
    selectedEmail,
    loading,
    fetchEmails,
    setSelectedEmail,
    updateEmail,
    syncEmails,
  } = useEmailStore();

  useEffect(() => {
    // 获取收件箱邮件
    fetchEmails({ folderType: "inbox" });
  }, [fetchEmails]);

  // 搜索条件变化时重置分页
  useEffect(() => {
    setCurrentPage(1);
  }, [searchText, searchType, filters]);

  // 拖拽处理函数
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    document.body.classList.add("resizing");
    e.preventDefault();
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;

    const containerRect = containerRef.current.getBoundingClientRect();
    const newWidth = e.clientX - containerRect.left;

    // 限制宽度范围 - 最大到右侧内容区的2/3
    const containerWidth = containerRef.current.offsetWidth;
    const maxWidth = Math.floor(containerWidth * 0.67);

    if (newWidth >= 400 && newWidth <= maxWidth) {
      setListWidth(newWidth);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    document.body.classList.remove("resizing");
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener("mousemove", handleMouseMove);
      document.addEventListener("mouseup", handleMouseUp);

      return () => {
        document.removeEventListener("mousemove", handleMouseMove);
        document.removeEventListener("mouseup", handleMouseUp);
      };
    }
  }, [isDragging]);

  const navigate = useNavigate();

  // 搜索过滤邮件
  const filteredEmails = emails.filter((email) => {
    // 文本搜索过滤
    if (searchText) {
      const searchLower = searchText.toLowerCase();
      let textMatch = false;

      switch (searchType) {
        case "sender":
          textMatch =
            (email.senderName?.toLowerCase().includes(searchLower) ?? false) ||
            (email.senderEmail?.toLowerCase().includes(searchLower) ?? false);
          break;
        case "subject":
          textMatch =
            email.subject?.toLowerCase().includes(searchLower) ?? false;
          break;
        case "content":
          textMatch =
            (email.contentText?.toLowerCase().includes(searchLower) ?? false) ||
            (email.contentHtml?.toLowerCase().includes(searchLower) ?? false);
          break;
        default: // "all"
          textMatch =
            (email.senderName?.toLowerCase().includes(searchLower) ?? false) ||
            (email.senderEmail?.toLowerCase().includes(searchLower) ?? false) ||
            (email.subject?.toLowerCase().includes(searchLower) ?? false) ||
            (email.contentText?.toLowerCase().includes(searchLower) ?? false) ||
            (email.contentHtml?.toLowerCase().includes(searchLower) ?? false);
          break;
      }

      if (!textMatch) {
        return false;
      }
    }

    // 高级过滤条件
    // 已读/未读过滤
    if (filters.isRead !== "all") {
      if (filters.isRead === "read" && !email.isRead) return false;
      if (filters.isRead === "unread" && email.isRead) return false;
    }

    // 附件过滤
    if (filters.hasAttachment !== "all") {
      const hasAttachment = email.attachments && email.attachments.length > 0;
      if (filters.hasAttachment === "yes" && !hasAttachment) return false;
      if (filters.hasAttachment === "no" && hasAttachment) return false;
    }

    // 星标过滤
    if (filters.isStarred !== "all") {
      if (filters.isStarred === "starred" && !email.isStarred) return false;
      if (filters.isStarred === "unstarred" && email.isStarred) return false;
    }

    // 日期范围过滤
    if (filters.dateRange !== "all" && email.receivedAt) {
      const emailDate = new Date(email.receivedAt);
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      switch (filters.dateRange) {
        case "today":
          if (emailDate < today) return false;
          break;
        case "week":
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
          if (emailDate < weekAgo) return false;
          break;
        case "month":
          const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
          if (emailDate < monthAgo) return false;
          break;
      }
    }

    return true;
  });

  // 清除搜索
  const handleClearSearch = () => {
    setSearchText("");
    setSearchType("all");
    setFilters({
      isRead: "all",
      hasAttachment: "all",
      dateRange: "all",
      isStarred: "all",
    });
    setShowAdvancedSearch(false);
    setCurrentPage(1);
  };

  // 处理搜索类型变化
  const handleSearchTypeChange = (value: string) => {
    setSearchType(value);
    setCurrentPage(1);
  };

  // 批量操作处理函数
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const currentPageEmails = filteredEmails.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
      );
      setSelectedEmailIds(currentPageEmails.map((email) => email.id));
    } else {
      setSelectedEmailIds([]);
    }
  };

  const handleSelectEmail = (emailId: string, checked: boolean) => {
    if (checked) {
      setSelectedEmailIds((prev) => [...prev, emailId]);
    } else {
      setSelectedEmailIds((prev) => prev.filter((id) => id !== emailId));
    }
  };

  const handleBatchMarkAsRead = async () => {
    try {
      for (const emailId of selectedEmailIds) {
        await updateEmail(emailId, { isRead: true });
      }
      message.success(`已将 ${selectedEmailIds.length} 封邮件标记为已读`);
      setSelectedEmailIds([]);
    } catch (error) {
      message.error("批量标记失败");
    }
  };

  const handleBatchMarkAsUnread = async () => {
    try {
      for (const emailId of selectedEmailIds) {
        await updateEmail(emailId, { isRead: false });
      }
      message.success(`已将 ${selectedEmailIds.length} 封邮件标记为未读`);
      setSelectedEmailIds([]);
    } catch (error) {
      message.error("批量标记失败");
    }
  };

  const handleBatchStar = async () => {
    try {
      for (const emailId of selectedEmailIds) {
        await updateEmail(emailId, { isStarred: true });
      }
      message.success(`已将 ${selectedEmailIds.length} 封邮件加星标`);
      setSelectedEmailIds([]);
    } catch (error) {
      message.error("批量加星标失败");
    }
  };

  const handleBatchDelete = async () => {
    try {
      for (const emailId of selectedEmailIds) {
        await updateEmail(emailId, { isDeleted: true });
      }
      message.success(`已删除 ${selectedEmailIds.length} 封邮件`);
      setSelectedEmailIds([]);
    } catch (error) {
      message.error("批量删除失败");
    }
  };

  // 重置所有邮件为未读（测试用）
  const handleResetToUnread = async () => {
    try {
      console.log('开始重置邮件状态...');
      message.loading('正在重置邮件状态...', 0);

      const response = await api.post('/emails/reset-to-unread');
      message.destroy(); // 清除loading消息

      console.log('重置响应:', response.data);

      if (response.data.success) {
        message.success(response.data.message);
        console.log('开始刷新邮件列表...');
        // 刷新邮件列表
        await fetchEmails({ folderType: "inbox" });
        console.log('邮件列表刷新完成');
      } else {
        message.error('重置失败: ' + (response.data.message || '未知错误'));
      }
    } catch (error: any) {
      message.destroy(); // 清除loading消息
      console.error('重置邮件状态失败:', error);

      if (error.response) {
        console.error('错误响应:', error.response.data);
        message.error(`重置失败: ${error.response.data.message || error.response.statusText}`);
      } else if (error.request) {
        console.error('网络错误:', error.request);
        message.error('网络连接失败，请检查网络');
      } else {
        console.error('其他错误:', error.message);
        message.error('重置失败: ' + error.message);
      }
    }
  };

  // 批量操作菜单项
  const batchActionsMenuItems = [
    {
      key: "markRead",
      icon: <CheckOutlined />,
      label: "标记为已读",
      onClick: handleBatchMarkAsRead,
    },
    {
      key: "markUnread",
      icon: <MailOutlined />,
      label: "标记为未读",
      onClick: handleBatchMarkAsUnread,
    },
    {
      key: "star",
      icon: <StarOutlined />,
      label: "添加星标",
      onClick: handleBatchStar,
    },
    {
      type: "divider" as const,
    },
    {
      key: "delete",
      icon: <DeleteOutlined />,
      label: "删除",
      onClick: handleBatchDelete,
      danger: true,
    },
  ];

  // 处理邮件点击
  const handleEmailClick = async (email: Email) => {
    if (viewMode === "list") {
      // 列表模式：跳转到邮件详情页面
      navigate(`/email/detail/${email.id}`);
    } else {
      // 分栏模式：在右侧显示邮件详情
      setSelectedEmail(email);

      // 如果邮件未读，标记为已读
      if (!email.isRead) {
        await updateEmail(email.id, { isRead: true });
      }
    }
  };

  // 处理星标切换
  const handleStarToggle = async (emailId: string, starred: boolean) => {
    try {
      await updateEmail(emailId, { isStarred: starred });
      message.success(starred ? "已添加星标" : "已移除星标");
    } catch (error) {
      message.error("星标操作失败");
    }
  };

  // 检测是否为紧凑模式（基于列表面板宽度）
  useEffect(() => {
    const checkCompactMode = () => {
      const newCompactMode = calculateCompactMode(viewMode, listWidth);
      setIsCompactMode(newCompactMode);
    };

    checkCompactMode();
    window.addEventListener("resize", checkCompactMode);
    return () => window.removeEventListener("resize", checkCompactMode);
  }, [viewMode, listWidth, setIsCompactMode]); // 依赖viewMode和listWidth

  return (
    <div
      className={`email-container ${viewMode}`}
      ref={containerRef}
      data-view-mode={viewMode}
    >
      <EmailPageHeader
        title="收件箱"
        searchText={searchText}
        searchType={searchType}
        onSearchTextChange={setSearchText}
        onSearchTypeChange={handleSearchTypeChange}
        onClearSearch={handleClearSearch}
        viewMode={viewMode}
        onViewModeChange={setViewMode}
        loading={loading}
        onRefresh={syncEmails}
        showAdvancedSearch={showAdvancedSearch}
        onToggleAdvancedSearch={() =>
          setShowAdvancedSearch(!showAdvancedSearch)
        }
        emails={filteredEmails}
        selectedEmailIds={selectedEmailIds}
        currentPage={currentPage}
        pageSize={pageSize}
        onSelectAll={handleSelectAll}
        onClearSelection={() => setSelectedEmailIds([])}
        batchActionsMenuItems={batchActionsMenuItems}
        onResetToUnread={handleResetToUnread}
      />
      {/* 内容区域 */}
      <div
        className={`email-content flex ${
          viewMode === "list" ? "flex-col" : "flex-row"
        }`}
      >
        {/* 邮件主体容器 - 包含列表、拖拽条、详情面板 */}
        <div className="email-main-container">
          {/* 邮件列表面板 */}
          <div
            className="email-list-panel"
            style={{
              width: viewMode === "list" ? "100%" : `${listWidth}px`,
              minWidth: viewMode === "list" ? "100%" : `${listWidth}px`,
              maxWidth: viewMode === "list" ? "100%" : `${listWidth}px`,
            }}
          >
            {/* 高级搜索面板 */}
            {showAdvancedSearch && (
              <div className="advanced-search-panel">
                <div className="filter-row">
                  <Space wrap>
                    <div className="filter-item">
                      <span className="filter-label">已读状态:</span>
                      <Select
                        value={filters.isRead}
                        onChange={(value) =>
                          setFilters((prev) => ({ ...prev, isRead: value }))
                        }
                        size="small"
                        style={{ width: 100 }}
                      >
                        <Select.Option value="all">全部</Select.Option>
                        <Select.Option value="read">已读</Select.Option>
                        <Select.Option value="unread">未读</Select.Option>
                      </Select>
                    </div>

                    <div className="filter-item">
                      <span className="filter-label">附件:</span>
                      <Select
                        value={filters.hasAttachment}
                        onChange={(value) =>
                          setFilters((prev) => ({
                            ...prev,
                            hasAttachment: value,
                          }))
                        }
                        size="small"
                        style={{ width: 100 }}
                      >
                        <Select.Option value="all">全部</Select.Option>
                        <Select.Option value="yes">有附件</Select.Option>
                        <Select.Option value="no">无附件</Select.Option>
                      </Select>
                    </div>

                    <div className="filter-item">
                      <span className="filter-label">星标:</span>
                      <Select
                        value={filters.isStarred}
                        onChange={(value) =>
                          setFilters((prev) => ({ ...prev, isStarred: value }))
                        }
                        size="small"
                        style={{ width: 100 }}
                      >
                        <Select.Option value="all">全部</Select.Option>
                        <Select.Option value="starred">已星标</Select.Option>
                        <Select.Option value="unstarred">未星标</Select.Option>
                      </Select>
                    </div>

                    <div className="filter-item">
                      <span className="filter-label">时间:</span>
                      <Select
                        value={filters.dateRange}
                        onChange={(value) =>
                          setFilters((prev) => ({ ...prev, dateRange: value }))
                        }
                        size="small"
                        style={{ width: 100 }}
                      >
                        <Select.Option value="all">全部</Select.Option>
                        <Select.Option value="today">今天</Select.Option>
                        <Select.Option value="week">本周</Select.Option>
                        <Select.Option value="month">本月</Select.Option>
                      </Select>
                    </div>

                    <Button
                      size="small"
                      onClick={() =>
                        setFilters({
                          isRead: "all",
                          hasAttachment: "all",
                          dateRange: "all",
                          isStarred: "all",
                        })
                      }
                    >
                      重置过滤器
                    </Button>
                  </Space>
                </div>
              </div>
            )}

            <EmailInboxLogic
              isCompactMode={isCompactMode}
              onEmailClick={handleEmailClick}
              selectedEmail={selectedEmail}
              searchText={searchText}
              selectedEmailIds={selectedEmailIds}
              onSelectEmail={handleSelectEmail}
            />
          </div>

          {/* 拖拽分隔条 */}
          {viewMode === "split" && (
            <div className="resize-handle" onMouseDown={handleMouseDown} />
          )}

          {/* 邮件详情 */}
          {viewMode === "split" && (
            <div className="email-detail-panel">
              {selectedEmail ? (
                <div className="email-detail">
                  <div className="detail-header">
                    <h2>{selectedEmail.subject || "(无主题)"}</h2>
                    <div className="email-meta">
                      <Avatar icon={<UserOutlined />} size={32} />
                      <div className="sender-info">
                        <div className="sender-name">
                          {selectedEmail.senderName ||
                            selectedEmail.senderEmail}
                        </div>
                        <div className="email-date">
                          {selectedEmail.receivedAt
                            ? new Date(
                                selectedEmail.receivedAt
                              ).toLocaleString()
                            : ""}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="detail-content">
                    <div
                      dangerouslySetInnerHTML={{
                        __html:
                          selectedEmail.contentHtml ||
                          selectedEmail.contentText ||
                          "",
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="no-email-selected">
                  <Empty description="请选择一封邮件查看详情" />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailInbox;
