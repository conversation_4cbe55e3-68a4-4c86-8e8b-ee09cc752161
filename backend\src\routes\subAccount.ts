import { Router } from 'express';
import { asyncHandler } from '../middleware/errorHandler';
import { authenticate } from '../middleware/auth';
import {
  requireSubAccountManagePermission,
  requireSubAccountOwnership
} from '../middleware/subAccountAuth';
import * as subAccountController from '../controllers/subAccountController';

const router = Router();

// 所有路由都需要认证
router.use(authenticate);

// 需要子账户管理权限的路由
router.use(requireSubAccountManagePermission);

// 检查是否可以创建子账户
router.get('/can-create',
  asyncHandler(subAccountController.checkCanCreateSubAccount)
);

// 生成随机子账户信息
router.post('/generate-random',
  asyncHandler(subAccountController.generateRandomSubAccountData)
);

// 启用/禁用子账户功能
router.put('/feature-toggle',
  asyncHandler(subAccountController.toggleSubAccountFeature)
);

// 创建子账户
router.post('/',
  asyncHandler(subAccountController.createSubAccount)
);

// 发送子账户邀请
router.post('/invitations',
  asyncHandler(subAccountController.sendSubAccountInvitation)
);

// 接受子账户邀请（公开路由，不需要认证）
router.post('/invitations/:invitationToken/accept',
  asyncHandler(subAccountController.acceptSubAccountInvitation)
);

// 获取子账户列表
router.get('/',
  asyncHandler(subAccountController.getSubAccounts)
);

// 获取子账户详情
router.get('/:subAccountId',
  requireSubAccountOwnership,
  asyncHandler(subAccountController.getSubAccountDetails)
);

// 更新子账户权限
router.put('/:subAccountId/permissions',
  requireSubAccountOwnership,
  asyncHandler(subAccountController.updateSubAccountPermissions)
);

// 更新子账户配额
router.put('/:subAccountId/quota',
  requireSubAccountOwnership,
  asyncHandler(subAccountController.updateSubAccountQuota)
);

// 停用/启用子账户
router.put('/:subAccountId/status',
  requireSubAccountOwnership,
  asyncHandler(subAccountController.toggleSubAccountStatus)
);

// 删除子账户
router.delete('/:subAccountId',
  requireSubAccountOwnership,
  asyncHandler(subAccountController.deleteSubAccount)
);

export default router;
