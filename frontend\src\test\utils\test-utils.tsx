import { ReactElement } from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import zhCN from 'antd/locale/zh_CN';

// Create a custom render function that includes providers
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  initialEntries?: string[];
  queryClient?: QueryClient;
}

const AllTheProviders = ({ 
  children, 
  initialEntries = ['/'],
  queryClient = createTestQueryClient()
}: {
  children: React.ReactNode;
  initialEntries?: string[];
  queryClient?: QueryClient;
}) => {
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ConfigProvider locale={zhCN}>
          {children}
        </ConfigProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

const customRender = (
  ui: ReactElement,
  options: CustomRenderOptions = {}
) => {
  const { initialEntries, queryClient, ...renderOptions } = options;
  
  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders 
        initialEntries={initialEntries}
        queryClient={queryClient}
      >
        {children}
      </AllTheProviders>
    ),
    ...renderOptions,
  });
};

// Mock data factories
export const createMockUser = (overrides = {}) => ({
  id: 1,
  email: '<EMAIL>',
  username: 'testuser',
  displayName: 'Test User',
  isActive: true,
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const createMockEmail = (overrides = {}) => ({
  id: '1',
  messageId: 'message-1',
  userId: 1,
  folderId: 1,
  subject: 'Test Email',
  senderEmail: '<EMAIL>',
  senderName: 'Sender Name',
  recipients: [{ email: '<EMAIL>', name: 'Recipient' }],
  contentText: 'Test email content',
  contentHtml: '<p>Test email content</p>',
  isRead: false,
  isStarred: false,
  isDeleted: false,
  receivedAt: '2024-01-01T00:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  attachments: [],
  labels: [],
  ...overrides,
});

export const createMockFolder = (overrides = {}) => ({
  id: 1,
  userId: 1,
  name: '收件箱',
  type: 'inbox' as const,
  createdAt: '2024-01-01T00:00:00Z',
  _count: { emails: 0 },
  ...overrides,
});

export const createMockContact = (overrides = {}) => ({
  id: 1,
  userId: 1,
  email: '<EMAIL>',
  name: 'Contact Name',
  phone: '+1234567890',
  company: 'Test Company',
  notes: 'Test notes',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const createMockLabel = (overrides = {}) => ({
  id: 1,
  userId: 1,
  name: 'Important',
  color: '#ff0000',
  createdAt: '2024-01-01T00:00:00Z',
  ...overrides,
});

export const createMockAuthResponse = (overrides = {}) => ({
  user: createMockUser(),
  accessToken: 'mock-access-token',
  refreshToken: 'mock-refresh-token',
  ...overrides,
});

export const createMockPaginatedResponse = <T>(data: T[], overrides = {}) => ({
  data,
  pagination: {
    page: 1,
    limit: 20,
    total: data.length,
    totalPages: Math.ceil(data.length / 20),
    hasNext: false,
    hasPrev: false,
  },
  ...overrides,
});

// Mock API response helpers
export const createMockApiResponse = <T>(data: T, overrides = {}) => ({
  data: {
    success: true,
    message: 'Success',
    data,
    ...overrides,
  },
});

export const createMockApiError = (message = 'Error occurred', status = 400) => ({
  response: {
    status,
    data: {
      success: false,
      message,
      error: message,
    },
  },
});

// Test utilities for localStorage
export const mockLocalStorage = () => {
  const store: Record<string, string> = {};
  
  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      Object.keys(store).forEach(key => delete store[key]);
    },
    length: Object.keys(store).length,
    key: (index: number) => Object.keys(store)[index] || null,
  };
};

// Test utilities for WebSocket
export const createMockWebSocket = () => ({
  send: vi.fn(),
  close: vi.fn(),
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  readyState: 1,
  url: 'ws://localhost:3000',
  protocol: '',
  extensions: '',
  bufferedAmount: 0,
  binaryType: 'blob' as BinaryType,
  onopen: null,
  onclose: null,
  onmessage: null,
  onerror: null,
  dispatchEvent: vi.fn(),
});

// Test utilities for form validation
export const expectFormValidation = async (
  getByText: (text: string) => HTMLElement,
  errorMessage: string
) => {
  await waitFor(() => {
    expect(getByText(errorMessage)).toBeInTheDocument();
  });
};

// Test utilities for async operations
export const waitForLoadingToFinish = async () => {
  await waitFor(() => {
    expect(screen.queryByText('加载中...')).not.toBeInTheDocument();
  });
};

// Re-export everything from testing-library
export * from '@testing-library/react';
export { customRender as render };
export { userEvent } from '@testing-library/user-event';

// Import necessary dependencies for the utilities
import { waitFor, screen } from '@testing-library/react';
import { vi } from 'vitest';
