#!/usr/bin/env node

/**
 * 检查邮箱账户状态
 * 查看数据库中的连接状态和错误信息
 */

require('dotenv').config();

async function checkAccountStatus() {
  try {
    console.log('🔍 检查邮箱账户状态...\n');

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    // 查询所有邮箱账户的状态
    const accounts = await prisma.emailAccount.findMany({
      select: {
        id: true,
        email: true,
        connectionStatus: true,
        connectionError: true,
        lastConnectionTest: true,
        isActive: true
      },
      orderBy: {
        id: 'asc'
      }
    });

    console.log('📋 邮箱账户状态:');
    console.log('================');
    
    for (const account of accounts) {
      console.log(`\n账户 ${account.id}: ${account.email}`);
      console.log(`  状态: ${account.connectionStatus || '未知'}`);
      console.log(`  激活: ${account.isActive ? '是' : '否'}`);
      console.log(`  最后测试: ${account.lastConnectionTest || '从未'}`);
      console.log(`  连接错误: ${account.connectionError || '无'}`);

      // 检查是否有"Imap is not defined"错误
      if (account.connectionError && account.connectionError.includes('Imap is not defined')) {
        console.log(`  ⚠️ 发现旧的错误信息!`);
      }
    }

    // 清理旧的错误信息
    console.log('\n🧹 清理旧的错误信息...');
    
    const updateResult = await prisma.emailAccount.updateMany({
      where: {
        connectionError: {
          contains: 'Imap is not defined'
        }
      },
      data: {
        connectionError: null,
        connectionStatus: 'pending'
      }
    });

    console.log(`✅ 已清理 ${updateResult.count} 个账户的旧错误信息`);

    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ 检查失败:', error);
  }
}

if (require.main === module) {
  checkAccountStatus();
}
