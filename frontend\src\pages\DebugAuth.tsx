import { useEffect, useState } from 'react';
import { Card, Button, Space, Typography, Alert, Descriptions, Tag } from 'antd';
import { useAuthStore } from '../store/authStore';
import api from '../config/api';

const { Title, Text, Paragraph } = Typography;

const DebugAuth: React.FC = () => {
  const { user, isAuthenticated, loading, getCurrentUser } = useAuthStore();
  const [tokenInfo, setTokenInfo] = useState<any>(null);
  const [apiTest, setApiTest] = useState<any>(null);

  useEffect(() => {
    // 检查本地存储的token
    const token = localStorage.getItem('accessToken');
    if (token) {
      try {
        // 解析JWT token（不验证签名，仅用于调试）
        const payload = JSON.parse(atob(token.split('.')[1]));
        setTokenInfo({
          token: token.substring(0, 50) + '...',
          payload,
          expired: payload.exp * 1000 < Date.now()
        });
      } catch (error) {
        setTokenInfo({ error: 'Token解析失败' });
      }
    }
  }, []);

  const testApiCall = async () => {
    try {
      const response = await api.get('/auth/me');
      setApiTest({
        success: true,
        data: response.data
      });
    } catch (error: any) {
      setApiTest({
        success: false,
        error: error.response?.data?.message || error.message
      });
    }
  };

  const refreshUserInfo = () => {
    getCurrentUser();
  };

  return (
    <div className="p-6">
      <Card>
        <Title level={2}>用户认证状态调试</Title>
        
        {/* 当前用户状态 */}
        <Card title="当前用户状态" size="small" style={{ marginBottom: 16 }}>
          <Descriptions column={2}>
            <Descriptions.Item label="认证状态">
              <Tag color={isAuthenticated ? 'green' : 'red'}>
                {isAuthenticated ? '已认证' : '未认证'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="加载状态">
              <Tag color={loading ? 'blue' : 'default'}>
                {loading ? '加载中' : '空闲'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="用户ID">
              {user?.id || '无'}
            </Descriptions.Item>
            <Descriptions.Item label="邮箱">
              {user?.email || '无'}
            </Descriptions.Item>
            <Descriptions.Item label="用户名">
              {user?.username || '无'}
            </Descriptions.Item>
            <Descriptions.Item label="角色">
              <Tag color={user?.role === 'admin' ? 'gold' : 'default'}>
                {user?.role || '无'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="显示名">
              {user?.displayName || '无'}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={user?.isActive ? 'green' : 'red'}>
                {user?.isActive ? '活跃' : '禁用'}
              </Tag>
            </Descriptions.Item>
          </Descriptions>

          {user?.role === 'admin' ? (
            <Alert
              message="管理员权限确认"
              description="当前用户具有管理员权限，应该能看到管理员设置菜单"
              type="success"
              showIcon
              style={{ marginTop: 16 }}
            />
          ) : (
            <Alert
              message="非管理员用户"
              description="当前用户不具有管理员权限，无法看到管理员设置菜单"
              type="info"
              showIcon
              style={{ marginTop: 16 }}
            />
          )}
        </Card>

        {/* Token信息 */}
        <Card title="Token信息" size="small" style={{ marginBottom: 16 }}>
          {tokenInfo ? (
            <div>
              {tokenInfo.error ? (
                <Alert message={tokenInfo.error} type="error" />
              ) : (
                <div>
                  <Paragraph>
                    <Text strong>Token:</Text> {tokenInfo.token}
                  </Paragraph>
                  <Paragraph>
                    <Text strong>过期状态:</Text>{' '}
                    <Tag color={tokenInfo.expired ? 'red' : 'green'}>
                      {tokenInfo.expired ? '已过期' : '有效'}
                    </Tag>
                  </Paragraph>
                  <Paragraph>
                    <Text strong>Payload:</Text>
                  </Paragraph>
                  <pre style={{ background: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                    {JSON.stringify(tokenInfo.payload, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          ) : (
            <Alert message="本地存储中没有找到访问令牌" type="warning" />
          )}
        </Card>

        {/* API测试 */}
        <Card title="API测试" size="small" style={{ marginBottom: 16 }}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Button type="primary" onClick={testApiCall}>
              测试 /auth/me API
            </Button>
            
            {apiTest && (
              <div>
                {apiTest.success ? (
                  <Alert
                    message="API调用成功"
                    description={
                      <pre style={{ background: '#f5f5f5', padding: 8, borderRadius: 4 }}>
                        {JSON.stringify(apiTest.data, null, 2)}
                      </pre>
                    }
                    type="success"
                  />
                ) : (
                  <Alert
                    message="API调用失败"
                    description={apiTest.error}
                    type="error"
                  />
                )}
              </div>
            )}
          </Space>
        </Card>

        {/* 操作按钮 */}
        <Card title="调试操作" size="small">
          <Space>
            <Button onClick={refreshUserInfo} loading={loading}>
              刷新用户信息
            </Button>
            <Button 
              onClick={() => {
                localStorage.clear();
                window.location.reload();
              }}
              danger
            >
              清除本地存储并刷新
            </Button>
            <Button 
              onClick={() => {
                console.log('用户状态:', { user, isAuthenticated, loading });
                console.log('Token:', localStorage.getItem('accessToken'));
              }}
            >
              输出到控制台
            </Button>
          </Space>
        </Card>

        {/* 调试说明 */}
        <Card title="调试说明" size="small" style={{ marginTop: 16 }}>
          <Paragraph>
            <Text strong>如果管理员设置菜单不显示，请检查：</Text>
          </Paragraph>
          <ul>
            <li>用户角色是否为 "admin"</li>
            <li>用户是否已正确认证</li>
            <li>Token是否有效且未过期</li>
            <li>/auth/me API是否返回正确的用户信息</li>
            <li>前端路由和导航组件是否正确判断用户角色</li>
          </ul>
          
          <Paragraph style={{ marginTop: 16 }}>
            <Text strong>预期行为：</Text>
          </Paragraph>
          <ul>
            <li>admin用户登录后，应该在导航菜单中看到"管理员设置"选项</li>
            <li>点击"管理员设置"应该能访问管理员功能页面</li>
            <li>非admin用户不应该看到管理员相关菜单</li>
          </ul>
        </Card>
      </Card>
    </div>
  );
};

export default DebugAuth;
