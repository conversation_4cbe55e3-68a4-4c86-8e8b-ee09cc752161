# 多阶段构建 Dockerfile，确保跨平台兼容性
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的构建工具
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# 复制 package 文件
COPY package*.json ./
COPY .npmrc ./

# 清理并安装依赖
RUN npm cache clean --force && \
    npm ci --production=false

# 复制源代码
COPY . .

# 构建项目
RUN npm run build

# 生产阶段 - 使用 nginx 提供静态文件服务
FROM nginx:alpine AS production

# 复制构建结果
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制 nginx 配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
