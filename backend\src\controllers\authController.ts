import { Request, Response } from 'express';
import { AuthenticatedRequest, AppError, ApiResponse, RegisterData, LoginData, JWTPayload } from '../types';

import prisma from '../config/database';
import logger from '../utils/logger';
import { generateAccessToken, generateRefreshToken, verifyToken } from '../utils/jwt';
import SystemEmailService from '../services/systemEmailService';
import { logSecurityEvent, detectAnomalousLogin, detectBruteForceAttack } from '../utils/securityLogger';
import { createUserPassword, verifyUserPassword, updateUserPassword } from '../utils/passwordManager';
import { createUserMailboxFolders } from '../services/mailboxService';
import { ensureDefaultAppPassword } from '../services/simpleAppPasswordService';
// import { sendEmailVerificationCode, verifyEmailCode, EmailVerificationType } from '../services/emailVerificationService';
// import { MailPasswordSyncService } from '../services/mailPasswordSyncService';

// 生成JWT令牌
const generateTokens = (payload: JWTPayload) => {
  const accessToken = generateAccessToken(payload);
  const refreshToken = generateRefreshToken(payload);
  return { accessToken, refreshToken };
};

// 用户注册（暂时移除邮箱验证码检查）
export const register = async (req: Request, res: Response) => {
  const { email, username, password, displayName }: RegisterData = req.body;

  // TODO: 邮箱验证功能暂时搁置，直接设置 emailVerified = true
  // 相关问题已归档到 "邮箱验证问题归档.md"

  // 检查用户是否已存在
  const existingUser = await prisma.user.findFirst({
    where: {
      OR: [
        { email },
        { username },
      ],
    },
  });

  if (existingUser) {
    throw new AppError('邮箱或用户名已存在', 409);
  }

  // 使用新的密码管理器创建密码
  const passwordResult = await createUserPassword(password);

  if (passwordResult.error) {
    throw new AppError(passwordResult.error, 500);
  }

  // 提取域名
  const domain = email.split('@')[1];

  // 确保域名存在于 virtual_domains 表中
  let virtualDomain = await prisma.virtualDomain.findFirst({
    where: { name: domain }
  });

  if (!virtualDomain) {
    virtualDomain = await prisma.virtualDomain.create({
      data: {
        name: domain,
        active: 1
      }
    });
  }

  // 创建用户
  const user = await prisma.user.create({
    data: {
      email,
      username,
      password: passwordResult.webPassword,
      mailPassword: passwordResult.mailPassword || null,
      displayName: displayName || null || null,
      isMailActive: !!passwordResult.mailPassword, // 如果有邮件密码则激活
      emailVerified: true, // 邮箱验证码已验证
      domainId: virtualDomain.id, // 关联到虚拟域名
    },
    select: {
      id: true,
      email: true,
      username: true,
      displayName: true,
      avatarUrl: true,
      role: true,
      createdAt: true,
    },
  });

  // 创建默认邮箱文件夹（数据库记录）
  try {
    await prisma.folder.createMany({
      data: [
        { userId: user.id, name: 'INBOX', type: 'inbox' },
        { userId: user.id, name: 'Sent', type: 'sent' },
        { userId: user.id, name: 'Drafts', type: 'draft' },
        { userId: user.id, name: 'Trash', type: 'trash' },
        { userId: user.id, name: 'Spam', type: 'spam' },
      ],
    });
    logger.info(`为用户 ${email} 创建了默认文件夹记录`);
  } catch (error) {
    logger.error(`创建默认文件夹记录失败 (${email}):`, error);
    // 不影响注册流程
  }

  // 创建物理邮箱文件夹
  try {
    await createUserMailboxFolders(email);
    logger.info(`为用户 ${email} 创建了物理邮箱文件夹`);
  } catch (error) {
    logger.error(`创建物理邮箱文件夹失败 (${email}):`, error);
    // 不影响注册流程，但记录错误
  }

  // 创建默认邮箱账户配置
  try {
    if (passwordResult.mailPassword) {
      const { encrypt } = require('../utils/encryption');

      await prisma.emailAccount.create({
        data: {
          userId: user.id,
          name: '默认邮箱',
          email,
          displayName: user.displayName || user.username,
          imapHost: 'mail.blindedby.love',
          imapPort: 993,
          imapSecure: true,
          imapUsername: email,
          imapPassword: encrypt(passwordResult.mailPassword),
          smtpHost: 'mail.blindedby.love',
          smtpPort: 587,
          smtpSecure: false,
          smtpUsername: email,
          smtpPassword: encrypt(passwordResult.mailPassword),
          authType: 'password',
          isDefault: true,
          isActive: true,
          syncEnabled: true,
          syncInterval: 5,
          autoConfigured: true,
          provider: 'blindedby.love'
        }
      });

      logger.info(`为用户 ${email} 创建了默认邮箱账户配置`);
    }
  } catch (error) {
    logger.error(`创建默认邮箱账户配置失败 (${email}):`, error);
    // 不影响注册流程，但记录错误
  }

  // 创建虚拟别名映射（用户邮箱指向自己）
  // await prisma.virtualAlias.create({
  //   data: {
  //     domainId: virtualDomain.id,
  //     source: email,
  //     destination: email,
  //     active: 1
  //   }
  // });

  // 注意：默认文件夹已在上面创建，这里不再重复创建

  // 生成令牌
  const tokenPayload: JWTPayload = {
    userId: user.id,
    email: user.email,
    username: user.username,
  };

  const { accessToken, refreshToken } = generateTokens(tokenPayload);

  // 保存会话
  await prisma.userSession.create({
    data: {
      id: refreshToken, // 使用refreshToken作为ID
      userId: user.id,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天
      ipAddress: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || null || null,
    },
  });

  // // 发送欢迎邮件
  // try {
  //   await SystemEmailService.sendWelcomeEmail(email, user.username);
  // } catch (error) {
  //   logger.error('发送欢迎邮件失败:', error);
  //   // 不影响注册流程
  // }

  // 记录安全事件
  await logSecurityEvent(user.id, 'user_registered', req.ip || 'unknown', req.get('User-Agent'));

  logger.info(`用户注册成功: ${email}`);

  const response: ApiResponse = {
    success: true,
    message: '注册成功',
    data: {
      user,
      accessToken,
      refreshToken,
    },
  };

  res.status(201).json(response);
};

// 用户登录
export const login = async (req: Request, res: Response) => {
  const { email, password }: LoginData = req.body;

  // 检测暴力破解攻击
  await detectBruteForceAttack(req.ip || 'unknown');

  // 查找用户
  const user = await prisma.user.findUnique({
    where: { email },
  });

  if (!user) {
    // 记录失败的登录尝试
    await logSecurityEvent(null, 'login', req.ip || 'unknown', req.get('User-Agent'), { email }, false);
    throw new AppError('邮箱或密码错误', 401);
  }

  if (!user.isActive) {
    await logSecurityEvent(user.id, 'login', req.ip || 'unknown', req.get('User-Agent'), { reason: 'account_disabled' }, false);
    throw new AppError('账户已被禁用', 401);
  }

  // 使用新的密码管理器验证密码
  const passwordVerification = await verifyUserPassword(user.id, password, user.password, user.mailPassword);

  if (!passwordVerification.isValid) {
    await logSecurityEvent(user.id, 'login', req.ip || 'unknown', req.get('User-Agent'), { reason: 'invalid_password' }, false);
    throw new AppError('邮箱或密码错误', 401);
  }

  // 记录密码迁移或同步状态
  if (passwordVerification.migrated) {
    logger.info(`用户 ${user.email} 密码已迁移到新格式`);
  }
  if (passwordVerification.synced) {
    logger.info(`用户 ${user.email} 邮件密码已同步`);
  }

  // 生成令牌
  const tokenPayload: JWTPayload = {
    userId: user.id,
    email: user.email,
    username: user.username,
  };

  const { accessToken, refreshToken } = generateTokens(tokenPayload);

  // 保存会话
  await prisma.userSession.create({
    data: {
      id: refreshToken, // 使用refreshToken作为ID
      userId: user.id,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天
      ipAddress: req.ip || 'unknown',
      userAgent: req.get('User-Agent') || null || null,
      lastUsedAt: new Date(),
    },
  });

  // 检测异常登录
  await detectAnomalousLogin(user.id, req.ip || 'unknown', req.get('User-Agent'));

  // 记录成功登录
  await logSecurityEvent(user.id, 'login', req.ip || 'unknown', req.get('User-Agent'));

  // 确保用户有默认的应用专用密码（用于IMAP连接）
  try {
    await ensureDefaultAppPassword(user.id);
  } catch (error) {
    logger.warn(`为用户 ${user.email} 创建默认应用专用密码失败:`, error);
    // 不影响登录流程，只记录警告
  }

  logger.info(`用户登录成功: ${email}`);

  const response: ApiResponse = {
    success: true,
    message: '登录成功',
    data: {
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        displayName: user.displayName,
        avatarUrl: user.avatarUrl,
        role: user.role,
        createdAt: user.createdAt,
      },
      accessToken,
      refreshToken,
    },
  };

  res.json(response);
};

// 刷新令牌
export const refreshToken = async (req: Request, res: Response) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new AppError('未提供刷新令牌', 401);
  }

  // 验证刷新令牌
  verifyToken(refreshToken);

  // 查找会话
  const session = await prisma.userSession.findUnique({
    where: { id: refreshToken },
    include: { user: true },
  });

  if (!session || session.expiresAt < new Date()) {
    throw new AppError('刷新令牌无效或已过期', 401);
  }

  // 生成新的令牌
  const tokenPayload: JWTPayload = {
    userId: session.user.id,
    email: session.user.email,
    username: session.user.username,
  };

  const { accessToken, refreshToken: newRefreshToken } = generateTokens(tokenPayload);

  // 删除旧会话并创建新会话
  await prisma.userSession.delete({
    where: { id: session.id },
  });

  await prisma.userSession.create({
    data: {
      id: newRefreshToken,
      userId: session.userId,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      ipAddress: session.ipAddress,
      userAgent: session.userAgent,
      lastUsedAt: new Date(),
    },
  });

  const response: ApiResponse = {
    success: true,
    message: '令牌刷新成功',
    data: {
      accessToken,
      refreshToken: newRefreshToken,
    },
  };

  res.json(response);
};

// 用户登出
export const logout = async (req: AuthenticatedRequest, res: Response) => {
  const { refreshToken } = req.body;

  if (refreshToken) {
    // 删除会话
    await prisma.userSession.deleteMany({
      where: {
        userId: req.user!.id,
        id: refreshToken,
      },
    });
  }

  logger.info(`用户登出: ${req.user!.email}`);

  const response: ApiResponse = {
    success: true,
    message: '登出成功',
  };

  res.json(response);
};

// 获取当前用户信息
export const getCurrentUser = async (req: AuthenticatedRequest, res: Response) => {
  const response: ApiResponse = {
    success: true,
    message: '获取用户信息成功',
    data: req.user,
  };

  res.json(response);
};

// 修改密码
export const changePassword = async (req: AuthenticatedRequest, res: Response) => {
  const { currentPassword, newPassword } = req.body;

  // 获取用户完整信息
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
  });

  if (!user) {
    throw new AppError('用户不存在', 404);
  }

  // 验证当前密码
  const currentPasswordVerification = await verifyUserPassword(user.id, currentPassword, user.password, user.mailPassword);
  if (!currentPasswordVerification.isValid) {
    throw new AppError('当前密码错误', 400);
  }

  // 使用新的密码管理器更新密码
  const updateResult = await updateUserPassword(user.id, newPassword);

  if (!updateResult.success) {
    throw new AppError(updateResult.error || '密码更新失败', 500);
  }

  // 删除所有会话（强制重新登录）
  await prisma.userSession.deleteMany({
    where: { userId: user.id },
  });

  logger.info(`用户修改密码: ${user.email}`);

  const response: ApiResponse = {
    success: true,
    message: '密码修改成功，请重新登录',
  };

  res.json(response);
};

// 更新用户资料
export const updateProfile = async (req: AuthenticatedRequest, res: Response) => {
  const { displayName, avatarUrl } = req.body;

  // 验证输入
  if (displayName && displayName.length > 50) {
    throw new AppError('显示名称不能超过50个字符', 400);
  }

  // 更新用户信息
  const updatedUser = await prisma.user.update({
    where: { id: req.user!.id },
    data: {
      ...(displayName !== undefined && { displayName }),
      ...(avatarUrl !== undefined && { avatarUrl }),
    },
    select: {
      id: true,
      email: true,
      username: true,
      displayName: true,
      avatarUrl: true,
      isActive: true,
      role: true,
      emailVerified: true,
      createdAt: true,
      updatedAt: true,
    },
  });

  logger.info(`用户更新资料: ${updatedUser.email}`);

  const response: ApiResponse = {
    success: true,
    message: '用户资料更新成功',
    data: updatedUser,
  };

  res.json(response);
};

// 上传头像
export const uploadAvatar = async (req: AuthenticatedRequest, res: Response) => {
  // 这里应该处理文件上传逻辑
  // 由于没有配置文件上传中间件，暂时返回一个模拟的URL

  // TODO: 实现真正的文件上传功能
  // 可以使用 multer 中间件处理文件上传
  // 然后将文件保存到服务器或云存储

  const mockAvatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${req.user!.username}`;

  logger.info(`用户上传头像: ${req.user!.email}`);

  const response: ApiResponse = {
    success: true,
    message: '头像上传成功',
    data: { url: mockAvatarUrl },
  };

  res.json(response);
};
