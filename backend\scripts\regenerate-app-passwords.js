// 重新生成应用专用密码
require('dotenv').config();

async function regenerateAppPasswords() {
  try {
    console.log('🔄 重新生成应用专用密码...\n');

    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();

    // 1. 查找所有活跃用户
    console.log('1. 查找活跃用户...');
    const users = await prisma.user.findMany({
      where: {
        isActive: true,
        emailVerified: true,
        isMailActive: true
      },
      select: {
        id: true,
        email: true
      }
    });

    console.log(`找到 ${users.length} 个活跃用户`);

    // 2. 为每个用户重新生成应用专用密码
    for (const user of users) {
      console.log(`\n处理用户: ${user.email}`);

      // 删除旧的应用专用密码
      const deleteResult = await prisma.$executeRaw`
        DELETE FROM app_passwords 
        WHERE user_id = ${user.id} AND purpose = 'imap'
      `;
      console.log(`  删除了 ${deleteResult} 个旧密码`);

      // 生成新的明文密码
      function generateRandomPassword(length) {
        const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let password = '';
        for (let i = 0; i < length; i++) {
          password += charset.charAt(Math.floor(Math.random() * charset.length));
        }
        return password;
      }

      const plainPassword = generateRandomPassword(16);

      // 创建新的应用专用密码记录
      await prisma.$executeRaw`
        INSERT INTO app_passwords (user_id, name, password, purpose, is_active, created_at, updated_at)
        VALUES (${user.id}, 'Default IMAP', ${plainPassword}, 'imap', 1, NOW(), NOW())
      `;

      console.log(`  ✅ 创建新密码: ${plainPassword.substring(0, 8)}...`);
    }

    // 3. 验证结果
    console.log('\n3. 验证结果...');

    // 使用原始SQL查询避免Prisma关系问题
    const appPasswords = await prisma.$queryRaw`
      SELECT ap.id, ap.password, u.email
      FROM app_passwords ap
      JOIN users u ON ap.user_id = u.id
      WHERE ap.purpose = 'imap' AND ap.is_active = 1
      ORDER BY ap.created_at DESC
    ` as any[];

    console.log(`\n✅ 成功创建 ${appPasswords.length} 个应用专用密码:`);
    appPasswords.forEach((pwd, index) => {
      console.log(`  ${index + 1}. ${pwd.email}: ${pwd.password.substring(0, 8)}...`);
    });

    console.log('\n🎉 应用专用密码重新生成完成！');
    
    await prisma.$disconnect();

  } catch (error) {
    console.error('❌ 重新生成失败:', error);
  }
}

// 运行重新生成
regenerateAppPasswords().catch(console.error);
